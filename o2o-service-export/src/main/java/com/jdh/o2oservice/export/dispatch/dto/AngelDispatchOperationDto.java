package com.jdh.o2oservice.export.dispatch.dto;

import lombok.Data;

/**
 * @ClassName AngelDispatchOperationDto
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 17:37
 **/
@Data
public class AngelDispatchOperationDto {

    /**
     * 派单任务明细ID
     */
    private Long dispatchDetailId;

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 服务者履约工单ID
     */
    private Long workId;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 服务单详情地址
     */
    private String workIdDetailUrl;

    /**
     * 操作成功后的提示语
     */
    private String businessCode;

    /**
     * 操作成功后的提示语
     */
    private String businessMsg;
}