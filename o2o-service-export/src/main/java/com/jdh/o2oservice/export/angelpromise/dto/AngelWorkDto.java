package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author:lichen55
 * @Description: 工单详情
 * @date 2024-05-23 21:47
 */
@Data
@NoArgsConstructor
public class AngelWorkDto {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 京东订单Id
     */
    private Long jdOrderId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     */
    private Integer	workType;

    /**
     * 服务工单类型-页面展示用
     */
    private String workTypeDesc;

    /**
     * 服务者Id
     */
    private String angelId;

    /**
     * 服务者Cpin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者联系方式
     */
    private String angelPhone;

    /**
     * 预计服务费用
     */
    private BigDecimal angelCharge;

    /**
     * 服务者工单状态：
     */
    private Integer	status;

    /**
     * 服务者工单状态：
     */
    private String statusDesc;

    /**
     * 任务暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 失效的，如果退款了，则disabled为true
     */
    private Boolean disabled;

    /**
     * 服务站Id
     */
    private String angelStationId;

    /**
     * 着装照片地址
     */
    private List<String> clothingPicUrls;

    /**
     * 医疗废物照片地址
     */
    private List<String> medicalWastePicUrls;

    /**
     * 服务记录地址
     */
    private List<String> serviceRecordPicUrls;

    /**
     * 扩展信息：订单信息{订单Id,订单remark,预约人姓名、联系方式}，医生着装图片，医疗废物处理图片
     */
    private String extend;

    /**
     * 保单Id
     */
    private String insureId;

    /**
     * 投保状态：1=审核中，2=成功，3=失败，4=失效
     */
    private Integer	insureStatus;

    /**
     * 所需全部耗材：（试管*2；血常规检测包*2）
     */
    private String materialNames;

    /**
     * 是否需要上传服务记录，true需要弹出上传服务记录的弹层
     */
    private boolean needServiceRecord;

    /**
     * 是否需要上传服务记录，true要展示服务记录上传楼层
     */
    private boolean needServiceRecordFloor;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 虚拟状态导航栏
     */
    private List<AngelVirtualStatusDto> angelVirtualStatusList;

    /**
     * 任务单信息
     * 被服务者信息、检验项目、耗材
     */
    private List<AngelTaskDto> angelTasks;

    /**
     * 订单信息
     */
    private AngelWorkOrderDto angelWorkOrder;

    /**
     * 投保信息
     */
    private AngelWorkInsureDto angelWorkInsure;

    /**
     * 样本信息列表
     */
    private List<AngelWorkSpecimenDto> angelWorkSpecimens;

    /**
     * 耗材包信息
     */
    private List<AngelWorkMaterialPackageDto> angelWorkMaterialPackages;

    /**
     * 服务记录信息
     */
    private AngelWorkServiceRecordDto angelWorkServiceRecordDto;

    /**
     * 运力楼层信息
     */
    private CourierFloorInfoDto courierFloorInfoDto;

    /**
     * 剩余接单时长，单位秒
     */
    private Integer remainingDuration;

    /**
     * 预计服务费用
     */
    private String angelChargeDesc;

    /**
     * 服务开始时间
     */
    private Date workStartTime;

    /**
     * 服务结束时间
     */
    private Date workEndTime;

    /**
     * workList
     */
    private List<AngelWorkHistoryDto> workHistoryList;

    /**
     * 是否需要展示实验室指引楼层
     */
    private boolean needAcceptGuideFloor;

    /**
     * 是否需要修改按钮
     */
    private boolean canModifyDate;

    /**
     * 是否展示护理单
     */
    private boolean needNurseSheet;

    /**
     * 是否展示沟通记录
     */
    private boolean needCallRecordSheet;

    /**
     * 录音标记
     */
    private boolean audioFlag;

    /**
     * 医嘱楼层
     */
    private DoctorAdviceFloorDto doctorAdviceFloorDto;

    /**
     * 服务开始时间：即时单：label==1，展示下单时间+1h；label==2，今日开始的不展示：次日开始的，展示预约的后一个时间段
     */
    private String serviceStartTimeDesc;

    /**
     * 服务者工单类型
     */
    private Integer angelWorkDeliveryType;
}
