package com.jdh.o2oservice.export.settlement;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.settlement.dto.AngelBankDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementMoneyDto;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:45 下午
 * @Description:
 */
public interface AngelSettleReadExport {


    /**
     * 查询护士结算信息
     *
     * @param param
     * @return
     */
    Response<PageDto<AngelSettlementDto>> querySettlementPage(Map<String, String> param);

    /**
     * 查询账单明细
     *
     * @param param
     * @return
     */
    Response<AngelSettlementDto> querySettlementDetailList(Map<String, String> param);

    /**
     * 查询护士账号金额
     *
     * @param param
     * @return
     */
    Response<AngelSettlementMoneyDto> queryAngelSettlementMoneyDto(Map<String, String> param);

    /**
     * 查询护士明细金额汇总
     *
     * @param param
     * @return
     */
    Response<Map<String, BigDecimal>> querySettleTotal(Map<String, String> param);

    /**
     * 查询护士绑定银行卡
     *
     * @param param
     * @return
     */
    Response<AngelBankDto> queryBindBank(Map<String, String> param);

    /**
     * 护士结算
     *
     * @param param
     * @return
     */
    Response<Long> submitCashOut(Map<String, String> param);
}
