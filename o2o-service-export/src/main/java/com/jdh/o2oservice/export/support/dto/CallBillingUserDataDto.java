package com.jdh.o2oservice.export.support.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description 虚拟号业务数据
 * @Date 2024/12/19 下午6:30
 * <AUTHOR>
 **/
@Data
public class CallBillingUserDataDto implements Serializable {

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务兑换的来源id，兑换id/orderId/outerOrderId
     */
    private String sourceVoucherId;

    /**
     * 通话类型：1-预约人打给服务者 2-服务者打给预约人 3-服务者打给被服务人
     */
    private Integer bizCallType;

    /**
     * 业务单元id，pop业务传venderId，厂直业务传venderCode（包含字母）
     */
    private String buId;

    /**
     * 通话录音下载地址，有效期2小时
     */
    private String recordingFileDownloadUrl;

    /**
     * AXB中的A号码
     */
    private String phoneNoA;

    /**
     * AXB中的B号码
     */
    private String phoneNoB;

    /**
     * 绑定时间
     */
    private Date bindDate;

}
