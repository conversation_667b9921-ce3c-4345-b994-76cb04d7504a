package com.jdh.o2oservice.export.angelpromise.dto;

import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 护士端服务单列表查询，服务单数据
 * @author:lichen55
 * @createTime: 2024-04-23 10:45
 * @Description:
 */
@Data
public class AngelWorkListNodeDto {

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务工单类型：1=骑手,2=护士,3=护工,4=康复师
     */
    private Integer	workType;

    /**
     * 服务工单类型-页面展示用
     */
    private String workTypeDesc;

    /**
     * 预计服务费用
     */
    private BigDecimal angelCharge;

    /**
     * 服务者工单状态：1待接单，2、已接单，3、待服务，4、服务中，5送检中，6服务完成，7退款中，8已退款，9已取消
     */
    private Integer	status;

    /**
     * 服务者工单状态：1待接单，2、已接单，3、待服务，4、服务中，5送检中，6服务完成，7退款中，8已退款，9已取消
     */
    private String statusDesc;

    /**
     * 任务暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 服务详细地址
     */
    private String userFullAddress;
    /**
     * 服务地址经度
     */
    private BigDecimal longitude;
    /**
     * 服务地址纬度
     */
    private BigDecimal latitude;
    /**
     * 服务开始时间
     */
    private String serviceStartTime;

    /**
     * 服务预计耗时
     */
    private Integer servicePlanConsumeTime;

    /**
     * 详情链接
     */
    private String orderDetailLink;

    /**
     * 任务单信息
     * 被服务者信息、检验项目、耗材
     */
    private List<AngelTaskDto> angelTasks;

    /**
     * 检验项信息
     */
    private List<ServiceItemDto> serviceItems;

    /**
     * 服务开始时间描述：即时单：label==1，展示下单时间+1h；label==2，今日开始的不展示：次日开始的，展示预约的后一个时间段
     */
    private String serviceStartTimeDesc;
}
