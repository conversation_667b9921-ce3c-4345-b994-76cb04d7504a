package com.jdh.o2oservice.export.dispatch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName DispatchServiceLocationDto
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 23:07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchServiceLocationDto {

    /**
     * 服务地址ID
     */
    private String serviceLocationId;

    /**
     * 服务地址省ID
     */
    private Integer serviceLocationProvinceId;

    /**
     * 服务地址市ID
     */
    private Integer serviceLocationCityId;

    /**
     * 服务地址区ID
     */
    private Integer serviceLocationDistrictId;

    /**
     * 服务地址详细信息
     */
    private String serviceLocationDetail;

    /**
     * 总距离（米）
     */
    private Double distance;

    /**
     * 方案估算时间（含路况）分钟
     */
    private Double duration;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;
}