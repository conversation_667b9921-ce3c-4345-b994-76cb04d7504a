package com.jdh.o2oservice.export.promise.enums;

import java.util.Objects;

/**
 * PromiseTimelineActionEnum
 *
 */
public enum PromiseTimelineActionEnum {

    /**
     * 创建履约单
     */
    CREATE_PROMISE("CREATE_PROMISE", "创建预约"),
    /**
     * 作废履约单
     */
    INVALID_PROMISE("INVALID_PROMISE", "作废预约"),
    /**
     * 修改履约单
     */
    MODIFY_PROMISE("MODIFY_PROMISE", "修改预约"),
    /**
     * 完成履约单
     */
    COMPLETE_PROMISE("COMPLETE_PROMISE", "服务完成"),
    /**
     * 服务者接单
     */
    RECEIVED_WORK("RECEIVED_WORK", "服务者接单"),
    /**
     * 服务者出发
     */
    DEPART_WORK("DEPART_WORK", "服务者出发"),
    /**
     * 服务者到达
     */
    ARRIVED_WORK("ARRIVED_WORK", "服务者到达"),
    /**
     * 服务者开始服务
     */
    SERVICING_WORK("SERVICING_WORK", "服务者开始服务"),
    /**
     * 服务者开始服务
     */
    SERVICED_WORK("SERVICED_WORK", "服务者完成服务"),
    /**
     * 电话沟通
     */
    CALLED_WORK("CALLED_WORK", "电话沟通"),
    /**
     * 完成评估
     */
    COMPLETE_ASSESS("COMPLETE_ASSESS", "完成评估"),
    ;

    /**
     *
     */
    private String code;
    /**
     *
     */
    private String desc;

    /**
     * @param code
     * @param desc
     */
    PromiseTimelineActionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PromiseTimelineActionEnum matchEnum(Integer voucherOp) {
        if (Objects.isNull(voucherOp)) {
            return null;
        }
        for (PromiseTimelineActionEnum value : PromiseTimelineActionEnum.values()) {
            if (value.getCode().equals(voucherOp)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "PromiseTimelineActionEnum{" +
                "code='" + code + '\'' +
                ", desc='" + desc + '\'' +
                '}';
    }
}
