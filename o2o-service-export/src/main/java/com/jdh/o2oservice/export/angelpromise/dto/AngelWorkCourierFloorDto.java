package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-21 16:18
 * @Description: 样本信息列表
 */
@Data
@NoArgsConstructor
public class AngelWorkCourierFloorDto {

    /**
     * 实验室Id
     */
    private String receiverId;

    /**
     * 实验室名称
     */
    private String receiverName;

    /**
     * 实验室联系方式
     */
    private String receiverPhone;

    /**
     * 实验室联系方式加密
     */
    private String receiverPhoneEncrypt;

    /**
     * 实验室详细地址
     */
    private String receiverFullAddress;

    /**
     * 实验室经度
     */
    private double receiverLng;

    /**
     * 实验室纬度
     */
    private double receiverLat;

    /**
     * 样本类型 1:项目名称 2:样本条码
     */
    private Integer specimenType;

    /**
     * 样本行名称展示列表
     */
    private String specimenNames;

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 外部运单号
     */
    private String outShipNo;

    /**
     * 运单状态
     */
    private Integer shipStatus;

    /**
     * 运单状态描述
     */
    private String shipStatusDesc;

    /**
     * 运单类型：1=自行寄送，2=达达骑手
     */
    private Integer shipType;

    /**
     * 骑手Id
     */
    private String transferId;

    /**
     * 骑手姓名
     */
    private String transferName;

    /**
     * 骑手联系方式
     */
    private String transferPhone;

    /**
     * 骑手联系方式加密
     */
    private String transferPhoneEncrypt;

    /**
     * 骑手轨迹
     */
    private String transferTrack;

    /**
     * 寄件人姓名
     */
    private String senderName;

    /**
     * 寄件人电话
     */
    private String senderPhone;

    /**
     * 寄件人电话加密
     */
    private String senderPhoneEncrypt;

    /**
     * 寄件人地址
     */
    private String senderFullAddress;

    /**
     * 可选运力类型
     */
    private List<AngelDeliveryTypeDTO> shipTypeList;

    /**
     * 接收样本指引
     */
    private String acceptSampleGuide;
}
