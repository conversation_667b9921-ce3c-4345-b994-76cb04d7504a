package com.jdh.o2oservice.export.promise.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.export.promise.enums.PromiseTimelineActionEnum;
import com.jdh.o2oservice.export.promise.enums.PromiseTimelineExtendFieldEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * PromiseTimelineDetailDto
 */
@Data
public class PromiseTimelineDetailDto implements Serializable {

    /**
     * 时间线动作Code
     */
    private PromiseTimelineActionEnum actionEnum;

    /**
     * 发生时间
     */
    private Date createTime;

    /**
     * 扩展信息 JSON
     */
    private String extend;

    /**
     * 事件内容
     */
    public String getContent() {
        String content = "";
        JSONObject extendJson = JSON.parseObject(extend);
        if (Objects.isNull(extendJson)) {
            return content;
        }
        ;
        switch (actionEnum) {
            case CREATE_PROMISE:
                content = "";
                break;
            case INVALID_PROMISE:
                content = "";
                break;
            case MODIFY_PROMISE:
                content = new StringBuilder("新上门时间：")
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.NEW_APPOINTTIME.getField()))
                        .append("\n原上门时间：")
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.OLD_APPOINTTIME.getField()))
                        .toString();
                break;
            case COMPLETE_PROMISE:
                content = "";
                break;
            case RECEIVED_WORK:
                content = new StringBuilder()
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.SERVICER_NAME.getField()))
                        .append("已接单")
                        .toString();
                break;
            case DEPART_WORK:
                content = new StringBuilder()
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.SERVICER_NAME.getField()))
                        .append("已出发")
                        .toString();
                break;
            case ARRIVED_WORK:
                content = new StringBuilder()
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.SERVICER_NAME.getField()))
                        .append("已到达")
                        .toString();
                break;
            case SERVICING_WORK:
                content = new StringBuilder()
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.SERVICER_NAME.getField()))
                        .append("开始服务")
                        .toString();
                break;
            case SERVICED_WORK:
                content = new StringBuilder()
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.SERVICER_NAME.getField()))
                        .append("服务完成")
                        .toString();
                break;
            case CALLED_WORK:
                StringBuilder s = new StringBuilder()
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.CALLER.getField()))
                        .append("电联了")
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.CALLEE.getField()));
                int callDuration = extendJson.getIntValue(PromiseTimelineExtendFieldEnum.CALL_DURATION.getField());
                if (callDuration > 0) {
                    s.append("，通话").append(callDuration).append("秒");
                } else {
                    s.append("，未接通");
                }
                content = s.toString();
                break;
            case COMPLETE_ASSESS:
                content = new StringBuilder("评估师完成了评估，评估结果为")
                        .append(extendJson.getString(PromiseTimelineExtendFieldEnum.ASSESS_RESULT.getField()))
                        .toString();
                break;
        }

        return content;
    }
}
