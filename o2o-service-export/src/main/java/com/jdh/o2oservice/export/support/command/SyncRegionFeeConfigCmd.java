package com.jdh.o2oservice.export.support.command;
import lombok.Data;
import java.io.Serializable;

@Data
public class SyncRegionFeeConfigCmd implements Serializable {

    /**
     * 业务身份: 1 骑手检测 2 护士检测 3 护士护理
     */
    private Integer serviceType;

    /**
     * 渠道id 比如C端(1010645803)、互医(1020410783)
     */
    private String channelId;

    /**
     * 数据清除
     */
    private Boolean justClean = false;

}
