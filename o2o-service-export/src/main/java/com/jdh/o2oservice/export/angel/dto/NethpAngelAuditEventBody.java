package com.jdh.o2oservice.export.angel.dto;

import lombok.Data;

/**
 * @Description: 互医护士审核事件body
 * <AUTHOR>
 * @Date 2024/5/8
 * @Version V1.0
 **/
@Data
public class NethpAngelAuditEventBody {

    /**
     * 主数据医生ID （资质审核驳回时会返回空）
     */
    private Long platformId;

    /**
     * 职业：2-护士，24-上门护士
     */
    private Integer professionType;

    /**
     * 审核类型
     * 1=资质
     * 2=执业
     * 3=信息变更
     * 4=益世身份证审核
     */
    private Integer auditType;

    /**
     * auditType=1资质审核时：
     * 0=基本信息入驻
     * 1=审核通过
     * 2=审核驳回
     *
     * auditType=2执业审核时：
     * 0=待审核
     * 1=审核通过（主体备案通过）
     * 2=审核驳回
     * 3=同步
     * 4=初审通过
     *
     * auditType=3信息变更审核时：
     * 1=审核通过
     * 2=审核驳回
     *
     * auditType=4，益世身份证审核状态：
     * 1=审核通过
     * 2=审核驳回
     */
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    private String auditReason;

    /**
     * 医生Pin
     */
    private String doctorPin;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * json扩展字段
     */
    private String extAttr;
}
