package com.jdh.o2oservice.export.dispatch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName JdhDispatchDetailDto
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 15:08
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhDispatchDetailDto {

    /**
     * 派单任务明细ID
     */
    private Long dispatchDetailId;

    /**
     * 派单任务ID
     */
    private Long dispatchId;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;


    /**
     * 派单任务明细类型 1-派单 2-抢单
     */
    private Integer dispatchDetailType;

    /**
     * 派单明细状态：已派单/已接单/已取消/已过期/已拒绝
     */
    private Integer dispatchDetailStatus;

    /**
     * 派单明细状态描述：已派单/已接单/已取消/已过期/已拒绝
     */
    private String dispatchDetailStatusDesc;

    /**
     * 服务者ID
     */
    private Long angelId;

    /**
     * 服务者名称
     */
    private String angelName;

    /**
     * 外部服务者ID
     */
    private String outAngelId;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 电话
     */
    private String phone;

    /**
     * 派单任务类型 1-即时单 2-预约单
     */
    private Integer dispatchType;

    /**
     * 预计收入
     */
    private String angelCharge;

    /**
     * 预计收入描述，带￥
     */
    private String angelChargeDesc;

    /**
     * 服务地点
     */
    private DispatchServiceLocationDto serviceLocation;

    /**
     * 预约时间
     */
    private DispatchAppointmentTimeDto appointmentTime;

    /**
     * 顾客
     */
    private List<DispatchAppointmentPatientDto> patients;

    /**
     * 备注
     */
    private String remark;

    /**
     * 服务项目数量
     */
    private Integer serviceItemSize;

    /**
     * 服务项目聚合描述
     */
    private String serviceItemAggregatedDesc;

    /**
     * 耗材聚合描述
     */
    private String materialPackageAggregatedDesc;

    /**
     * 剩余接单时长，单位秒
     */
    private Integer remainingDuration;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;

    /**
     * 业务模式
     */
    private String businessModeCode;

    /**
     * 服务类型描述
     */
    private String serviceTypeDesc;

    /**
     * 用户电话-加密
     */
    private String orderUserPhone;

    /**
     * 购买人姓名-加密
     */
    private String orderUserName;

    /**
     * 订单创建时间
     */
    private Date submitOrderTime;

    /**
     * 派单详情页跳转链接
     */
    private String dispatchDetailUrl;

    /**
     * 标签类型 1即时单 2临期单 3其他单
     */
    private Integer label;
    /**
     * 标签类型 1即时单 2临期单 3其他单
     */
    private String labelIcon;

    /**
     * 服务开始时间：即时单：label==1，展示下单时间+1h；label==2，今日开始的不展示：次日开始的，展示预约的后一个时间段
     */
    private String serviceStartTimeDesc;
}