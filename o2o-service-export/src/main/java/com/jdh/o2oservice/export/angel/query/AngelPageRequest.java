package com.jdh.o2oservice.export.angel.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Description: 服务者职业信息分页查询参数
 * <AUTHOR>
 * @Date 2024/4/19
 * @Version V1.0
 **/
@Data
public class AngelPageRequest extends AbstractPageQuery {

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 证件号
     */
    private String idCard;

    /**
     * 电话
     */
    private String phone;

    /**
     * 服务者审批状态
     */
    private Integer auditProcessStatus;

    /**
     * 服务者id集合
     */
    private List<Long> angelIdList;

    /**
     * 互医医生id集合
     */
    private List<Long> nethpDocIdList;

    /**
     * 服务站点id
     */
    private Long stationId;

    /**
     * 挂靠结构id
     */
    private Long jdhProviderId;

    /**
     * 挂靠结构id集合
     */
    private Set<Long> jdhProviderIds;

    /**
     * 站长erp
     */
    private String stationMaster;

    /**
     * 是否关联站长 0未关联 1关联
     */
    private Integer relationStationMasterFlag;

    /**
     * 是否关联服务站 0未关联 1关联
     */
    private Integer relationStationFlag;

    /**
     * 人员标签 0兼职 1全职 2空
     */
    private Integer jobNature;

    /**
     * 接单状态 0关闭 1开启
     * 枚举:AngelTakeOrderStatusEnum
     */
    private Integer takeOrderStatus;

    /**
     * 仅查询结算机构下的护士数据
     */
    private Boolean queryAllJdhProvider;

    /**
     * 姓名脱敏
     */
    private Boolean nameMask = true;

    /**
     * 省地区code
     */
    private String provinceCode;

    /**
     * 市地区code
     */
    private String cityCode;

    /**
     * 县地区code
     */
    private String countyCode;

    /**
     * 一级科室code
     */
    private String oneDepartmentCode;

    /**
     * 二级科室code
     */
    private String twoDepartmentCode;

    /**
     * 工作类型
     */
    private Integer workIdentity;
}
