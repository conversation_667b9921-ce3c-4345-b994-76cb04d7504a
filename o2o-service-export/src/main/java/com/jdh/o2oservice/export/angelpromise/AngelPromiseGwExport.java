package com.jdh.o2oservice.export.angelpromise;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.cmd.JdhAngelWorkSaveCmd;
import com.jdh.o2oservice.export.angelpromise.cmd.SubmitMedicalCertificateFileCmd;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailQuery;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssUserDetailDTO;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentDetailManDTO;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentDetailRequest;

import java.util.List;
import java.util.Map;

/**
 * 服务者履约 网关服务
 * @date 2024-05-15 15:36
 * <AUTHOR>
 */
public interface AngelPromiseGwExport {

    /**
     * 创建服务者工单
     *
     * @param jdhAngelWorkSaveCmd
     * @return
     */
    Response<Boolean> createAngelWork(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd);

    /**
     * 执行工单
     * @param param
     * @return
     */
    Response<AngelWorkStatusDto> execute(Map<String, String> param);

    /**
     * 工单详情接口
     *
     * @param param
     * @return
     */
    Response<AngelWorkDto> queryAngelWorkDetail(Map<String,String> param);

    /**
     * 检查绑定条码，用于护士上门绑定条码页面下的下一步按钮(护士检测)
     *
     * @param param
     * @return
     */
    Response<Boolean> submitBindBarCode(Map<String, String> param);

    /**
     * 服务完成(护工护理)
     *
     * @param param
     * @return
     */
    Response<Boolean> finishService(Map<String, String> param);

    /**
     * 确认全部订单已配送(护士检测)
     *
     * @param param
     * @return
     */
    Response<Boolean> confirmTransferOrderDeliver(Map<String, String> param);

    /**
     * 护士呼叫运力
     *
     * @param param
     * @return
     */
    Response<Boolean> createDeliveryShip(Map<String, String> param);

    /**
     * 工单列表接口
     *
     * @param param
     * @return
     */
    Response<AngelWorkListDto> queryAngelWorkList(Map<String, String> param);

    /**
     * 流程查询接口
     * @param param
     * @return
     */
    Response<List<AngelWorkShowTemplateDto>> queryAngelWorkShowTemplate(Map<String, String> param);

    /**
     * 自配送送达
     *
     * @param param
     * @return
     */
    Response<Boolean> deliver(Map<String, String> param);

    /**
     * 核销兑换码
     * @param param
     * @return
     */
    Response<Boolean> codeVerification(Map<String, String> param);

    /**
     * 检验项绑定样本条码
     * @param param
     * @return
     */
    Response<Boolean> bindSpecimenCode(Map<String, String> param);

    /**
     * 查询枚举列表
     * @param param
     * @return
     */
    Response<List<AngelWorkEnumDto>> queryEnum(Map<String, String> param);

    /**
     * 取消运单
     *
     * @param param
     * @return
     */
    Response<Boolean> cancelShip(Map<String, String> param);

    /**
     * 确认工单任务信息
     * @param param
     * @return
     */
    Response<Boolean> confirmTaskInfo(Map<String, String> param);

    /**
     * 提交工单完成信息
     * @param param
     * @return
     */
    Response<Boolean> submitWorkCompleteInfo(Map<String, String> param);

    /**
     * 提交服务记录录音信息
     * @param param
     * @return
     */
    Response<Boolean> submitRecording(Map<String, String> param);

    /**
     * 查询第三方物流供应商列表
     * @param param 查询参数
     * @return 第三方物流供应商DTO列表
     */
    Response<List<ThirdShipSupplierDTO>> queryThirdShipSupplierList(Map<String, String> param);

    /**
     * 护士点击已到达
     * @param param
     * @return
     */
    Response<AngelArrivedDTO> submitArrived(Map<String, String> param);

    /**
     * 护士点击开始服务
     * @param param
     * @return
     */
    Response<AngelInServiceDTO> submitInService(Map<String, String> param);

    /**
     * 查询服务单外呼记录
     * @param param
     * @return
     */
    Response<AngelCallRecordsDTO> queryCallRecords(Map<String, String> param);

    /**
     * 工单详情接口jsf
     *
     * @param angelWorkDetailQuery
     * @return
     */
    Response<AngelWorkDto> queryAngelWorkBaseInfo(AngelWorkDetailQuery angelWorkDetailQuery);

    /**
     * 提交医疗证明文件
     * @param param
     * @return
     */
    Response<Boolean> submitMedicalCertificateFile(Map<String, String> param);

    /**
     * 查询风险评估详情
     * @param param
     * @return
     */
    Response<RiskAssUserDetailDTO> queryRiskAssUserDetail(Map<String, String> param);
}
