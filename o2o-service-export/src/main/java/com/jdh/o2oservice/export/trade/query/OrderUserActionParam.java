package com.jdh.o2oservice.export.trade.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import com.jdh.o2oservice.export.promise.cmd.AppointmentTime;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * OrderUserActionParam 结算页用户行为入参
 *
 * <AUTHOR>
 * @version 2024/3/7 14:34
 **/
@Data
public class OrderUserActionParam extends AbstractQuery implements Serializable {

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 用户行为的类型
     */
    private String userActionType;

    /**
     * 购物车对外的入参
     */
    private CartInfoParam cartInfoParam;

    /**
     * sku信息 (结算页在立即购买环节会有修改数量的用户行为，这里只关注skuId 和数量的数据)
     */
    private List<SkuInfoParam> skuInfoParamList;

    /**
     * 地址信息
     */
    private AddressUpdateParam addressUpdateParam;

    /**
     * 优惠卷
     */
    private List<CouponTradeInfoParam> couponTradeInfoParamList;

    /**
     * 发票信息
     */
    private InvoiceQueryParam invoiceQueryParam;

    /**
     * 配送方式
     */
    private ShipmentParam shipmentParam;

    /**
     * 时效
     */
    private PromiseParam promiseParam;

    /**
     * 支付方式
     */
    private PaymentParam paymentParam;

    /**
     * 红包信息
     */
    private HongBaoInfoParam hongBaoInfoParam;

    /**
     * 运费信息
     */
    private FreightInfoParam freightInfoParam;

    /**
     * 预约时间
     */
    private AppointmentTimeParam appointmentTimeParam;

    /**
     * 预约人ID列表
     */
    private List<Long> patientIds;

    /**
     * 新版结算页标记
     */
    private String usp;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;

    /**
     * 不支持的服务项列表
     */
    private List<String> notSupportServiceItemList;

    /**
     * 服务人员升级是否选中 true-选中 false-未选中
     */
    private Boolean serviceUpgradeSelected;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag = false;

    /**
     * 服务人员升级按钮互斥排除
     */
    private Boolean serviceUpgradeSelectedExclude = false;

    /**
     * 服务人员升级楼层启用开关
     */
    private Boolean serviceUpgradeSwitchEnable = true;

    /**
     * 当前选择的时间是夜间服务时间
     */
    private Boolean nightServiceSelected;

    private String saleChannelId;
}
