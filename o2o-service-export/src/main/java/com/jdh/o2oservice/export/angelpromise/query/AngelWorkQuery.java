package com.jdh.o2oservice.export.angelpromise.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @author:lichen55
 * @Description: 工单详情查询入参
 * @date 2024-05-23 21:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelWorkQuery extends AbstractRequest {

    /**
     * 工单Id
     */
    private Long workId;

    /**
     * 履约单Id
     */
    private Long promiseId;

    /**
     * 服务者工单状态：
     * @Link AngelWorkStatusEnum
     */
    private Set<Integer> workStatus;

    /**
     * 被服务人实验室查询
     */
    private List<AngelTaskStationQuery> taskStationQueryList;

    /**
     * 履约单Id
     */
    private List<Long> promiseIdList;
}
