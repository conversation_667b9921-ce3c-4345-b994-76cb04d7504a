package com.jdh.o2oservice.export.medicalpromise;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.medicalpromise.cmd.open.MedPromiseOperateOpenCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.open.MedPromiseForOpenDTO;
import com.jdh.o2oservice.export.medicalpromise.query.open.MedPromiseDetailOpenRequest;
import com.jdh.o2oservice.export.medicalpromise.query.open.MedPromisePageOpenRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/18
 */
public interface MedPromiseOpenJsfExport {

    /**
     * 分页查询检测单列表for开放平台
     *
     * @param medPromisePageOpenRequest 分页参数和筛选条件。
     * @return 分页结果，包含当前页数据和总页数等信息。
     */
    Response<PageDto<MedicalPromiseDTO>> pageMedPromiseOpen(MedPromisePageOpenRequest medPromisePageOpenRequest);

    /**
     * 查询检测单详情for开放平台
     *
     * @param medPromiseDetailOpenRequest 查询参数对象，包含分页信息和其他过滤条件
     * @return JsfResult对象，包含查询到的MedPromiseOpenDetailDTO列表和分页信息
     */
    Response<MedPromiseForOpenDTO> queryMedPromiseDetailOpen(MedPromiseDetailOpenRequest medPromiseDetailOpenRequest);

    /**
     * 操作检测单for开放平台
     *
     * @param medPromiseOperateOpenCmd 医疗承诺页面打开参数
     * @return 操作结果，true表示成功，false表示失败
     */
    Response<Boolean> medPromiseOperateOpen(MedPromiseOperateOpenCmd medPromiseOperateOpenCmd);
}
