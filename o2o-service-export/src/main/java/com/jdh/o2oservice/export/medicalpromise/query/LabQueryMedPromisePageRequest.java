package com.jdh.o2oservice.export.medicalpromise.query;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 实验室端分页查询检测单入参
 * @Author: wangpengfei144
 * @Date: 2024/6/9
 */
@Data
public class LabQueryMedPromisePageRequest {

    /**
     * <pre>
     * 分页 页码
     * </pre>
     */
    private int pageNum = 1;
    /**
     * <pre>
     * 分页 页大小
     * </pre>
     */
    private int pageSize = 10;
    /**
     * 样本条码
     */
    private String specimenCode;
    /**
     * 供应商ID
     */
    private Long providerId;
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;
    /**
     * 预约开始时间
     */
    private Date appointmentStartTime;
    /**
     * 预约结束时间
     */
    private Date appointmentEndTime;
    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private Long serviceItemId;
    /**
     * <pre>
     * 项目名称
     * </pre>
     */
    private String serviceItemName;
    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本扫码时间
     */
    private Integer reportTimeOutStatus;
    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本送达时间
     */
    private Integer reportCheckTimeOutStatus;
    /**
     * 快检综合状态筛选
     */
    private Integer compositeStatus;
    /**
     * 预约日期
     */
    private String appointmentDate;
    /**
     * pin
     */
    private String userPin;

    /**
     *
     */
    private Boolean queryEncryptionName;
    /**
     * 检测单id
     */
    private Long medicalPromiseId;

    /**
     * 查询未加密项目
     */
    private Boolean queryNoEncryptionName;

    /**
     * 对接方式
     * see com.jdh.o2oservice.export.laboratory.enums.DockingTypeEnum
     */
    private Integer dockingType;

    /**
     * 检测单标签状态，与检测单状态互斥，优先以标签状态为准
     */
    private String compositeCode;

    private String flowCode;

    /**
     * 是否用新端
     */
    private Boolean heartSmart;

}
