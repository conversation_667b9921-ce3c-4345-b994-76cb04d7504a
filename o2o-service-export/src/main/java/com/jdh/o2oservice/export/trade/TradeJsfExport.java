package com.jdh.o2oservice.export.trade;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.trade.cmd.CalcTradeServiceFeeCmd;
import com.jdh.o2oservice.export.trade.cmd.VtpReviseOrderFinishCmd;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.*;

import java.util.List;
import java.util.Map;

/**
 * @ClassName TradeJsfExport
 * @Description JDH Trade Jsf服务
 * <AUTHOR>
 * @Date 2024/1/23 11:28
 **/
public interface TradeJsfExport {

    /**
     * 查询预约单列表
     * @param request
     * @return
     */
    Response<List<OrderPromiseDto>> queryOrderPromiseByList(OrderPromiseListRequest request);

    /**
     * 结算页用户行为
     *
     * @param orderUserActionParam 入参
     * @return OrderUserActionDTO
     */
    Response<OrderUserActionDTO> executeAction(OrderUserActionParam orderUserActionParam);

    /**
     * 提交订单
     *
     * @param submitOrderParam 提单参数
     * @return SubmitOrderDTO
     */
    Response<SubmitOrderDTO> submitOrder(SubmitOrderParam submitOrderParam);


    Response<AvaiableAppointmentTimeDTO> queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param);

    /**
     * 计算服务费
     *
     * @param cmd cmd
     * @return {@link Response}<{@link List}<{@link TradeServiceFeeInfoDTO}>>
     */
    Response<List<TradeServiceFeeInfoDTO>> calcServiceFee(CalcTradeServiceFeeCmd cmd);

    /**
     * 申请退款
     *
     * @param param
     * @return
     */
    Response<Boolean> xfylOrderRefund(RefundOrderParam param);

    /**
     * 获取订单详细信息
     *
     * @param request 请求
     * @return {@link Response}<{@link CompleteOrderDto}>
     */
    Response<CompleteOrderDto> findCompleteOrder(CompleteOrderRequest request);
    /**
     * 获取订单信息
     *
     * @param request 请求
     * @return {@link Response}<{@link CompleteOrderDto}>
     */
    Response<JdOrderDTO> queryJdOrderDTO(CompleteOrderRequest request);

    /**
     * 批量获取订单详细信息
     *
     * @param request 请求
     * @return {@link Response}<{@link List}<{@link CompleteOrderDto}>>
     */
    Response<List<CompleteOrderDto>> findCompleteOrderList(CompleteOrderListRequest request);

    /**
     * 查询自定义订单列表
     * @param param
     * @return
     */
    Response<List<CustomOrderInfoDTO>> queryCustomOrderList(QueryCustomOrderListParam param);

    /**
     * 收单接口
     * @param request
     * @return
     */
    Response<Integer> receiveOrder(ReceiveOrderRequest request);

    /**
     * 查询可退信息
     * @param request
     * @return
     */
    Response<RefundAbleInfoDTO> findRefundable(ReceiveOrderRequest request);

    /**
     * 退款任务
     * @return
     */
    Response<Boolean> orderRefundTaskJob();

    /**
     * 主站订单详情楼层
     * @param param
     * @return
     */
    Response<CustomOrderDetailFloorDTO> queryCustomOrderDetailFloor(CustomOrderDetailParam param);

    /**
     * 查询意向服务者
     * @param param
     * @return
     */
    Response<IntendedAngelDTO> queryIntendedAngel(QueryIntendedAngelParam param);

    /**
     * 收单并提交预约
     * @param param
     * @return
     */
    Response<Boolean> receiveOrderAndAppointment(ReceiveOrderAndAppointmentParam param);

    /**
     * 校验是否可以提单并预约
     * @param param
     * @return
     */
    Response<Boolean> checkSubmitOrderAndAppointment(CheckSubmitOrderAndAppointmentParam param);

    /**
     * 取消订单（取消、退款）
     * @param cancelOrderParam
     * @return
     */
    Response<Boolean> cancelAndRefundOrder(CancelRefundOrderParam cancelOrderParam);

    /**
     * 查询订单详细信息
     * @param request
     * @return
     */
    Response<StandardOrderDetailDto> queryStandardOrderDetail(StandardOrderDetailRequest request);

    /**
     * vtp本地订单拉完成
     * @param cmd
     * @return
     */
    Response<Boolean> vtpReviseOrderFinish(VtpReviseOrderFinishCmd cmd);

}