package com.jdh.o2oservice.export.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * AmountInfoDTO 金额
 *
 * <AUTHOR>
 * @version 2024/3/7 14:04
 **/
@Data
public class AmountInfoDTO implements Serializable {

    /**
     * 订单应付总金额
     */
    private BigDecimal factOrderAmount;

    /**
     * 清单应付总金额
     */
    private BigDecimal factShoppingListAmount;

    /**
     * 商品金额
     */
    private BigDecimal goodsAmount;

    /**
     * 立减金额
     */
    private BigDecimal totalReprice;

    /**
     * 折扣 --- 订单详情页
     */
    private BigDecimal discount;

    /**
     * 抵扣金额  --- 订单详情页
     */
    private List<AssetDiscountDTO> assetDiscountDTOList;

    /**
     * 总运费
     */
    private BigDecimal totalFreight;

    /**
     * 已优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 官方立减金额
     */
    private BigDecimal totalOfficialDiscount;

    /**
     * 跨店满减金额
     */
    private BigDecimal overlaySumReward;

    /**
     * 促销优惠金额(店铺新人礼金+并行促销)
     */
    private BigDecimal promotionDiscountAmount;

    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmount;

    /**
     * 价格标记；1:起始价
     */
    private Integer priceFlag = 1 ;

    /**
     * 订单合计金额；商品合计金额+订单服务费
     */
    private BigDecimal totalOrderAmount;

    /**
     * 店铺新人礼金
     */
    private BigDecimal giftCashDiscountAmount;

    /**
     * 并行促销
     */
    private BigDecimal totalParallelDiscount;

}
