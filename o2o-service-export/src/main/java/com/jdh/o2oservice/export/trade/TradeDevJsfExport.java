package com.jdh.o2oservice.export.trade;


import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreateCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreatePromiseCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockModifyPromiseCmd;

public interface TradeDevJsfExport {

    /**
     * 生成订单数据
     * @return
     */
    Response<Boolean> mockGeneralOrder(OrderMockCreateCmd cmd);

    /**
     * 创建履约单
     * @return
     */
    Response<Boolean> mockGeneralPromise(OrderMockCreatePromiseCmd cmd);

    /**
     * 修改履约单
     * @return
     */
    Response<Boolean> mockModifyPromise(OrderMockModifyPromiseCmd cmd);

    /**
     * 作废订单下数据
     * @return
     */
    Response<Boolean> mockInvalidOrderService(OrderMockModifyPromiseCmd cmd);
}
