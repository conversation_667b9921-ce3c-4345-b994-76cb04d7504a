package com.jdh.o2oservice.export.angelpromise.cmd;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName:DeliverCmd
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/24 02:16
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverCmd extends AbstractRequest {

    /**
     * 工单id
     */
    private String workId;

    /**
     * 实验室id
     */
    private String LaboratoryId;

    /**
     * 运单id
     */
    private String shipId;

    /**
     * 护士纬度
     */
    private Double angelLat;

    /**
     * 护士经度
     */
    private Double angelLng;

}
