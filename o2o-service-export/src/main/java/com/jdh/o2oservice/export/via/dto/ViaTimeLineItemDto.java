package com.jdh.o2oservice.export.via.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * ViaTimeLineItemDto
 *
 * <AUTHOR>
 * @date 2024/05/03
 */
@Data
@Builder
public class ViaTimeLineItemDto implements Serializable {
    
    /**
     * 标签
     */
    private String label;

    /**
     * 时间
     */
    private String time;

    /**
     * 内容
     */
    private String content;

    /**
     * 扩展信息
     */
    private Map<String,String> extendInfo;

    /**
     * 事件编码
     */
    private String eventCode;
}
