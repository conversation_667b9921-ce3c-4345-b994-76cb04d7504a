package com.jdh.o2oservice.export.support;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.dto.SecurityNumberBindAxbResultDto;
import java.util.List;
import java.util.Map;

/**
 * @Description 外呼服务
 * @Date 2024/12/20 下午2:39
 * <AUTHOR>
 **/
public interface CallGwExport {

    /**
     * 虚拟号绑定
     * @param param
     * @return
     */
    Response<SecurityNumberBindAxbResultDto> bindAxb(Map<String, String> param);

    /**
     * 录音调取
     * @param param
     */
    Response<Boolean> callRecordingRetrieval(Map<String, String> param);

    /**
     * 外呼记录列表
     * @param param
     */
    Response<List<CallRecordDto>> queryCallRecordList(Map<String, String> param);

    /**
     * 查询外呼url
     * @param param
     */
    Response<String> queryCallRecordUrl(Map<String, String> param);

    /**
     * 同步护士手机号
     * @param param
     */
    Response<Boolean> syncAngelPhone(Map<String, String> param);
}
