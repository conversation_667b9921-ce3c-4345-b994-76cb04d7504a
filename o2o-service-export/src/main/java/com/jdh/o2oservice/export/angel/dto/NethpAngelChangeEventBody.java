package com.jdh.o2oservice.export.angel.dto;

import lombok.Data;

import java.util.List;

/**
 * @Description: 互医护士数据变更事件body
 * <AUTHOR>
 * @Date 2024/5/8
 * @Version V1.0
 **/
@Data
public class NethpAngelChangeEventBody {

    /**
     * 主业务id号，保证追溯信息使用，唯一即可。
     */
    private String businessId;

    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 医生Pin
     */
    private String doctorPin;

    /**
     * 变更时间
     */
    private Long changeTime;

    /**
     * 变更字段名集合
     */
    private List<String> remark;

    /**
     * 变更字段数据（json），只有变更后的字段数据
     */
    private String changeValue;

    /**
     * 是否测试医生
     */
    private Boolean testDoctor;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 职业：2-护士，24-上门护士
     */
    private Integer professionType;

}
