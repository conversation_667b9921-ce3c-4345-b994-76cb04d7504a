package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 10:17
 * @Description: 运单信息
 * @date 2024-05-09 23:25
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AngelShipDto {

    /**
     * 运单Id
     */
    private Long shipId;

    /**
     * 服务工单Id
     */
    private Long angelWorkId;

    /**
     * 外部运单Id
     */
    private String outShipId;

    /**
     * 运单类型：1=自行寄送，2=达达
     */
    private Integer type;

    /**
     * 骑手Id
     */
    private String transferId;

    /**
     * 骑手姓名
     */
    private String transferName;

    /**
     * 骑手联系方式
     */
    private String transferPhone;

    /**
     * 发件人姓名
     */
    private String senderName;

    /**
     * 发件全地址
     */
    private String senderFullAddress;

    /**
     * 运单状态：
     */
    private Integer shipStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 预计呼叫时间
     */
    private Date planCallTime;

    /**
     * 预计接单时间
     */
    private Date estimateGrabTime;

    /**
     * 预计取件时间
     */
    private Date estimatePickUpTime;

    /**
     * 预计完单时间
     */
    private Date estimateReceiveTime;

    /**
     * 运单历史明细
     */
    private List<AngelShipHistoryDto> shipHistoryDtoList;

    /**
     * 骑手经度
     */
    private String transporterLng;

    /**
     * 骑手纬度
     */
    private String transporterLat;

    private AngelShipExtDTO angelShipExtDTO;

    /**
     * 物流动态信息
     */
    private String logisticsMessage;

    private Integer remainingDistance;//预计剩余航程 单位: 米

    private Double alt;//海拔

    private Integer remainingTime;//无人机 预计剩余航时 单位: 米

    private Long uavTimestamp;//无人机数据时间戳 单位:秒

    //无人机异常信息
    private UavResultDto uavResultDto;

    /**
     * 接收点Id
     */
    private String receiverId;

    /**
     * 接收点名称
     */
    private String receiverName;

    /**
     * 接收点全地址
     */
    private String receiverFullAddress;
}
