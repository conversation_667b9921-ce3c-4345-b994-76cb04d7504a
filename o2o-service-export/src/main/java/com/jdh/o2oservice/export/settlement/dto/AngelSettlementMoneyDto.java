package com.jdh.o2oservice.export.settlement.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 3:25 下午
 * @Description:
 */
@Data
public class AngelSettlementMoneyDto implements Serializable {
    private static final long serialVersionUID = 8106614193508979069L;


    /**
     * 今日收入
     */
    private BigDecimal toDaySettleAmount = BigDecimal.ZERO;


    /**
     * 累计收入
     */
    private BigDecimal totSettleAmount = BigDecimal.ZERO;


    /**
     * 可提现金额
     */
    private BigDecimal settleAmount = BigDecimal.ZERO;


    /**
     * 不可提现金额
     */
    private BigDecimal noSettleAmount = BigDecimal.ZERO;

}
