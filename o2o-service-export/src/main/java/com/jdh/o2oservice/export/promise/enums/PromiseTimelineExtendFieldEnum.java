package com.jdh.o2oservice.export.promise.enums;

import java.util.Arrays;
import java.util.List;

/**
 * PromiseTimelineExtendFieldEnum
 */
public enum PromiseTimelineExtendFieldEnum {

    /**
     * 原上门时间
     */
    OLD_APPOINTTIME("OLD_APPOINTTIME", "原上门时间", Arrays.asList(PromiseTimelineActionEnum.MODIFY_PROMISE)),
    /**
     * 新上门时间
     */
    NEW_APPOINTTIME("OLD_APPOINTTIME", "新上门时间", Arrays.asList(PromiseTimelineActionEnum.MODIFY_PROMISE)),

    /**
     * 服务者姓名
     */
    SERVICER_NAME("SERVICER_NAME", "服务者姓名", Arrays.asList(PromiseTimelineActionEnum.RECEIVED_WORK, PromiseTimelineActionEnum.DEPART_WORK, PromiseTimelineActionEnum.ARRIVED_WORK, PromiseTimelineActionEnum.SERVICING_WORK, PromiseTimelineActionEnum.SERVICED_WORK)),

    /**
     * 主叫
     */
    CALLER("CALLER", "主叫", Arrays.asList(PromiseTimelineActionEnum.CALLED_WORK)),
    /**
     * 被叫
     */
    CALLEE("CALLEE", "被叫", Arrays.asList(PromiseTimelineActionEnum.CALLED_WORK)),

    /**
     * 通话时长
     */
    CALL_DURATION("CALL_DURATION", "通话时长", Arrays.asList(PromiseTimelineActionEnum.CALLED_WORK)),

    /**
     * 评估结果
     */
    ASSESS_RESULT("ASSESS_RESULT", "评估结果", Arrays.asList(PromiseTimelineActionEnum.COMPLETE_ASSESS)),

    ;

    /**
     *
     */
    private String field;

    /**
     *
     */
    private String desc;
    /**
     *
     */
    private List<PromiseTimelineActionEnum> actionEnumList;

    PromiseTimelineExtendFieldEnum(String field, String desc, List<PromiseTimelineActionEnum> actionEnumList) {
        this.field = field;
        this.desc = desc;
        this.actionEnumList = actionEnumList;
    }

    public String getField() {
        return field;
    }

    public String getDesc() {
        return desc;
    }

    public List<PromiseTimelineActionEnum> getActionEnumList() {
        return actionEnumList;
    }

    @Override
    public String toString() {
        return "PromiseTimelineExtendFieldEnum{" + "field='" + field + '\'' + ", desc='" + desc + '\'' + ", actionEnumList=" + actionEnumList + '}';
    }
}
