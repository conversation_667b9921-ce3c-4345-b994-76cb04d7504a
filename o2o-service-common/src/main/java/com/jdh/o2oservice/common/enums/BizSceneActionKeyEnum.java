package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * BizSceneActionKeyEnum
 * <AUTHOR>
 * @date 2025-08-04 16:17
 */

@Getter
@AllArgsConstructor
public enum BizSceneActionKeyEnum {

    /**
     * 收样
     */
    RECEIVE_SAMPLE("receiveSample", "收样"),

    /**
     * 生成流转码
     */
    GENERATE_FLOW_CODE("generateFlowCode", "生成流转码"),

    /**
     * 上机
     */
    TEST_SAMPLE("testSample", "上机"),

    /**
     * 检测完成
     */
    TEST_FINISH("testFinish", "检测完成"),

    /**
     * 审核通过
     */
    AUDIT_PASS("auditPass", "审核通过"),

    /**
     * 审核不通过
     */
    AUDIT_REFUSE("auditRefuse", "审核不通过"),

    /**
     * 异常上报
     */
    EXCEPTION_SUBMIT("exceptionSubmit", "异常上报"),

    /**
     * 让步检测
     */
    CONCESSION_TEST("concessionTest", "让步检测"),

    /**
     * 换绑条码
     */
    CHANGE_SPECIMEN_CODE("changeSpecimenCode", "换绑条码");

    ;

    /**
     * 业务场景动作，用于标识不同的业务场景动作。
     */
    private final String bizSceneActionKey;

    /**
     * 业务场景动作描述，用于记录每个业务场景动作的详细信息。
     */
    private final String bizSceneActionDesc;


    public static String getDescByKey(String key) {
        for (BizSceneActionKeyEnum enums : BizSceneActionKeyEnum.values()) {
            if (enums.getBizSceneActionKey().equals(key)) {
                return enums.getBizSceneActionDesc();

            }
        }
        return null;
    }
}
