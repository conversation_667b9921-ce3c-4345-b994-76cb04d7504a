package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelExtendQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepPageQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.*;
import com.jdh.o2oservice.infrastructure.repository.db.dao.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 服务者基础信息repository
 * <AUTHOR>
 * @Date 2024/4/17
 * @Version V1.0
 **/
@Repository
@Slf4j
public class AngelRepositoryImpl implements AngelRepository {

    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     *
     */
    @Resource
    private JdhAngelPoMapper jdhAngelPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelExtendPoMapper jdhAngelExtendPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelProfessionRelPoMapper jdhAngelProfessionRelPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelProfessionTitleDictPoMapper jdhAngelProfessionTitleDictPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelSkillRelPoMapper jdhAngelSkillRelPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelProfessionQualificationRelPoMapper jdhAngelProfessionQualificationRelPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelSkillDictPoMapper jdhAngelSkillDictPoMapper;

    /**
     *
     */
    @Resource
    private TdeClientUtil tdeClientUtil;

    @Override
    public List<JdhAngel> findList(JdhAngelRepQuery query) {
        //如果有职级和机构名称查询，先查询Profession表获取护士ID
        List<Long> professionFilterAngelList = new ArrayList<>();
        if (StringUtils.isNotBlank(query.getProfessionTitleCode()) || StringUtils.isNotBlank(query.getInstitutionName())) {
            LambdaQueryWrapper<JdhAngelProfessionRelPo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StringUtils.isNotBlank(query.getProfessionTitleCode()), JdhAngelProfessionRelPo::getProfessionTitleCode, query.getProfessionTitleCode());
            queryWrapper.like(StringUtils.isNotBlank(query.getInstitutionName()), JdhAngelProfessionRelPo::getInstitutionName, query.getInstitutionName());
            List<JdhAngelProfessionRelPo> jdhAngelProfessionRelPos = jdhAngelProfessionRelPoMapper.selectList(queryWrapper);
            professionFilterAngelList = jdhAngelProfessionRelPos.stream().map(JdhAngelProfessionRelPo::getAngelId).distinct().collect(Collectors.toList());
        }

        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollUtil.isNotEmpty(query.getAngelIdList()),JdhAngelPo::getAngelId, query.getAngelIdList());
        queryWrapper.eq(Objects.nonNull(query.getAngelPin()),JdhAngelPo::getAngelPin, query.getAngelPin());
        queryWrapper.eq(Objects.nonNull(query.getAngelId()),JdhAngelPo::getAngelId, query.getAngelId());
        queryWrapper.eq(Objects.nonNull(query.getAuditProcessStatus()),JdhAngelPo::getAuditProcessStatus, query.getAuditProcessStatus());
        queryWrapper.like(StringUtil.isNotEmpty(query.getAngelName()), JdhAngelPo::getAngelName, query.getAngelName());
        queryWrapper.ge(Objects.nonNull(query.getRegisterStartTime()), JdhAngelPo::getCreateTime, query.getRegisterStartTime());
        queryWrapper.le(Objects.nonNull(query.getRegisterEndTime()), JdhAngelPo::getCreateTime, query.getRegisterEndTime());
        queryWrapper.ge(Objects.nonNull(query.getAuditProcessDateStartTime()), JdhAngelPo::getAuditProcessDate, query.getAuditProcessDateStartTime());
        queryWrapper.le(Objects.nonNull(query.getAuditProcessDateEndTime()), JdhAngelPo::getAuditProcessDate, query.getAuditProcessDateEndTime());
        queryWrapper.in(CollUtil.isNotEmpty(professionFilterAngelList),JdhAngelPo::getAngelId, professionFilterAngelList);
        List<JdhAngelPo> jdhAngelPos = jdhAngelPoMapper.selectList(queryWrapper);

        List<JdhAngel> list = Lists.newArrayListWithExpectedSize(jdhAngelPos.size());
        for (JdhAngelPo jdhAngelPo : jdhAngelPos) {
            JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(jdhAngelPo);
            list.add(jdhAngel);
        }
        return list;
    }

    @Override
    public List<JdhAngel> queryByStationId(JdhAngelRepQuery query) {
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getStationId()), JdhAngelPo::getStationId, query.getStationId());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getStationIdList()), JdhAngelPo::getStationId, query.getStationIdList());
        //服务者审核状态
        queryWrapper.eq(Objects.nonNull(query.getAuditProcessStatus()), JdhAngelPo::getAuditProcessStatus, query.getAuditProcessStatus());
        queryWrapper.eq(Objects.nonNull(query.getJobNature()), JdhAngelPo::getJobNature, query.getJobNature());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getIntendedAngelIds()), JdhAngelPo::getAngelId, query.getIntendedAngelIds());
        List<JdhAngelPo> jdhAngelPos = jdhAngelPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(jdhAngelPos)){
            return new ArrayList<>();
        }

        List<JdhAngel> list = Lists.newArrayListWithExpectedSize(jdhAngelPos.size());
        for (JdhAngelPo jdhAngelPo : jdhAngelPos) {
            JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(jdhAngelPo);
            list.add(jdhAngel);
        }
        return list;
    }

    @Override
    public JdhAngel find(@NotNull JdhAngelIdentifier jdhAngelIdentifier) {
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelPo::getAngelId, jdhAngelIdentifier.getAngelId());
        //有效
        queryWrapper.in(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());

        JdhAngelPo jdhAngelPo = jdhAngelPoMapper.selectOne(queryWrapper);
        return JdhAngelPoConvert.INSTANCE.convertToJdhAngel(jdhAngelPo);
    }

    @Override
    public int updateAngelJobNature(JdhAngel jdhAngel) {
        if (Objects.isNull(jdhAngel) || Objects.isNull(jdhAngel.getAngelId()) || Objects.isNull(jdhAngel.getJobNature())) {
            return 0;
        }

        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelPo::getJobNature, jdhAngel.getJobNature())
                .set(StringUtils.isNotBlank(jdhAngel.getOperator()), JdhAngelPo::getUpdateUser, jdhAngel.getOperator())
                .eq(JdhAngelPo::getAngelId, jdhAngel.getAngelId());

        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    @Override
    public int updateAngelTakeOrderStatus(JdhAngel jdhAngel) {
        if (Objects.isNull(jdhAngel) || Objects.isNull(jdhAngel.getAngelId()) || Objects.isNull(jdhAngel.getTakeOrderStatus())) {
            return 0;
        }

        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelPo::getTakeOrderStatus, jdhAngel.getTakeOrderStatus())
                .set(StringUtils.isNotBlank(jdhAngel.getOperator()), JdhAngelPo::getUpdateUser, jdhAngel.getOperator())
                .eq(JdhAngelPo::getAngelId, jdhAngel.getAngelId());

        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    @Override
    public JdhAngel queryByUniqueId(JdhAngelRepQuery query) {

        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getAngelId()), JdhAngelPo::getAngelId, query.getAngelId());
        queryWrapper.eq(Objects.nonNull(query.getNethpDocId()), JdhAngelPo::getNethpDocId, query.getNethpDocId());
        queryWrapper.eq(Objects.nonNull(query.getAngelPin()), JdhAngelPo::getAngelPin, query.getAngelPin());
        //有效
        if (Objects.equals(query.getSearchDel(), true)) {
            log.info("AngelRepositoryImpl -> queryByUniqueId, 查询已删除的，不设置yn=1条件，query={}", JSON.toJSONString(query));
        } else {
            queryWrapper.eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        }

        JdhAngelPo jdhAngelPo = jdhAngelPoMapper.selectOne(queryWrapper);
        return wrap(jdhAngelPo);
    }

    @Override
    public List<JdhAngel> queryByPhoneOrIdCard(JdhAngelRepQuery query) {
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(query.getIdCard())){
            String idCardIndex = tdeClientUtil.obtainKeyWordIndex(query.getIdCard());
            //身份证号索引
            queryWrapper.eq(JdhAngelPo::getIdCardIndex,idCardIndex);
        }
        if(StringUtils.isNotBlank(query.getPhone())){
            String phoneIndex = tdeClientUtil.obtainKeyWordIndex(query.getPhone());
            //电话索引
            queryWrapper.eq(JdhAngelPo::getPhoneIndex,phoneIndex);
        }
        queryWrapper.isNotNull(JdhAngelPo::getStationId);
        queryWrapper.eq(JdhAngelPo::getJobNature,JobNatureEnum.FULL_TIME.getValue());

        //有效
        queryWrapper.in(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhAngelPo> jdhAngelPos= jdhAngelPoMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(jdhAngelPos)){
            return new ArrayList<>();
        }

        List<JdhAngel> list = Lists.newArrayListWithExpectedSize(jdhAngelPos.size());
        for (JdhAngelPo jdhAngelPo : jdhAngelPos) {
            JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(jdhAngelPo);
            list.add(jdhAngel);
        }
        return list;
    }

    @Override
    public int batchBindStationMaster(JdhAngelBindStationMasterEntity entity) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelPo::getStationMaster, entity.getStationMaster())
                .set(StringUtils.isNotBlank(entity.getOperator()), JdhAngelPo::getUpdateUser, entity.getOperator())
                .in(JdhAngelPo::getAngelId, entity.getAngelIdList());
        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    @Override
    public int batchUnbindStationMaster(JdhAngelBindStationMasterEntity entity) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(entity.getOperator()), JdhAngelPo::getUpdateUser, entity.getOperator())
                .setSql("`station_master` = null")
                .in(JdhAngelPo::getAngelId, entity.getAngelIdList());
        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    @Override
    public int batchBindStation(JdhAngelBindStationEntity entity) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(entity.getOperator()), JdhAngelPo::getUpdateUser, entity.getOperator())
                .set(JdhAngelPo::getStationId, entity.getStationId())
                .in(JdhAngelPo::getAngelId, entity.getAngelIdList());

        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    @Override
    public int batchUnBindStation(JdhAngelBindStationEntity entity) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(entity.getOperator()), JdhAngelPo::getUpdateUser, entity.getOperator())
                .setSql("`station_id` = null")
                .in(JdhAngelPo::getAngelId, entity.getAngelIdList());

        return jdhAngelPoMapper.update(null, updateWrapper);
    }


    @Override
    public JdhAngel queryAngelDetail(JdhAngelRepQuery query) {
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelPo::getAngelId, query.getAngelId());
        queryWrapper.eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());

        JdhAngelPo jdhAngelPo = jdhAngelPoMapper.selectOne(queryWrapper);
       /* if (Objects.isNull(jdhAngelPo)) {
            throw new BusinessException(AngelErrorCode.ANGEL_NOT_EXIST);
        }*/
        JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(jdhAngelPo);
        if (Objects.isNull(jdhAngel)) {
            return null;
        }

        //查询服务者职业信息
        List<JdhAngelProfessionRelPo> angelProfessionRelList = getAngelProfessionRelList(jdhAngel.getAngelId());
        //职业职级信息 key:职级code value:职级信息
        //Map<Long, JdhAngelProfessionTitleDictPo> angelProfessionTitleDictMap = getAngelProfessionTitleDictMap();
        //查询服务者职业资质信息
        Map<String, List<JdhAngelProfessionQualificationRelPo>> professionQualificationMap = queryJdhAngelProfessionQualificationRel(jdhAngel.getAngelId());

        //组装职业职级信息
        jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convertJdhAngelProfessionRelList(jdhAngel,
                angelProfessionRelList);
        //组装服务者职业资质信息
        jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelProfessionQualificationRelList(jdhAngel, professionQualificationMap);

        //组装服务者已开启职业信息
        List<JdhAngelSkillRelPo> angelSkillRelList = getAngelSkillRelList(jdhAngel.getAngelId());
        //组装服务者技能信息
        jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convertJdhAngelSkillRelList(jdhAngel, angelSkillRelList);
        //组装服务者未开启技能信息
        if (Boolean.TRUE.equals(query.getIsQueryNotOpenSkill())) {
            List<String> alreadyOpenSkillCodeList = angelSkillRelList.stream().map(JdhAngelSkillRelPo::getAngelSkillCode).collect(Collectors.toList());
            List<JdhAngelSkillDictPo> angelNotOpenSkillList = getAngelNotOpenSkillList(alreadyOpenSkillCodeList);
            jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convertJdhAngelNotOpenSkillList(jdhAngel, angelNotOpenSkillList);
        }

        return jdhAngel;
    }

    @Override
    public Page<JdhAngel> page(JdhAngelRepPageQuery query) {
        Page<JdhAngelPo> param = new Page<>(query.getPageNum(), query.getPageSize());

        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        //服务者id
        queryWrapper.eq(Objects.nonNull(query.getAngelId()), JdhAngelPo::getAngelId, query.getAngelId());
        //服务者姓名
        queryWrapper.like(StringUtils.isNotBlank(query.getAngelName()), JdhAngelPo::getAngelName, query.getAngelName());

        //身份证号索引
        if (StringUtils.isNotBlank(query.getIdCardIndex())) {
            String idCardIndex = tdeClientUtil.calculateKeyWord(query.getIdCardIndex());
            queryWrapper.like(JdhAngelPo::getIdCardIndex, idCardIndex);
        }
        //电话索引
        if (StringUtils.isNotBlank(query.getPhoneIndex())) {
            String phoneIndex = tdeClientUtil.calculateKeyWord(query.getPhoneIndex());
            queryWrapper.like(JdhAngelPo::getPhoneIndex,  phoneIndex);
        }
        //站点id
        queryWrapper.eq(Objects.nonNull(query.getStationId()), JdhAngelPo::getStationId, query.getStationId());
        //有效
        if (Objects.equals(query.getSearchDel(), true)) {
            log.info("AngelRepositoryImpl -> page, 查询已删除的，不设置yn=1条件，query={}", JSON.toJSONString(query));
        } else {
            queryWrapper.in(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        }
        //服务者id集合
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getAngelIdList()),
                JdhAngelPo::getAngelId, query.getAngelIdList());
        //互医医生id集合
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getNethpDocIdList()),
                JdhAngelPo::getNethpDocId, query.getNethpDocIdList());
        //服务者审核状态
        queryWrapper.eq(Objects.nonNull(query.getAuditProcessStatus()), JdhAngelPo::getAuditProcessStatus, query.getAuditProcessStatus());
        //是否关联站长
        if (AngelRelationStationMasterFlagEnum.NO.getCode().equals(query.getRelationStationMasterFlag())) {
            queryWrapper.isNull(JdhAngelPo::getStationMaster);
        } else if (AngelRelationStationMasterFlagEnum.YES.getCode().equals(query.getRelationStationMasterFlag())) {
            queryWrapper.isNotNull(JdhAngelPo::getStationMaster);
        }
        //人员标签
        if (Objects.nonNull(query.getJobNature()) && JobNatureEnum.NULL.getValue() != query.getJobNature()) {
            queryWrapper.eq(JdhAngelPo::getJobNature, query.getJobNature());
        } else if (Objects.nonNull(query.getJobNature()) && JobNatureEnum.NULL.getValue() == query.getJobNature()) {
            queryWrapper.isNull(JdhAngelPo::getJobNature);
        }
        //站长erp
        queryWrapper.eq(Objects.nonNull(query.getStationMaster()), JdhAngelPo::getStationMaster, query.getStationMaster());
        //是否关联服务站
        if (AngelRelationStationFlagEnum.NO.getCode().equals(query.getRelationStationFlag())) {
            queryWrapper.isNull(JdhAngelPo::getStationId);
        } else if (AngelRelationStationFlagEnum.YES.getCode().equals(query.getRelationStationFlag())) {
            queryWrapper.isNotNull(JdhAngelPo::getStationId);
            queryWrapper.in(CollectionUtils.isNotEmpty(query.getStationIds()),JdhAngelPo::getStationId,query.getStationIds());
        }
        //接单开关状态
        queryWrapper.eq(Objects.nonNull(query.getTakeOrderStatus()), JdhAngelPo::getTakeOrderStatus, query.getTakeOrderStatus());
        queryWrapper.eq(query.getJdhProviderId() != null, JdhAngelPo::getJdhProviderId, query.getJdhProviderId());
        queryWrapper.in(CollUtil.isNotEmpty(query.getJdhProviderIds()), JdhAngelPo::getJdhProviderId, query.getJdhProviderIds());
        // 如果明确查询机构下护士数据,构造供应商id大于1000的sql,机构id为分布式自增id,一定会大于100
        queryWrapper.ge(Boolean.TRUE.equals(query.getQueryAllJdhProvider()), JdhAngelPo::getJdhProviderId, 1000);
        //排序
        if (AngelAuditProcessStatusEnum.AUDIT_WAIT.getCode().equals(query.getAuditProcessStatus())) {
            queryWrapper.orderByDesc(JdhAngelPo::getCreateTime);
        } else {
            queryWrapper.orderByDesc(JdhAngelPo::getAuditProcessDate);
        }

        //按区域和科室查询
        queryWrapper.eq(StringUtils.isNotBlank(query.getProvinceCode()),JdhAngelPo::getProvinceCode,query.getProvinceCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getCityCode()),JdhAngelPo::getCityCode,query.getCityCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getCountyCode()),JdhAngelPo::getCountyCode,query.getCountyCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getOneDepartmentCode()),JdhAngelPo::getOneDepartmentCode,query.getOneDepartmentCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getTwoDepartmentCode()),JdhAngelPo::getTwoDepartmentCode,query.getTwoDepartmentCode());

        //工作类型
        if(Objects.equals(WorkIdentityEnum.NULL.getCode(), query.getWorkIdentity())) {
            queryWrapper.isNull(JdhAngelPo::getWorkIdentity);
        }else if(Objects.nonNull(query.getWorkIdentity())){
            queryWrapper.eq(JdhAngelPo::getWorkIdentity, query.getWorkIdentity());
        }

        log.info("AngelRepositoryImpl -> page queryWrapper:{}", JSON.toJSONString(queryWrapper));
        Page<JdhAngelPo> page = jdhAngelPoMapper.selectPage(param, queryWrapper);
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return null;
        }

        //服务者id集合
        List<Long> angelIds = page.getRecords().stream().map(JdhAngelPo::getAngelId).collect(Collectors.toList());
        //服务者职业map
        Map<Long, List<JdhAngelProfessionRelPo>> angelProfessionRelMap = getAngelProfessionRelMap(angelIds);
        //服务者技能map
        Map<Long, List<JdhAngelSkillRelPo>> angelSkillRelMap = getAngelSkillRelMap(angelIds);
        //职业职级信息 key:职级code value:职级信息
        //Map<Long, JdhAngelProfessionTitleDictPo> angelProfessionTitleDictMap = getAngelProfessionTitleDictMap();

        List<JdhAngel> list = Lists.newArrayListWithExpectedSize(page.getRecords().size());
        for (JdhAngelPo record : page.getRecords()) {
            JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(record);
            //组装服务者职业信息
            jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convertJdhAngelProfessionRelList(jdhAngel,
                    angelProfessionRelMap.get(record.getAngelId()));
            //组装服务者技能信息
            jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convertJdhAngelSkillRelList(jdhAngel, angelSkillRelMap.get(record.getAngelId()));

            list.add(jdhAngel);
        }

        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * @param query
     * @Description: 查询服务者分页列表
     * @Return: java.util.List<com.jdh.o2oservice.core.domain.angel.model.JdhAngel>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/21
     **/
    @Override
    public Page<JdhAngel> findPageOnlyAngel(JdhAngelRepPageQuery query) {
        Page<JdhAngelPo> pageParam = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getAngelIdList()), JdhAngelPo::getAngelId, query.getAngelIdList())
                .isNull(JdhAngelPo::getTechnicalTitle)
                .eq(Objects.nonNull(query.getAuditProcessStatus()), JdhAngelPo::getAuditProcessStatus, query.getAuditProcessStatus())
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());

        queryWrapper.orderByDesc(JdhAngelPo::getId);

        Page<JdhAngelPo> angelPage = jdhAngelPoMapper.selectPage(pageParam, queryWrapper);
        if (angelPage == null || CollectionUtils.isEmpty(angelPage.getRecords())) {
            return null;
        }
        List<JdhAngel> list = Lists.newArrayListWithExpectedSize(angelPage.getRecords().size());
        for (JdhAngelPo record : angelPage.getRecords()) {
            JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(record);
            list.add(jdhAngel);
        }
        return JdhBasicPoConverter.initPage(angelPage, list);
    }

    /**
     * @Description: 根据服务者id批量查询关联职业信息
     * @param angelIds
     * @Return: java.util.Map<java.lang.Long,java.util.List<com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelProfessionRelPo>>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/22
    **/
    private Map<Long, List<JdhAngelProfessionRelPo>> getAngelProfessionRelMap(List<Long> angelIds) {
        LambdaQueryWrapper<JdhAngelProfessionRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhAngelProfessionRelPo::getAngelId, angelIds).eq(JdhAngelProfessionRelPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhAngelProfessionRelPo> jdhAngelProfessionRelPos = jdhAngelProfessionRelPoMapper.selectList(queryWrapper);
        return jdhAngelProfessionRelPos.stream().collect(Collectors.groupingBy(JdhAngelProfessionRelPo::getAngelId));
    }

    /**
     * @Description: 根据服务者id查询关联职业信息
     * @param angelId
     * @Return: java.util.List<com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelProfessionRelPo>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
    **/
    private List<JdhAngelProfessionRelPo> getAngelProfessionRelList(Long angelId) {
        LambdaQueryWrapper<JdhAngelProfessionRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelProfessionRelPo::getAngelId, angelId).eq(JdhAngelProfessionRelPo::getYn, YnStatusEnum.YES.getCode());

        return jdhAngelProfessionRelPoMapper.selectList(queryWrapper);
    }

    /**
     * @Description: 根据服务者id批量查询关联技能信息
     * @param angelIds
     * @Return: java.util.Map<java.lang.Long,java.util.List<com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelSkillRelPo>>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/23
    **/
    private Map<Long, List<JdhAngelSkillRelPo>> getAngelSkillRelMap(List<Long> angelIds) {
        LambdaQueryWrapper<JdhAngelSkillRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhAngelSkillRelPo::getAngelId, angelIds).eq(JdhAngelSkillRelPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhAngelSkillRelPo> jdhAngelSkillRelPos = jdhAngelSkillRelPoMapper.selectList(queryWrapper);
        return jdhAngelSkillRelPos.stream().collect(Collectors.groupingBy(JdhAngelSkillRelPo::getAngelId));
    }

    /**
     * @Description: 根据服务者id查询关联技能信息
     * @param angelId
     * @Return: java.util.List<com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelSkillRelPo>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
    **/
    private List<JdhAngelSkillRelPo> getAngelSkillRelList(Long angelId) {
        LambdaQueryWrapper<JdhAngelSkillRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelSkillRelPo::getAngelId, angelId).eq(JdhAngelSkillRelPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhAngelSkillRelPo::getCreateTime) // 按照插入时间降序排列
        ;

        return jdhAngelSkillRelPoMapper.selectList(queryWrapper);
    }

    /**
     * @Description: 查询未开通技能信息
     * @param alreadyOpenSkillCodeList
     * @Return: java.util.List<com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelSkillDictPo>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
    **/
    private List<JdhAngelSkillDictPo> getAngelNotOpenSkillList(List<String> alreadyOpenSkillCodeList) {
        LambdaQueryWrapper<JdhAngelSkillDictPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.notIn(CollectionUtils.isNotEmpty(alreadyOpenSkillCodeList), JdhAngelSkillDictPo::getAngelSkillCode, alreadyOpenSkillCodeList);

        return jdhAngelSkillDictPoMapper.selectList(queryWrapper);
    }


    /**
     * @Description: 查询职业职级信息 key:职级code value:职级信息
     * @param
     * @Return: java.util.Map<java.lang.Long,com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelProfessionTitleDictPo>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/22
    **/
    private Map<Long, JdhAngelProfessionTitleDictPo> getAngelProfessionTitleDictMap() {
        LambdaQueryWrapper<JdhAngelProfessionTitleDictPo> queryWrapper = Wrappers.lambdaQuery();

        List<JdhAngelProfessionTitleDictPo> jdhAngelProfessionTitleDictPos = jdhAngelProfessionTitleDictPoMapper.selectList(queryWrapper);

        return jdhAngelProfessionTitleDictPos.stream().collect(Collectors.toMap(JdhAngelProfessionTitleDictPo::getProfessionTitleCode,
                each -> each, (value1, value2) -> value1));
    }

    /**
     * @Description: 根据服务者id查询职业资质信息
     * @param angelId
     * @Return: java.util.Map<java.lang.Long,java.util.List<com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelProfessionQualificationRelPo>>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
     **/
    private Map<String, List<JdhAngelProfessionQualificationRelPo>> queryJdhAngelProfessionQualificationRel(Long angelId) {
        LambdaQueryWrapper<JdhAngelProfessionQualificationRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelProfessionQualificationRelPo::getAngelId, angelId);

        List<JdhAngelProfessionQualificationRelPo> jdhAngelProfessionQualificationRelPos = jdhAngelProfessionQualificationRelPoMapper.selectList(queryWrapper);
        return jdhAngelProfessionQualificationRelPos.stream().collect(Collectors.groupingBy(JdhAngelProfessionQualificationRelPo::getProfessionCode));
    }


    @Override
    public int remove(@NotNull JdhAngel aggregate) {
        log.info("AngelRepositoryImpl -> remove start angel:{}", JSON.toJSONString(aggregate));
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(JdhAngelPo ::getYn, YnStatusEnum.NO.getCode());
        updateWrapper.eq(JdhAngelPo::getAngelId, aggregate.getAngelId());
        jdhAngelPoMapper.update(null, updateWrapper);

        LambdaUpdateWrapper<JdhAngelProfessionRelPo> updateWrapper2 = new LambdaUpdateWrapper<>();
        updateWrapper2.set(JdhAngelProfessionRelPo::getYn, YnStatusEnum.NO.getCode());
        updateWrapper2.eq(JdhAngelProfessionRelPo::getAngelId, aggregate.getAngelId());
        jdhAngelProfessionRelPoMapper.update(null, updateWrapper2);

        LambdaUpdateWrapper<JdhAngelProfessionQualificationRelPo> updateWrapper3 = new LambdaUpdateWrapper<>();
        updateWrapper3.set(JdhAngelProfessionQualificationRelPo::getYn, YnStatusEnum.NO.getCode());
        updateWrapper3.eq(JdhAngelProfessionQualificationRelPo::getAngelId, aggregate.getAngelId());
        jdhAngelProfessionQualificationRelPoMapper.update(null, updateWrapper3);

        LambdaUpdateWrapper<JdhAngelSkillRelPo> updateWrapper4 = new LambdaUpdateWrapper<>();
        updateWrapper4.set(JdhAngelSkillRelPo::getYn, YnStatusEnum.NO.getCode());
        updateWrapper4.eq(JdhAngelSkillRelPo::getAngelId, aggregate.getAngelId());
        jdhAngelSkillRelPoMapper.update(null, updateWrapper4);
        log.info("AngelRepositoryImpl -> remove success angelId:{}", aggregate.getAngelId());
        return 1;
    }

    @Override
    public int save(@NotNull JdhAngel entity) {
        return 0;
    }

    /**
     * @Description: 保存服务者基础信息
     * @param jdhAngel
     * @Return: int
     * @Author: zhangxiaojie17
     * @Date: 2024/4/18
    **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAngel(@NotNull JdhAngel jdhAngel) {
        JdhAngelPo jdhAngelPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelPo(jdhAngel);
        //新增服务者信息
        if (Objects.isNull(jdhAngelPo.getAngelId())) {
            JdhBasicPoConverter.initInsertBasicPo(jdhAngelPo);
            //生成服务者id
            Long angelId = generateIdFactory.getId();
            jdhAngelPo.setAngelId(angelId);
            jdhAngel.setAngelId(angelId);
            log.info("AngelRepositoryImpl -> save jdhAngelPo:{}", JSON.toJSONString(jdhAngelPo));
            jdhAngelPoMapper.insert(jdhAngelPo);

            List<JdhAngelProfessionRel> jdhAngelProfessionRelList = jdhAngel.getJdhAngelProfessionRelList();
            for (JdhAngelProfessionRel jdhAngelProfessionRel : jdhAngelProfessionRelList) {
                JdhAngelProfessionRelPo jdhAngelProfessionRelPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelProfessionRelPo(jdhAngelProfessionRel);
                jdhAngelProfessionRelPo.setAngelId(angelId);
                JdhBasicPoConverter.initInsertBasicPo(jdhAngelProfessionRelPo);
                log.info("AngelRepositoryImpl -> save jdhAngelProfessionRelPo:{}", JSON.toJSONString(jdhAngelProfessionRelPo));
                jdhAngelProfessionRelPoMapper.insert(jdhAngelProfessionRelPo);

                List<JdhAngelProfessionQualificationRel> jdhAngelProfessionQualificationRelList = jdhAngelProfessionRel.getJdhAngelProfessionQualificationRelList();
                for (JdhAngelProfessionQualificationRel jdhAngelProfessionQualificationRel : jdhAngelProfessionQualificationRelList) {
                    JdhAngelProfessionQualificationRelPo jdhAngelProfessionQualificationRelPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelProfessionQualificationRelPo(jdhAngelProfessionQualificationRel);
                    jdhAngelProfessionQualificationRelPo.setAngelId(angelId);
                    JdhBasicPoConverter.initInsertBasicPo(jdhAngelProfessionQualificationRelPo);
                    log.info("AngelRepositoryImpl -> save jdhAngelProfessionQualificationRelPo:{}", JSON.toJSONString(jdhAngelProfessionRelPo));
                    jdhAngelProfessionQualificationRelPoMapper.insert(jdhAngelProfessionQualificationRelPo);
                }
            }
            if (CollectionUtils.isNotEmpty(jdhAngel.getAngelExtends())) {
                for (JdhAngelExtend angelExtend : jdhAngel.getAngelExtends()) {
                    JdhAngelExtendPo extendPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelExtendPo(angelExtend);
                    extendPo.setAngelId(jdhAngelPo.getAngelId());
                    extendPo.setCreateTime(new Date());
                    extendPo.setUpdateTime(new Date());
                    extendPo.setYn(YnStatusEnum.YES.getCode());
                    jdhAngelExtendPoMapper.insert(extendPo);
                }
            }
        } else {
            //修改服务者信息
            LambdaUpdateWrapper<JdhAngelPo> updateAngelWrapper = new LambdaUpdateWrapper<>();
            //version++
            jdhAngel.versionIncrease();
            String certificateNo = "";
            String certificateNoIndex = "";
            if(StringUtils.isNotBlank(jdhAngelPo.getCertificateNo())) {
                certificateNo = tdeClientUtil.encrypt(jdhAngelPo.getCertificateNo());
                certificateNoIndex = tdeClientUtil.obtainKeyWordIndex(jdhAngelPo.getCertificateNo());
            }

            updateAngelWrapper.eq(JdhAngelPo::getAngelId, jdhAngelPo.getAngelId())
                    .eq(JdhAngelPo::getVersion, jdhAngelPo.getVersion())
                    .set(Objects.nonNull(jdhAngelPo.getNethpDocId()), JdhAngelPo::getNethpDocId, jdhAngelPo.getNethpDocId())
                    .set(JdhAngelPo::getAngelPin, jdhAngelPo.getAngelPin())
                    .set(JdhAngelPo::getAngelName, jdhAngelPo.getAngelName())
                    .set(JdhAngelPo::getHeadImg, jdhAngelPo.getHeadImg())
                    .set(JdhAngelPo::getIdCard, tdeClientUtil.encrypt(jdhAngelPo.getIdCard()))
                    .set(JdhAngelPo::getIdCardIndex, tdeClientUtil.obtainKeyWordIndex(jdhAngelPo.getIdCard()))
                    .set(JdhAngelPo::getIdCardImgFrontUrl, jdhAngelPo.getIdCardImgFrontUrl())
                    .set(JdhAngelPo::getIdCardImgOppositeUrl, jdhAngelPo.getIdCardImgOppositeUrl())
                    .set(JdhAngelPo::getPhone, tdeClientUtil.encrypt(jdhAngelPo.getPhone()))
                    .set(JdhAngelPo::getPhoneIndex, tdeClientUtil.obtainKeyWordIndex(jdhAngelPo.getPhone()))
                    .set(JdhAngelPo::getAuditProcessStatus, jdhAngelPo.getAuditProcessStatus())
                    .set(JdhAngelPo::getAuditProcessDate, jdhAngelPo.getAuditProcessDate())

                    .set(StringUtils.isNotBlank(jdhAngelPo.getTechnicalTitle()), JdhAngelPo::getTechnicalTitle, jdhAngelPo.getTechnicalTitle())
                    .set(StringUtils.isNotBlank(jdhAngelPo.getCertificateNo()), JdhAngelPo::getCertificateNo, certificateNo)
                    .set(StringUtils.isNotBlank(jdhAngelPo.getCertificateNoIndex()), JdhAngelPo::getCertificateNoIndex, certificateNoIndex)
                    .set(StringUtils.isNotBlank(jdhAngelPo.getCertificateIssuingAuthority()), JdhAngelPo::getCertificateIssuingAuthority, jdhAngelPo.getCertificateIssuingAuthority())
                    .set(StringUtils.isNotBlank(jdhAngelPo.getGrade()), JdhAngelPo::getGrade, jdhAngelPo.getGrade())
                    .set(StringUtils.isNotBlank(jdhAngelPo.getSpeciality()), JdhAngelPo::getSpeciality, jdhAngelPo.getSpeciality())
                    .set(StringUtils.isNotBlank(jdhAngelPo.getCardIssuingAuthority()), JdhAngelPo::getCardIssuingAuthority, jdhAngelPo.getCardIssuingAuthority())
                    .set(StringUtils.isNotBlank(jdhAngelPo.getAngeExt()), JdhAngelPo::getAngeExt, jdhAngelPo.getAngeExt())

                    .set(JdhAngelPo::getAuditProcessRemark, jdhAngelPo.getAuditProcessRemark())
                    .set(JdhAngelPo::getIntroduction, jdhAngelPo.getIntroduction())
                    .set(StringUtils.isNotBlank(jdhAngel.getOperator()), JdhAngelPo::getUpdateUser, jdhAngel.getOperator())
                    .set(JdhAngelPo::getVersion, jdhAngel.getVersion())
                    .set(JdhAngelPo::getWorkIdentity, jdhAngel.getWorkIdentity())
            ;
            log.info("AngelRepositoryImpl -> update  updateAngelWrapper:{}", JSON.toJSONString(updateAngelWrapper));
            int updateSum = jdhAngelPoMapper.update(null, updateAngelWrapper);
            if (updateSum < 1) {
                throw new BusinessException(AngelErrorCode.ANGEL_UPDATE_DB_VERSION_ERROR);
            }


            List<JdhAngelProfessionRel> jdhAngelProfessionRelList = jdhAngel.getJdhAngelProfessionRelList();
            if (CollectionUtils.isNotEmpty(jdhAngelProfessionRelList)) {
                JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngelProfessionRelList.get(0);
                //修改服务者职业信息
                LambdaUpdateWrapper<JdhAngelProfessionRelPo> updateAngelProfessionRelWrapper = new LambdaUpdateWrapper<>();
                updateAngelProfessionRelWrapper.eq(JdhAngelProfessionRelPo::getAngelId, jdhAngelPo.getAngelId())
                        .set(JdhAngelProfessionRelPo::getProfessionCode, jdhAngelProfessionRel.getProfessionCode())
                        .set(JdhAngelProfessionRelPo::getProfessionName, jdhAngelProfessionRel.getProfessionName())
                        .set(JdhAngelProfessionRelPo::getProfessionTitleCode, jdhAngelProfessionRel.getProfessionTitleCode())
                        .set(JdhAngelProfessionRelPo::getProfessionTitleName, jdhAngelProfessionRel.getProfessionTitleName())
                        .set(JdhAngelProfessionRelPo::getInstitutionCode, jdhAngelProfessionRel.getInstitutionCode())
                        .set(JdhAngelProfessionRelPo::getInstitutionName, jdhAngelProfessionRel.getInstitutionName());
                log.info("AngelRepositoryImpl -> update  updateAngelProfessionRelWrapper:{}", JSON.toJSONString(updateAngelProfessionRelWrapper));
                jdhAngelProfessionRelPoMapper.update(null, updateAngelProfessionRelWrapper);

                List<JdhAngelProfessionQualificationRel> jdhAngelProfessionQualificationRelList = jdhAngelProfessionRel.getJdhAngelProfessionQualificationRelList();
                if (CollectionUtils.isNotEmpty(jdhAngelProfessionQualificationRelList)) {
                    JdhAngelProfessionQualificationRel jdhAngelProfessionQualificationRel = jdhAngelProfessionQualificationRelList.get(0);
                    //修改服务者职业资质信息
                    LambdaUpdateWrapper<JdhAngelProfessionQualificationRelPo> updateAngelProfessionQualificationRelWrapper = new LambdaUpdateWrapper<>();

                    updateAngelProfessionQualificationRelWrapper.eq(JdhAngelProfessionQualificationRelPo::getAngelId, jdhAngelPo.getAngelId())
                            .set(JdhAngelProfessionQualificationRelPo::getProfessionCode, jdhAngelProfessionQualificationRel.getProfessionCode())
                            .set(JdhAngelProfessionQualificationRelPo::getQualificationUrl, jdhAngelProfessionQualificationRel.getQualificationUrl());
                    log.info("AngelRepositoryImpl -> update  jdhAngelProfessionQualificationRelPoMapper:{}", JSON.toJSONString(jdhAngelProfessionQualificationRelPoMapper));
                    jdhAngelProfessionQualificationRelPoMapper.update(null, updateAngelProfessionQualificationRelWrapper);
                }
            }
            List<JdhAngelExtend> extendList = jdhAngel.getAngelExtends();
            if(CollUtil.isNotEmpty(extendList)){
                for (JdhAngelExtend angelExtend : extendList) {
                    JdhAngelExtendPo extendPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelExtendPo(angelExtend);
                    extendPo.setAngelId(jdhAngelPo.getAngelId());
                    //更新
                    if(Objects.nonNull(angelExtend.getId())){
                        LambdaUpdateWrapper<JdhAngelExtendPo> extendLambdaUpdateWrapper = Wrappers.lambdaUpdate();
                        extendLambdaUpdateWrapper
                                .set(JdhAngelExtendPo::getValue,extendPo.getValue())
                                .eq(JdhAngelExtendPo::getAngelId,extendPo.getAngelId())
                                .eq(JdhAngelExtendPo::getAttribute,extendPo.getAttribute());
                        jdhAngelExtendPoMapper.update(extendPo, extendLambdaUpdateWrapper);
                    }else{
                        //新增
                        extendPo.setAngelId(jdhAngelPo.getAngelId());
                        extendPo.setCreateTime(new Date());
                        extendPo.setUpdateTime(new Date());
                        extendPo.setYn(YnStatusEnum.YES.getCode());
                        jdhAngelExtendPoMapper.insert(extendPo);
                    }
                }
            }
        }
    }

    @Override
    public void removeAllAngel(Long angelId) {
        if (angelId == null) {
            LambdaUpdateWrapper<JdhAngelPo> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.eq(JdhAngelPo::getYn, "1");
            jdhAngelPoMapper.delete(deleteWrapper);

            LambdaUpdateWrapper<JdhAngelProfessionRelPo> deleteWrapper2 = new LambdaUpdateWrapper<>();
            deleteWrapper2.eq(JdhAngelProfessionRelPo::getYn, "1");
            jdhAngelProfessionRelPoMapper.delete(deleteWrapper2);

            LambdaUpdateWrapper<JdhAngelProfessionQualificationRelPo> deleteWrapper3 = new LambdaUpdateWrapper<>();
            deleteWrapper3.eq(JdhAngelProfessionQualificationRelPo::getYn, "1");
            jdhAngelProfessionQualificationRelPoMapper.delete(deleteWrapper3);

            LambdaUpdateWrapper<JdhAngelSkillRelPo> deleteWrapper4 = new LambdaUpdateWrapper<>();
            deleteWrapper4.eq(JdhAngelSkillRelPo::getYn, "1");
            jdhAngelSkillRelPoMapper.delete(deleteWrapper4);
            log.info("AngelRepositoryImpl -> removeAllAngel success");
        } else {
            LambdaUpdateWrapper<JdhAngelPo> deleteWrapper = new LambdaUpdateWrapper<>();
            deleteWrapper.eq(JdhAngelPo::getAngelId, angelId);
            jdhAngelPoMapper.delete(deleteWrapper);

            LambdaUpdateWrapper<JdhAngelProfessionRelPo> deleteWrapper2 = new LambdaUpdateWrapper<>();
            deleteWrapper2.eq(JdhAngelProfessionRelPo::getAngelId, angelId);
            jdhAngelProfessionRelPoMapper.delete(deleteWrapper2);

            LambdaUpdateWrapper<JdhAngelProfessionQualificationRelPo> deleteWrapper3 = new LambdaUpdateWrapper<>();
            deleteWrapper3.eq(JdhAngelProfessionQualificationRelPo::getAngelId, angelId);
            jdhAngelProfessionQualificationRelPoMapper.delete(deleteWrapper3);

            LambdaUpdateWrapper<JdhAngelSkillRelPo> deleteWrapper4 = new LambdaUpdateWrapper<>();
            deleteWrapper4.eq(JdhAngelSkillRelPo::getAngelId, angelId);
            jdhAngelSkillRelPoMapper.delete(deleteWrapper4);
            log.info("AngelRepositoryImpl -> removeAllAngel success angelId:{}", angelId);
        }


    }

    /**
     * 聚合查询机构下人员总量
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> countByJdhProviderIdList(Set<Long> jdProviderIds) {
        if (CollUtil.isEmpty(jdProviderIds)) {
            return Collections.emptyList();
        }

        String providerIdField = "jdh_provider_id";
        QueryWrapper<JdhAngelPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(providerIdField + " as jdhProviderId,count(*) as queryCount")
                .in(providerIdField, jdProviderIds)
                .eq("yn", YnStatusEnum.YES.getCode())
                .groupBy(providerIdField);
        return jdhAngelPoMapper.selectMaps(queryWrapper);
    }

    /**
     * 查询证件号
     *
     * @param query
     * @return
     */
    @Override
    public List<JdhAngel> queryByIdCard(JdhAngelRepQuery query) {
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        String idCardIndex = tdeClientUtil.calculateKeyWord(query.getIdCard());
        queryWrapper.eq(JdhAngelPo::getIdCardIndex, idCardIndex);
        if (Objects.equals(query.getSearchDel(), true)) {
            log.info("AngelRepositoryImpl -> queryByIdCard, 查询已删除的，不设置yn=1条件，query={}", JSON.toJSONString(query));
        } else {
            queryWrapper.eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        }
        List<JdhAngelPo> jdhAngelPos = jdhAngelPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(jdhAngelPos)){
            return new ArrayList<>();
        }
        List<JdhAngel> list = Lists.newArrayListWithExpectedSize(jdhAngelPos.size());
        for (JdhAngelPo jdhAngelPo : jdhAngelPos) {
            JdhAngel jdhAngel = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngel(jdhAngelPo);
            list.add(jdhAngel);
        }
        return list;
    }

    /**
     * 绑定护士到机构
     *
     * @param jdhAngel model
     * @return count
     */
    @Override
    public int bindJdhProvider(JdhAngel jdhAngel) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        String idCardIndex = tdeClientUtil.calculateKeyWord(jdhAngel.getIdCard());
        if (jdhAngel.getJdhProviderBindTime() == null) {
            jdhAngel.setJdhProviderBindTime(new Date());
        }
        updateWrapper.set(JdhAngelPo::getJdhProviderId, jdhAngel.getJdhProviderId())
                .set(JdhAngelPo::getJdhProviderBindTime, jdhAngel.getJdhProviderBindTime())
                .eq(JdhAngelPo::getIdCardIndex, idCardIndex)
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode())
                .isNull(JdhAngelPo::getJdhProviderId);
        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    /**
     * 解绑护士机构
     *
     * @param jdhAngel model
     * @return count
     */
    @Override
    public int unBindJdhProvider(JdhAngel jdhAngel) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelPo::getJdhProviderId, null)
                .set(JdhAngelPo::getJdhProviderBindTime, null)
                .eq(JdhAngelPo::getAngelId, jdhAngel.getAngelId())
                .eq(JdhAngelPo::getJdhProviderId, jdhAngel.getJdhProviderId())
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    /**
     * 解绑机构下全部护士
     *
     * @param jdhAngel model
     * @return count
     */
    @Override
    public int unBindJdhProviderAllStaff(JdhAngel jdhAngel) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelPo::getJdhProviderId, null)
                .set(JdhAngelPo::getJdhProviderBindTime, null)
                .eq(JdhAngelPo::getJdhProviderId, jdhAngel.getJdhProviderId())
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhAngelPoMapper.update(null, updateWrapper);
    }

    /**
     * 护士端-我的-维护服务信息
     * @param jdhAngel
     * @return
     */
    @Override
    public Boolean updateServiceInfo(JdhAngel jdhAngel) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();

        updateWrapper.set(StringUtils.isNotBlank(jdhAngel.getProvinceCode()),JdhAngelPo::getProvinceCode, jdhAngel.getProvinceCode())
                .set(StringUtils.isNotBlank(jdhAngel.getCityCode()),JdhAngelPo::getCityCode, jdhAngel.getCityCode())
                .set(StringUtils.isNotBlank(jdhAngel.getCountyCode()),JdhAngelPo::getCountyCode, jdhAngel.getCountyCode())
                .set(StringUtils.isNotBlank(jdhAngel.getOneDepartmentCode()),JdhAngelPo::getOneDepartmentCode, jdhAngel.getOneDepartmentCode())
                .set(StringUtils.isNotBlank(jdhAngel.getTwoDepartmentCode()),JdhAngelPo::getTwoDepartmentCode, jdhAngel.getTwoDepartmentCode())

                .set(StringUtils.isNotBlank(jdhAngel.getProvinceName()),JdhAngelPo::getProvinceName, jdhAngel.getProvinceName())
                .set(StringUtils.isNotBlank(jdhAngel.getCityName()),JdhAngelPo::getCityName, jdhAngel.getCityName())
                .set(StringUtils.isNotBlank(jdhAngel.getCountyName()),JdhAngelPo::getCountyName, jdhAngel.getCountyName())
                .set(StringUtils.isNotBlank(jdhAngel.getOneDepartmentName()),JdhAngelPo::getOneDepartmentName, jdhAngel.getOneDepartmentName())
                .set(StringUtils.isNotBlank(jdhAngel.getTwoDepartmentName()),JdhAngelPo::getTwoDepartmentName, jdhAngel.getTwoDepartmentName())
                .set(JdhAngelPo::getUpdateTime, new Date())
                .set(JdhAngelPo::getUpdateUser, jdhAngel.getAngelPin())

                .eq(JdhAngelPo::getAngelPin, jdhAngel.getAngelPin())
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhAngelPoMapper.update(null, updateWrapper)>0;
    }

    @Override
    public List<JdhAngelExtend> findAngelExtendList(JdhAngelExtendQuery query) {
        AssertUtils.nonNull(query, "AngelRepositoryImpl -> findAngelExtendList query is null");

        if(CollectionUtils.isEmpty(query.getExtendKeyList()) && StringUtils.isEmpty(query.getExtendValue()) && Objects.isNull(query.getAngelId())){
            return Collections.emptyList();
        }

        LambdaQueryWrapper<JdhAngelExtendPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelExtendPo::getYn, YnStatusEnum.YES.getCode())
                .eq(Objects.nonNull(query.getAngelId()), JdhAngelExtendPo::getAngelId, query.getAngelId())
                .in(CollectionUtils.isNotEmpty(query.getExtendKeyList()), JdhAngelExtendPo::getAttribute, query.getExtendKeyList())
                .eq(StringUtils.isNotBlank(query.getExtendValue()), JdhAngelExtendPo::getValue, query.getExtendValue());

        List<JdhAngelExtendPo> jdhAngelExtendPos = jdhAngelExtendPoMapper.selectList(queryWrapper);
        return JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelExtendList(jdhAngelExtendPos);
    }

    @Override
    public int insertAngelExtend(JdhAngelExtend jdhAngelExtend) {
        JdhAngelExtendPo extendPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelExtendPo(jdhAngelExtend);
        extendPo.setCreateTime(new Date());
        extendPo.setUpdateTime(new Date());
        extendPo.setYn(YnStatusEnum.YES.getCode());
        return jdhAngelExtendPoMapper.insert(extendPo);
    }

    @Override
    public int saveAngelExtend(JdhAngelExtend jdhAngelExtend) {
        JdhAngelExtendQuery query = JdhAngelExtendQuery.builder().angelId(jdhAngelExtend.getAngelId()).extendKeyList(Arrays.asList(jdhAngelExtend.getAttribute())).build();
        List<JdhAngelExtend> angelExtendList = this.findAngelExtendList(query);
        if(CollUtil.isNotEmpty(angelExtendList)){
            for (JdhAngelExtend angelExtend : angelExtendList) {
                JdhAngelExtendPo extendPo = JdhAngelInfrastructureConverter.INSTANCE.convert2JdhAngelExtendPo(angelExtend);
                LambdaUpdateWrapper<JdhAngelExtendPo> extendLambdaUpdateWrapper = Wrappers.lambdaUpdate();
                extendLambdaUpdateWrapper
                        .set(JdhAngelExtendPo::getValue,jdhAngelExtend.getValue())
                        .eq(JdhAngelExtendPo::getAngelId,extendPo.getAngelId())
                        .eq(JdhAngelExtendPo::getAttribute,extendPo.getAttribute());
                return jdhAngelExtendPoMapper.update(extendPo, extendLambdaUpdateWrapper);
            }
        } else{
            return insertAngelExtend(jdhAngelExtend);
        }
        return 0;
    }

    /**
     * 查询服务者及职业
     *
     * @param query
     * @return
     */
    @Override
    public JdhAngel queryAngelWithProfession(JdhAngelRepQuery query) {
        if(Objects.isNull(query) || Objects.isNull(query.getAngelId())) {
            log.error("AngelRepositoryImpl -> queryAngelWithProfession, 查询护士主数据参数异常");
            return null;
        }
        LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelPo::getAngelId, query.getAngelId())
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhAngelPo> jdhAngelPos = jdhAngelPoMapper.selectList(queryWrapper);

        if(CollectionUtils.isEmpty(jdhAngelPos)) {
            return null;
        }
        JdhAngelPo jdhAngelPo = jdhAngelPos.get(0);
        return wrap(jdhAngelPo);
    }

    /**
     * 更新服务者状态
     *
     * @param jdhAngelOld
     * @return
     */
    @Override
    public int updateAuditProcessStatus(JdhAngel jdhAngelOld) {
        LambdaUpdateWrapper<JdhAngelPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelPo::getAuditProcessStatus, jdhAngelOld.getAuditProcessStatus())
                .set(JdhAngelPo::getUpdateTime, new Date())
                .eq(JdhAngelPo::getAngelId, jdhAngelOld.getAngelId())
                .eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
        return jdhAngelPoMapper.update(null, updateWrapper);
    }


    /**
     *
     * @param po
     * @return
     */
    private JdhAngel wrap(JdhAngelPo po){
        if (Objects.isNull(po)){
            return null;
        }
        JdhAngel angel = JdhAngelPoConvert.INSTANCE.convertToJdhAngel(po);
        Map<Long,List<JdhAngelExtendPo>> extendMap = getExtendMap(Lists.newArrayList(angel.getAngelId()));

        if(MapUtil.isNotEmpty(extendMap)){
            angel = JdhAngelPoConvert.INSTANCE.convertAngelExtendList(angel, extendMap.get(po.getAngelId()));
        }
        //查询服务者职业信息
        List<JdhAngelProfessionRelPo> angelProfessionRelList = getAngelProfessionRelList(angel.getAngelId());

        //组装职业职级信息
        angel = JdhAngelInfrastructureConverter.INSTANCE.convertJdhAngelProfessionRelList(angel,
                angelProfessionRelList);
        return angel;
    }

    /**
     * 获取扩展信息
     *
     * @param angelIds promiseIds
     * @return {@link Map}<{@link Long}, {@link List}<{@link JdhPromiseExtendPo}>>
     */
    private Map<Long, List<JdhAngelExtendPo>> getExtendMap(List<Long> angelIds) {
        LambdaQueryWrapper<JdhAngelExtendPo> detailQueryWrapper = Wrappers.lambdaQuery();
        detailQueryWrapper.in(JdhAngelExtendPo::getAngelId, angelIds)
                .eq(JdhAngelExtendPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhAngelExtendPo> jdhAngelExtendPos = jdhAngelExtendPoMapper.selectList(detailQueryWrapper);
        return jdhAngelExtendPos.stream().collect(Collectors.groupingBy(JdhAngelExtendPo::getAngelId));
    }
}
