package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.ProviderStore;
import com.jdh.o2oservice.core.domain.provider.model.ProviderStoreIdentifier;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.ProviderStationPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhStationServiceItemRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhStationServiceItemRelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ProviderStoreRepositoryImpl implements ProviderStoreRepository {

    /**
     * 站点
     */
    @Resource
    JdhStationServiceItemRelPoMapper jdhStationServiceItemRelPoMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param providerStoreIdentifier
     */
    @Override
    public ProviderStore find(ProviderStoreIdentifier providerStoreIdentifier) {
        return null;
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(ProviderStore aggregate) {
        return 0;
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param aggregate
     */
    @Override
    public int save(ProviderStore aggregate) {
        return 0;
    }

    /**
     * 保存商品项目关系
     *
     * @param rel
     * @return
     */
    @Override
    public int saveStationServiceItem(JdhStationServiceItemRel rel) {
        JdhStationServiceItemRelPo po = ProviderStationPoConverter.INS.modelToPo(rel);
        Date now = new Date();
        po.setCreateUser(rel.getCreateUser());
        po.setCreateTime(now);
        po.setUpdateUser(rel.getUpdateUser());
        po.setUpdateTime(now);
        return jdhStationServiceItemRelPoMapper.insert(po);
    }

    /**
     * 更新门店项目关系
     *
     * @param rel
     * @return
     */
    @Override
    public int updateStationServiceItem(JdhStationServiceItemRel rel) {
        if (StringUtils.isBlank(rel.getStationId())) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "站点id为空"));
        }
        if (rel.getServiceItemId() == null) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "服务项目id为空"));
        }
        LambdaUpdateWrapper<JdhStationServiceItemRelPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhStationServiceItemRelPo po = JdhStationServiceItemRelPo.builder().build();
        updateWrapper.set(rel.getSettlementPrice() != null, JdhStationServiceItemRelPo::getSettlementPrice, rel.getSettlementPrice())
                .set(rel.getMaterialPackageId() != null, JdhStationServiceItemRelPo::getMaterialPackageId, rel.getMaterialPackageId())
                .set(StringUtils.isNotBlank(rel.getSpecimenWay()), JdhStationServiceItemRelPo::getSpecimenWay, rel.getSpecimenWay())
                .set(rel.getSpecimenPreserveDuration() != null, JdhStationServiceItemRelPo::getSpecimenPreserveDuration, rel.getSpecimenPreserveDuration())
                .set(StringUtils.isNotBlank(rel.getSpecimenNum()), JdhStationServiceItemRelPo::getSpecimenNum, rel.getSpecimenNum())
                .set(rel.getServiceDuration() != null, JdhStationServiceItemRelPo::getServiceDuration, rel.getServiceDuration())
                .set(StringUtils.isNotBlank(rel.getSpecimenPreserveCondition()), JdhStationServiceItemRelPo::getSpecimenPreserveCondition, rel.getSpecimenPreserveCondition())
                .set(rel.getTestDuration() != null, JdhStationServiceItemRelPo::getTestDuration, rel.getTestDuration())
                .set(rel.getServiceCondition() != null, JdhStationServiceItemRelPo::getServiceCondition, rel.getServiceCondition())
                .set(StringUtils.isNotBlank(rel.getRemark()), JdhStationServiceItemRelPo::getRemark, rel.getRemark())
                .set(rel.getOnOffShelf() != null, JdhStationServiceItemRelPo::getOnOffShelf, rel.getOnOffShelf())
                .set(rel.getEquipmentBizId() != null, JdhStationServiceItemRelPo::getEquipmentBizId, rel.getEquipmentBizId())
                .set(rel.getContentBizId() != null, JdhStationServiceItemRelPo::getContentBizId, rel.getContentBizId())
                .set(rel.getReportShowType()!=null,JdhStationServiceItemRelPo::getReportShowType, rel.getReportShowType())
                .setSql("`version` = version+1")
                .eq(JdhStationServiceItemRelPo::getStationId, rel.getStationId())
                .eq(JdhStationServiceItemRelPo::getServiceItemId, rel.getServiceItemId())
                .eq(JdhStationServiceItemRelPo::getVersion, rel.getVersion());
        Date now = new Date();
        po.setUpdateUser(rel.getUpdateUser());
        po.setUpdateTime(now);
        return jdhStationServiceItemRelPoMapper.update(po, updateWrapper);
    }

    @Override
    public int updateEquipmentId(JdhStationServiceItemRel rel) {
        if (StringUtils.isBlank(rel.getStationId())) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "站点id为空"));
        }
        if (rel.getServiceItemId() == null) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "服务项目id为空"));
        }
        LambdaUpdateWrapper<JdhStationServiceItemRelPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhStationServiceItemRelPo po = JdhStationServiceItemRelPo.builder().build();
        updateWrapper.set(JdhStationServiceItemRelPo::getEquipmentBizId, rel.getEquipmentBizId())
                .set(rel.getReportShowType()!=null,JdhStationServiceItemRelPo::getReportShowType, rel.getReportShowType())
                .setSql("`version` = version+1")
                .eq(JdhStationServiceItemRelPo::getStationId, rel.getStationId())
                .eq(JdhStationServiceItemRelPo::getServiceItemId, rel.getServiceItemId())
                .eq(JdhStationServiceItemRelPo::getVersion, rel.getVersion());
        Date now = new Date();
        po.setUpdateUser(rel.getUpdateUser());
        po.setUpdateTime(now);
        return jdhStationServiceItemRelPoMapper.update(po, updateWrapper);
    }

    @Override
    public int updateContentId(JdhStationServiceItemRel rel) {
        if (StringUtils.isBlank(rel.getStationId())) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "站点id为空"));
        }
        if (rel.getServiceItemId() == null) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "服务项目id为空"));
        }
        LambdaUpdateWrapper<JdhStationServiceItemRelPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhStationServiceItemRelPo po = JdhStationServiceItemRelPo.builder().build();
        updateWrapper.set(JdhStationServiceItemRelPo::getContentBizId, rel.getContentBizId())
                .setSql("`version` = version+1")
                .eq(JdhStationServiceItemRelPo::getStationId, rel.getStationId())
                .eq(JdhStationServiceItemRelPo::getServiceItemId, rel.getServiceItemId())
                .eq(rel.getVersion() != null,JdhStationServiceItemRelPo::getVersion, rel.getVersion());
        Date now = new Date();
        po.setUpdateUser(rel.getUpdateUser());
        po.setUpdateTime(now);
        return jdhStationServiceItemRelPoMapper.update(po, updateWrapper);
    }

    /**
     * 删除门店项目关系
     *
     * @param rel
     * @return
     */
    @Override
    public int deleteStationServiceItem(JdhStationServiceItemRel rel) {
        LambdaUpdateWrapper<JdhStationServiceItemRelPo> deleteWrapper = Wrappers.lambdaUpdate();
        deleteWrapper.eq(JdhStationServiceItemRelPo::getStationId, rel.getStationId());
        deleteWrapper.eq(JdhStationServiceItemRelPo::getServiceItemId, rel.getServiceItemId());
        return jdhStationServiceItemRelPoMapper.delete(deleteWrapper);
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStationServiceItemRel jdhStationServiceItemRel
     * @return
     */
    @Override
    public List<JdhStationServiceItemRel> queryStationServiceItemList(JdhStationServiceItemRel jdhStationServiceItemRel) {
        if (jdhStationServiceItemRel.getServiceItemId() == null && StringUtils.isBlank(jdhStationServiceItemRel.getStationId())
           && CollUtil.isEmpty(jdhStationServiceItemRel.getServiceItemIds())) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "查询条件为空"));
        }
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        boolean queryOnOffShelf = jdhStationServiceItemRel.getOnOffShelf() != null || !Boolean.TRUE.equals(jdhStationServiceItemRel.getQueryIgnoreOnOffShelf());
        queryWrapper.eq(StringUtils.isNotBlank(jdhStationServiceItemRel.getStationId()), JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationId())
                .eq(jdhStationServiceItemRel.getServiceItemId() != null, JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemId())
                .eq(jdhStationServiceItemRel.getProvinceId() != null, JdhStationServiceItemRelPo::getProvinceId, jdhStationServiceItemRel.getProvinceId())
                .eq(jdhStationServiceItemRel.getCityId() != null, JdhStationServiceItemRelPo::getCityId, jdhStationServiceItemRel.getCityId())
                .in(CollUtil.isNotEmpty(jdhStationServiceItemRel.getServiceItemIds()), JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemIds())
                .in(CollUtil.isNotEmpty(jdhStationServiceItemRel.getStationIdSet()), JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationIdSet())
                .eq(queryOnOffShelf, JdhStationServiceItemRelPo::getOnOffShelf, jdhStationServiceItemRel.getOnOffShelf() != null ? jdhStationServiceItemRel.getOnOffShelf() : YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhStationServiceItemRelPo::getCreateTime);
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStationServiceItemRel jdhStationServiceItemRel
     * @return
     */
    @Override
    public List<JdhStationServiceItemRel> queryStationServiceItemList(List<JdhStationServiceItemRel> jdhStationServiceItemRel) {
        Set<String> stationIds = jdhStationServiceItemRel.stream().filter(Objects::nonNull).map(JdhStationServiceItemRel::getStationId).collect(Collectors.toSet());
        Set<Long> serviceItemIds = jdhStationServiceItemRel.stream().filter(Objects::nonNull).map(JdhStationServiceItemRel::getServiceItemId).collect(Collectors.toSet());
        log.info("ProviderStoreRepositoryImpl queryList stationIds={}, serviceItemIds={}", JSON.toJSONString(stationIds), JSON.toJSONString(serviceItemIds));
        if (CollUtil.isEmpty(stationIds) && CollUtil.isEmpty(serviceItemIds)) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PARAM_NULL_ERROR.getCode(), "查询条件为空"));
        }
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollUtil.isNotEmpty(stationIds), JdhStationServiceItemRelPo::getStationId, stationIds).
                in(CollUtil.isNotEmpty(serviceItemIds), JdhStationServiceItemRelPo::getServiceItemId, serviceItemIds)
                .eq(JdhStationServiceItemRelPo::getOnOffShelf, YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhStationServiceItemRelPo::getCreateTime);
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 分页查询sku列表
     *
     * @param jdhStationServiceItemRel
     */
    @Override
    public Page<JdhStationServiceItemRel> queryStationServiceItemListPage(JdhStationServiceItemRel jdhStationServiceItemRel) {
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        boolean queryOnOffShelf = jdhStationServiceItemRel.getOnOffShelf() != null || !Boolean.TRUE.equals(jdhStationServiceItemRel.getQueryIgnoreOnOffShelf());
        queryWrapper.eq(jdhStationServiceItemRel.getServiceItemId() != null, JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemId())
                .in(CollUtil.isNotEmpty(jdhStationServiceItemRel.getServiceItemIds()), JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemIds())
                .eq(JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationId())
                .eq(queryOnOffShelf, JdhStationServiceItemRelPo::getOnOffShelf, jdhStationServiceItemRel.getOnOffShelf() != null ? jdhStationServiceItemRel.getOnOffShelf() : YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhStationServiceItemRelPo::getCreateTime);
        Page<JdhStationServiceItemRelPo> param = new Page<>(jdhStationServiceItemRel.getPageNum(), jdhStationServiceItemRel.getPageSize());
        IPage<JdhStationServiceItemRelPo> jdhSkuPoIPage = jdhStationServiceItemRelPoMapper.selectPage(param, queryWrapper);
        List<JdhStationServiceItemRel> jdhSkus = ProviderStationPoConverter.INS.poToModel(jdhSkuPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhSkuPoIPage, jdhSkus);
    }

    /**
     * 查询门店项目关系
     *
     * @param jdhStationServiceItemRel
     */
    @Override
    public JdhStationServiceItemRel queryStationServiceItem(JdhStationServiceItemRel jdhStationServiceItemRel) {
        if (jdhStationServiceItemRel.getServiceItemId() == null || StringUtils.isBlank(jdhStationServiceItemRel.getStationId())) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        boolean queryOnOffShelf = jdhStationServiceItemRel.getOnOffShelf() != null || !Boolean.TRUE.equals(jdhStationServiceItemRel.getQueryIgnoreOnOffShelf());
        queryWrapper.eq(JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationId())
                .eq(JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemId())
                .eq(queryOnOffShelf, JdhStationServiceItemRelPo::getOnOffShelf, jdhStationServiceItemRel.getOnOffShelf() != null ? jdhStationServiceItemRel.getOnOffShelf(): YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode());
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.selectOne(queryWrapper));
    }

    /**
     * 保存时专用-查询门店项目关系
     *
     * @param jdhStationServiceItemRel
     */
    @Override
    public JdhStationServiceItemRel saveQueryStationServiceItem(JdhStationServiceItemRel jdhStationServiceItemRel) {
        if (jdhStationServiceItemRel.getServiceItemId() == null || StringUtils.isBlank(jdhStationServiceItemRel.getStationId())) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationId())
                .eq(JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemId())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode());
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.selectOne(queryWrapper));
    }

    /**
     * 查询项目在同一个门店数据
     *
     * @param jdhStationServiceItemRel jdhStationServiceItemRel
     * @return
     */
    @Override
    public List<JdhStationServiceItemRel> queryStationListForServiceItemAllInOneStore(List<JdhStationServiceItemRel> jdhStationServiceItemRel) {
        if (CollUtil.isEmpty(jdhStationServiceItemRel)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        List<JdhStationServiceItemRelPo> list = ProviderStationPoConverter.INS.modelToPo(jdhStationServiceItemRel);
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.queryStationListForServiceItemAllInOneStore(list, list.size()));
    }

    /**
     * 内容管理查询门店项目关系
     *
     * @param jdhStationServiceItemRel
     */
    @Override
    public Page<JdhStationServiceItemRel> queryStationServiceItemByContentPage(JdhStationServiceItemRel jdhStationServiceItemRel) {
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        boolean queryOnOffShelf = jdhStationServiceItemRel.getOnOffShelf() != null || !Boolean.TRUE.equals(jdhStationServiceItemRel.getQueryIgnoreOnOffShelf());
        queryWrapper.eq(jdhStationServiceItemRel.getContentBizId()!=null,JdhStationServiceItemRelPo::getContentBizId, jdhStationServiceItemRel.getContentBizId())
                .eq(StringUtils.isNotEmpty(jdhStationServiceItemRel.getStationId()),JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationId())
                .eq(jdhStationServiceItemRel.getServiceItemId() != null ,JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemId())
                .in(CollUtil.isNotEmpty(jdhStationServiceItemRel.getStationIdSet()), JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationIdSet())
                .in(CollUtil.isNotEmpty(jdhStationServiceItemRel.getServiceItemIds()), JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemIds())
                .eq(queryOnOffShelf, JdhStationServiceItemRelPo::getOnOffShelf, jdhStationServiceItemRel.getOnOffShelf() != null ? jdhStationServiceItemRel.getOnOffShelf(): YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode());
        Page<JdhStationServiceItemRelPo> param = new Page<>(jdhStationServiceItemRel.getPageNum(), jdhStationServiceItemRel.getPageSize());
        IPage<JdhStationServiceItemRelPo> jdhSkuPoIPage = jdhStationServiceItemRelPoMapper.selectPage(param, queryWrapper);
        List<JdhStationServiceItemRel> jdhSkus = ProviderStationPoConverter.INS.poToModel(jdhSkuPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhSkuPoIPage, jdhSkus);
    }

    @Override
    public List<JdhStationServiceItemRel> queryEquipmentIdByStationServiceItem(JdhStationServiceItemRel jdhStationServiceItemRel) {
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        boolean queryOnOffShelf = jdhStationServiceItemRel.getOnOffShelf() != null || !Boolean.TRUE.equals(jdhStationServiceItemRel.getQueryIgnoreOnOffShelf());
        queryWrapper.eq(JdhStationServiceItemRelPo::getStationId, jdhStationServiceItemRel.getStationId())
                .in(JdhStationServiceItemRelPo::getServiceItemId, jdhStationServiceItemRel.getServiceItemIds())
                .isNotNull(JdhStationServiceItemRelPo::getEquipmentBizId)
                .eq(queryOnOffShelf, JdhStationServiceItemRelPo::getOnOffShelf, jdhStationServiceItemRel.getOnOffShelf() != null ? jdhStationServiceItemRel.getOnOffShelf(): YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode());
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 通过设备Id或内容ID查询门店项目关系
     *
     * @param jdhStationServiceItemRel
     */
    @Override
    public List<JdhStationServiceItemRel> queryStationServiceItemByEquipmentIdOrContentId(JdhStationServiceItemRel jdhStationServiceItemRel) {
        LambdaQueryWrapper<JdhStationServiceItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        boolean queryOnOffShelf = jdhStationServiceItemRel.getOnOffShelf() != null || !Boolean.TRUE.equals(jdhStationServiceItemRel.getQueryIgnoreOnOffShelf());
        queryWrapper.eq(jdhStationServiceItemRel.getEquipmentBizId()!=null,JdhStationServiceItemRelPo::getEquipmentBizId, jdhStationServiceItemRel.getEquipmentBizId())
                .eq(jdhStationServiceItemRel.getContentBizId()!=null,JdhStationServiceItemRelPo::getContentBizId, jdhStationServiceItemRel.getContentBizId())
                .eq(queryOnOffShelf, JdhStationServiceItemRelPo::getOnOffShelf, jdhStationServiceItemRel.getOnOffShelf() != null ? jdhStationServiceItemRel.getOnOffShelf(): YnStatusEnum.YES.getCode())
                .eq(JdhStationServiceItemRelPo::getYn, YnStatusEnum.YES.getCode());
        return ProviderStationPoConverter.INS.poToModel(jdhStationServiceItemRelPoMapper.selectList(queryWrapper));
    }
}