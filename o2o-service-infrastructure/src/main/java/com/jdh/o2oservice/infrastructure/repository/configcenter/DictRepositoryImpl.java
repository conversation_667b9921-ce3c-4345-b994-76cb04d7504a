package com.jdh.o2oservice.infrastructure.repository.configcenter;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jd.laf.config.Configuration;
import com.jd.laf.config.Property;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.DictInfo;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 词典管理仓库
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Slf4j
@Component
public class DictRepositoryImpl implements DictRepository {
    
    /**
     * 缓存
     */
    private static final Map<String, List<DictInfo>> CACHE = Maps.newHashMap();
    /**
     * 加载配置信息
     *
     * @param configuration config
     */
    @LafValue(name="dict_config")
    public void loadProperties(Configuration configuration) {
        log.info("DictRepositoryImpl->loadProperties 配置调整");
        Set<String> newPinConfig = new HashSet<>();
        for (Property property : configuration.getProperties()) {
            newPinConfig.add(property.getKey());
            try {
                CACHE.put(property.getKey(), JSON.parseArray(property.getValue().toString(), DictInfo.class));
            }catch (Exception e){
                log.error("DictRepositoryImpl loadProperties error key={}, msg={}",property.getKey(), e.getMessage());
                throw e;
            }
        }

        //删除配置数据
        Set<String> removeKeys = removeConfig(newPinConfig, CACHE.keySet());
        log.info("DictRepositoryImpl loadProperties 移除的配置key:{}",removeKeys);
        if(!removeKeys.isEmpty()){
            for (String removeKey : removeKeys) {
                CACHE.remove(removeKey);
            }
        }
    }
    
    /**
     * 删除配置
     *
     * @param newPinConfig newPinConfig
     * @param oldPinConfig oldPinConfig
     * @return {@link Set}<{@link String}>
     */
    private static Set<String> removeConfig(Set<String> newPinConfig,Set<String> oldPinConfig){
        Set<String> allConfig = new HashSet<>(oldPinConfig);
        allConfig.removeAll(newPinConfig);
        return allConfig;
    }

    /**
     * 获取多个词典数据
     *
     * @param keys keys
     * @return map
     */
    @Override
    public Map<String, List<DictInfo>> queryCommonDictList(Set<String> keys) {
        if (CollUtil.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        Map<String, List<DictInfo>> retMap = new HashMap<>(1);
        for (String key : keys) {
            List<DictInfo> ret = CACHE.get(key);
            // 空置返回, 多词典查询仅返回层级为1的数据
            if (CollUtil.isEmpty(ret)) {
                continue;
            }
            retMap.put(key, ret);
        }
        return retMap;
    }

    /**
     * 获取多个词典数据
     *
     * @param groups groups
     * @return map
     */
    @Override
    public Map<String, List<DictInfo>> queryMultiDictList(Set<String> groups) {
        if (CollUtil.isEmpty(groups)) {
            return Collections.emptyMap();
        }
        Map<String, List<DictInfo>> retMap = new HashMap<>(1);
        for (String key : groups) {
            List<DictInfo> ret = CACHE.get(key);
            // 空置返回, 多词典查询仅返回层级为1的数据
            if (CollUtil.isEmpty(ret)) {
                retMap.put(key, ret);
                continue;
            }
            retMap.put(key, ret.stream().filter(dictInfo -> dictInfo.getLevel() == 1).collect(Collectors.toList()));
        }
        return retMap;
    }
    
    /**
     * 获取多个词典数据
     *
     * @param group group
     * @param parentLevel parentLevel
     * @return map
     */
    @Override
    public List<DictInfo> querySubDictList(String group, String parentValue, Integer parentLevel) {
        List<DictInfo> ret = CACHE.get(group);
        // 空置返回, 多词典查询仅返回层级为1的数据
        if (CollUtil.isEmpty(ret)) {
            return ret;
        }
        if (parentLevel == null) {
            parentLevel = 1;
        }
        int subLevel = parentLevel + 1;
        return ret.stream().filter(dictInfo -> (dictInfo.getLevel() == (subLevel) && StringUtils.isNotBlank(dictInfo.getParentValue()) && dictInfo.getParentValue().equalsIgnoreCase(parentValue))).collect(Collectors.toList());
    }
}
