package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.health.delayqueue.export.param.DelayTaskParam;
import com.jd.health.delayqueue.export.service.DelayApiExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.core.domain.support.basic.enums.DelayTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.XfylDelayServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/11/10 10:37
 * @Doc
 **/
@Service
@Slf4j
public class XfylDelayServiceRpcImpl implements XfylDelayServiceRpc {

    /**
     * delayApiExportService
     */
    @Resource
    DelayApiExportService delayApiExportService;

    /**
     * 线程池
     */
    @Resource
    ExecutorPoolFactory executorPoolFactory;

    /**
     * 异步添加延时消息
     * @param businessKey
     * @param delayTypeEnum
     */
    @Override
    @LogAndAlarm(jKey = "com.jd.health.medical.examination.rpc.impl.XfylDelayServiceRpcImpl.addDelayTask")
    public Boolean addDelayTask(String businessKey, DelayTypeEnum delayTypeEnum) {
        try {
            if(delayTypeEnum.getTimeLater() < 1){
                return Boolean.FALSE;
            }
            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()
                    -> {
                JsfResult<String> result = delayApiExportService.addDelayTask(buildDelayParam(businessKey, delayTypeEnum,delayTypeEnum.getTimeLater()));
                log.info("XfylDelayServiceRpcImpl -> addDelayTask result={}", JsonUtil.toJSONString(result));
                if(!result.isSuccess()){
                    UmpUtil.showWarnMsg(UmpKeyEnum.DELAY_SEND_UMP_KEY);
                }
            });
            log.info("XfylDelayServiceRpcImpl -> addDelayTaskAsyn secondLater end, result={},later={}", JsonUtil.toJSONString(businessKey),delayTypeEnum.getTimeLater());
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    /**
     * 异步添加延时消息
     * @param businessKey
     * @param secondLater
     */
    @Override
    @LogAndAlarm(jKey = "com.jd.health.medical.examination.rpc.impl.XfylDelayServiceRpcImpl.addDelayTaskForSec")
    public Boolean addDelayTaskForSec(String businessKey, DelayTypeEnum delayTypeEnum, Integer secondLater) {
        try {
            if(secondLater < 1){
                return Boolean.FALSE;
            }
            JsfResult<String> result = delayApiExportService.addDelayTask(buildDelayParam(businessKey, delayTypeEnum, secondLater));
            if(!result.isSuccess()){
                throw new BusinessException(SupportErrorCode.DELAY_QUEUE_ADD_ERROR);
            }
            log.info("XfylDelayServiceRpcImpl -> addDelayTaskAsyn secondLater end, result={},later={}", JsonUtil.toJSONString(businessKey),secondLater);
            return Boolean.TRUE;
        } catch (BusinessException e) {
            UmpUtil.showWarnMsg(UmpKeyEnum.DELAY_SEND_UMP_KEY);
            throw e;
        } catch (Exception e) {
            UmpUtil.showWarnMsg(UmpKeyEnum.DELAY_SEND_UMP_KEY);
            throw new BusinessException(SupportErrorCode.DELAY_QUEUE_ADD_ERROR);
        }
    }

    /**
     * 组装延迟消息
     * @param businessKey
     * @param delayTypeEnum
     * @param secondLater
     * @return
     */
    private DelayTaskParam buildDelayParam(String businessKey, DelayTypeEnum delayTypeEnum, Integer secondLater) {
        DelayTaskParam delayTaskParam = new DelayTaskParam();
        delayTaskParam.setSourceType(SOURCE_TYPE);
        delayTaskParam.setBusinessType(delayTypeEnum.getBusinessType());
        delayTaskParam.setSecondLater(secondLater);
        delayTaskParam.setBusinessKey(businessKey);
        log.info("XfylDelayServiceRpcImpl -> buildDelayParam ,param={}", JsonUtil.toJSONString(delayTaskParam));
        return delayTaskParam;
    }

    /**
     * 添加延时任务
     *
     * @param businessKey
     * @param delayTypeEnum
     * @param execTime
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jd.health.medical.examination.rpc.impl.XfylDelayServiceRpcImpl.addDelayTaskForTime")
    public Boolean addDelayTaskForTime(String businessKey, DelayTypeEnum delayTypeEnum, Date execTime) {
        try {
            Integer time = 1000;
            //小于1秒不处理
            if(execTime.getTime() < System.currentTimeMillis()+time){
                return Boolean.FALSE;
            }
            Long secondLater = (execTime.getTime() - System.currentTimeMillis()) / time ;
            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()
                    -> {
                JsfResult<String> result = delayApiExportService.addDelayTask(buildDelayParam(businessKey, delayTypeEnum, secondLater.intValue()));
                if(!result.isSuccess()){
                    UmpUtil.showWarnMsg(UmpKeyEnum.DELAY_SEND_UMP_KEY);
                }
            });
            log.info("XfylDelayServiceRpcImpl -> addDelayTaskAsyn execTime end, result={},later={}", JsonUtil.toJSONString(businessKey),secondLater);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }
    
    /**
     * 添加延时任务
     *
     * @param businessKey
     * @param businessType
     * @param secondLater
     * @return
     */
    @Override
    public Boolean addCommonDelayTask(String businessKey, Integer businessType, Integer secondLater) {
        try {
            if(secondLater < 1){
                return Boolean.FALSE;
            }
            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()
                    -> {
                JsfResult<String> result = delayApiExportService.addDelayTask(buildCommonDelayParam(businessKey, businessType, secondLater));
                if(!result.isSuccess()){
                    UmpUtil.showWarnMsg(UmpKeyEnum.DELAY_SEND_UMP_KEY);
                }
            });
            log.info("XfylDelayServiceRpcImpl -> addDelayTaskAsyn secondLater end, result={},later={}", JsonUtil.toJSONString(businessKey),secondLater);
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }
    
    /**
     * 组装延迟消息
     * @param businessKey
     * @param businessType
     * @param secondLater
     * @return
     */
    private DelayTaskParam buildCommonDelayParam(String businessKey, Integer businessType, Integer secondLater) {
        DelayTaskParam delayTaskParam = new DelayTaskParam();
        delayTaskParam.setSourceType(SOURCE_TYPE);
        delayTaskParam.setBusinessType(businessType);
        delayTaskParam.setSecondLater(secondLater);
        delayTaskParam.setBusinessKey(businessKey);
        log.info("XfylDelayServiceRpcImpl -> buildDelayParam ,param={}", JsonUtil.toJSONString(delayTaskParam));
        return delayTaskParam;
    }
}
