package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfigdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhAreaFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhAreaFeeConfigQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhFeeConfigPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAreaFeeConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAreaFeeConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * JdhAreaFeeConfigRepositoryImpl
 * @author:lwm
 * @createTime: 2025-01-06 16:06
 * @Description:
 */
@Component
@Slf4j
public class JdhAreaFeeConfigRepositoryImpl implements JdhAreaFeeConfigRepository {

    /**
     *
     */
    @Resource
    private JdhAreaFeeConfigPoMapper jdhAreaFeeConfigPoMapper;

    /**
     *
     * @param jdhAreaFeeConfigdentifier
     * @return
     */
    @Override
    public JdhAreaFeeConfig find(JdhAreaFeeConfigdentifier jdhAreaFeeConfigdentifier) {
        LambdaQueryWrapper<JdhAreaFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAreaFeeConfigPo::getAreaFeeConfigId, jdhAreaFeeConfigdentifier.getAreaFeeConfigId())
                .eq(JdhAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhAreaFeeConfigPo::getId);
        JdhAreaFeeConfigPo jdhAreaFeeConfigPoPo = jdhAreaFeeConfigPoMapper.selectOne(queryWrapper);
        if (Objects.nonNull(jdhAreaFeeConfigPoPo)) {
            JdhAreaFeeConfig jdhAreaFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2JdhAreaFeeConfig(jdhAreaFeeConfigPoPo);
            return jdhAreaFeeConfig;
        }
        return null;
    }

    @Override
    public int remove(JdhAreaFeeConfig entity) {
        return 0;
    }

    /**
     *
     * @param jdhAreaFeeConfig
     * @return
     */
    @Override
    public int save(JdhAreaFeeConfig jdhAreaFeeConfig) {
        JdhAreaFeeConfigPo jdhAreaFeeConfigPo = JdhFeeConfigPoConverter.INSTANCE.convertPo2JdhAreaFeeConfigPo(jdhAreaFeeConfig);
        return jdhAreaFeeConfigPoMapper.insert(jdhAreaFeeConfigPo);
    }

    /**
     *
     * @param jdhAreaFeeConfigList
     * @return
     */
    @Override
    public int batchSaveJdhAreaFeeConfig(List<JdhAreaFeeConfig> jdhAreaFeeConfigList) {
        List<JdhAreaFeeConfigPo> jdhAreaFeeConfigPoList = JdhFeeConfigPoConverter.INSTANCE.convertPo2JdhAreaFeeConfigPoList(jdhAreaFeeConfigList);
        return jdhAreaFeeConfigPoMapper.batchSaveJdhAreaFeeConfig(jdhAreaFeeConfigPoList);
    }

    /**
     * @param jdhAreaFeeConfig
     * @return
     */
    @Override
    public int update(JdhAreaFeeConfig jdhAreaFeeConfig) {
        LambdaUpdateWrapper<JdhAreaFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAreaFeeConfigPo::getOnSiteFee, jdhAreaFeeConfig.getOnSiteFee())
                .set(JdhAreaFeeConfigPo::getHolidayFee, jdhAreaFeeConfig.getHolidayFee())
                .set(JdhAreaFeeConfigPo::getImmediatelyFee, jdhAreaFeeConfig.getImmediatelyFee())
                .set(JdhAreaFeeConfigPo::getUpgrageAngelFee, jdhAreaFeeConfig.getUpgrageAngelFee())
                .set(JdhAreaFeeConfigPo::getNightDoorFee, jdhAreaFeeConfig.getNightDoorFee())
                .set(JdhAreaFeeConfigPo::getPeakServiceFee, jdhAreaFeeConfig.getPeakServiceFee())
                .set(JdhAreaFeeConfigPo::getUpgrageSkuList, jdhAreaFeeConfig.getUpgrageSkuList())
                .eq(JdhAreaFeeConfigPo::getAreaFeeConfigId, jdhAreaFeeConfig.getAreaFeeConfigId())
                .eq(JdhAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        return jdhAreaFeeConfigPoMapper.update(null, updateWrapper);
    }
    /**
     *
     * @param jdhAreaFeeConfig
     * @return
     */
    @Override
    public int updateByCode(JdhAreaFeeConfig jdhAreaFeeConfig) {
        LambdaUpdateWrapper<JdhAreaFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAreaFeeConfigPo::getOnSiteFee, jdhAreaFeeConfig.getOnSiteFee())
                .set(JdhAreaFeeConfigPo::getHolidayFee, jdhAreaFeeConfig.getHolidayFee())
                .set(JdhAreaFeeConfigPo::getImmediatelyFee, jdhAreaFeeConfig.getImmediatelyFee())
                .set(JdhAreaFeeConfigPo::getUpgrageAngelFee, jdhAreaFeeConfig.getUpgrageAngelFee())
                .set(JdhAreaFeeConfigPo::getUpgrageSkuList,jdhAreaFeeConfig.getUpgrageSkuList())
                .eq(JdhAreaFeeConfigPo::getChannelId,jdhAreaFeeConfig.getChannelId())
                .eq(JdhAreaFeeConfigPo::getServiceType,jdhAreaFeeConfig.getServiceType())
                .eq(JdhAreaFeeConfigPo::getProvinceCode,jdhAreaFeeConfig.getProvinceCode())
                .eq(StringUtil.isNotBlank(jdhAreaFeeConfig.getCityCode()),JdhAreaFeeConfigPo::getCityCode,jdhAreaFeeConfig.getCityCode())
                .eq(StringUtil.isNotBlank(jdhAreaFeeConfig.getCountyCode()),JdhAreaFeeConfigPo::getCountyCode,jdhAreaFeeConfig.getCountyCode())
                .eq(StringUtil.isNotBlank(jdhAreaFeeConfig.getTownCode()),JdhAreaFeeConfigPo::getTownCode,jdhAreaFeeConfig.getTownCode())
                .eq(JdhAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        return jdhAreaFeeConfigPoMapper.update(null, updateWrapper);
    }

    /**
     * 查询地区费项配置列表
     * @param jdhAreaFeeConfigQuery
     * @return
     */
    @Override
    public List<JdhAreaFeeConfig> queryJdhAreaFeeConfigList(JdhAreaFeeConfigQuery jdhAreaFeeConfigQuery) {
        LambdaQueryWrapper<JdhAreaFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAreaFeeConfigPo::getProvinceCode, jdhAreaFeeConfigQuery.getProvinceCode())
                .eq(StringUtil.isNotBlank(jdhAreaFeeConfigQuery.getChannelId()),JdhAreaFeeConfigPo::getChannelId, jdhAreaFeeConfigQuery.getChannelId())
                .in(CollectionUtils.isNotEmpty(jdhAreaFeeConfigQuery.getChannelIds()),JdhAreaFeeConfigPo::getChannelId, jdhAreaFeeConfigQuery.getChannelIds())
                .eq(Objects.nonNull(jdhAreaFeeConfigQuery.getServiceType()),JdhAreaFeeConfigPo::getServiceType, jdhAreaFeeConfigQuery.getServiceType())
                .eq(StringUtil.isNotBlank(jdhAreaFeeConfigQuery.getCityCode()),JdhAreaFeeConfigPo::getCityCode, jdhAreaFeeConfigQuery.getCityCode())
                .eq(JdhAreaFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhAreaFeeConfigPo::getId);
        List<JdhAreaFeeConfigPo> jdhAreaFeeConfigPoList = jdhAreaFeeConfigPoMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(jdhAreaFeeConfigPoList)) {
            List<JdhAreaFeeConfig> jdhAreaFeeConfigList = JdhFeeConfigPoConverter.INSTANCE.convertPo2JdhAreaFeeConfigList(jdhAreaFeeConfigPoList);
            return jdhAreaFeeConfigList;
        }
        return Collections.emptyList();
    }
}
