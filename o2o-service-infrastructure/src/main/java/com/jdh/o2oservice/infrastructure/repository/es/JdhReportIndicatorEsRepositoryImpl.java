package com.jdh.o2oservice.infrastructure.repository.es;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvEnum;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseFull;
import com.jdh.o2oservice.core.domain.report.ReportErrorCode;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorES;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderFullPageContext;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderIdContext;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderFull;
import com.jdh.o2oservice.core.domain.trade.repository.es.JdhReportIndicatorEsRepository;
import com.jdh.o2oservice.export.report.cmd.MedicalReportIndicatorDeleteFromEsCmd;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.query.MedicalReportIndicatorRateFromEsRequest;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalReportConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.cardinality.Cardinality;
import org.elasticsearch.search.aggregations.metrics.cardinality.CardinalityAggregationBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 18:13
 * @Desc :
 */
@Slf4j
@Repository
public class JdhReportIndicatorEsRepositoryImpl implements JdhReportIndicatorEsRepository {

    /**
     * defaultIndexName
     */
    private final static String defaultIndexName = "medical_report_indicator_index";

    /**
     * indexName
     */
    private static String indexName = "medical_report_indicator_index";
    /**
     * typeName
     */
    private final static String typeName = "doc";

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        if(!EnvEnum.PRODUCTION.getCode().equals(value)){
            indexName = defaultIndexName + "_" +value;
        } else {
            indexName = defaultIndexName;
        }
    }

    @Resource
    private EsClientFactoryHealthcare esClientFactoryHealthcare;

    /**
     * 保存
     *
     * @param medicalReportIndicatorES@return
     */
    @Override
    public Boolean save(MedicalReportIndicatorES medicalReportIndicatorES) {
        try {
            esClientFactoryHealthcare.updateOrInsertDocument(indexName, typeName, String.valueOf(medicalReportIndicatorES.getId()), medicalReportIndicatorES);
            return true;
        }catch (Exception e){
            log.error("JdhReportIndicatorEsRepositoryImpl.save error",e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 保存
     *
     * @param medicalReportIndicatorES es
     * @return
     */
    @Override
    public Boolean delete(MedicalReportIndicatorDeleteFromEsCmd medicalReportIndicatorES) {
        try {
            esClientFactoryHealthcare.deleteDocument(indexName, typeName, String.valueOf(medicalReportIndicatorES.getId()));
            return true;
        }catch (Exception e){
            log.error("JdhReportIndicatorEsRepositoryImpl.save error",e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public IndicatorResultAndAggDTO queryAggregation(MedicalReportIndicatorRateFromEsRequest medicalReportIndicatorRateFromEsRequest) {

        if (medicalReportIndicatorRateFromEsRequest.getCheckStartTime() == null || medicalReportIndicatorRateFromEsRequest.getCheckEndTime() == null) {
            Date nowDate = new Date();
            medicalReportIndicatorRateFromEsRequest.setCheckStartTime(TimeUtils.getStartTime(TimeUtils.addDays(nowDate, -2)));
            medicalReportIndicatorRateFromEsRequest.setCheckEndTime(nowDate);
        }
        if (TimeUtils.addDays(medicalReportIndicatorRateFromEsRequest.getCheckStartTime(), 365).before(medicalReportIndicatorRateFromEsRequest.getCheckEndTime())) {
            throw new BusinessException(ReportErrorCode.LIMIT_QUERY_TIME_RANGE);
        }

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(medicalReportIndicatorRateFromEsRequest.getStationId())) {
            queryBuilder.must(QueryBuilders.termQuery("stationId", medicalReportIndicatorRateFromEsRequest.getStationId()));
        }
        if (StringUtils.isNotBlank(medicalReportIndicatorRateFromEsRequest.getReportId())) {
            queryBuilder.must(QueryBuilders.termQuery("reportId", medicalReportIndicatorRateFromEsRequest.getReportId()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getServiceItemId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("serviceItemId", String.valueOf(medicalReportIndicatorRateFromEsRequest.getServiceItemId())));
        }
        if (medicalReportIndicatorRateFromEsRequest.getMedicalPromiseId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("medicalPromiseId", medicalReportIndicatorRateFromEsRequest.getMedicalPromiseId()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getPatientProvinceCode() != null) {
            queryBuilder.must(QueryBuilders.termQuery("patientProvinceCode", medicalReportIndicatorRateFromEsRequest.getPatientProvinceCode()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getPatientCityCode() != null) {
            queryBuilder.must(QueryBuilders.termQuery("patientCityCode", medicalReportIndicatorRateFromEsRequest.getPatientCityCode()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getPatientCountyCode() != null) {
            queryBuilder.must(QueryBuilders.termQuery("patientCountyCode", medicalReportIndicatorRateFromEsRequest.getPatientCountyCode()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getPatientTownCode() != null) {
            queryBuilder.must(QueryBuilders.termQuery("patientTownCode", medicalReportIndicatorRateFromEsRequest.getPatientTownCode()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getPatientGender() != null) {
            queryBuilder.must(QueryBuilders.termQuery("patientGender", medicalReportIndicatorRateFromEsRequest.getPatientGender()));
        }
        if (medicalReportIndicatorRateFromEsRequest.getPatientBirthdayStartTime() != null || medicalReportIndicatorRateFromEsRequest.getPatientBirthdayEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("patientBirthday");
            if (medicalReportIndicatorRateFromEsRequest.getPatientBirthdayStartTime() != null) {
                builder.gte(medicalReportIndicatorRateFromEsRequest.getPatientBirthdayStartTime().getTime());
            }
            if (medicalReportIndicatorRateFromEsRequest.getPatientBirthdayEndTime() != null) {
                builder.lte(medicalReportIndicatorRateFromEsRequest.getPatientBirthdayEndTime().getTime());
            }
            queryBuilder.filter(builder);
        }
        RangeQueryBuilder builder = QueryBuilders.rangeQuery("checkTime");
        builder.gte(medicalReportIndicatorRateFromEsRequest.getCheckStartTime().getTime());
        builder.lte(medicalReportIndicatorRateFromEsRequest.getCheckEndTime().getTime());
        queryBuilder.filter(builder);

        int from = (medicalReportIndicatorRateFromEsRequest.getPageNum() - 1) * medicalReportIndicatorRateFromEsRequest.getPageSize();
        //执行查询条件
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setSearchType(SearchType.QUERY_THEN_FETCH).setQuery(queryBuilder).setFrom(from).setSize(medicalReportIndicatorRateFromEsRequest.getPageSize())
                .setTimeout(new TimeValue(EsClientFactoryHealthcare.TIMEOUT_MILLIS, TimeUnit.MILLISECONDS));
        requestBuilder.addSort("createTime", SortOrder.DESC);
        if (Boolean.TRUE.equals(medicalReportIndicatorRateFromEsRequest.getAggregationIndicatorName())) {
            // 聚合指标
            TermsAggregationBuilder abnormalMarkTypeAgg = AggregationBuilders.terms("abnormalMarkTypeAgg").field("abnormalMarkType").size(Integer.MAX_VALUE);
            TermsAggregationBuilder termsAgg = AggregationBuilders.terms("indicatorNameAgg").field("indicatorName").subAggregation(abnormalMarkTypeAgg).size(Integer.MAX_VALUE);
            requestBuilder.addAggregation(termsAgg);

            // 聚合查询检测单数量
            CardinalityAggregationBuilder medicalCountAgg = AggregationBuilders.cardinality("medicalCount").field("medicalPromiseId");
            requestBuilder.addAggregation(medicalCountAgg);

            // 聚合设备指标
            TermsAggregationBuilder equipmentAbnormalMarkTypeAgg = AggregationBuilders.terms("abnormalMarkTypeAgg").field("abnormalMarkType").size(Integer.MAX_VALUE);
            TermsAggregationBuilder equipmentNameAgg = AggregationBuilders.terms("indicatorNameAgg").field("indicatorName").subAggregation(equipmentAbnormalMarkTypeAgg).size(Integer.MAX_VALUE);
            TermsAggregationBuilder equipmentAgg = AggregationBuilders.terms("equipmentSNAgg").field("equipmentSN").subAggregation(equipmentNameAgg).size(Integer.MAX_VALUE);
            requestBuilder.addAggregation(equipmentAgg);
        }
        log.info("JdhReportIndicatorEsRepositoryImpl.queryPage --> indexName={} requestBuilder={}", indexName, requestBuilder.toString());
        SearchResponse response = requestBuilder.execute().actionGet(EsClientFactoryHealthcare.TIMEOUT_MILLIS);
        IndicatorResultAndAggDTO integerResultAndAggDTO = new IndicatorResultAndAggDTO();
        integerResultAndAggDTO.setMedicalCount(0L);
        if (response.getAggregations() != null) {
            log.info("JdhReportIndicatorEsRepositoryImpl.getAggregations={}", response.getAggregations().getAsMap().toString());
            // 获取指标统计数据
            List<IndicatorAggDTO> resultList = new ArrayList<>();
            Terms indicatorNameAgg = response.getAggregations().get("indicatorNameAgg");
            if (indicatorNameAgg != null) {
                for (Terms.Bucket indicatorBucket : indicatorNameAgg.getBuckets()) {
                    IndicatorAggDTO indicatorAggVO = new IndicatorAggDTO();
                    indicatorAggVO.setIndicatorName(indicatorBucket.getKeyAsString());

                    // 解析 abnormalMarkTypeAgg 子聚合
                    Terms abnormalMarkTypeAgg = indicatorBucket.getAggregations().get("abnormalMarkTypeAgg");
                    List<AbnormalMarkTypeAggDTO> abnormalList = new ArrayList<>();
                    for (Terms.Bucket abnormalBucket : abnormalMarkTypeAgg.getBuckets()) {
                        AbnormalMarkTypeAggDTO abnormalVO = new AbnormalMarkTypeAggDTO();
                        abnormalVO.setAbnormalMarkType(abnormalBucket.getKeyAsString());
                        abnormalVO.setCount(abnormalBucket.getDocCount());
                        abnormalList.add(abnormalVO);
                    }
                    indicatorAggVO.setAbnormalMarkTypeList(abnormalList);
                    resultList.add(indicatorAggVO);
                }
                integerResultAndAggDTO.setIndicatorAggDTO(resultList);
            }
            // 获取检测单数量统计
            Cardinality cardinality = response.getAggregations().get("medicalCount");
            if (cardinality != null) {
                long distinctCount = cardinality.getValue();
                integerResultAndAggDTO.setMedicalCount(distinctCount);
            }
            // 获取设备指标统计数据
            List<EquipmentIndicatorResultAndAggDTO> equipmentIndicatorResultAndAggDTOList = new ArrayList<>();
            Terms equipmentNameAgg = response.getAggregations().get("equipmentSNAgg");
            if (equipmentNameAgg != null) {
                for (Terms.Bucket equipmentBucket : equipmentNameAgg.getBuckets()) {
                    String equipmentSn = equipmentBucket.getKeyAsString();
                    // 解析 指标名称 子聚合
                    Terms equipmentIndicatorNameAgg = equipmentBucket.getAggregations().get("indicatorNameAgg");
                    if (equipmentIndicatorNameAgg != null) {
                        for (Terms.Bucket indicatorBucket : equipmentIndicatorNameAgg.getBuckets()) {
                            String indicatorName = indicatorBucket.getKeyAsString();
                            // 解析 abnormalMarkTypeAgg 子聚合
                            Terms abnormalMarkTypeAgg = indicatorBucket.getAggregations().get("abnormalMarkTypeAgg");
                            List<AbnormalMarkTypeAggDTO> abnormalList = new ArrayList<>();
                            for (Terms.Bucket abnormalBucket : abnormalMarkTypeAgg.getBuckets()) {
                                String abnormalMarkType = abnormalBucket.getKeyAsString();
                                Long count = abnormalBucket.getDocCount();
                                EquipmentIndicatorResultAndAggDTO resultAndAggDTO = new EquipmentIndicatorResultAndAggDTO();
                                resultAndAggDTO.setEquipmentSn(equipmentSn);
                                resultAndAggDTO.setIndicatorName(indicatorName);
                                resultAndAggDTO.setAbnormalMarkType(abnormalMarkType);
                                resultAndAggDTO.setResultCount(count);
                                equipmentIndicatorResultAndAggDTOList.add(resultAndAggDTO);
                            }
                        }
                    }
                }
                integerResultAndAggDTO.setEquipmentIndicatorAggDTO(equipmentIndicatorResultAndAggDTOList);
            }
        }
        if (response.getHits() != null) {
            List<MedicalReportIndicatorResultDTO> resList = Lists.newArrayList();
            for (SearchHit hit : response.getHits()) {
                String json = hit.getSourceAsString();
                MedicalReportIndicatorES res = JSON.parseObject(json, MedicalReportIndicatorES.class);
                resList.add(MedicalReportConverter.INS.convertResultIndicatorDTO(res));
            }
            PageDto<MedicalReportIndicatorResultDTO> resultPage = new PageDto<>();
            int pageSize = medicalReportIndicatorRateFromEsRequest.getPageSize();
            resultPage.setPageNum(medicalReportIndicatorRateFromEsRequest.getPageNum());
            resultPage.setPageSize(pageSize);
            long totalPage = response.getHits().totalHits % pageSize == 0 ? response.getHits().totalHits / pageSize : response.getHits().totalHits / pageSize + 1;
            resultPage.setTotalPage(totalPage);
            resultPage.setList(resList);
            integerResultAndAggDTO.setIndicatorResultPage(resultPage);
        }
        integerResultAndAggDTO.setCheckStartTime(medicalReportIndicatorRateFromEsRequest.getCheckStartTime() != null ? TimeUtils.dateTimeToStr(medicalReportIndicatorRateFromEsRequest.getCheckStartTime()) : null);
        integerResultAndAggDTO.setCheckEndTime(medicalReportIndicatorRateFromEsRequest.getCheckEndTime() != null ? TimeUtils.dateTimeToStr(medicalReportIndicatorRateFromEsRequest.getCheckEndTime()) : null);
        return integerResultAndAggDTO;
    }
}
