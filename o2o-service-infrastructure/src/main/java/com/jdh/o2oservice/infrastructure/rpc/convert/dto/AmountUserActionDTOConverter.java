package com.jdh.o2oservice.infrastructure.rpc.convert.dto;

import com.jd.trade2.base.export.relation.Relation;
import com.jd.trade2.core.domain.amount.export.dto.model.AmountCollectionDTO;
import com.jd.trade2.core.domain.coupon.export.dto.model.CouponCollectionDTO;
import com.jd.trade2.core.domain.freight.export.dto.model.FreightCollectionDTO;
import com.jd.trade2.core.domain.presale.export.dto.AbstractPresaleItemDTO;
import com.jd.trade2.core.domain.presale.export.dto.PresaleCollectionDTO;
import com.jd.trade2.core.domain.price.export.dto.model.PriceCollectionDTO;
import com.jd.trade2.core.domain.promotion.export.dto.PromotionCollectionDTO;
import com.jd.trade2.core.domain.promotion.export.dto.part.collection.GiftCashPartDTO;
import com.jd.trade2.core.domain.redpacket.export.dto.model.RedPacketCollectionDTO;
import com.jd.trade2.vertical.tool.ParseTool;
import com.jdh.o2oservice.base.enums.IdentityUserActionEnum;
import com.jdh.o2oservice.core.domain.trade.vo.AmountInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderTradeValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderUserActionValueObject;
import com.jdh.o2oservice.infrastructure.rpc.convert.TradeParamConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * AmountUserActionDTOConverter 用户行为-金额
 *
 * <AUTHOR>
 * @version 2024/3/4 10:39
 **/
@Slf4j
@Component
public class AmountUserActionDTOConverter extends AbstractUserActionDTOConverter<OrderTradeValueObject> {

    @Resource
    private TradeParamConverter tradeParamConverter;

    @Override
    public boolean identityContainsUserAction(List<String> identityUserActionList) {
        return identityUserActionList.contains(IdentityUserActionEnum.AMOUNT_USER_ACTION.getUserActionType());
    }

    @Override
    public void handlerIdentityTradeOrder(ParseTool parseTool, OrderTradeValueObject orderTradeResult, Relation relation, String identity) {
        // 金额
        AmountInfoValueObject amountInfo = buildAmountInfo(parseTool);
        if (amountInfo == null) {
            return;
        }
        orderTradeResult.setAmountInfo(amountInfo);

    }

    @Override
    public void handlerIdentityUserAction(ParseTool parseTool, OrderUserActionValueObject orderUserActionResult, Relation relation, String identity) {
        // 金额
        AmountInfoValueObject amountInfo = buildAmountInfo(parseTool);
        if (amountInfo == null) {
            return;
        }
        orderUserActionResult.setAmountInfo(amountInfo);
    }

    /**
     * 处理金额域出参
     *
     * @param parseTool parseTool
     * @return AmountInfo
     */
    public AmountInfoValueObject buildAmountInfo(ParseTool parseTool) {
        AmountCollectionDTO amountCollectionDTO = parseTool.getCollectionDTO(AmountCollectionDTO.class);
        PresaleCollectionDTO presaleCollectionDTO = parseTool.getCollectionDTO(PresaleCollectionDTO.class);
        PromotionCollectionDTO promotionCollectionDTO = parseTool.getCollectionDTO(PromotionCollectionDTO.class);
        PriceCollectionDTO priceCollectionDTO = parseTool.getCollectionDTO(PriceCollectionDTO.class);
        FreightCollectionDTO freightCollectionDTO = parseTool.getCollectionDTO(FreightCollectionDTO.class);
        CouponCollectionDTO couponCollectionDTO = parseTool.getCollectionDTO(CouponCollectionDTO.class);
        RedPacketCollectionDTO redPacketCollectionDTO = parseTool.getCollectionDTO(RedPacketCollectionDTO.class);

        if (amountCollectionDTO == null) {
            return null;
        }
        AmountInfoValueObject amountInfo = tradeParamConverter.convertAmountInfo(amountCollectionDTO);

        if (presaleCollectionDTO != null) {
            List<AbstractPresaleItemDTO> childList = presaleCollectionDTO.getChildList();
            if (CollectionUtils.isNotEmpty(childList)) {
                AbstractPresaleItemDTO abstractPresaleItemDTO = childList.get(0);
                BigDecimal factEarnest = abstractPresaleItemDTO.getFactEarnest();
                if (factEarnest.compareTo(BigDecimal.ZERO) > 0 && abstractPresaleItemDTO.getPresaleType() == 2) {
                    amountInfo.setFactOrderAmount(factEarnest);
                }
            }
        }

        if (priceCollectionDTO == null) {
            return amountInfo;
        }
        if (promotionCollectionDTO == null) {
            amountInfo.setGoodsAmount(priceCollectionDTO.getTotalPrice());
            return amountInfo;
        }
        amountInfo.setTotalReprice(promotionCollectionDTO.getTotalReprice());
        amountInfo.setGoodsAmount(priceCollectionDTO.getTotalPrice());
        amountInfo.setTotalFreight(freightCollectionDTO.getTotalFreight());
        BigDecimal totalDiscount = promotionCollectionDTO.getTotalDiscount();
        if(Objects.nonNull(totalDiscount) && Objects.nonNull(promotionCollectionDTO.getTotalParallelDiscount())){
            amountInfo.setTotalParallelDiscount(promotionCollectionDTO.getTotalParallelDiscount());
            totalDiscount = totalDiscount.add(promotionCollectionDTO.getTotalParallelDiscount());
        }
        // 店铺新人礼金
        List<GiftCashPartDTO> giftCashPartDTOList = promotionCollectionDTO.getGiftCashPartDTOList();
        if (CollectionUtils.isNotEmpty(giftCashPartDTOList)){
            GiftCashPartDTO giftCashPartDTO = giftCashPartDTOList.get(0);
            if ("1012".equals(giftCashPartDTO.getGiftCashTag()) && Objects.nonNull(giftCashPartDTO.getTotalGiftCashDiscount())){
                amountInfo.setGiftCashDiscountAmount(giftCashPartDTO.getTotalGiftCashDiscount());
                totalDiscount = totalDiscount.add(giftCashPartDTO.getTotalGiftCashDiscount());
            }
        }
        amountInfo.setPromotionDiscountAmount(totalDiscount);
        Map<String, String> discountInfoMap = promotionCollectionDTO.getDiscountInfoMap();
        if (MapUtils.isNotEmpty(discountInfoMap)) {
            String totalOfficialDiscount = discountInfoMap.get("totalOfficialDiscount");
            if (StringUtils.isNotEmpty(totalOfficialDiscount)) {
                amountInfo.setTotalOfficialDiscount(new BigDecimal(totalOfficialDiscount));
            }
            String overlaySumReward = discountInfoMap.get("overlaySumReward");
            if (StringUtils.isNotEmpty(overlaySumReward)) {
                amountInfo.setOverlaySumReward(new BigDecimal(overlaySumReward));
            }
        }
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (couponCollectionDTO != null) {
            amountInfo.setCouponDiscountAmount(couponCollectionDTO.getCouponTotalDiscount());
            discountAmount = discountAmount.add(couponCollectionDTO.getCouponTotalDiscount());
        }
        if (redPacketCollectionDTO != null && redPacketCollectionDTO.isSelected()) {
            discountAmount = discountAmount.add(redPacketCollectionDTO.getTotalAvailableBalance());
        }
        discountAmount = discountAmount.add(totalDiscount);
        amountInfo.setDiscountAmount(discountAmount);
        return amountInfo;
    }
}
