package com.jdh.o2oservice.infrastructure.rpc.convert.dto;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.trade2.base.export.relation.Relation;
import com.jd.trade2.core.description.trade.promotion.TradePromotionDomainDesc;
import com.jd.trade2.core.domain.amount.export.dto.model.AbstractAmountItemDTO;
import com.jd.trade2.core.domain.amount.export.dto.model.AmountCollectionDTO;
import com.jd.trade2.core.domain.prescription.export.dto.AbstractPrescriptionItemDTO;
import com.jd.trade2.core.domain.prescription.export.dto.PrescriptionCollectionDTO;
import com.jd.trade2.core.domain.price.export.dto.model.AbstractPriceItemDTO;
import com.jd.trade2.core.domain.price.export.dto.model.PriceCollectionDTO;
import com.jd.trade2.core.domain.product.export.dto.ProductCollectionDTO;
import com.jd.trade2.core.domain.product.export.dto.RetailProductItemDTO;
import com.jd.trade2.core.domain.promotion.export.dict.PromotionTypeDict;
import com.jd.trade2.core.domain.promotion.export.dto.AbstractPromotionItemDTO;
import com.jd.trade2.core.domain.promotion.export.dto.PromotionCollectionDTO;
import com.jd.trade2.core.domain.promotion.export.dto.model.GiftPromotionItemDTO;
import com.jd.trade2.core.domain.promotion.export.dto.part.GiftPartDTO;
import com.jd.trade2.core.domain.promotion.export.dto.part.PromotionShoppingItemPartDTO;
import com.jd.trade2.core.domain.quantity.export.dto.model.AbstractQuantityItemDTO;
import com.jd.trade2.core.domain.quantity.export.dto.model.QuantityCollectionDTO;
import com.jd.trade2.core.domain.shoppinglist.export.dto.model.AbstractShoppingItemDTO;
import com.jd.trade2.core.domain.shoppinglist.export.dto.model.ShoppingListCollectionDTO;
import com.jd.trade2.core.domain.stock.export.dto.AbstractStockItemDTO;
import com.jd.trade2.core.domain.stock.export.dto.StockCollectionDTO;
import com.jd.trade2.core.domain.store.export.dto.model.AbstractStoreItemDTO;
import com.jd.trade2.core.domain.store.export.dto.model.StoreCollectionDTO;
import com.jd.trade2.core.domain.vender.export.dto.model.AbstractVenderItemDTO;
import com.jd.trade2.core.domain.vender.export.dto.model.VenderCollectionDTO;
import com.jd.trade2.vertical.tool.ParseTool;
import com.jdh.o2oservice.base.enums.IdentityUserActionEnum;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.OptionalDouble;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ProductUserActionDTOConverter 用户行为-商品
 *
 * <AUTHOR>
 * @version 2024/3/4 14:01
 **/
@Slf4j
@Component
public class ProductUserActionDTOConverter extends AbstractUserActionDTOConverter<OrderTradeValueObject> {

    @Override
    public boolean identityContainsUserAction(List<String> identityUserActionList) {
        return identityUserActionList.contains(IdentityUserActionEnum.PRODUCT_USER_ACTION.getUserActionType());
    }

    @Override
    public void handlerIdentityTradeOrder(ParseTool parseTool, OrderTradeValueObject orderTradeResult, Relation relation, String identity) {
        // 组装sku信息
        List<SkuItemValueObject> skuItemVoList = getSkuItemVoList(relation, parseTool);
        if (CollectionUtils.isEmpty(skuItemVoList)) {
            return;
        }
        orderTradeResult.setSkuItemVoList(skuItemVoList);
    }

    @Override
    public void handlerIdentityUserAction(ParseTool parseTool, OrderUserActionValueObject orderUserActionResult, Relation relation, String identity) {
        // 组装sku信息
        List<SkuItemValueObject> skuItemVoList = getSkuItemVoList(relation, parseTool);
        if (CollectionUtils.isEmpty(skuItemVoList)) {
            return;
        }
    }

    /**
     * 合并商品清单中相同的SKU为一行，并取价格均值；规避拆行引起不能修改数量
     *
     * @param skuItemVoList
     */
    private List<SkuItemValueObject> mergeProducts(List<SkuItemValueObject> skuItemVoList) {
        List<SkuItemValueObject> result = new ArrayList<>();
        DecimalFormat decimalFormat = new DecimalFormat("0.##");
        skuItemVoList.stream().collect(Collectors.groupingBy(SkuItemValueObject::getSkuId)).forEach((k, v) -> {
            if (v.size() > 0) {
                // 总数量
                int totalBuyNum = v.stream().mapToInt(SkuItemValueObject::getBuyNum).sum();
                // 平均价格
                double avgPrice = v.stream().mapToDouble(map -> Double.parseDouble(map.getPrice())*map.getBuyNum()).sum()/totalBuyNum;
                // 平均优惠
                double avgDiscount =v.stream().mapToDouble(map -> Double.parseDouble(map.getDiscount())*map.getBuyNum()).sum()/totalBuyNum;
                // 平均返现
                double avgReprice = v.stream().mapToDouble(map -> Double.parseDouble(map.getReprice())*map.getBuyNum()).sum()/totalBuyNum;
                SkuItemValueObject skuItemValueObject = v.get(0);
                skuItemValueObject.setBuyNum(totalBuyNum);
                skuItemValueObject.setPrice(decimalFormat.format(avgPrice).toString());
                skuItemValueObject.setReprice(decimalFormat.format(avgReprice).toString());
                skuItemValueObject.setDiscount(decimalFormat.format(avgDiscount).toString());
                result.add(skuItemValueObject);
            }
        });
        return result;
    }

    /**
     * 处理商品域出参
     *
     * @param relation  relation
     * @param parseTool parseTool
     * @return SkuItemVo
     */
    public List<SkuItemValueObject> getSkuItemVoList(Relation relation, ParseTool parseTool) {
        List<SkuItemValueObject> skuItemVoList = new ArrayList<>();
        ShoppingListCollectionDTO shoppingListCollectionDTO = parseTool.getCollectionDTO(ShoppingListCollectionDTO.class);
        if (shoppingListCollectionDTO == null) {
            return null;
        }
        List<AbstractShoppingItemDTO> childList = shoppingListCollectionDTO.getChildList();
        if (CollectionUtils.isEmpty(childList)) {
            return null;
        }
        // 解析跟某个清单项相关的商品信息
        for (AbstractShoppingItemDTO shoppingItemDTO : childList) {
            if (shoppingItemDTO.isGift()) {
                continue;
            }
            if (shoppingItemDTO.isComplexItem()) {
                SkuItemValueObject skuItemVo = buildSkuItemVo(shoppingItemDTO, relation, parseTool);
                if (skuItemVo == null) {
                    continue;
                }
                List<SkuItemValueObject> realSkuItemVoList = new ArrayList<>();
                Set<String> childShoppingItemSet = shoppingItemDTO.getComplexChildItemUUIDSet();
                for (String uuid : childShoppingItemSet) {
                    AbstractShoppingItemDTO realShoppingItemDTO = shoppingListCollectionDTO.getShoppingItemByShoppingItemUUID(uuid);
                    SkuItemValueObject realSkuItemVo = buildSkuItemVo(realShoppingItemDTO, relation, parseTool);
                    if (realSkuItemVo == null) {
                        continue;
                    }
                    realSkuItemVoList.add(realSkuItemVo);
                    skuItemVo.setBundleId(realSkuItemVo.getBundleId());
                }
                skuItemVo.setRelationSkuItemList(realSkuItemVoList);
                skuItemVoList.add(skuItemVo);
            } else if (StringUtils.isEmpty(shoppingItemDTO.getComplexMainItemUUID())) {
                // 普通商品
                SkuItemValueObject skuItemVo = buildSkuItemVo(shoppingItemDTO, relation, parseTool);
                if (skuItemVo == null) {
                    continue;
                }
                List<SkuGiftItemValueObject> skuGiftItemVos = buildGift(relation, shoppingItemDTO, parseTool);
                if (CollectionUtils.isNotEmpty(skuGiftItemVos)) {
                    skuItemVo.setSkuGiftItemVoList(skuGiftItemVos);
                }
                skuItemVoList.add(skuItemVo);
            }
        }
        skuItemVoList = mergeProducts(skuItemVoList);
        return skuItemVoList;
    }

    /**
     * 处理商品域出参
     *
     * @param shoppingItemDTO shoppingItemDTO
     * @param relation        relation
     * @param parseTool       parseTool
     * @return SkuItemVo
     */
    public SkuItemValueObject buildSkuItemVo(AbstractShoppingItemDTO shoppingItemDTO, Relation relation, ParseTool parseTool) {
        SkuItemValueObject skuItemVo = new SkuItemValueObject();
        // 商品
        ProductCollectionDTO productCollectionDTO = parseTool.getCollectionDTO(ProductCollectionDTO.class);
        if (productCollectionDTO == null) {
            log.error("ProductUserActionDTOAssembler productCollectionDTO is null parseTool:{}", JSONObject.toJSONString(parseTool));
            return null;
        }

        // 数量
        QuantityCollectionDTO quantityCollectionDTO = parseTool.getCollectionDTO(QuantityCollectionDTO.class);
        if (quantityCollectionDTO == null) {
            log.error("ProductUserActionDTOAssembler quantityCollectionDTO is null parseTool:{}", JSONObject.toJSONString(parseTool));
            return null;
        }
        // 价格
        PriceCollectionDTO priceCollectionDTO = parseTool.getCollectionDTO(PriceCollectionDTO.class);
        if (priceCollectionDTO == null) {
            log.error("ProductUserActionDTOAssembler quantityCollectionDTO is null parseTool:{}", JSONObject.toJSONString(parseTool));
            return null;
        }

        // 库存
        StockCollectionDTO stockCollectionDTO = parseTool.getCollectionDTO(StockCollectionDTO.class);
        if (stockCollectionDTO == null) {
            log.error("ProductUserActionDTOAssembler stockCollectionDTO is null parseTool:{}", JSONObject.toJSONString(parseTool));
            return null;
        }

        // 促销
        PromotionCollectionDTO promotionCollectionDTO = parseTool.getCollectionDTO(PromotionCollectionDTO.class);
        // 商家
        VenderCollectionDTO venderCollectionDTO = parseTool.getCollectionDTO(VenderCollectionDTO.class);
        // 处方
        PrescriptionCollectionDTO prescriptionCollectionDTO = parseTool.getCollectionDTO(PrescriptionCollectionDTO.class);
        /// 总金额
        AmountCollectionDTO amountCollectionDTO = parseTool.getCollectionDTO(AmountCollectionDTO.class);
        // 门店
        StoreCollectionDTO storeCollectionDTO = parseTool.getCollectionDTO(StoreCollectionDTO.class);

        RetailProductItemDTO productItemDTO = (RetailProductItemDTO) productCollectionDTO.getProductItemByShoppingItemV2(shoppingItemDTO.obtainUUID(), relation);
        AbstractQuantityItemDTO quantityItemDTO = quantityCollectionDTO.getQuantityItemByShoppingItem(shoppingItemDTO.obtainUUID(), relation);
        AbstractPriceItemDTO priceItemDTO = priceCollectionDTO.getPriceItemByShoppingItem(shoppingItemDTO.obtainUUID(), relation);
        AbstractStockItemDTO stockItemDTO = stockCollectionDTO.getStockByShoppingItem(shoppingItemDTO.obtainUUID(), relation);
        if (venderCollectionDTO != null) {
            AbstractVenderItemDTO venderItemDTO = venderCollectionDTO.getVenderItemByShoppingItem(shoppingItemDTO.obtainUUID(), relation);
            if (venderItemDTO != null) {
                skuItemVo.setVenderId(StringUtils.isNotEmpty(venderItemDTO.getVenderId()) ? Long.valueOf(venderItemDTO.getVenderId()) : null);
            }
        }
        if (promotionCollectionDTO != null) {
            PromotionShoppingItemPartDTO abstractPromotionItemDTO = promotionCollectionDTO.getPromotionShoppingItemPartDTO(shoppingItemDTO.obtainUUID());
            if (abstractPromotionItemDTO != null) {
                skuItemVo.setDiscount(abstractPromotionItemDTO.getDiscount().toString());
                skuItemVo.setReprice(abstractPromotionItemDTO.getReprice().toString());
            }
        }

        if (prescriptionCollectionDTO != null) {
            AbstractPrescriptionItemDTO abstractPrescriptionItemDTO = prescriptionCollectionDTO.getPrescriptionItemByShoppingItem(shoppingItemDTO.obtainUUID(), relation);
//			if (abstractPrescriptionItemDTO != null && StringUtils.isNotEmpty(abstractPrescriptionItemDTO.getPrescriptionId())) {
//				skuItemVo.setPrescriptionId(abstractPrescriptionItemDTO.getPrescriptionId());
//			}
        }

        if (storeCollectionDTO != null) {
            AbstractStoreItemDTO abstractStoreItemDTO = storeCollectionDTO.getStoreItemByShoppingItem(shoppingItemDTO.obtainUUID(), relation);
            if (abstractStoreItemDTO != null) {
                if (NumberUtils.isDigits(abstractStoreItemDTO.getStoreId())) {
                    skuItemVo.setStoreId(Long.valueOf(abstractStoreItemDTO.getStoreId()));
                }
            }
        }

        List<String> basketIds = (List<String>) relation.parse(shoppingItemDTO.obtainUUID(), relation.obtainAllBasket());
        List<String> bundleIds = (List<String>) relation.parse(shoppingItemDTO.obtainUUID(), relation.obtainAllBundle());
        String orderObtainUUID = relation.obtainOrder();
        skuItemVo.setOrderObtainUUID(orderObtainUUID);
        skuItemVo.setBundleId(bundleIds.get(0));
        skuItemVo.setBasketId(basketIds.get(0));
        skuItemVo.setSkuObtainUUID(shoppingItemDTO.obtainUUID());
        if (stockItemDTO != null) {
            skuItemVo.setStockState(stockItemDTO.getStockState());
        }
        skuItemVo.setSkuId(productItemDTO.getSkuId());
        skuItemVo.setId(productItemDTO.getSkuId());
        skuItemVo.setSkuName(productItemDTO.getName());
        skuItemVo.setName(productItemDTO.getName());
        if (StringUtils.isNotEmpty(productItemDTO.getTopCategoryId())) {
            skuItemVo.setFirstCid(Integer.valueOf(productItemDTO.getTopCategoryId()));
        }
        if (StringUtils.isNotEmpty(productItemDTO.getSecondCategoryId())) {
            skuItemVo.setSecondCid(Integer.valueOf(productItemDTO.getSecondCategoryId()));
        }
        if (StringUtils.isNotEmpty(productItemDTO.getThirdCategoryId())) {
            skuItemVo.setThirdCid(Integer.valueOf(productItemDTO.getThirdCategoryId()));
        }
        skuItemVo.setSkuPic(productItemDTO.getImagePath());
        skuItemVo.setPrice(priceItemDTO.getPrice().toString());
        skuItemVo.setItemType(Integer.valueOf(shoppingItemDTO.getItemType()));
        skuItemVo.setBuyNum(quantityItemDTO.getBuyNum().intValue());
        skuItemVo.setWeight(productItemDTO.getWeight());

        SkuItemFieldsValueObject skuItemFieldsVo = new SkuItemFieldsValueObject();

        skuItemVo.setSkuItemFieldsVo(skuItemFieldsVo);

        AbstractAmountItemDTO discountDto = amountCollectionDTO.getAmountSpecificItemByShoppingItem(
                shoppingItemDTO.obtainUUID(), TradePromotionDomainDesc.DOMAIN.getNamespace(), "discount", relation);
        BigDecimal amount = BigDecimal.ZERO;
        if (discountDto != null) {
            amount = discountDto.getAmount();
        }

        AbstractAmountItemDTO repriceDto = amountCollectionDTO.getAmountSpecificItemByShoppingItem(
                shoppingItemDTO.obtainUUID(), TradePromotionDomainDesc.DOMAIN.getNamespace(), "reprice", relation);
        BigDecimal reprice = BigDecimal.ZERO;
        if (repriceDto != null) {
            reprice = repriceDto.getAmount();
        }
        log.info("buildSkuItemVo amount:{} reprice:{}", amount, reprice);
        return skuItemVo;
    }


    /**
     * 默认只处理赠品
     *
     * @param relation        relation
     * @param shoppingItemDTO shoppingItemDTO
     * @param parseTool       parseTool
     */
    private List<SkuGiftItemValueObject> buildGift(Relation relation, AbstractShoppingItemDTO shoppingItemDTO, ParseTool parseTool) {
        PromotionCollectionDTO promotionCollectionDTO = parseTool.getCollectionDTO(PromotionCollectionDTO.class);
        List<AbstractPromotionItemDTO> promotionItemDTOList =
                promotionCollectionDTO.getSelectedListByShoppingItem(shoppingItemDTO.obtainUUID(),
                        relation, Lists.newArrayList(PromotionTypeDict.GIFT_PROMO.getId()));
        if (CollectionUtils.isEmpty(promotionItemDTOList)) {
            return null;
        }
        List<SkuGiftItemValueObject> skuGiftItemVoList = new ArrayList<>();

        for (AbstractPromotionItemDTO promotionItemDTO : promotionItemDTOList) {
            GiftPromotionItemDTO giftPromotionItemDTO = (GiftPromotionItemDTO) promotionItemDTO;
            // 获取赠品清单
//			List<String> giftShoppingItemUUIDList = giftPromotionItemDTO.getGiftShoppingItemUUIDList();
            List<GiftPartDTO> giftPartDTOList = giftPromotionItemDTO.getGiftPartDTOs();
            if (CollectionUtils.isEmpty(giftPartDTOList)) {
                continue;
            }
            for (GiftPartDTO giftPartDTO : giftPartDTOList) {
                // 获取不同类型的赠品，其中满赠、加价购属性总价促销
//				if (giftPartDTO.getGiftType().equals(GiftTypeEnum.GENERAL.getType())) {
                String shoppingItemUUID = giftPartDTO.getShoppingItemUUID();
                // todo
                SkuGiftItemValueObject skuGiftItemVo = buildGiftItem(shoppingItemUUID, giftPartDTO, parseTool, relation);
                if (skuGiftItemVo != null) {
                    skuGiftItemVoList.add(skuGiftItemVo);
                }
//				}
            }

        }
        return skuGiftItemVoList;
    }

    private SkuGiftItemValueObject buildGiftItem(String shoppingItemUUID, GiftPartDTO giftPartDTO, ParseTool parseTool, Relation relation) {
        // 商品
        ProductCollectionDTO productCollectionDTO = parseTool.getCollectionDTO(ProductCollectionDTO.class);
        if (productCollectionDTO == null) {
            log.error("ProductUserActionDTOAssembler productCollectionDTO is null parseTool:{}", JSONObject.toJSONString(parseTool));
            return null;
        }
        SkuGiftItemValueObject skuGiftItemVo = new SkuGiftItemValueObject();

        RetailProductItemDTO productItemDTO = (RetailProductItemDTO) productCollectionDTO.getProductItemByShoppingItemV2(shoppingItemUUID, relation);
        // 数量
        QuantityCollectionDTO quantityCollectionDTO = parseTool.getCollectionDTO(QuantityCollectionDTO.class);
        if (quantityCollectionDTO != null) {
            AbstractQuantityItemDTO quantityItemDTO = quantityCollectionDTO.getQuantityItemByShoppingItem(shoppingItemUUID, relation);
            skuGiftItemVo.setNum(quantityItemDTO.getBuyNum().intValue());
        }
        skuGiftItemVo.setDiscount(String.valueOf(giftPartDTO.getDiscount()));
        skuGiftItemVo.setType(giftPartDTO.getGiftType());
        skuGiftItemVo.setSkuId(productItemDTO.getSkuId());
        skuGiftItemVo.setSkuName(productItemDTO.getName());
        skuGiftItemVo.setSkuPic(productItemDTO.getImagePath());
        return skuGiftItemVo;
    }
}
