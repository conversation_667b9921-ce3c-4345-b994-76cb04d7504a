package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.rpc.CouponMainServiceRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.QueryBindAccountInfoBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.QueryBindAccountInfoParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import com.jd.official.omdm.is.coupon.CouponService;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/17
 * @description 人资主数据
 */
@Component
@Slf4j
public class CouponServiceMainRpcImpl implements CouponMainServiceRpc {

    private String appCode = "jdh-o2o-service_JDOS";

    private String token = "54c3d07bd3647acb573e";


    @Resource
    private CouponService couponService;


    @LogAndAlarm
    @Override
    public QueryBindAccountInfoBo queryBindAccountInfo(QueryBindAccountInfoParam queryBindAccountInfoParam) throws IOException {
        String param = StringUtils.isEmpty(queryBindAccountInfoParam.getPin())?queryBindAccountInfoParam.getErp():queryBindAccountInfoParam.getPin();
        String sign = DigestUtils.md5DigestAsHex((appCode+queryBindAccountInfoParam.getBusinessId()+ queryBindAccountInfoParam.getRequestTimestamp()+this.token+param).getBytes());
        String result="";
        if(StringUtils.isNotEmpty(queryBindAccountInfoParam.getPin())){
            result= couponService.queryBindAccountInfo(this.appCode,queryBindAccountInfoParam.getBusinessId(),queryBindAccountInfoParam.getRequestTimestamp(),sign,"JSON",null,queryBindAccountInfoParam.getPin());
        }else{
            result= couponService.queryBindAccountInfo(this.appCode,queryBindAccountInfoParam.getBusinessId(),queryBindAccountInfoParam.getRequestTimestamp(),sign,"JSON",queryBindAccountInfoParam.getErp(),null);
        }
        String decoded = URLDecoder.decode(result, StandardCharsets.UTF_8.toString());
        log.info("result={}",decoded);
        if(StringUtils.isEmpty(result)){
            throw new BusinessException(new DynamicErrorCode("-1","查询人资接口结果返回为空"));
        }
        JSONObject jsonObject = JSON.parseObject(decoded);
        if("200".equals(jsonObject.getString("resStatus"))){
            return JSON.parseObject(jsonObject.getJSONObject("responsebody").toJSONString(),QueryBindAccountInfoBo.class);
        }
        throw new BusinessException(new DynamicErrorCode("-1",jsonObject.getString("resMsg")));
    }
}
