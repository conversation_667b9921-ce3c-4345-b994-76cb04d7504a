package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.mobilePhoneMsg.sender.client.request.BatchSmsTemplateMessage;
import com.jd.mobilePhoneMsg.sender.client.request.SmsTemplateMessage;
import com.jd.mobilePhoneMsg.sender.client.response.BatchSmsTemplateResponse;
import com.jd.mobilePhoneMsg.sender.client.response.SmsTemplateResponse;
import com.jd.mobilePhoneMsg.sender.client.service.SmsMessageTemplateRpcService;
import com.jd.newnethp.diag.message.center.export.dto.msgchannel.SmsMsgChannelDTO;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant;
import com.jdh.o2oservice.core.domain.support.reach.context.ReachTemplateParamBO;
import com.jdh.o2oservice.core.domain.support.reach.model.ReachTemplate;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.ReachSmsRpc;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.bo.BatchSendSmsBO;
import com.jdh.o2oservice.core.domain.support.reach.rpc.bo.ReachSendResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

/**
 * 短信发送rpc实现
 * @author: yangxiyu
 * @date: 2024/4/19 5:36 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class ReachSmsRpcImpl implements ReachSmsRpc {

    /**
     * smsMessageTemplateRpcService
     */
    @Resource
    private SmsMessageTemplateRpcService smsMessageTemplateRpcService;


    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.ReachSmsRpcImpl.send")
    @Override
    public ReachSendResult send(BatchSendSmsBO sendSmsBO) {
        ReachSendResult result = new ReachSendResult();
        try {
            SmsTemplateMessage templateMessage = new SmsTemplateMessage();
            templateMessage.setToken(sendSmsBO.getToken());
            templateMessage.setSenderNum(sendSmsBO.getSenderNum());
            templateMessage.setExtension(sendSmsBO.getExtension());
            templateMessage.setTemplateParam(buildSmsParam(sendSmsBO));
            templateMessage.setTemplateId(sendSmsBO.getTemplateId());
            templateMessage.setMobileNum(sendSmsBO.getPhone());
            log.info("SmsReachRpcImpl -> send templateMessage:{}", JSON.toJSONString(templateMessage));
            // 发送短信jsf 接口
            SmsTemplateResponse response = smsMessageTemplateRpcService.sendSmsTemplateMessage(templateMessage);
            if (ReachConstant.BASE_RESULT_CODE.equals(response.getBaseResultMsg().getErrorCode())) {
                if (ReachConstant.RESULT_CODE.equals(response.getResultMsg().getErrorCode())) {
                    log.info("ReachSmsRpcImpl->send end, result ={}", JSON.toJSONString(response));
                    result.setSuccess(Boolean.TRUE);
                    return result;
                }
            }
            log.info("ReachSmsRpcImpl->send end, result ={}", JSON.toJSONString(response));
            result.setSuccess(Boolean.FALSE);
            result.setErrorInfo(response.getResultMsg().getErrorMsg() + response.getBaseResultMsg().getErrorMsg());
        }catch (Throwable e){
            result.setSuccess(Boolean.FALSE);
            result.setErrorInfo(e.getMessage());
            log.error("ReachSmsRpcImpl->send error", e);
        }

        return result;
    }


    /**
     *
     * @return
     */
    private String[] buildSmsParam(BatchSendSmsBO sendSmsBO){
        if (ArrayUtils.isEmpty(sendSmsBO.getTemplateParam())){
            return null;
        }
        String[] params = new String[sendSmsBO.getTemplateParam().length];
        for (int i = 0; i < sendSmsBO.getTemplateParam().length; i++) {
            params[i] = sendSmsBO.getTemplateParam()[i].getValue();
        }
        return params;
    }
}
