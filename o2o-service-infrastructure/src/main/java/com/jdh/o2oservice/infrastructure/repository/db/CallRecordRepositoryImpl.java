package com.jdh.o2oservice.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.support.securitynumber.model.CallRecord;
import com.jdh.o2oservice.core.domain.support.securitynumber.repository.CallRecordRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhCallRecordPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhCallRecordPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhCallRecordPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description 外呼记录
 * @Date 2024/12/19 下午4:46
 * <AUTHOR>
 **/
@Component
@Slf4j
public class CallRecordRepositoryImpl implements CallRecordRepository {

    @Resource
    private JdhCallRecordPoMapper callRecordPoMapper;

    /**
     * 保存
     * @param entity
     * @return
     */
    @Override
    public int save(CallRecord entity) {
        JdhCallRecordPo jdhCallRecordPo = JdhCallRecordPoConverter.INSTANCE.convertToCallRecordPo(entity);
        JdhBasicPoConverter.initInsertBasicTimePo(jdhCallRecordPo);
        return callRecordPoMapper.insert(jdhCallRecordPo);
    }

    /**
     * 列表查询
     * @param query
     * @return
     */
    @Override
    public List<CallRecord> queryList(CallRecord query) {
        LambdaQueryWrapper<JdhCallRecordPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getPromiseId()), JdhCallRecordPo::getPromiseId, query.getPromiseId());
        queryWrapper.eq(Objects.nonNull(query.getSourceVoucherId()), JdhCallRecordPo::getSourceVoucherId, query.getSourceVoucherId());
        queryWrapper.eq(Objects.nonNull(query.getCallId()), JdhCallRecordPo::getCallId, query.getCallId());
        queryWrapper.eq(StringUtils.isNotBlank(query.getAngelPin()), JdhCallRecordPo::getAngelPin, query.getAngelPin());
        queryWrapper.eq(Objects.nonNull(query.getConnectedStatus()), JdhCallRecordPo::getConnectedStatus, query.getConnectedStatus());
        queryWrapper.eq(JdhCallRecordPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhCallRecordPo> jdhCallRecordPos = callRecordPoMapper.selectList(queryWrapper);
        return JdhCallRecordPoConverter.INSTANCE.convertPoToCallRecordList(jdhCallRecordPos);
    }

    /**
     * 更新
     * @param entity
     * @return
     */
    @Override
    public int updateById(CallRecord entity) {
        JdhCallRecordPo jdhCallRecordPo = JdhCallRecordPoConverter.INSTANCE.convertToCallRecordPo(entity);
        return callRecordPoMapper.updateById(jdhCallRecordPo);
    }

    /**
     * 根据外呼id查询
     * @param callId
     * @return
     */
    @Override
    public CallRecord queryCallRecordByCallId(String callId) {
        LambdaQueryWrapper<JdhCallRecordPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhCallRecordPo::getCallId, callId);
        queryWrapper.eq(JdhCallRecordPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhCallRecordPo> jdhCallRecordPos = callRecordPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(jdhCallRecordPos)){
            return null;
        }
        List<CallRecord> callRecords = JdhCallRecordPoConverter.INSTANCE.convertPoToCallRecordList(jdhCallRecordPos);
        return callRecords.get(0);
    }
}
