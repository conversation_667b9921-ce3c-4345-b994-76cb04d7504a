package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.jd.pop.seller.i18n.common.params.I18nParam;
import com.jd.pop.vender.center.i18n.api.shop.VenderShopFacade;
import com.jd.pop.vender.center.i18n.api.shop.vo.VenderShopVo;
import com.jd.pop.vender.center.service.shop.ShopSafService;
import com.jd.pop.vender.center.service.shop.dto.BasicShop;
import com.jd.pop.vender.center.service.shop.dto.BasicShopResult;
import com.jd.pop.vender.center.service.shop.dto.ShopFieldEnum;
import com.jd.ump.profiler.CallerInfo;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.trade.rpc.VenderShopRpc;
import com.jdh.o2oservice.core.domain.trade.vo.RpcVenderShopVO;
import com.jdh.o2oservice.core.domain.trade.vo.VenderInfoValueObject;
import com.jdh.o2oservice.infrastructure.rpc.convert.VenderConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

/**
 * 店铺信息PRC
 * @author: yangxiyu
 * @date: 2023/8/8 2:34 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class VenderShopRpcImpl implements VenderShopRpc {
    /** */
    @Resource
    private VenderShopFacade venderShopFacade;

    /**
     * 商家服务
     */
    @Resource
    private ShopSafService shopSafService;

    /**
     * 定义默认静态参数
     */
    private static final I18nParam DEFAULT_PARAM = new I18nParam();
    static {
        DEFAULT_PARAM.setBusinessUnit(301);
    }

    @Resource
    private VenderConverter venderConverter;

    /**
     * 要查询的字段
     */
    private final static String[] COL_NAMES = {
            ShopFieldEnum.TITLE.getColName(),
            ShopFieldEnum.APP_NAME.getColName(),
            ShopFieldEnum.ID_V.getColName(),
            ShopFieldEnum.LOGO_URI.getColName(),
            ShopFieldEnum.OPEN_TIME.getColName()
    };

    /**
     * 根据venderID获取店铺信息
     * @param venderId
     * @return
     */
    @Override
    public RpcVenderShopVO queryInfo(Long venderId) {
        VenderShopVo vo = null;
        try {
            vo = venderShopFacade.getVenderShopByVenderId(venderId, DEFAULT_PARAM);
        }catch (Throwable e){
            log.error("VenderShopRpcImpl->queryInfo error venderId={}", venderId, e);
        }

        if (Objects.isNull(vo)){
            log.error("VenderShopRpcImpl->queryInfo vo is null venderId={}", venderId);
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }

        RpcVenderShopVO res = new RpcVenderShopVO();
        res.setVenderId(vo.getVenderId());
        res.setShopName(vo.getShopName());
        res.setShopId(vo.getShopId());
        return res;
    }

    @Override
    public VenderInfoValueObject getVenderInfoByVenderId(Long venderId) {
        if (venderId == null || venderId <= 0) {
            return null;
        }
        try {
            BasicShop basicShopResult = null;
            try {
                BasicShopResult result = shopSafService.getBasicShopByVenderId(venderId, COL_NAMES, 1);
                log.debug("VenderRpcImpl getVenderShopByVenderId after rpc call verderId{} result{}", venderId, JSON.toJSONString(result));

                if (result != null && result.isSuccess() && result.getBasicShop() != null) {
                    basicShopResult = result.getBasicShop();
                }
            } catch (Exception e) {
                log.error("VenderInfoRpcImpl getVenderInfoByVenderId is error, param is venderId:{}", venderId, e);
            }
            // must not return null !!!
            return venderConverter.convertVenderInfo(basicShopResult);
        } catch (Exception e) {
            log.error("VenderInfoRpcImpl getVenderInfoByVenderId is error, param is venderId:{}", venderId, e);
        }
        return null;
    }
}
