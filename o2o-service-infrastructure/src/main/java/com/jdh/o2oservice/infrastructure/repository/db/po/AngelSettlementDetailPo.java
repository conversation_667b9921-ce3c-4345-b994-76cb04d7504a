package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 护士结算明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 05:53:18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("jdh_angel_settlement_detail")
public class AngelSettlementDetailPo extends Model<AngelSettlementDetailPo> {

    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结算id
     */
    @TableField("settle_id")
    private Long settleId;

    /**
     * 结算id
     */
    @TableField("settle_detail_id")
    private Long settleDetailId;
    /**
     * 费用项目名称
     */
    @TableField("fee_name")
    private String feeName;

    /**
     * 金额
     */
    @TableField("settle_amount")
    private BigDecimal settleAmount;

    /**
     * 银行卡号
     */
    @TableField("bank_no")
    private String bankNo;

    /**
     * 银行名称
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 个税代缴
     */
    @TableField("tax")
    private BigDecimal tax;

    /**
     * 到账金额
     */
    @TableField("received_amount")
    private BigDecimal receivedAmount;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer version;

    /**
     * 是否有效 0-无效 1-有效
     */
    @TableField(value = "yn", fill = FieldFill.INSERT)
    private Integer yn;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
