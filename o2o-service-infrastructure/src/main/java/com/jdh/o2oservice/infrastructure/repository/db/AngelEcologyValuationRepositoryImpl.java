package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.angel.enums.AngelErrorCode;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelEcologyValuation;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelEcologyValuationIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelEcologyValuationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelEcologyValuationQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelValuationInfrastructureConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelEcologyValuationPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelEcologyValuationPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName AngelEcologyValuationRepositoryImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/3/26 14:12
 **/
@Repository
@Slf4j
public class AngelEcologyValuationRepositoryImpl implements AngelEcologyValuationRepository {

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    private String ACTIVE;

    /**
     *
     */
    @Resource
    private JdhAngelEcologyValuationPoMapper angelEcologyValuationPoMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     */
    @Override
    public JdhAngelEcologyValuation find(JdhAngelEcologyValuationIdentifier jdhAngelEcologyValuationIdentifier) {
        LambdaQueryWrapper<JdhAngelEcologyValuationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelEcologyValuationPo::getValuationId, jdhAngelEcologyValuationIdentifier.getValuationId());
        //有效
        queryWrapper.eq(JdhAngelEcologyValuationPo::getYn, YnStatusEnum.YES.getCode());
        JdhAngelEcologyValuationPo jdhAngelEcologyValuationPo = angelEcologyValuationPoMapper.selectOne(queryWrapper);
        return JdhAngelValuationInfrastructureConverter.INSTANCE.po2Entity(jdhAngelEcologyValuationPo);
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     */
    @Override
    public int remove(JdhAngelEcologyValuation entity) {
        return 0;
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     */
    @Override
    public int save(JdhAngelEcologyValuation angelEcologyValuation) {
        JdhAngelEcologyValuationPo ecologyValuationPo = JdhAngelValuationInfrastructureConverter.INSTANCE.entity2Po(angelEcologyValuation);
        log.info("AngelEcologyValuationRepositoryImpl -> save, ecologyValuationPo={}", JSON.toJSONString(ecologyValuationPo));
        //新增
        if (Objects.isNull(ecologyValuationPo.getId())) {
            Date cur = new Date();
            ecologyValuationPo.setCreateTime(cur);
            ecologyValuationPo.setUpdateTime(cur);
            ecologyValuationPo.setYn(YnStatusEnum.YES.getCode());
            ecologyValuationPo.setBranch(ACTIVE);
            ecologyValuationPo.setVersion(NumConstant.NUM_1);
            return angelEcologyValuationPoMapper.insert(ecologyValuationPo);
        }
        //修改
        Integer oldVersion = angelEcologyValuation.getVersion();
        angelEcologyValuation.versionIncrease();
        ecologyValuationPo.setUpdateTime(new Date());
        ecologyValuationPo.setVersion(angelEcologyValuation.getVersion());

        LambdaUpdateWrapper<JdhAngelEcologyValuationPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(JdhAngelEcologyValuationPo::getVersion,oldVersion)
                .eq(JdhAngelEcologyValuationPo::getValuationId,ecologyValuationPo.getValuationId());

        int count = angelEcologyValuationPoMapper.update(ecologyValuationPo, updateWrapper);
        if (count < 1) {
            throw new BusinessException(AngelErrorCode.ANGEL_SAVE_BUSY);
        }
        return count;
    }

    /**
     * 查询护士评价数据
     * @param query
     * @return
     */
    @Override
    public List<JdhAngelEcologyValuation> findList(AngelEcologyValuationQuery query) {
        LambdaQueryWrapper<JdhAngelEcologyValuationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getValuationId()),JdhAngelEcologyValuationPo::getValuationId, query.getValuationId());
        queryWrapper.eq(Objects.nonNull(query.getSkuValuationId()),JdhAngelEcologyValuationPo::getSkuValuationId, query.getSkuValuationId());
        queryWrapper.eq(Objects.nonNull(query.getPromiseId()),JdhAngelEcologyValuationPo::getPromiseId, query.getPromiseId());
        queryWrapper.in(CollUtil.isNotEmpty(query.getPromiseIdList()),JdhAngelEcologyValuationPo::getPromiseId, query.getPromiseIdList());
        queryWrapper.eq(Objects.nonNull(query.getAngelId()),JdhAngelEcologyValuationPo::getAngelId, query.getAngelId());
        queryWrapper.eq(Objects.nonNull(query.getOrderId()),JdhAngelEcologyValuationPo::getOrderId, query.getOrderId());
        queryWrapper.eq(Objects.nonNull(query.getUserPin()),JdhAngelEcologyValuationPo::getUserPin, query.getUserPin());
        //有效
        queryWrapper.eq(JdhAngelEcologyValuationPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhAngelEcologyValuationPo> ecologyValuationPos = angelEcologyValuationPoMapper.selectList(queryWrapper);
        return JdhAngelValuationInfrastructureConverter.INSTANCE.po2Entity(ecologyValuationPos);
    }

    /**
     * 查询护士评价数据
     *
     * @param query
     * @return
     */
    @Override
    public Page<JdhAngelEcologyValuation> findPageList(AngelEcologyValuationQuery query) {
        IPage<JdhAngelEcologyValuationPo> ipage = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhAngelEcologyValuationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(query.getAngelId()), JdhAngelEcologyValuationPo::getAngelId, query.getAngelId())
                .eq(Objects.nonNull(query.getValuationId()), JdhAngelEcologyValuationPo::getValuationId, query.getValuationId())
                .eq(Objects.nonNull(query.getAppChannel()), JdhAngelEcologyValuationPo::getAppChannel, query.getAppChannel())
                .eq(Objects.nonNull(query.getOrderId()), JdhAngelEcologyValuationPo::getOrderId, query.getOrderId())
                .eq(Objects.nonNull(query.getPromiseId()), JdhAngelEcologyValuationPo::getPromiseId, query.getPromiseId())
                .in(CollectionUtils.isNotEmpty(query.getPromiseIdList()), JdhAngelEcologyValuationPo::getPromiseId, query.getPromiseIdList())
                .eq(StringUtils.isNotBlank(query.getUserPin()), JdhAngelEcologyValuationPo::getUserPin, query.getUserPin())
                .ge(Objects.nonNull(query.getMinRoundScore()), JdhAngelEcologyValuationPo::getAroundScore, query.getMinRoundScore())
                .ge(Objects.nonNull(query.getStartCreateTime()), JdhAngelEcologyValuationPo::getCreateTime, query.getStartCreateTime())
                .eq(Objects.nonNull(query.getValuationSensitiveStatus()), JdhAngelEcologyValuationPo::getValuationSensitiveStatus, query.getValuationSensitiveStatus())
                .eq(Objects.nonNull(query.getShowStatus()), JdhAngelEcologyValuationPo::getShowStatus, query.getShowStatus())
                .eq(JdhAngelEcologyValuationPo::getYn, YnStatusEnum.YES.getCode());

        if(CollectionUtils.isNotEmpty(query.getSkuIdList())) {
            queryWrapper.apply("JSON_CONTAINS(sku_id, {0})", JSON.toJSONString(query.getSkuIdList()));
        }

        if(BooleanUtils.isTrue(query.getAroundSort())) {
            queryWrapper.orderByDesc(JdhAngelEcologyValuationPo::getAroundScore);
        }
        queryWrapper.orderByDesc(JdhAngelEcologyValuationPo::getId);
        IPage<JdhAngelEcologyValuationPo> valuationPoIPage = angelEcologyValuationPoMapper.selectPage(ipage, queryWrapper);

        if(Objects.isNull(valuationPoIPage) || CollectionUtils.isEmpty(valuationPoIPage.getRecords())) {
            return null;
        }

        List<JdhAngelEcologyValuation> valuationList = JdhAngelValuationInfrastructureConverter.INSTANCE.po2Entity(valuationPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(valuationPoIPage, valuationList);
    }

    /**
     * 分页查询护士评价数据
     *
     * @param query
     * @return
     */
    @Override
    public Page<JdhAngelEcologyValuation> findNullFieldPageList(AngelEcologyValuationQuery query) {
        Page<JdhAngelEcologyValuationPo> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhAngelEcologyValuationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(query.getStartCreateTime()), JdhAngelEcologyValuationPo::getCreateTime, query.getStartCreateTime())
                .isNull(JdhAngelEcologyValuationPo::getBusinessMode)
                .eq(JdhAngelEcologyValuationPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByAsc(JdhAngelEcologyValuationPo::getId);

        Page<JdhAngelEcologyValuationPo> valuationPoPage = angelEcologyValuationPoMapper.selectPage(page, queryWrapper);
        if(Objects.isNull(valuationPoPage) || CollectionUtils.isEmpty(valuationPoPage.getRecords())) {
            log.error("AngelEcologyValuationRepositoryImpl -> findNullFieldPageList, 没有查到评论数据");
            return null;
        }

        List<JdhAngelEcologyValuation> valuationList = JdhAngelValuationInfrastructureConverter.INSTANCE.po2Entity(valuationPoPage.getRecords());
        return JdhBasicPoConverter.initPage(valuationPoPage, valuationList);
    }

    /**
     * 更新刷数
     *
     * @param record
     * @return
     */
    @Override
    public int updateFlushValuation(JdhAngelEcologyValuation record) {
        LambdaUpdateWrapper<JdhAngelEcologyValuationPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Objects.nonNull(record.getVerticalCode()), JdhAngelEcologyValuationPo::getVerticalCode, record.getVerticalCode())
                .set(Objects.nonNull(record.getBusinessMode()), JdhAngelEcologyValuationPo::getBusinessMode, record.getBusinessMode())
                .set(Objects.nonNull(record.getServiceType()), JdhAngelEcologyValuationPo::getServiceType, record.getServiceType())
                .set(Objects.nonNull(record.getSkuId()), JdhAngelEcologyValuationPo::getSkuId, record.getSkuId())
                .eq(JdhAngelEcologyValuationPo::getValuationId, record.getValuationId())
                .eq(JdhAngelEcologyValuationPo::getYn, YnStatusEnum.YES.getCode());
        return angelEcologyValuationPoMapper.update(null, updateWrapper);
    }
}