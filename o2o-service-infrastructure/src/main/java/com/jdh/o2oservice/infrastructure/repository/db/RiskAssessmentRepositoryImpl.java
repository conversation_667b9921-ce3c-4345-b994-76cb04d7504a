package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.common.enums.RiskAssessmentStatusEnum;
import com.jdh.o2oservice.common.enums.RiskQuestionStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessmentDetail;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssDetailRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssessmentRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentPageQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.RiskAssRepositoryConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.RiskAssessmentPoConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhRiskAssessmentDetailMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhRiskAssessmentMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhRiskAssessmentDetailPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhRiskAssessmentPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/26
 */
@Component
@Slf4j
public class RiskAssessmentRepositoryImpl implements RiskAssessmentRepository {

    /**
     * JdhRiskAssessmentMapper 的实例，用于与数据库进行交互，执行相关的数据操作。
     */
    @Autowired
    private JdhRiskAssessmentMapper jdhRiskAssessmentMapper;


    /**
     * JdhRiskAssessmentDetailMapper 的实例，用于与数据库进行交互，执行相关的数据操作。
     */
    @Autowired
    private JdhRiskAssessmentDetailMapper jdhRiskAssessmentDetailMapper;

    @Autowired
    private RiskAssDetailRepository riskAssDetailRepository;

    /**
     * 保存风险评估信息。
     *
     * @param riskAssessment 风险评估对象，包含需要保存的信息。
     * @return 保存操作是否成功。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(RiskAssessment riskAssessment) {

        JdhRiskAssessmentPo po = RiskAssRepositoryConvert.INSTANCE.convert(riskAssessment);

        if (Objects.isNull(po.getId())){
            JdhBasicPoConverter.initInsertBasicPo(po);
            jdhRiskAssessmentMapper.insert(po);
            if (CollectionUtils.isNotEmpty(riskAssessment.getRiskAssessmentDetailList())){
                for (RiskAssessmentDetail riskAssessmentDetail : riskAssessment.getRiskAssessmentDetailList()) {
                    riskAssDetailRepository.save(riskAssessmentDetail);
                }
            }
            return Boolean.TRUE;
        }


        LambdaUpdateWrapper<JdhRiskAssessmentPo> updateWrapper = new LambdaUpdateWrapper<>();

        updateWrapper
                .set(Objects.nonNull(po.getRiskAssessmentStatus()), JdhRiskAssessmentPo::getRiskAssessmentStatus, po.getRiskAssessmentStatus())
                .set(StringUtil.isNotBlank(po.getAssessmentUser()), JdhRiskAssessmentPo::getAssessmentUser, po.getAssessmentUser())
                .set(Objects.nonNull(po.getAssessmentUserType()),JdhRiskAssessmentPo::getAssessmentUserType, po.getAssessmentUserType())
                .set(Objects.nonNull(po.getAssessmentTime()), JdhRiskAssessmentPo::getAssessmentTime, po.getAssessmentTime())
                .set(StringUtil.isNotBlank(po.getReturnVisitUser()), JdhRiskAssessmentPo::getReturnVisitUser, po.getReturnVisitUser())
                .set(Objects.nonNull(po.getReturnVisitTime()), JdhRiskAssessmentPo::getReturnVisitTime, po.getReturnVisitTime())
                .set(StringUtil.isNotBlank(po.getReturnVisitRecord()), JdhRiskAssessmentPo::getReturnVisitRecord, po.getReturnVisitRecord())
                .set(Objects.nonNull(po.getServiceFinishTime()), JdhRiskAssessmentPo::getServiceFinishTime, po.getServiceFinishTime())
                .set(Objects.nonNull(po.getReturnVisitStatus()), JdhRiskAssessmentPo::getReturnVisitStatus, po.getReturnVisitStatus())
                .setSql(CommonConstant.UPDATE_TIME_SQL_NOW)
                .set(Objects.nonNull(po.getUpdateUser()), JdhRiskAssessmentPo::getUpdateUser, po.getUpdateUser())
                .set(Objects.nonNull(po.getAssessmentDeadlineTime()),JdhRiskAssessmentPo::getAssessmentDeadlineTime, po.getAssessmentDeadlineTime())
                .set(Objects.nonNull(po.getAssessmentTimeoutStatus()),JdhRiskAssessmentPo::getAssessmentTimeoutStatus, po.getAssessmentTimeoutStatus())
                .eq(JdhRiskAssessmentPo::getId, po.getId())
        ;

        if (CollectionUtils.isNotEmpty(riskAssessment.getRiskAssessmentDetailList())){
            for (RiskAssessmentDetail riskAssessmentDetail : riskAssessment.getRiskAssessmentDetailList()) {
                riskAssDetailRepository.save(riskAssessmentDetail);
            }
        }

        return jdhRiskAssessmentMapper.update(null, updateWrapper) > 0;
    }


    /**
     * 根据查询条件获取风险评估信息。
     *
     * @param query 查询条件对象，包含要查询的风险评估的相关信息。
     * @return 满足查询条件的风险评估对象。
     */
    @Override
    public RiskAssessment queryRiskAssessment(RiskAssessmentQuery query) {
        LambdaQueryWrapper<JdhRiskAssessmentPo> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper
                .eq(Objects.nonNull(query.getRiskAssessmentId()), JdhRiskAssessmentPo::getRiskAssessmentId, query.getRiskAssessmentId())
                .eq(Objects.nonNull(query.getPromiseId()), JdhRiskAssessmentPo::getPromiseId, query.getPromiseId())
                .eq(Objects.nonNull(query.getRiskLevel()), JdhRiskAssessmentPo::getRiskLevel, query.getRiskLevel())
                .eq(JdhRiskAssessmentPo::getYn,YnStatusEnum.YES.getCode())
        ;


        JdhRiskAssessmentPo jdhRiskAssessmentPo = jdhRiskAssessmentMapper.selectOne(queryWrapper);

        RiskAssessment res = RiskAssessmentPoConvert.INSTANCE.convert(jdhRiskAssessmentPo);

        if (Objects.nonNull(jdhRiskAssessmentPo) && Boolean.TRUE.equals(query.getDetailQuery())){
            LambdaQueryWrapper<JdhRiskAssessmentDetailPo> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper
                    .eq(JdhRiskAssessmentDetailPo::getRiskAssessmentId, jdhRiskAssessmentPo.getRiskAssessmentId())
                    .notIn(JdhRiskAssessmentDetailPo::getRiskQuestionStatus, RiskQuestionStatusEnum.getInvalidStatus())
                    .eq(Objects.nonNull(query.getPromisePatientId()), JdhRiskAssessmentDetailPo::getPromisePatientId, query.getPromisePatientId())
                    .eq(JdhRiskAssessmentDetailPo::getYn, YnStatusEnum.YES.getCode())
            ;
            List<JdhRiskAssessmentDetailPo> jdhRiskAssessmentDetailPos = jdhRiskAssessmentDetailMapper.selectList(detailQueryWrapper);
            res.setRiskAssessmentDetailList(RiskAssessmentPoConvert.INSTANCE.convertList(jdhRiskAssessmentDetailPos));
        }

        return res;
    }

    /**
     * 根据查询条件获取风险评估列表
     *
     * @param query 查询条件对象
     * @return 符合条件的风险评估列表
     */
    @Override
    public List<RiskAssessment> queryRiskAssessmentList(RiskAssessmentQuery query) {
        LambdaQueryWrapper<JdhRiskAssessmentPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(query.getRiskAssessmentId()), JdhRiskAssessmentPo::getRiskAssessmentId, query.getRiskAssessmentId())
                .eq(Objects.nonNull(query.getPromiseId()), JdhRiskAssessmentPo::getPromiseId, query.getPromiseId())
                .in(CollectionUtils.isNotEmpty(query.getPromiseIdList()), JdhRiskAssessmentPo::getPromiseId, query.getPromiseIdList())
                .eq(JdhRiskAssessmentPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhRiskAssessmentPo> poList = jdhRiskAssessmentMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(poList)){
            return Collections.emptyList();
        }
        return RiskAssessmentPoConvert.INSTANCE.convert(poList);
    }


    /**
     * 分页查询风险评估信息。
     *
     * @param query 查询条件对象，包含分页信息和其他过滤条件。
     * @return 分页结果对象，包含当前页数据和分页信息。
     */
    @Override
    public Page<RiskAssessment> queryRiskAssessmentPage(RiskAssessmentPageQuery query) {

        Page<JdhRiskAssessmentPo> page = new Page<>(query.getPageNum(), query.getPageSize());

        LambdaQueryWrapper<JdhRiskAssessmentPo> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper
                .eq(StringUtil.isNotBlank(query.getBusinessMode()), JdhRiskAssessmentPo::getBusinessMode, query.getBusinessMode())
                .eq(Objects.nonNull(query.getOrderId()), JdhRiskAssessmentPo::getOrderId, query.getOrderId())
                .eq(StringUtil.isNotBlank(query.getUserPin()), JdhRiskAssessmentPo::getUserPin, query.getUserPin())
                .eq(Objects.nonNull(query.getRiskAssessmentId()), JdhRiskAssessmentPo::getRiskAssessmentId, query.getRiskAssessmentId())
                .eq(Objects.nonNull(query.getRiskAssessmentStatus()), JdhRiskAssessmentPo::getRiskAssessmentStatus, query.getRiskAssessmentStatus())
                .eq(Objects.nonNull(query.getPatientProvince()), JdhRiskAssessmentPo::getPatientProvince, query.getPatientProvince())
                .eq(Objects.nonNull(query.getPatientCity()), JdhRiskAssessmentPo::getPatientCity, query.getPatientCity())
                .eq(Objects.nonNull(query.getPatientCounty()), JdhRiskAssessmentPo::getPatientCounty, query.getPatientCounty())
                .eq(Objects.nonNull(query.getPatientTown()), JdhRiskAssessmentPo::getPatientTown, query.getPatientTown())
                .eq(StringUtil.isNotBlank(query.getServiceId()), JdhRiskAssessmentPo::getServiceId, query.getServiceId())
                .like(StringUtil.isNotBlank(query.getServiceItemName()), JdhRiskAssessmentPo::getServiceItemName, query.getServiceItemName())
                .ge(Objects.nonNull(query.getAssessmentStartTime()), JdhRiskAssessmentPo::getAssessmentTime, query.getAssessmentStartTime())
                .le(Objects.nonNull(query.getAssessmentEndTime()), JdhRiskAssessmentPo::getAssessmentTime, query.getAssessmentEndTime())
                .ge(Objects.nonNull(query.getOrderStartTime()), JdhRiskAssessmentPo::getOrderTime, query.getOrderStartTime())
                .le(Objects.nonNull(query.getOrderEndTime()), JdhRiskAssessmentPo::getOrderTime, query.getOrderEndTime())
                .ge(Objects.nonNull(query.getAppointmentStartTime()), JdhRiskAssessmentPo::getAppointmentTime, query.getAppointmentStartTime())
                .le(Objects.nonNull(query.getAppointmentEndTime()), JdhRiskAssessmentPo::getAppointmentTime, query.getAppointmentEndTime())
                .ge(Objects.nonNull(query.getServiceFinishStartTime()), JdhRiskAssessmentPo::getServiceFinishTime, query.getServiceFinishStartTime())
                .le(Objects.nonNull(query.getServiceFinishEndTime()), JdhRiskAssessmentPo::getServiceFinishTime, query.getServiceFinishEndTime())
                .ge(Objects.nonNull(query.getReturnVisitStartTime()), JdhRiskAssessmentPo::getReturnVisitTime, query.getReturnVisitStartTime())
                .le(Objects.nonNull(query.getReturnVisitEndTime()), JdhRiskAssessmentPo::getReturnVisitTime, query.getReturnVisitEndTime())
                .eq(JdhRiskAssessmentPo::getYn, YnStatusEnum.YES.getCode())
                .eq(Objects.nonNull(query.getPartnerSource()), JdhRiskAssessmentPo::getPartnerSource, query.getPartnerSource())
                .eq(Objects.nonNull(query.getReturnVisitStatus()), JdhRiskAssessmentPo::getReturnVisitStatus, query.getReturnVisitStatus())
                .eq(StringUtil.isNotBlank(query.getAssessmentUser()), JdhRiskAssessmentPo::getAssessmentUser, query.getAssessmentUser())
                .eq(StringUtil.isNotBlank(query.getReturnVisitUser()), JdhRiskAssessmentPo::getReturnVisitUser, query.getReturnVisitUser())
                .eq(Objects.nonNull(query.getSkuServiceType()), JdhRiskAssessmentPo::getSkuServiceType, query.getSkuServiceType())
                .eq(Objects.nonNull(query.getRiskLevel()), JdhRiskAssessmentPo::getRiskLevel, query.getRiskLevel())

        ;


        if (CollectionUtils.isNotEmpty(query.getPatientCitySet())
                || CollectionUtils.isNotEmpty(query.getPatientCountySet())
                || CollectionUtils.isNotEmpty(query.getPatientTownSet())
                || CollectionUtils.isNotEmpty(query.getPatientProvinceSet())
        ){
            queryWrapper.and(
                    w->
                            w
                                    .in(CollectionUtils.isNotEmpty(query.getPatientProvinceSet()),JdhRiskAssessmentPo::getPatientProvince,query.getPatientProvinceSet())
                                    .or()
                                    .in(CollectionUtils.isNotEmpty(query.getPatientCountySet()),JdhRiskAssessmentPo::getPatientCounty,query.getPatientCountySet())
                                    .or()
                                    .in(CollectionUtils.isNotEmpty(query.getPatientTownSet()),JdhRiskAssessmentPo::getPatientTown,query.getPatientTownSet())
                                    .or()
                                    .in(CollectionUtils.isNotEmpty(query.getPatientCitySet()),JdhRiskAssessmentPo::getPatientCity,query.getPatientCitySet())

            );
        }

        if (Objects.nonNull(query.getAssessmentOutTimeStatus())){
            //未超时
            //已完成未超时 & 未完成未超时
            if (Objects.equals(CommonConstant.ZERO, query.getAssessmentOutTimeStatus())){
                Set<Integer> finishStatus = RiskAssessmentStatusEnum.getFinishStatus();
                queryWrapper.and(
                        w->
                                w.eq(JdhRiskAssessmentPo::getAssessmentTimeoutStatus,CommonConstant.ZERO)
                                        .eq(JdhRiskAssessmentPo::getRiskAssessmentStatus,finishStatus)
                                        .or()
                                        .eq(JdhRiskAssessmentPo::getRiskAssessmentStatus,RiskAssessmentStatusEnum.WAITING_ASS.getStatus())
                                        .ge(JdhRiskAssessmentPo::getAssessmentDeadlineTime,new Date())
                );

            }else {
                //已完成超时 & 未完成超时
                Set<Integer> finishStatus = RiskAssessmentStatusEnum.getFinishStatus();
                queryWrapper.and(
                        w->
                                w.eq(JdhRiskAssessmentPo::getAssessmentTimeoutStatus,CommonConstant.ONE)
                                        .eq(JdhRiskAssessmentPo::getRiskAssessmentStatus,finishStatus)
                                        .or()
                                        .eq(JdhRiskAssessmentPo::getRiskAssessmentStatus,RiskAssessmentStatusEnum.WAITING_ASS.getStatus())
                                        .le(JdhRiskAssessmentPo::getAssessmentDeadlineTime,new Date())
                );

            }
        }

        queryWrapper.orderByDesc(JdhRiskAssessmentPo::getCreateTime);


        Page<JdhRiskAssessmentPo> resPage = jdhRiskAssessmentMapper.selectPage(page, queryWrapper);
        List<RiskAssessment> riskAssessments = RiskAssessmentPoConvert.INSTANCE.convert(resPage.getRecords());
        return JdhBasicPoConverter.initPage(resPage, riskAssessments);

    }

    /**
     * 保存风险评估的回访记录。
     *
     * @param riskAssessment 风险评估对象，包含需要保存的访问记录信息。
     * @return 保存操作是否成功。
     */
    @Override
    public Boolean saveReturnVisit(RiskAssessment riskAssessment) {
        LambdaUpdateWrapper<JdhRiskAssessmentPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .set(JdhRiskAssessmentPo::getReturnVisitRecord,riskAssessment.getReturnVisitRecord())
                .setSql(CommonConstant.VISIT_TIME_SQL_NOW)
                .set(JdhRiskAssessmentPo::getReturnVisitUser,riskAssessment.getReturnVisitUser())
                .set(JdhRiskAssessmentPo::getUpdateUser,riskAssessment.getReturnVisitUser())
                .set(JdhRiskAssessmentPo::getReturnVisitStatus,riskAssessment.getReturnVisitStatus())
                .setSql(CommonConstant.UPDATE_TIME_SQL_NOW)
                .eq(JdhRiskAssessmentPo::getRiskAssessmentId,riskAssessment.getRiskAssessmentId())
        ;

        return jdhRiskAssessmentMapper.update(null, updateWrapper) > 0;
    }

    /**
     * @param riskAssessment
     * @return
     */
    @Override
    public Boolean deleteRiskAssessment(RiskAssessment riskAssessment) {
        LambdaUpdateWrapper<JdhRiskAssessmentPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set( JdhRiskAssessmentPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhRiskAssessmentPo::getRiskAssessmentId,riskAssessment.getRiskAssessmentId())
        ;

        LambdaUpdateWrapper<JdhRiskAssessmentDetailPo> updateDetailWrapper = new LambdaUpdateWrapper<>();
        updateDetailWrapper.set( JdhRiskAssessmentDetailPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhRiskAssessmentDetailPo::getRiskAssessmentId,riskAssessment.getRiskAssessmentId())
        ;

        jdhRiskAssessmentMapper.update(null, updateWrapper);
        jdhRiskAssessmentDetailMapper.update(null, updateDetailWrapper);
        return Boolean.TRUE;

    }

    /**
     * 修改风险评估单状态
     *
     * @param riskAssessmentId
     * @param riskAssessmentStatus
     * @return
     */
    @Override
    public boolean updateRiskAssessmentStatus(Long riskAssessmentId, Integer riskAssessmentStatus) {
        LambdaUpdateWrapper<JdhRiskAssessmentPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(JdhRiskAssessmentPo::getRiskAssessmentStatus, riskAssessmentStatus)
                .set(JdhRiskAssessmentPo::getUpdateTime, new Date())
                .eq(JdhRiskAssessmentPo::getRiskAssessmentId, riskAssessmentId)
                .eq(JdhRiskAssessmentPo::getYn, YnStatusEnum.YES.getCode());
        return jdhRiskAssessmentMapper.update(null, updateWrapper) > 0;
    }


}
