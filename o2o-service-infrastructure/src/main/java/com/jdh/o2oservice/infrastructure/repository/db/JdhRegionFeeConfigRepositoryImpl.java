package com.jdh.o2oservice.infrastructure.repository.db;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.JdhRegionFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhRegionFeeConfigQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhFeeConfigPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhRegionFeeConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhRegionFeeConfigPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class JdhRegionFeeConfigRepositoryImpl implements JdhRegionFeeConfigRepository {

    @Resource
    private JdhRegionFeeConfigPoMapper jdhRegionFeeConfigPoMapper;

    @Override
    public JdhRegionFeeConfig find(JdhRegionFeeConfigIdentifier jdhRegionFeeConfigIdentifier) {
        LambdaQueryWrapper<JdhRegionFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhRegionFeeConfigPo::getRegionFeeConfigId, jdhRegionFeeConfigIdentifier.getRegionFeeConfigId())
                .eq(JdhRegionFeeConfigPo::getYn, YnStatusEnum.YES.getCode());
        JdhRegionFeeConfigPo jdhRegionFeeConfigPo = jdhRegionFeeConfigPoMapper.selectOne(queryWrapper);
        if (Objects.nonNull(jdhRegionFeeConfigPo)) {
            return JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(jdhRegionFeeConfigPo);
        }
        return null;
    }

    @Override
    public int remove(JdhRegionFeeConfig entity) {
        LambdaUpdateWrapper<JdhRegionFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhRegionFeeConfigPo po = JdhRegionFeeConfigPo.builder().build();
        updateWrapper.set(JdhRegionFeeConfigPo::getYn, YnStatusEnum.NO.getCode())
                .setSql("`version` = version+1")
                .eq(StringUtil.isNotBlank(entity.getDestPrefix()), JdhRegionFeeConfigPo::getDestPrefix, entity.getDestPrefix())
                .eq(StringUtil.isNotBlank(entity.getDestCode()), JdhRegionFeeConfigPo::getDestCode, entity.getDestCode());
        po.setUpdateTime(new Date());
        return jdhRegionFeeConfigPoMapper.update(po, updateWrapper);



    }

    /**
     * 保存
     * @param entity
     * @return
     */
    @Override
    public int save(JdhRegionFeeConfig entity) {
        JdhRegionFeeConfigPo po = JdhRegionFeeConfigPo.builder()
                .regionFeeConfigId(entity.getRegionFeeConfigId()).feeConfigId(entity.getFeeConfigId())
                .serviceType(entity.getServiceType()).channelId(entity.getChannelId())
                .provinceCode(entity.getProvinceCode()).provinceName(entity.getProvinceName())
                .cityCode(entity.getCityCode()).cityName(entity.getCityName())
                .countyCode(entity.getCountyCode()).countyName(entity.getCountyName())
                .townCode(entity.getTownCode()).townName(entity.getTownName())
                .destCode(entity.getDestCode()).destName(entity.getDestName())
                .destPrefix(entity.getDestPrefix()).onSiteFee(entity.getOnSiteFee())
                .immediatelyFee(entity.getImmediatelyFee()).holidayFee(entity.getHolidayFee())
                .holidayFee(entity.getHolidayFee()).upgrageAngelFee(entity.getUpgrageAngelFee())
                .upgrageSkuList(entity.getUpgrageSkuList()).nightDoorFee(entity.getNightDoorFee())
                .peakServiceFee(entity.getPeakServiceFee()).extend(entity.getExtend())
                .build();
        JdhBasicPoConverter.initInsertBasicPo(po);
        return jdhRegionFeeConfigPoMapper.insert(po);
    }

    /**
     * 更新
     * @param entity
     * @return
     */
    @Override
    public int update(JdhRegionFeeConfig entity) {
        LambdaUpdateWrapper<JdhRegionFeeConfigPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhRegionFeeConfigPo po = JdhRegionFeeConfigPo.builder().build();
        updateWrapper.set(entity.getFeeConfigId() != null, JdhRegionFeeConfigPo::getFeeConfigId, entity.getFeeConfigId())
                .set(entity.getServiceType() != null, JdhRegionFeeConfigPo::getServiceType, entity.getServiceType())
                .set(entity.getChannelId() != null, JdhRegionFeeConfigPo::getChannelId, entity.getChannelId())
                .set(entity.getProvinceCode() != null, JdhRegionFeeConfigPo::getProvinceCode, entity.getProvinceCode())
                .set(entity.getProvinceName() != null, JdhRegionFeeConfigPo::getProvinceName, entity.getProvinceName())
                .set(entity.getCityCode() != null, JdhRegionFeeConfigPo::getCityCode, entity.getCityCode())
                .set(entity.getCityName() != null, JdhRegionFeeConfigPo::getCityName, entity.getCityName())
                .set(entity.getCountyCode() != null, JdhRegionFeeConfigPo::getCountyCode, entity.getCountyCode())
                .set(entity.getCountyName() != null, JdhRegionFeeConfigPo::getCountyName, entity.getCountyName())
                .set(entity.getTownCode() != null, JdhRegionFeeConfigPo::getTownCode, entity.getTownCode())
                .set(entity.getTownName() != null, JdhRegionFeeConfigPo::getTownName, entity.getTownName())
                .set(entity.getDestCode() != null, JdhRegionFeeConfigPo::getDestCode, entity.getDestCode())
                .set(entity.getDestName() != null, JdhRegionFeeConfigPo::getDestName, entity.getDestName())
                .set(entity.getDestPrefix() != null, JdhRegionFeeConfigPo::getDestPrefix, entity.getDestPrefix())
                .set(entity.getOnSiteFee() != null, JdhRegionFeeConfigPo::getOnSiteFee, entity.getOnSiteFee())
                .set(entity.getImmediatelyFee() != null, JdhRegionFeeConfigPo::getImmediatelyFee, entity.getImmediatelyFee())
                .set(entity.getHolidayFee() != null, JdhRegionFeeConfigPo::getHolidayFee, entity.getHolidayFee())
                .set(entity.getUpgrageAngelFee() != null, JdhRegionFeeConfigPo::getUpgrageAngelFee, entity.getUpgrageAngelFee())
                .set(entity.getUpgrageSkuList() != null, JdhRegionFeeConfigPo::getUpgrageSkuList, entity.getUpgrageSkuList())
                .set(entity.getNightDoorFee() != null, JdhRegionFeeConfigPo::getNightDoorFee, entity.getNightDoorFee())
                .set(entity.getPeakServiceFee() != null, JdhRegionFeeConfigPo::getPeakServiceFee, entity.getPeakServiceFee())
                .set(entity.getExtend() != null, JdhRegionFeeConfigPo::getExtend, entity.getExtend())
                .setSql("`version` = version+1")
                .eq(JdhRegionFeeConfigPo::getRegionFeeConfigId, entity.getRegionFeeConfigId());
        po.setUpdateTime(new Date());
        return jdhRegionFeeConfigPoMapper.update(po, updateWrapper);
    }

    /**
     * 分页列表
     * @param query
     * @return
     */
    @Override
    public Page<JdhRegionFeeConfig> queryPageRegionFeeConfig(JdhRegionFeeConfigQuery query) {
        LambdaQueryWrapper<JdhRegionFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getRegionFeeConfigId() != null, JdhRegionFeeConfigPo::getRegionFeeConfigId, query.getRegionFeeConfigId())
                .eq(query.getFeeConfigId() != null, JdhRegionFeeConfigPo::getFeeConfigId, query.getFeeConfigId())
                .eq(query.getServiceType() != null, JdhRegionFeeConfigPo::getServiceType, query.getServiceType())
                .eq(query.getChannelId() != null, JdhRegionFeeConfigPo::getChannelId, query.getChannelId())
                .eq(query.getDestCode() != null, JdhRegionFeeConfigPo::getDestCode, query.getDestCode())
                .eq(query.getDestPrefix() != null, JdhRegionFeeConfigPo::getDestPrefix, query.getDestPrefix())
                .eq(JdhRegionFeeConfigPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhRegionFeeConfigPo::getUpdateTime);
        Page<JdhRegionFeeConfigPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<JdhRegionFeeConfigPo> iPage = jdhRegionFeeConfigPoMapper.selectPage(param, queryWrapper);
        List<JdhRegionFeeConfig> dataList = JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(iPage.getRecords());
        return JdhBasicPoConverter.initPage(iPage, dataList);
    }

    /**
     * 列表
     * @param query
     * @return
     */
    @Override
    public List<JdhRegionFeeConfig> findList(JdhRegionFeeConfigQuery query) {
        LambdaQueryWrapper<JdhRegionFeeConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(query.getRegionFeeConfigId() != null, JdhRegionFeeConfigPo::getRegionFeeConfigId, query.getRegionFeeConfigId())
                .eq(query.getFeeConfigId() != null, JdhRegionFeeConfigPo::getFeeConfigId, query.getFeeConfigId())
                .eq(query.getServiceType() != null, JdhRegionFeeConfigPo::getServiceType, query.getServiceType())
                .eq(query.getChannelId() != null, JdhRegionFeeConfigPo::getChannelId, query.getChannelId())
                .eq(query.getDestCode() != null, JdhRegionFeeConfigPo::getDestCode, query.getDestCode())
                .eq(query.getDestPrefix() != null, JdhRegionFeeConfigPo::getDestPrefix, query.getDestPrefix())
                .eq(JdhRegionFeeConfigPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhRegionFeeConfigPo::getUpdateTime);
        List<JdhRegionFeeConfigPo> poList = jdhRegionFeeConfigPoMapper.selectList(queryWrapper);
        return JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(poList);
    }

    /**
     * 清除
     * @param entity
     * @return
     */
    @Override
    public int clean(JdhRegionFeeConfig entity) {
        LambdaQueryWrapper<JdhRegionFeeConfigPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtil.isNotBlank(entity.getDestPrefix()), JdhRegionFeeConfigPo::getDestPrefix, entity.getDestPrefix());
        queryWrapper.eq(StringUtil.isNotBlank(entity.getDestCode()), JdhRegionFeeConfigPo::getDestCode, entity.getDestCode());
        return jdhRegionFeeConfigPoMapper.delete(queryWrapper);
    }

    /**
     * 批量保存
     * @param regionFeeConfigList
     * @return
     */
    @Override
    public Integer batchSaveRegionFeeConfig(List<JdhRegionFeeConfig> regionFeeConfigList) {
        List<JdhRegionFeeConfigPo> regionFeeConfigPoList = JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfigPoList(regionFeeConfigList);
        return jdhRegionFeeConfigPoMapper.batchSaveRegionFeeConfig(regionFeeConfigPoList);
    }
}
