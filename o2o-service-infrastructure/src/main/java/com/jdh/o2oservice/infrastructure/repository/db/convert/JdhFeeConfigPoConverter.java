package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhContrastSource;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfig;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FeeConfig;
import com.jdh.o2oservice.export.support.dto.FeeConfigExcelRowRecord;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAreaFeeConfigPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhContrastSourcePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhFeeConfigPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhRegionFeeConfigPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @InterfaceName:JdhFeeConfigPoConverter
 * @Description:
 * @Author: wangyu1387
 * @Date: 2024/1/9 15:17
 * @Vserion: 1.0
 **/
@Mapper
public interface JdhFeeConfigPoConverter {

    JdhFeeConfigPoConverter INSTANCE = Mappers.getMapper(JdhFeeConfigPoConverter.class);

    /**
     * convert to JdhContrastSourcePo
     *
     * @param
     * @return
     */
    JdhFeeConfigPo convertToJdhFeeConfigPo(FeeConfig FeeConfig);

    FeeConfig convertPo2FeeConfig(JdhFeeConfigPo jdhFeeConfigPo);


    JdhAreaFeeConfig convertPo2JdhAreaFeeConfig(JdhAreaFeeConfigPo jdhAreaFeeConfigPo);

    JdhAreaFeeConfigPo convertPo2JdhAreaFeeConfigPo(JdhAreaFeeConfig jdhAreaFeeConfig);

    List<JdhAreaFeeConfig> convertPo2JdhAreaFeeConfigList(List<JdhAreaFeeConfigPo> jdhAreaFeeConfigPoList);

    List<JdhAreaFeeConfigPo> convertPo2JdhAreaFeeConfigPoList(List<JdhAreaFeeConfig> jdhAreaFeeConfigList);

    @Mapping(target = "provinceCode", source = "provinceId")
    @Mapping(target = "cityCode", source = "cityId")
    @Mapping(target = "countyCode", source = "countyId")
    @Mapping(target = "townCode", source = "townId")
    @Mapping(target = "holidayFee", source = "holiday")
    @Mapping(target = "immediatelyFee", source = "immediately")
    JdhAreaFeeConfig convertPo2SaveJdhAreaFeeConfig(FeeConfigExcelRowRecord feeConfigExcelRowRecord);

    JdhRegionFeeConfig convertPo2RegionFeeConfig(JdhRegionFeeConfigPo jdhRegionFeeConfigPo);

    List<JdhRegionFeeConfig> convertPo2RegionFeeConfig(List<JdhRegionFeeConfigPo> poList);


    @Mapping(target = "destCode", source = "keyValue")
    @Mapping(target = "destName", source = "keyDesc")
    @Mapping(target = "destPrefix", source = "cacheKeyPrefix")
    JdhRegionFeeConfig convertPo2RegionFeeConfig(JdhAreaFeeConfig jdhAreaFeeConfig);

    @Mapping(target = "destCode", source = "keyValue")
    @Mapping(target = "destName", source = "keyDesc")
    @Mapping(target = "destPrefix", source = "sheetName")
    JdhRegionFeeConfig convertPo2RegionFeeConfig(FeeConfigExcelRowRecord feeConfigExcelRowRecord);

    List<JdhRegionFeeConfigPo> convertPo2RegionFeeConfigPoList(List<JdhRegionFeeConfig> jdhRegionFeeConfigList);

}
