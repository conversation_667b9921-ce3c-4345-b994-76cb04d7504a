package com.jdh.o2oservice.infrastructure.repository.es;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseFull;
import com.jdh.o2oservice.core.domain.medpromise.query.CompositeMedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.es.JdMedicalPromiseEsRepository;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description:
 * @Author: wangpengfei144
 * @Date: 2024/6/9
 */
@Component
@Slf4j
public class JdMedicalPromiseEsRepositoryImpl implements JdMedicalPromiseEsRepository {

    /**
     * defaultIndexName
     */
    private final static String defaultIndexName = "jd_order_full_index";

    /**
     * indexName
     */
    private static String indexName = "jd_order_full_index";
    /**
     * typeName
     */
    private final static String typeName = "doc";


    @Autowired
    private EsClientFactoryHealthcare esClientFactoryHealthcare;

    /**
     * 分页查询
     * @param medicalPromiseEsQuery
     * @return
     */
    public PageDto<MedicalPromiseFull> labQueryPage(MedicalPromiseEsQuery medicalPromiseEsQuery){
        int pageSize = medicalPromiseEsQuery.getPageSize();
        int pageNum = medicalPromiseEsQuery.getPageNum();
        BoolQueryBuilder qb = this.buildSearchQuery(medicalPromiseEsQuery);
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setQuery(qb)
                .setFrom((pageNum - 1) * pageSize)
                .setSize(pageSize).setSearchType(SearchType.DFS_QUERY_THEN_FETCH);
        LinkedHashMap<String, SortOrder> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("checkTime", SortOrder.DESC);
        linkedHashMap.put("appointmentStartTime", SortOrder.DESC);
        for (Map.Entry<String, SortOrder> entry : linkedHashMap.entrySet()) {
            requestBuilder.addSort(entry.getKey(), entry.getValue());
        }
        requestBuilder.addSort("medicalPromiseId", SortOrder.DESC);
        log.info("JdMedicalPromiseEsRepositoryImpl->labQueryPage 搜索语句：" + requestBuilder.toString());
        SearchResponse sResponse = requestBuilder.get();
        return convert2JdOrderFullPage(sResponse,pageNum,pageSize);
    }

    /**
     * @param medicalPromiseEsQuery
     * @return
     */
    @Override
    public Long queryQuickCheckCount(MedicalPromiseEsQuery medicalPromiseEsQuery) {
        PageDto<MedicalPromiseFull> medicalPromiseFullPageDto = labQueryPage(medicalPromiseEsQuery);
        return medicalPromiseFullPageDto.getTotalCount();
    }

    @Override
    public PageDto<MedicalPromiseFull> pageCompositeMedicalPromise(CompositeMedicalPromiseEsQuery query) {
        int pageSize = query.getPageSize();
        int pageNum = query.getPageNum();

        /**
         * 构建查询条件
         */
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termQuery("providerId", query.getProviderId()));
        boolQueryBuilder.must(QueryBuilders.termQuery("laboratoryStationId", query.getStationId()));
        if (Objects.nonNull(query.getAppointmentBeginTime()) && Objects.nonNull(query.getAppointmentEndTime())){
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("appointmentStartTime");
            builder.gte(query.getAppointmentBeginTime().getTime());
            builder.lte(query.getAppointmentEndTime().getTime());
            boolQueryBuilder.filter(builder);
        }
        if (CollectionUtil.isNotEmpty(query.getMedicalPromiseStatus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", query.getMedicalPromiseStatus()));
        }

        // workStatus处理
        if (query.getMatchNullWorkStatus() && CollectionUtils.isNotEmpty(query.getWorkStatus())){
            BoolQueryBuilder innerBool = QueryBuilders.boolQuery();
            List<QueryBuilder> should = innerBool.should();
            should.add(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("workStatus")));
            should.add(QueryBuilders.termsQuery("workStatus", query.getWorkStatus()));
            boolQueryBuilder.must(innerBool);

        }else if(CollectionUtils.isNotEmpty(query.getWorkStatus())){
            boolQueryBuilder.must(QueryBuilders.termsQuery("workStatus", query.getWorkStatus()));
        }

        // shipStatus处理
        if(CollectionUtils.isNotEmpty(query.getShipStatus())){
            boolQueryBuilder.must(QueryBuilders.termsQuery("shipStatus", query.getShipStatus()));
        }


        if (StringUtils.isNotBlank(query.getSpecimenCode())){
            boolQueryBuilder.must(QueryBuilders.termQuery("specimenCode", query.getSpecimenCode()));
        }

        if (StringUtils.isNotBlank(query.getServiceItemName())){
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("serviceItemName",query.getServiceItemName()));
        }

        // 收样超时
        if (Objects.equals(query.getIsReceiveTimeout(), Boolean.TRUE)){
            boolQueryBuilder.must(QueryBuilders.termQuery("waitingTestTimeOutStatus", YnStatusEnum.YES.getCode()));
        }else if(Objects.equals(query.getIsReceiveTimeout(), Boolean.FALSE)){
            boolQueryBuilder.must(QueryBuilders.termQuery("waitingTestTimeOutStatus", YnStatusEnum.NO.getCode()));
        }

        // 出报告超时
        if (Objects.equals(query.getIsReportTimeout(), Boolean.TRUE)){
            boolQueryBuilder.must(QueryBuilders.termQuery("testingTimeOutStatus", YnStatusEnum.YES.getCode()));
        }else if(Objects.equals(query.getIsReportTimeout(), Boolean.FALSE)){
            boolQueryBuilder.must(QueryBuilders.termQuery("testingTimeOutStatus", YnStatusEnum.NO.getCode()));
        }


        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setQuery(boolQueryBuilder)
                .setFrom((pageNum - 1) * pageSize)
                .setSize(pageSize).setSearchType(SearchType.DFS_QUERY_THEN_FETCH);
        LinkedHashMap<String, SortOrder> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("appointmentStartTime", SortOrder.DESC);
        for (Map.Entry<String, SortOrder> entry : linkedHashMap.entrySet()) {
            requestBuilder.addSort(entry.getKey(), entry.getValue());
        }
        requestBuilder.addSort("medicalPromiseId", SortOrder.DESC);
        log.info("JdMedicalPromiseEsRepositoryImpl->pageCompositeMedicalPromise 搜索语句：" + requestBuilder.toString());
        SearchResponse sResponse = requestBuilder.get();
        return convert2JdOrderFullPage(sResponse,pageNum,pageSize);
    }

    /**
     * 分页查询
     *
     * @param medicalPromiseEsQuery
     * @return
     */
    @Override
    public MedicalPromiseFull labQueryDetail(MedicalPromiseEsQuery medicalPromiseEsQuery) {
        int pageSize = 1;
        BoolQueryBuilder qb = QueryBuilders.boolQuery();
        if (null != medicalPromiseEsQuery.getMedicalPromiseId()) {
            qb.must(QueryBuilders.termQuery("medicalPromiseId", medicalPromiseEsQuery.getMedicalPromiseId()));
        }
        if (medicalPromiseEsQuery.getProviderId() != null) {
            qb.must(QueryBuilders.termQuery("providerId", medicalPromiseEsQuery.getProviderId()));
        }

        if (StringUtil.isNotEmpty(medicalPromiseEsQuery.getLaboratoryStationId())) {
            TermQueryBuilder checkTerm = QueryBuilders.termQuery("laboratoryStationId", medicalPromiseEsQuery.getLaboratoryStationId());
            qb.must(checkTerm);
        }
        SearchRequestBuilder requestBuilder = esClientFactoryHealthcare.getClient().prepareSearch(indexName).setTypes(typeName)
                .setQuery(qb)
                .setFrom(0)
                .setSize(pageSize).setSearchType(SearchType.DFS_QUERY_THEN_FETCH);
        LinkedHashMap<String, SortOrder> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("checkTime", SortOrder.DESC);
        linkedHashMap.put("appointmentStartTime", SortOrder.DESC);
        for (Map.Entry<String, SortOrder> entry : linkedHashMap.entrySet()) {
            requestBuilder.addSort(entry.getKey(), entry.getValue());
        }
        requestBuilder.addSort("medicalPromiseId", SortOrder.DESC);
        log.info("JdMedicalPromiseEsRepositoryImpl->labQueryDetail 搜索语句：" + requestBuilder.toString());
        SearchResponse sResponse = requestBuilder.get();
        List<MedicalPromiseFull> dtoList = convert2JdOrderFullList(sResponse);
        if (CollUtil.isEmpty(dtoList)) {
            return null;
        }
        return dtoList.get(0);
    }


    /**
     * 组装查询ES的参数
     *
     * @param appointment
     * @return
     */
    private BoolQueryBuilder buildSearchQuery(MedicalPromiseEsQuery appointment) {
        log.info("PhyexamintionAppointInfoEsServiceImpl -> buildSearchQuery start, appointment={}", appointment);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (appointment == null) {
            return boolQueryBuilder;
        }

        if (StringUtils.isNotBlank(appointment.getVerticalCode())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("verticalCode", appointment.getVerticalCode()));
        }

        if (Objects.nonNull(appointment.getAppointmentStartTime()) && Objects.nonNull(appointment.getAppointmentEndTime())){
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("appointmentStartTime");

//            boolQueryBuilder.must(QueryBuilders.rangeQuery("appointmentStartTime").gte(appointment.getAppointmentStartTime().getTime()));
//            boolQueryBuilder.must(QueryBuilders.rangeQuery("appointmentStartTime").lte(appointment.getAppointmentEndTime().getTime()));
            builder.gte(appointment.getAppointmentStartTime().getTime());
            builder.lte(appointment.getAppointmentEndTime().getTime());
            boolQueryBuilder.filter(builder);
        }

        if (CollectionUtil.isNotEmpty(appointment.getStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("medicalPromiseStatus", appointment.getStatusList()));
        }

        if (null != appointment.getMedicalPromiseId()) {
            boolQueryBuilder.must(QueryBuilders.termQuery("medicalPromiseId", appointment.getMedicalPromiseId()));
        }

        if (CollectionUtil.isNotEmpty(appointment.getOrderStatusNotInList())){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("medicalPromiseStatus", appointment.getOrderStatusNotInList()));

        }
        if (CollectionUtil.isNotEmpty(appointment.getShipStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("shipStatus", appointment.getShipStatusList()));
        }
        if (CollectionUtil.isNotEmpty(appointment.getNotInShipStatusList())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("shipStatus", appointment.getNotInShipStatusList()));
        }

        if (null != appointment.getReportStatus()) {
            boolQueryBuilder.must(QueryBuilders.termQuery("reportStatus", appointment.getReportStatus()));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(appointment.getSpecimenCode())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("specimenCode", appointment.getSpecimenCode()));
        }
        if (appointment.getProviderId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("providerId", appointment.getProviderId()));
        }

        if (appointment.getCheckStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("checkStatus", appointment.getCheckStatus()));
        }
        if (StringUtil.isNotEmpty(appointment.getLaboratoryStationId())) {
            TermQueryBuilder checkTerm = QueryBuilders.termQuery("laboratoryStationId", appointment.getLaboratoryStationId());
            boolQueryBuilder.must(checkTerm);
        }

        if (StringUtil.isNotBlank(appointment.getServiceItemName())){
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("serviceItemName",appointment.getServiceItemName()));
        }

        if (StringUtil.isNotBlank(appointment.getFlowCode())){
            boolQueryBuilder.must(QueryBuilders.termQuery("flowCode",appointment.getFlowCode()));
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(appointment.getSubStatusList())){
            boolQueryBuilder.must(QueryBuilders.termsQuery("subStatus", appointment.getSubStatusList()));
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(appointment.getNotInSubStatusList())){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("subStatus", appointment.getNotInSubStatusList()));
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(appointment.getInSubStatusList())){
            boolQueryBuilder.must(QueryBuilders.termsQuery("subStatus", appointment.getInSubStatusList()));
        }

        if (!Boolean.TRUE.equals(appointment.getHeartSmart())){
            buildReportTimeOutSearchQuery(appointment, boolQueryBuilder);
            buildReportCheckTimeOutSearchQuery(appointment, boolQueryBuilder);
        }else {
            //        // 收样超时
            if (appointment.getReportCheckTimeOutStatus() != null && appointment.getReportCheckTimeOutStatus() == 1){
                boolQueryBuilder.must(QueryBuilders.termQuery("waitingTestTimeOutStatus", YnStatusEnum.YES.getCode()));
            }else if(appointment.getReportCheckTimeOutStatus() != null && appointment.getReportCheckTimeOutStatus() == 0){
                boolQueryBuilder.must(QueryBuilders.termQuery("waitingTestTimeOutStatus", YnStatusEnum.NO.getCode()));
            }

            // 出报告超时
            if (appointment.getReportTimeOutStatus() != null && appointment.getReportTimeOutStatus() == 1 ){
                boolQueryBuilder.must(QueryBuilders.termQuery("testingTimeOutStatus", YnStatusEnum.YES.getCode()));
                // 质检单超时忽略
                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("verticalCode", Lists.newArrayList(ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode())));
            }else if(appointment.getReportTimeOutStatus() != null && appointment.getReportTimeOutStatus() == 0 ){
                boolQueryBuilder.must(QueryBuilders.termQuery("testingTimeOutStatus", YnStatusEnum.NO.getCode()));
            }
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(appointment.getNotInWorkStatusList())){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("workStatus", appointment.getNotInWorkStatusList()));
        }
        if (CollectionUtil.isNotEmpty(appointment.getWorkStatusList())){
            boolQueryBuilder.must(QueryBuilders.termsQuery("workStatus", appointment.getWorkStatusList()));
        }


        log.info("ES##PhyexamintionAppointInfoEsServiceImpl -> buildSearchQuery,boolQueryBuilder={}", boolQueryBuilder.toString());
        return boolQueryBuilder;
    }

    /**
     * 组装查询ES的参数
     *
     * @param appointment
     * @return
     */
    private void buildReportTimeOutSearchQuery(MedicalPromiseEsQuery appointment, BoolQueryBuilder boolQueryBuilder) {
        if (appointment.getReportTimeOutStatus() != null && appointment.getReportTimeOutStatus() == 0 ) {
            // 存在报告时效字段,查询符合条件数据
            BoolQueryBuilder innerBool = new BoolQueryBuilder();
            //实际检测时长小于预计检测时长
//            RangeQueryBuilder reportIntervalTime = QueryBuilders.rangeQuery("reportIntervalTime").lt("inspectDuration");
            ScriptQueryBuilder painless = QueryBuilders.scriptQuery(
                    new Script(ScriptType.INLINE, "painless",
                            "if(doc['reportIntervalTime'].size()==0 || doc['inspectDuration'].size()==0){return true;}"+
                            "return doc['reportIntervalTime'].value <= doc['inspectDuration'].value;",
                            Collections.emptyMap())
            );
            List<QueryBuilder> innerShould = innerBool.should();
            innerShould.add(painless);
            // 不存在报告时效字段,查询符合条件数据
            BoolQueryBuilder secondInnerBool = new BoolQueryBuilder();
            RangeQueryBuilder checkTime = QueryBuilders.rangeQuery("expiredCheckTime").gt(new Date().getTime());
//            RangeQueryBuilder checkTime = QueryBuilders.rangeQuery("checkTime").lt("expiredCheckTime");
//             ScriptQueryBuilder painless2 = QueryBuilders.scriptQuery(
//                    new Script(ScriptType.INLINE, "painless",
//                            "if(doc['checkTime'].size()==0 || doc['expiredCheckTime'].size()==0){return true;}"+
//                            "return doc['checkTime'].value.getMillis() <= doc['expiredCheckTime'].value.getMillis();",
//                            Collections.emptyMap())
//            );
            secondInnerBool.must(checkTime);
            ExistsQueryBuilder reportIntervalTimeExist = QueryBuilders.existsQuery("reportIntervalTime");
            secondInnerBool.mustNot(reportIntervalTimeExist);
            innerShould.add(secondInnerBool);
            boolQueryBuilder.must(innerBool);
        } else if (appointment.getReportTimeOutStatus() != null && appointment.getReportTimeOutStatus() == 1 ) {
            // 存在报告时效字段,查询符合条件数据
            BoolQueryBuilder innerBool = new BoolQueryBuilder();
            //实际检测时长大于预计检测时长
//            RangeQueryBuilder reportIntervalTime = QueryBuilders.rangeQuery("reportIntervalTime").gte("inspectDuration");
              ScriptQueryBuilder painless = QueryBuilders.scriptQuery(
                    new Script(ScriptType.INLINE, "painless",
                            "if(doc['reportIntervalTime'].size()==0 || doc['inspectDuration'].size()==0){return false;}"+
                            "return doc['reportIntervalTime'].value > doc['inspectDuration'].value;",
                            Collections.emptyMap())
            );
            List<QueryBuilder> innerShould = innerBool.should();
            innerShould.add(painless);
            // 不存在报告时效字段,查询符合条件数据
            BoolQueryBuilder secondInnerBool = new BoolQueryBuilder();
            RangeQueryBuilder checkTime = QueryBuilders.rangeQuery("expiredCheckTime").lte(new Date().getTime());
//            RangeQueryBuilder checkTime = QueryBuilders.rangeQuery("checkTime").gt("expiredCheckTime");
//             ScriptQueryBuilder painless2 = QueryBuilders.scriptQuery(
//                    new Script(ScriptType.INLINE, "painless",
//                            "if(doc['checkTime'].size()==0 || doc['expiredCheckTime'].size()==0){return false;}"+
//                            "return doc['checkTime'].value.getMillis() > doc['expiredCheckTime'].value.getMillis();",
//                            Collections.emptyMap())
//            );
            secondInnerBool.must(checkTime);
            ExistsQueryBuilder reportIntervalTimeExist = QueryBuilders.existsQuery("reportIntervalTime");
            secondInnerBool.mustNot(reportIntervalTimeExist);
            innerShould.add(secondInnerBool);
            boolQueryBuilder.must(innerBool);
        }
    }

    /**
     * 组装查询ES的参数
     *
     * @param appointment
     * @return
     */
    private void buildReportCheckTimeOutSearchQuery(MedicalPromiseEsQuery appointment, BoolQueryBuilder boolQueryBuilder) {
        if (appointment.getReportCheckTimeOutStatus() != null && appointment.getReportCheckTimeOutStatus() == 0 ) {
            // 存在报告时效字段,查询符合条件数据
            BoolQueryBuilder innerBool = new BoolQueryBuilder();
//            RangeQueryBuilder betweenDeliveryToReportTime = QueryBuilders.rangeQuery("betweenDeliveryToReportTime").lt("inspectDuration");
             ScriptQueryBuilder painless = QueryBuilders.scriptQuery(
                    new Script(ScriptType.INLINE, "painless",
                            "if(doc['betweenDeliveryToReportTime'].size()==0 || doc['inspectDuration'].size()==0){return false;}"+
                            "return doc['betweenDeliveryToReportTime'].value <= doc['inspectDuration'].value;",
                            Collections.emptyMap())
            );
            List<QueryBuilder> innerShould = innerBool.should();
            innerShould.add(painless);
            // 不存在报告时效字段,查询符合条件数据
            BoolQueryBuilder secondInnerBool = new BoolQueryBuilder();
            RangeQueryBuilder deliveryStoreTime = QueryBuilders.rangeQuery("expiredCheckTime").gt(new Date().getTime());
//            RangeQueryBuilder deliveryStoreTime = QueryBuilders.rangeQuery("deliveryStoreTime").lt("expiredCheckTime");
//             ScriptQueryBuilder painless2 = QueryBuilders.scriptQuery(
//                    new Script(ScriptType.INLINE, "painless",
//                            "if(doc['deliveryStoreTime'].size()==0 || doc['expiredCheckTime'].size()==0){return true;}"+
//                            "return doc['deliveryStoreTime'].value.getMillis() <= doc['expiredCheckTime'].value.getMillis();",
//                            Collections.emptyMap())
//            );
            secondInnerBool.must(deliveryStoreTime);
            ExistsQueryBuilder betweenDeliveryToReportTimeExist = QueryBuilders.existsQuery("betweenDeliveryToReportTime");
            secondInnerBool.mustNot(betweenDeliveryToReportTimeExist);
            innerShould.add(secondInnerBool);
            boolQueryBuilder.must(innerBool);
        } else if (appointment.getReportCheckTimeOutStatus() != null && appointment.getReportCheckTimeOutStatus() == 1 ) {
            // 存在报告时效字段,查询符合条件数据
            BoolQueryBuilder innerBool = new BoolQueryBuilder();
//            RangeQueryBuilder betweenDeliveryToReportTime = QueryBuilders.rangeQuery("betweenDeliveryToReportTime").gte("inspectDuration");
              ScriptQueryBuilder painless = QueryBuilders.scriptQuery(
                    new Script(ScriptType.INLINE, "painless",
                            "if(doc['betweenDeliveryToReportTime'].size()==0 || doc['inspectDuration'].size()==0){return false;}"+
                            "return doc['betweenDeliveryToReportTime'].value> doc['inspectDuration'].value;",
                            Collections.emptyMap())
            );
            List<QueryBuilder> innerShould = innerBool.should();
            innerShould.add(painless);
            // 不存在报告时效字段,查询符合条件数据
            BoolQueryBuilder secondInnerBool = new BoolQueryBuilder();
            RangeQueryBuilder deliveryStoreTime = QueryBuilders.rangeQuery("expiredCheckTime").lte(new Date().getTime());
//            RangeQueryBuilder deliveryStoreTime = QueryBuilders.rangeQuery("deliveryStoreTime").gt("expiredCheckTime");
//   ScriptQueryBuilder painless2 = QueryBuilders.scriptQuery(
//                    new Script(ScriptType.INLINE, "painless",
//                            "if(doc['deliveryStoreTime'].size()==0 || doc['expiredCheckTime'].size()==0){return false;}"+
//                            "return doc['deliveryStoreTime'].value.getMillis() > doc['expiredCheckTime'].value.getMillis();",
//                            Collections.emptyMap())
//            );
            secondInnerBool.must(deliveryStoreTime);
            ExistsQueryBuilder betweenDeliveryToReportTimeExist = QueryBuilders.existsQuery("betweenDeliveryToReportTime");
            secondInnerBool.mustNot(betweenDeliveryToReportTimeExist);
            innerShould.add(secondInnerBool);
            boolQueryBuilder.must(innerBool);
        }
    }

    private PageDto<MedicalPromiseFull> convert2JdOrderFullPage(SearchResponse response, int pageNum, int pageSize) {
        PageDto<MedicalPromiseFull> resultPage = new PageDto<>();
        resultPage.setPageNum(pageNum);
        resultPage.setPageSize(pageSize);
        List<MedicalPromiseFull> dtoList = convert2JdOrderFullList(response);
        resultPage.setTotalCount(response.getHits().totalHits);
        long totalPage = response.getHits().totalHits % pageSize == 0 ? response.getHits().totalHits / pageSize : response.getHits().totalHits / pageSize + 1 ;
        resultPage.setTotalPage(totalPage);
        resultPage.setList(dtoList);
        return resultPage;
    }

    private List<MedicalPromiseFull> convert2JdOrderFullList(SearchResponse response) {
        List<MedicalPromiseFull> resList = Lists.newArrayList();
        for (SearchHit hit : response.getHits()) {
            String json = hit.getSourceAsString();
            MedicalPromiseFull res = JSON.parseObject(json, MedicalPromiseFull.class);
            res.setId(hit.getId());
            resList.add(res);
        }
        return resList;
    }
}
