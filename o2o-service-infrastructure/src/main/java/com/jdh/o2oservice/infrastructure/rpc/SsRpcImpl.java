package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.ship.SsRpc;
import com.jdh.o2oservice.core.domain.support.ship.dto.GetSsUserAccountDto;
import com.jdh.o2oservice.ext.ship.reponse.ShunFengResponse;
import com.jdh.o2oservice.ext.ship.reponse.SsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * @ClassName SsRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/1/3 5:11 PM
 * @Version 1.0
 **/
@Service
@Slf4j
public class SsRpcImpl implements SsRpc {

    @Value("${shansong.config.clientId}")
    private String clientId;

    @Value("${shansong.config.shopId}")
    private String shopId;

    @Value("${shansong.config.accessToken}")
    private String accessToken;

    @Value("${shansong.config.url}")
    private String url;


    /**
     * 查询闪送余额
     * @return
     */
    @Override
    @LogAndAlarm
    public GetSsUserAccountDto getUserAccountDto(){
        String  methodUrl = "/openapi/merchants/v5/getUserAccount";

        Map map = new HashMap();
        map.put("clientId",clientId);
        map.put("shopId",shopId);
        map.put("timestamp",System.currentTimeMillis());
        map.put("sign",this.sign(accessToken,map));

        return this.processInvoke(methodUrl,map, GetSsUserAccountDto.class);
    }

    /**
     * 生成sign签名
     * @param s
     * @param param
     * @return
     */
    private  String sign(String s, Map param) {
        StringBuilder sb = new StringBuilder();
        sb.append(s).append("clientId").append(param.get("clientId"));
        if (Objects.nonNull(param.get("data"))) {
            sb.append("data").append(param.get("data"));
        }
        sb.append("shopId").append(param.get("shopId"))
                .append("timestamp").append(param.get("timestamp"));
        String aa = bytesToMD5(sb.toString().getBytes(StandardCharsets.UTF_8));
        return aa;
    }

    private String bytesToMD5(byte[] input) {
        String md5str = null;
        try {
            //创建一个提供信息摘要算法的对象，初始化为md5算法对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            //计算后获得字节数组
            byte[] buff = md.digest(input);
            //把数组每一字节换成16进制连成md5字符串
            md5str = bytesToHex(buff);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5str.toUpperCase();
    }

    //把字节数组转成16进位制数
    private String bytesToHex(byte[] bytes) {
        StringBuffer md5str = new StringBuffer();
        //把数组每一字节换成16进制连成md5字符串
        int digital;
        for (int i = 0; i < bytes.length; i++) {
            digital = bytes[i];
            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString();
    }

    /**
     * 统一请求发送
     * @param methodUrl
     * @param paramMap
     * @param cls
     * @return
     * @param <T>
     */
    private <T> T processInvoke(String methodUrl, Map<String,Object> paramMap, Class<T> cls) {
        String requestUrl = url.concat(methodUrl);
        log.info("开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(paramMap));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        log.info("requestUrl={}", requestUrl);
        String httpResponse = sendPost(requestUrl,paramMap);
        log.info("httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        SsResponse response = JSON.parseObject(httpResponse, new TypeReference<SsResponse>() {
        });
        log.info("response={}", JSON.toJSONString(response));
        if (Objects.equals(response.getStatus(), 200)) {
            Object result = response.getData();
            log.info("result={}", JSON.toJSONString(result));
            return JSON.parseObject(JSON.toJSONString(result), cls);
        } else {
            log.error("[ShunFengRpcImpl->processResponse],闪送接口访问返回异常!");
            throw new BusinessException(BusinessErrorCode.CUSTOM_ERROR_CODE.formatDescription(response.getMsg()));
        }
    }

    /**
     * 使用httpClient发送请求
     * @param url
     * @param params
     * @return
     */
    public static String sendPost(String url, Map<String,Object> params) {
        String response = null;
        try {
            List<NameValuePair> pairs = null;
            if (params != null && !params.isEmpty()) {
                pairs = new ArrayList<NameValuePair>(params.size());
                for (String key : params.keySet()) {
                    pairs.add(new BasicNameValuePair(key, params.get(key).toString()));
                }
            }
            CloseableHttpClient httpClient = null;
            CloseableHttpResponse httpResponse = null;
            try {
                httpClient = HttpClients.createDefault();
                HttpPost httppost = new HttpPost(url);
                if (pairs != null && pairs.size() > 0) {
                    httppost.setEntity(new UrlEncodedFormEntity(pairs, "UTF-8"));
                }
                httpResponse = httpClient.execute(httppost);
                response = EntityUtils
                        .toString(httpResponse.getEntity());
                System.out.println(response);
            } finally {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (httpResponse != null) {
                    httpResponse.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }
}
