package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.common.util.Money;
import com.jd.fce.orb.domain.CancelRequest;
import com.jd.fce.orb.domain.CancelResponse;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.order.sdk.domain.param.ClientInfo;
import com.jd.vtp.client.outer.api.VtpOrderService;
import com.jd.vtp.client.outer.api.VtpRefundService;
import com.jd.vtp.client.outer.co.*;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.RpcBusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.trade.bo.RefundRequestBO;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundDetail;
import com.jdh.o2oservice.core.domain.trade.rpc.VtpOrderInfoRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * VtpOrderInfoRpcImpl 交易域rpc实现
 *
 * <AUTHOR>
 * @version 2024/3/1 16:14
 **/
@Slf4j
@Service
public class VtpOrderInfoRpcImpl implements VtpOrderInfoRpc {

    @Autowired(required = false )
    private VtpOrderService vtpOrderService;

  @Autowired(required = false )
    private VtpRefundService vtpRefundService;

    private static final String APP_ID = "jdh-o2o-service";
    private static final String TOKEN = "cacc0e88-ef37-4eb3-a3a7-c4d40b4b03a6";

    /**
     * 修订中台订单的完成状态
     *
     * @param orderId 单号
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.VtpOrderInfoRpcImpl.reviseOrderFinishState")
    public Boolean reviseOrderFinishState(Long orderId) {
        log.info("VtpOrderInfoRpcImpl -> reviseOrderFinishState orderId={}", orderId);
        VtpProtocolCO vtpProtocolCO = getVtpProtocolCO();
        try {
            log.info("VtpOrderInfoRpcImpl -> reviseOrderFinishState vtpProtocolCO={}", JsonUtil.toJSONString(vtpProtocolCO));
            OrderCompleteRequestCO orderCompleteRequestCO = new OrderCompleteRequestCO();
            orderCompleteRequestCO.setErpOrderId(orderId);
            VtpResult<OrderCompleteResponseCO> result = vtpOrderService.updateOrderComplete(vtpProtocolCO, orderCompleteRequestCO);
            if(Objects.nonNull(result) && result.isSuccess()){
                return Boolean.TRUE;
            }
            log.info("VtpOrderInfoRpcImpl -> reviseOrderFinishState response={}", JsonUtil.toJSONString(result));
            return Boolean.FALSE;
        }catch (com.jd.medicine.base.common.exception.BusinessException e){
            log.error("VtpOrderInfoRpcImpl -> reviseOrderFinishState business fail", e);
            throw e;
        }catch (Throwable e){
            log.error("VtpOrderInfoRpcImpl -> reviseOrderFinishState error", e);
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * vip申请退款
     * @param detail
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.VtpOrderInfoRpcImpl.refund")
    public Boolean refund(JdOrderRefundDetail detail) {
        log.info("TradeOrderRefundRpcImpl -> appointmentRefund start, JdOrderRefundDetail={},",  JsonUtil.toJSONString(detail));
        try{
            RefundParamCO refundParamCO = getRefundParamCO(detail);
            log.info("TradeOrderRefundRpcImpl -> appointmentRefund start, refundParamCO={},",  JsonUtil.toJSONString(refundParamCO));
            VtpResult result = vtpRefundService.refund(refundParamCO);
            log.info("TradeOrderRefundRpcImpl -> appointmentRefund end, VtpResult={}", JsonUtil.toJSONString(result));
            if(null != result && result.isSuccess()){
                log.info("TradeOrderRefundRpcImpl -> appointmentRefund success, JdOrderRefundDetail={}", detail);
                return true;
            }
            return false;
        }catch(Throwable e){
            log.error("TradeOrderRefundRpcImpl -> appointmentRefund exception, JdOrderRefundDetail={},errMessage={}", detail, e);
            return false;
        }
    }

    /**
     * VTP更新订单履约状态接口
     * @param order
     * @return
     */
    @Override
    public Boolean updatePerformanceStatus(JdOrder order) {
        log.info("VtpOrderInfoRpcImpl -> updatePerformanceStatus start, order={},",  JsonUtil.toJSONString(order));
        try{
            VtpProtocolCO vtpProtocolCO = getVtpProtocolCO();
            UpdatePerformanceStatusRequestCO performanceStatusRequestCO = new UpdatePerformanceStatusRequestCO();
            //todo
            performanceStatusRequestCO.setUpdateStatus(null);
            performanceStatusRequestCO.setErpOrderId(order.getOrderId());
            log.info("VtpOrderInfoRpcImpl -> updatePerformanceStatus start, vtpProtocolCO={}, performanceStatusRequestCO={}",  JsonUtil.toJSONString(vtpProtocolCO), JsonUtil.toJSONString(performanceStatusRequestCO));
            VtpResult<Void> result = vtpOrderService.updatePerformanceStatus(vtpProtocolCO, performanceStatusRequestCO);
            log.info("VtpOrderInfoRpcImpl -> updatePerformanceStatus end, VtpResult={}", JsonUtil.toJSONString(result));
            if(null != result && result.isSuccess()){
                log.info("VtpOrderInfoRpcImpl -> updatePerformanceStatus success, order={}", order);
                return true;
            }
            return false;
        }catch(Throwable e){
            log.error("VtpOrderInfoRpcImpl -> updatePerformanceStatus exception, order={}", order, e);
            return false;
        }
    }

    private VtpProtocolCO getVtpProtocolCO(){
        VtpProtocolCO vtpProtocolCO = new VtpProtocolCO();
        vtpProtocolCO.setToken(TOKEN);
        vtpProtocolCO.setAppId(APP_ID);
        vtpProtocolCO.setSource(2);
        return vtpProtocolCO;
    }

    private RefundParamCO getRefundParamCO(JdOrderRefundDetail jdOrderRefundDetail){
        RefundParamCO refundParamCO = new RefundParamCO();
        refundParamCO.setOrderId(jdOrderRefundDetail.getOrderId());
        refundParamCO.setUniqueUuid(jdOrderRefundDetail.getTransactionNum());
        refundParamCO.setIsHalfRefund(true);
        refundParamCO.setRefundMoney(new Money(jdOrderRefundDetail.getRefundAmount()));
        refundParamCO.setUserPin(jdOrderRefundDetail.getUserPin());
        refundParamCO.setAppId(APP_ID);
        return refundParamCO;
    }
}
