package com.jdh.o2oservice.core.domain.product.model;
import com.jdh.o2oservice.base.model.Identifier;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class JdhRegionFeeConfigIdentifier implements Identifier {

    /**
     * 地区费项配置id
     */
    private Long regionFeeConfigId;

    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(regionFeeConfigId);
    }
}
