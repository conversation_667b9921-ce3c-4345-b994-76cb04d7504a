package com.jdh.o2oservice.core.domain.product.repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhRegionFeeConfigQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JdhRegionFeeConfigRepository extends Repository<JdhRegionFeeConfig, JdhRegionFeeConfigIdentifier> {

    /**
     * 保存
     * @param jdhRegionFeeConfig
     * @return
     */
    int save(JdhRegionFeeConfig jdhRegionFeeConfig);

    /**
     * 更新
     * @param jdhRegionFeeConfig
     * @return
     */
    int update(JdhRegionFeeConfig jdhRegionFeeConfig);

    /**
     * 分页查询
     * @param jdhRegionFeeConfigQuery
     * @return
     */
    Page<JdhRegionFeeConfig> queryPageRegionFeeConfig(JdhRegionFeeConfigQuery jdhRegionFeeConfigQuery);

    /**
     * 列表
     * @param jdhRegionFeeConfigQuery
     * @return
     */
    List<JdhRegionFeeConfig> findList(JdhRegionFeeConfigQuery jdhRegionFeeConfigQuery);

    /**
     * 清除
     * @param entity
     * @return
     */
    int clean(JdhRegionFeeConfig entity);

    /**
     * 批量保存
     * @param regionFeeConfigList
     * @return
     */
    Integer batchSaveRegionFeeConfig(List<JdhRegionFeeConfig> regionFeeConfigList);

    /**
     * 删除
     * @param entity
     * @return
     */
    int remove(JdhRegionFeeConfig entity);
}
