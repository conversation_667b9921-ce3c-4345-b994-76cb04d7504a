package com.jdh.o2oservice.core.domain.product.repository.query;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * 地区费项配置
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhRegionFeeConfigQuery implements Serializable {

    /**
     * 地区费项配置id
     */
    private Long regionFeeConfigId;

    /**
     * 费项配置文件id
     */
    private String feeConfigId;

    /**
     * 业务身份: 1 骑手检测 2 护士检测 3 护士护理
     */
    private Integer serviceType;

    /**
     * 渠道id 比如C端(1010645803)、互医(1020410783)
     */
    private String channelId;

    /**
     * 目标地址code
     */
    private String destCode;

    /**
     * 等同jimdb缓存前缀
     */
    private String destPrefix;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;
}
