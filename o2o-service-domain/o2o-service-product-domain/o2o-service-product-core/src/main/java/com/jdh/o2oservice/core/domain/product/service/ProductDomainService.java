package com.jdh.o2oservice.core.domain.product.service;

import com.jdh.o2oservice.core.domain.product.bo.*;
import com.jdh.o2oservice.core.domain.product.bo.vo.CommentUgcListResult;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuItemRel;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuRel;
import com.jdh.o2oservice.core.domain.product.model.ProductServiceGoods;
import com.jdh.o2oservice.core.domain.product.repository.query.ProductServiceGoodsQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.PriceInfoResponseBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.SkuMaterialBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.VideoMarkResultBO;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.product.dto.ProductPurchasePriceDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName ProductDomainService
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 15:57
 **/
public interface ProductDomainService {

    /**
     * 查询服务套餐列表
     *
     * @param serviceGoodsQuery
     * @return
     */
    List<ProductServiceGoods> queryServiceGoodsList(ProductServiceGoodsQuery serviceGoodsQuery);

    /**
     * 获取用户地址
     *
     * @param userPin
     * @param addressId
     * @return
     */
    UserAddressDetailBO getUserLastAddress(String userPin, Long addressId);


    ProductDefaultSkuBO getProductDefaultSkuDTO(RpcSkuBO crsSkuBO, PriceInfoResponseBO priceInfoResponse,
                                                boolean selfFlag, JdhSku jdhSku, Map<String, ProductPurchasePriceDto> productPurchasePriceMap, Set<String> skuFreeFeeList, boolean spuFlag);

    /**
     * 根据商品信息，价格信息和判断标识获取商品默认SKU业务对象
     *
     * @param selfFlag          自营标志，表示商品是否为自营商品定商品的SKU信息
     * @return ProductDefaultSkuBO 返回封装了默认SKU信息的业务对象
     */

    List<ProductDefaultSkuBO> getProductDefaultSkuDTOS(List<JdhSkuRel> jdhSkuRels, Map<String, RpcSkuBO> skuInfo, Boolean selfFlag, PriceInfoResponseBO priceInfoResponseBO, Set<String> skuFreeFeeList
            , Map<String, ProductPurchasePriceDto> productPurchasePriceMap, Map<String, List<JdhStationDto>> skuStationMap, Boolean spuFlag);

    List<ProductCarouselFileBO> skuMaterialBO2Dto(SkuMaterialBO skuMaterialBO, VideoMarkResultBO videoMarkResultBO);

    List<ProductDetailCustomIconConfigBO> getCustomConfigButtons(String envType, Map<String, String> properties);

    Map<String, Map<String, String>> getShare(String skuNo, List<ProductCarouselFileBO> productCarouselFileBOS, ProductDefaultSkuBO productDefaultSkuBO, Map<String, String> properties);

    ProductDetailBottomBannerBO getProductDetailBottomBannerDTO(Integer buyLimitType);

    ProductInfoBO getProductInfoBO(boolean noStation, boolean login, UserAddressDetailBO userAddressDetail, JdhSku jdhSku);

    EvaluateInfoBO getEvaluateInfo(String skuNo);

    /**
     *
     * @param uuid
     * @param userPin
     * @param skuNo
     * @return
     */
    SkuQuesttion4Result getSkuQuestionList(String uuid,String userPin,String skuNo);

    EvaluateInfoBO buildEvaluateInfoDTO(CommentUgcListResult commentUgcListResult);
}