package com.jdh.o2oservice.core.domain.product.model;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 地区费项配置
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhRegionFeeConfig implements Aggregate<JdhRegionFeeConfigIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 地区费项配置id
     */
    private Long regionFeeConfigId;

    /**
     * 费项配置文件id
     */
    private String feeConfigId;

    /**
     * 业务身份: 1 骑手检测 2 护士检测 3 护士护理
     */
    private Integer serviceType;

    /**
     * 渠道id 比如C端(1010645803)、互医(1020410783)
     */
    private String channelId;

    /**
     * 省地区code
     */
    private String provinceCode;

    /**
     * 市地区code
     */
    private String cityCode;

    /**
     * 县地区code
     */
    private String countyCode;

    /**
     * 乡镇code
     */
    private String townCode;

    /**
     * 省地区
     */
    private String provinceName;

    /**
     * 市地区
     */
    private String cityName;

    /**
     * 县地区
     */
    private String countyName;

    /**
     * 乡镇
     */
    private String townName;

    /**
     * 目标地址code
     */
    private String destCode;

    /**
     * 目标地址名称
     */
    private String destName;

    /**
     * 等同jimdb缓存前缀
     */
    private String destPrefix;

    /**
     * 上门费
     */
    private String onSiteFee;

    /**
     * 立即上门加价
     */
    private String immediatelyFee;

    /**
     * 节假日
     */
    private String holidayFee;

    /**
     * 升级护士费
     */
    private String upgrageAngelFee;

    /**
     * 护士上门sku集合
     */
    private String upgrageSkuList;

    /**
     * 夜间上门费
     */
    private String nightDoorFee;

    /**
     * 高峰时段服务费
     */
    private String peakServiceFee;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    /**
     * 等同jimdb缓存前缀描述
     */
    private String destPrefixDesc;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PRODUCT;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return null;
    }

    @Override
    public Integer version() {
        return null;
    }

    @Override
    public void versionIncrease() {

    }

    @Override
    public JdhRegionFeeConfigIdentifier getIdentifier() {
        return null;
    }
}
