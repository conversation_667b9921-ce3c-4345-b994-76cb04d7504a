package com.jdh.o2oservice.core.domain.angel.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelExtendQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepPageQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 服务者repository
 * @Author: zhangxiaojie17
 * @Date: 2024/4/18
**/
public interface AngelRepository extends Repository<JdhAngel, JdhAngelIdentifier> {

    /**
     * @Description: 查询服务者列表
     * @param query
     * @Return: java.util.List<com.jdh.o2oservice.core.domain.angel.model.JdhAngel>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/21
    **/
    Page<JdhAngel> page(JdhAngelRepPageQuery query);

    /**
     * @Description: 查询服务者分页列表
     * @param query
     * @Return: java.util.List<com.jdh.o2oservice.core.domain.angel.model.JdhAngel>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/21
     **/
    Page<JdhAngel> findPageOnlyAngel(JdhAngelRepPageQuery query);

    /**
     * @Description: 更新服务者人员标签
     * @param jdhAngel
     * @Return: int
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
    **/
    int updateAngelJobNature(JdhAngel jdhAngel);

    /**
     * @Description: 更新服务者接单状态
     * @param jdhAngel
     * @Return: int
     * @Author: zhangxiaojie17
     * @Date: 2024/5/2
    **/
    int updateAngelTakeOrderStatus(JdhAngel jdhAngel);

    /**
     * @Description: 根据唯一id查询护士信息(服务者id or 互医护士id)
     * @param jdhAngelRepQuery
     * @Return: com.jdh.o2oservice.core.domain.angel.model.JdhAngel
     * @Author: zhangxiaojie17
     * @Date: 2024/4/23
    **/
    JdhAngel queryByUniqueId(JdhAngelRepQuery jdhAngelRepQuery);

    /**
     * 利用电话或IdCard 查服务者列表
     * @param jdhAngelRepQuery
     * @return
     */
    List<JdhAngel> queryByPhoneOrIdCard(JdhAngelRepQuery jdhAngelRepQuery);

    /**
     * @Description: 批量绑定服务者站长信息
     * @param entity
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/24
    **/
    int batchBindStationMaster(JdhAngelBindStationMasterEntity entity);

    /**
     * @Description: 批量解绑服务者站长信息
     * @param entity
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/24
    **/
    int batchUnbindStationMaster(JdhAngelBindStationMasterEntity entity);

    /**
     * @Description: 服务者批量绑定服务站
     * @param entity
     * @Return: int
     * @Author: zhangxiaojie17
     * @Date: 2024/4/28
    **/
    int batchBindStation(JdhAngelBindStationEntity entity);

    /**
     * @Description: 服务者批量解绑服务站
     * @param entity
     * @Return: int
     * @Author: zhangxiaojie17
     * @Date: 2024/4/28
    **/
    int batchUnBindStation(JdhAngelBindStationEntity entity);

    /**
     * @Description: 查询服务者详情
     * @param query
     * @Return: com.jdh.o2oservice.core.domain.angel.model.JdhAngel
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
    **/
    JdhAngel queryAngelDetail(JdhAngelRepQuery query);

    /**
     * @Description: 查询服务者列表信息
     * @param query
     * @Return: java.util.List<com.jdh.o2oservice.core.domain.angel.model.JdhAngel>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/29
    **/
    List<JdhAngel> findList(JdhAngelRepQuery query);

    /**
     * @Description: 根据服务站id查询服务者列表信息
     * @param query
     * @Return: java.util.List<com.jdh.o2oservice.core.domain.angel.model.JdhAngel>
     * @Author: zhangxiaojie17
     * @Date: 2024/5/8
    **/
    List<JdhAngel> queryByStationId(JdhAngelRepQuery query);

    /**
     * @Description: 刪除所有服务者信息
     * @param
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/6
    **/
    void removeAllAngel(Long angelId);

    /**
     * @Description: 保存服务者信息
     * @param jdhAngel
     * @Return: java.lang.Long
     * @Author: zhangxiaojie17
     * @Date: 2024/5/10
    **/
    void saveAngel(JdhAngel jdhAngel);

    /**
     * 聚合查询机构下人员总量
     * @param jdProviderIds jdProviderIds
     * @return list
     */
    List<Map<String, Object>> countByJdhProviderIdList(Set<Long> jdProviderIds);

    /**
     * 查询证件号
     *
     * @param query
     * @return
     */
    List<JdhAngel> queryByIdCard(JdhAngelRepQuery query);

    /**
     * 绑定护士到机构
     * @param jdhAngel model
     * @return count
     */
    int bindJdhProvider(JdhAngel jdhAngel);

    /**
     * 解绑护士机构
     * @param jdhAngel model
     * @return count
     */
    int unBindJdhProvider(JdhAngel jdhAngel);

    /**
     * 解绑机构下全部护士
     * @param jdhAngel model
     * @return count
     */
    int unBindJdhProviderAllStaff(JdhAngel jdhAngel);

    /**
     * 护士端-我的-维护服务信息
     * @param jdhAngel
     * @return
     */
    Boolean updateServiceInfo(JdhAngel jdhAngel);

    /**
     * 查询服务者邀请码信息
     * @param query
     * @return
     */
    List<JdhAngelExtend> findAngelExtendList(JdhAngelExtendQuery query);

    /**
     * 新增护士扩展信息
     * @param jdhAngelExtend
     * @return
     */
    int insertAngelExtend(JdhAngelExtend jdhAngelExtend);

    /**
     * 新增或更新
     * @param jdhAngelExtend
     * @return
     */
    int saveAngelExtend(JdhAngelExtend jdhAngelExtend);

    /**
     * 查询服务者及职业
     *
     * @param query
     * @return
     */
    JdhAngel queryAngelWithProfession(JdhAngelRepQuery query);

    /**
     * 更新服务者状态
     *
     * @param jdhAngelOld
     * @return
     */
    int updateAuditProcessStatus(JdhAngel jdhAngelOld);
}
