package com.jdh.o2oservice.core.domain.angel.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description: 服务者职业信息
 * <AUTHOR>
 * @Date 2024/4/22
 * @Version V1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhAngelProfessionRel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 职业code
     */
    private String professionCode;

    /**
     * 职业名称
     */
    private String professionName;

    /**
     * 职级code
     */
    private String professionTitleCode;

    /**
     * 职级名称
     */
    private String professionTitleName;

    /**
     * 所属机构code
     */
    private String institutionCode;

    /**
     * 所属机构名称
     */
    private String institutionName;

    /**
     * 状态 0-未生效 1-已生效
     */
    private Integer status;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    /**
     * 有效标志 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 职业职级信息(每个职业对应一个职级信息)
     */
    //private JdhAngelProfessionTitleDict jdhAngelProfessionTitleDict;

    /**
     * 职业资质信息集合
     */
    private List<JdhAngelProfessionQualificationRel> jdhAngelProfessionQualificationRelList;

}
