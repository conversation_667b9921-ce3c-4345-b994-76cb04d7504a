package com.jdh.o2oservice.core.domain.medpromise.enums;

import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.medpromise.event.MedPromiseSettlementEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseAggregateEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseReportEventBody;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 服务商履约域事件类型枚举
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
public enum MedPromiseEventTypeEnum implements EventType {

    /**
     * 实验室履约单事件
     */
    /** 实验室履约单 - 创建 */
    MED_PROMISE_CREATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseCreate","实验室履约单创建", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 绑定条码 */
    MED_PROMISE_BIND_SPECIMEN_CODE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseBindSpecimenCode","实验室履约单绑定条码", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 提交信息 */
    MED_PROMISE_SUBMIT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSubmit","实验室履约单提交信息", MedicalPromiseEventBody.class),

    /** 直接调用实验室履约单提交信息,不修改我们这边的任何数据 */
    DIRECT_CALL_MED_PROMISE_SUBMIT(MedPromiseAggregateEnum.MED_PROMISE,  "directCallMedPromiseSubmit","直接调用实验室履约单提交信息", MedicalPromiseEventBody.class),
    /**实验室迁移*/
    MED_PROMISE_STATION_MIGRATION(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseStationMigration","实验室迁移", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 主动提交信息 */
    MED_PROMISE_SUBJECTIVE_SUBMIT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSubjectiveSubmit","实验室履约单主动提交信息", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 实验室收样 */
    MED_PROMISE_STATION_WAIT_RECEIVE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseStationWaitReceive","实验室履约单实验室待收样", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 实验室收样 */
    MED_PROMISE_STATION_RECEIVE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseStationReceive","实验室履约单实验室收样", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 实验室收样超时报警 */
    MED_PROMISE_STATION_RECEIVE_DELAY_ALARM(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseStationReceiveDelayAlarm","实验室收样超时报警", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 实验室收样主动推送 */
    MED_PROMISE_SUBJECTIVE_STATION_RECEIVE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSubjectiveStationReceive","实验室履约单实验室收样主动推送", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 已出报告 */
    MED_PROMISE_GENERATE_REPORT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseGenerateReport","实验室履约单已出报告", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 履约单下首次出报告 */
    MED_PROMISE_FIRST_GENERATE_REPORT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseFirstGenerateReport","实验室履约单首次出报告", MedicalPromiseAggregateEventBody.class),

    /** 实验室履约单 - 全部已出报告 */
    MED_PROMISE_ALL_COMPLETE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseAllComplete","实验室履约单全部完成", MedicalPromiseAggregateEventBody.class),

    /** 实验室履约单 - 完成 */
    MED_PROMISE_COMPLETE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseComplete","实验室履约单完成", MedicalPromiseAggregateEventBody.class),

    /** 实验室履约单 - 作废 */
    MED_PROMISE_INVALID(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseInvalid","实验室履约单作废", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 主动推送报告 */
    MED_PROMISE_PUSH_REPORT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromisePushReport","推送报告", MedicalPromiseReportEventBody.class),

    /** 检测单结算 */
    MED_PROMISE_SETTLEMENT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSettlement","实验室履约单结算", MedPromiseSettlementEventBody.class),

    /** sku+人纬度 - 作废 */
    MED_PROMISE_SKU_PATIENT_INVALID(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSkuPatientInvalid","实验室履约单患者sku作废", MedicalPromiseEventBody.class),

    /** sku+人纬度 - 冻结 */
    MED_PROMISE_SKU_PATIENT_FREEZE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSkuPatientFreeze","实验室履约单患者sku冻结", MedicalPromiseEventBody.class),

    /** 实验室履约单 - sku+人纬度全部已出报告 */
    MED_PROMISE_SKU_PATIENT_ALL_GENERATE_REPORT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSkuPatientGeneReport","实验室履约单患者SKU全部已出报告", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 人纬度全部已出报告 */
    MED_PROMISE_PATIENT_ALL_GENERATE_REPORT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromisePatientGeneReport","实验室履约单患者全部已出报告", MedicalPromiseEventBody.class),

    /** 人纬度 - 作废 */
    MED_PROMISE_PATIENT_INVALID(MedPromiseAggregateEnum.MED_PROMISE,  "medPromisePatientInvalid","实验室履约单患者作废", MedicalPromiseEventBody.class),

    /** 人纬度 - 冻结 */
    MED_PROMISE_PATIENT_FREEZE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromisePatientFreeze","实验室履约单患者冻结", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 冻结 */
    MED_PROMISE_FREEZE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseFreeze","实验室履约单冻结", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 判断是否发送全部报告已出 */
    MED_PROMISE_JUDGE_ALL_GENERATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseJudgeAllGenerate","实验室履约单冻结", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 实验室结算成功 */
    MED_PROMISE_SETTLEMENT_COMPLETE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSettlementComplete","实验室结算成功", MedPromiseSettlementEventBody.class),

    /** 实验室履约单 - 人+sku纬度完成 */
    MED_PROMISE_PATIENT_SERVICE_COMPLETE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromisePidServiceComplete","实验室履约单预约单纬度完成", MedicalPromiseEventBody.class),

    /**
     *
     */
    /** 实验室履约单 - 批量创建 */
    //MED_PROMISE_BATCH_CREATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseBatchCreate","实验室履约单批量创建", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 创建 */
    //MED_PROMISE_ALL_CREATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseAllCreate","实验室履约单全部创建完成", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 实验室收样 */
    //MED_PROMISE_STATION_ALL_RECEIVE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseStationAllReceive","实验室履约单实验室收样", MedicalPromiseEventBody.class),

    /** 实验室履约单 - 全部完成 */
    //MED_PROMISE_ALL_COMPLETE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseAllComplete","实验室履约单全部完成", MedicalPromiseEventBody.class),


    /** 重置报告状态 */
    RESET_REPORT_STATUS(MedPromiseAggregateEnum.MED_PROMISE,  "resetReportStatus","重置报告状态", MedicalPromiseEventBody.class),
    /** 重置条码 */
    RESET_SPECIMEN_CODE(MedPromiseAggregateEnum.MED_PROMISE,  "resetSpecimenCode","重置条码", MedicalPromiseEventBody.class),

    /** 实验室收样延时报警 */
    MED_PROMISE_STATION_RECEIVE_ALARM_DELAY(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseStationReceiveAlarmDelay","实验室收样延时报警", MedicalPromiseEventBody.class),
    /** 实验室指派 */
    MED_PROMISE_TARGET_STATION(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseTargetStation","指派实验室", MedicalPromiseEventBody.class),

    MED_PROMISE_WAITING_TESTING_ETA_UPDATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseWaitingTestEtaUpdate","实验室待收样eta变更", MedicalPromiseEventBody.class),

    MED_PROMISE_WAITING_TESTING_TIMEOUT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseWaitingTestTimeOut","实验室待收样超时", MedicalPromiseEventBody.class),

    MED_PROMISE_TESTING_ETA_UPDATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseTestingEtaUpdate","实验室检测eta变更", MedicalPromiseEventBody.class),

    MED_PROMISE_TESTING_TIMEOUT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseTestingTimeOut","实验室检测超时", MedicalPromiseEventBody.class),

    MED_PROMISE_LAB_DISTRIBUTE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseLabDistribute","实验室派发", MedicalPromiseEventBody.class),


    /** 实验室履约单 - 工具提交信息 */
    MED_PROMISE_TOOL_SUBMIT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseToolSubmit","实验室履约单提交信息", MedicalPromiseEventBody.class),

    /**
     * 出报告发送短信判断
     */
    MED_PROMISE_SMS(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSms","出报告发送短信判断", MedicalPromiseEventBody.class),

    /**
     * 出报告发送部分短信
     */
    MED_PROMISE_SMS_PART(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSmsPart","出报告发送部分短信", MedicalPromiseEventBody.class),

    /**
     * 出报告发送短信-全部已出
     */
    MED_PROMISE_SMS_ALL(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseSmsAll","出报告发送短信全部已出", MedicalPromiseEventBody.class),

    /**
     * 检测单上报异常处理事件类型
     */
    MED_PROMISE_ERROR_DEAL(MedPromiseAggregateEnum.MED_PROMISE,"medPromiseErrorDeal","检测单上报异常处理", MedicalPromiseEventBody.class),


    MED_PROMISE_CALL_BACK(MedPromiseAggregateEnum.MED_PROMISE,"medPromiseCallBack","实验室履约单回调", MedicalPromiseEventBody.class),

    MED_PROMISE_CALL_BACK_SYNC_STATION(MedPromiseAggregateEnum.MED_PROMISE,"medPromiseCallBackSyncStation","实验室履约单回调通知实验室", MedicalPromiseEventBody.class),
    /** 实验室履约单 - 报告审核  */
    MED_PROMISE_REPORT_AUDIT(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseReportAudit","实验室履约单报告审核", MedicalPromiseEventBody.class),


    MED_PROMISE_REPORT_AUDIT_FINISH(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseReportAuditFinish","实验室履约单报告审核完成", MedicalPromiseEventBody.class),

    /** 检测已完成 */
    MED_PROMISE_LAB_TEST_FINISH(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseLabTestFinish","检测完成", MedicalPromiseEventBody.class),

    /** 让步检测 */
    MED_PROMISE_USER_AGREE_CONCESSION_TEST(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseUserAgreeConcessionTest","用户同意让步检测", MedicalPromiseEventBody.class),

    /**
     * 质控单创建
     */
    MED_PROMISE_QUALITY_CREATE(MedPromiseAggregateEnum.MED_PROMISE,  "medPromiseQualityCreate","质控单创建", MedicalPromiseEventBody.class)

    ;


    /** 事件所属领域，用于区分 */
    private MedPromiseAggregateEnum aggregate;

    /** 事件名称 */
    private String code;

    /** 事件说明 */
    private String desc;

    private Class<?> bodyClass;

    /** */
    MedPromiseEventTypeEnum(MedPromiseAggregateEnum aggregate, String code, String desc, Class<?> bodyClazz) {
        this.aggregate = aggregate;
        this.code = code;
        this.desc = desc;
        this.bodyClass = bodyClazz;
    }
    /** */
    @Override
    public AggregateCode getAggregateType() {
        return aggregate;
    }
    /** */
    @Override
    public String getCode() {
        return code;
    }
    /** */
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }

    private static final Map<String, MedPromiseEventTypeEnum> EVENT_MAP = Maps.newHashMap();
    static {
        for (MedPromiseEventTypeEnum value : values()) {
            EVENT_MAP.put(value.code, value);
        }
    }

    public static MedPromiseEventTypeEnum getByCode(String code){
        if (StringUtils.isBlank(code)){
            return null;
        }
        return EVENT_MAP.get(code);

    }
}
