package com.jdh.o2oservice.core.domain.medpromise.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.health.xfyl.merchant.export.dto.AppointResultDTO;
import com.jd.health.xfyl.merchant.export.param.AppointmentParam;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.DeliveryTypeExpression;
import com.jdh.o2oservice.base.ducc.model.ShipTypeConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.bo.MedicalPromiseExtendBo;
import com.jdh.o2oservice.core.domain.medpromise.context.*;
import com.jdh.o2oservice.core.domain.medpromise.converter.MedicalPromiseDomainConvert;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedicalDeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedicalPromiseDispatchRuleKeyEnum;
import com.jdh.o2oservice.core.domain.medpromise.factory.MedDispatchProcessorFactory;
import com.jdh.o2oservice.core.domain.medpromise.model.*;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.DispatchRuleProcessor;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.rpc.MerchantRpcService;
import com.jdh.o2oservice.core.domain.medpromise.service.MedicalPromiseDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.TianSuanSettlementRpc;
import com.jdh.o2oservice.core.domain.support.reach.model.WriteLedgerOrder;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 履约检测单domain层impl
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Service
@Slf4j
public class MedicalPromiseDomainServiceImpl implements MedicalPromiseDomainService {

    /**
     * 实验室派单工厂类
     */
    @Autowired
    private MedDispatchProcessorFactory medDispatchProcessorFactory;

    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 商家端RPC
     */
    @Autowired
    private MerchantRpcService merchantRpcService;

    /**
     * 线程池工厂
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * dispatchStatemachine
     */
    @Resource
    private StateMachine<MedicalPromiseStatusEnum, MedPromiseEventTypeEnum, StateContext> medicalPromiseStatemachine;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * 天算
     */
    @Autowired
    private TianSuanSettlementRpc tianSuanSettlementRpc;

    /**
     * 缓存
     */
    @Resource
    private Cluster jimClient;
    /**
     * ducc
     */
    @Autowired
    private DuccConfig duccConfig;



    /**
     * 创建检测单
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createMedicalPromise(MedicalPromiseCreateContext context) {
        //任务幂等校验
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(context.getPromiseId()).build());
        if (CollectionUtils.isNotEmpty(medicalPromiseList)) {
            log.error("MedicalPromiseDomainServiceImpl -> createMedicalPromise idempotent check return false, context={}", JSON.toJSONString(context));
            return false;
        }

        //批量创建
        List<MedicalPromise> result = Lists.newArrayList();
        for (MedicalPromiseAppointmentPatient patient : context.getPatientList()) {
            if(CollectionUtils.isEmpty(patient.getServiceItems())){
                continue;
            }
            for (MedicalPromiseServiceItem serviceItem : patient.getServiceItems()) {
                //商品基础数据-服务项目数据
                Long id = generateIdFactory.getId();
                MedicalPromise medicalPromise = MedicalPromise.builder()
                        .verticalCode(context.getVerticalCode())
                        .serviceType(context.getServiceType())
                        .medicalPromiseId(id)
                        .promiseId(context.getPromiseId())
                        .voucherId(context.getVoucherId())
                        .serviceId(Objects.nonNull(serviceItem.getServiceId()) ? serviceItem.getServiceId().toString() : null)
                        .userPin(context.getUserPin())
                        .promisePatientId(patient.getPromisePatientId())
                        .status(MedicalPromiseStatusEnum.WAIT_COLLECTED.getStatus())
                        .specimenCode(serviceItem.getSpecimenCode())
                        .serviceItemId(Objects.nonNull(serviceItem.getItemId()) ? serviceItem.getItemId().toString() : null)
                        .serviceItemName(serviceItem.getItemName())
                        .createUser(context.getUserPin())
                        .updateUser(context.getUserPin())
                        .reportShowType(serviceItem.getReportShowType())
                        .build();

                // 条码不为空，已采集
                if (StringUtils.isNotBlank(medicalPromise.getSpecimenCode())) {
                    medicalPromise.setStatus(MedicalPromiseStatusEnum.COLLECTED.getStatus());
                }

                //扩展字段数据
                dealMedicalPromiseExt(medicalPromise, serviceItem.getSampleType());

                medicalPromiseRepository.save(medicalPromise);
                result.add(medicalPromise);
            }
        }
        context.setMedicalPromiseList(result);
        return true;
    }

    /**
     * 批量绑定条码
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchBindSpecimenCode(MedicalPromiseBatchBindSpecimenCodeContext context) {
        if (CollectionUtils.isEmpty(context.getSpecimenCodeList())) {
            return false;
        }
        List<MedicalPromise> result = Lists.newArrayListWithCapacity(context.getSpecimenCodeList().size());
        List<MedicalPromise> snapShot = Lists.newArrayListWithCapacity(context.getSpecimenCodeList().size());
        for (MedicalPromiseSpecimenCode specimenCode : context.getSpecimenCodeList()) {
            MedicalPromise snapshot = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(specimenCode.getMedicalPromiseId()).build());
            MedicalPromiseBindSpecimenCodeContext specimenCodeContext = new MedicalPromiseBindSpecimenCodeContext();
            specimenCodeContext.setVerticalCode(context.getVerticalCode());
            specimenCodeContext.setServiceType(context.getServiceType());
            specimenCodeContext.setSpecimenCode(specimenCode);
            specimenCodeContext.init(snapshot, MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE);

            MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(snapshot.getStatus());
            medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, specimenCodeContext.getTriggerCommand()), specimenCodeContext);
            //更新数据库,打测试标记
            MedicalPromise medicalPromise = specimenCodeContext.getMedicalPromise();
            medicalPromise.setIsTest(duccConfig.getTestSpecimenCodeConfig().isTest(medicalPromise.getStationId(),medicalPromise.getSpecimenCode())?1:0);
            medicalPromiseRepository.save(medicalPromise);
            result.add(specimenCodeContext.getMedicalPromise());
            snapShot.add(specimenCodeContext.getSnapshot());
        }
        context.setMedicalPromiseList(result);
        context.setSnapShotMedicalPromiseList(snapShot);
        return true;
    }

    /**
     * 实验室派单
     */
    @Override
    public List<Map<String, Set<String>>> storeDispatch(MedicalPromiseDispatchContext medicalPromiseDispatchContext) {
        log.info("MedicalPromiseDomainServiceImpl->storeDispatch,medicalPromiseDispatchContext ={}", com.jd.medicine.base.common.util.JsonUtil.toJSONString(medicalPromiseDispatchContext));
        //路由key
        String routeKey = MedicalPromiseDispatchRuleKeyEnum.getRouteKey(medicalPromiseDispatchContext.getBusinessModeCode());
        if (StringUtils.isBlank(routeKey)){
            throw new BusinessException(MedPromiseErrorCode.DISPATCH_LOGIC_NULL);
        }
        DispatchRuleProcessor dispatchRuleProcessor = medDispatchProcessorFactory.createDispatchRuleProcessor(MedDispatchProcessorFactory.createRouteKey(routeKey));
        if (Objects.isNull(dispatchRuleProcessor)){
            log.info("MedicalPromiseDomainServiceImpl->storeDispatch,route not found,routeKey={}",routeKey);
            throw new BusinessException(MedPromiseErrorCode.DISPATCH_LOGIC_NULL);
        }
        return dispatchRuleProcessor.storeDispatch(medicalPromiseDispatchContext);
    }

    /**
     * 提交预约
     * @param context m
     * @return r
     */
    @Override
    @LogAndAlarm
    public Boolean submitMedicalPromiseToStation(MedicalPromiseSubmitContext context) {
        MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(context.getSnapshot().getStatus());
        medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, context.getTriggerCommand()), context);
        log.info("submitMedicalPromiseToStation -> appointmentParam={}", JsonUtil.toJSONString(context.getAppointmentParam()));

        Map<String, Map<String, String>> serviceItemIdToKnight = duccConfig.getServiceItemIdToKnight();
        AppointmentParam appointmentParam = context.getAppointmentParam();
        if (serviceItemIdToKnight.containsKey(String.valueOf(appointmentParam.getChannelType()))){
            Map<String, String> stringStringMap = serviceItemIdToKnight.get(String.valueOf(appointmentParam.getChannelType()));
            appointmentParam.getServiceItemList().forEach(p->{
                if (Objects.equals(2289330932L,appointmentParam.getChannelType())){
                    if (stringStringMap.containsKey(p.getItemId())){
                        p.setItemName(stringStringMap.get(p.getItemId()));
                    }
                }else {
                    if (stringStringMap.containsKey(p.getItemId())){
                        p.setItemId(stringStringMap.get(p.getItemId()));
                    }
                }
            });
        }

        AppointResultDTO appointment = merchantRpcService.appointment(context.getAppointmentParam());
        log.info("submitMedicalPromiseToStation-> res={}", JsonUtil.toJSONString(appointment));
        if (Objects.isNull(appointment)){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SUBMIT_ERROR);
        }
        context.getMedicalPromise().setOuterId(appointment.getAppointmentNo());
        if(context.getDirectCall()==null||YnStatusEnum.NO.getCode().equals(context.getDirectCall())){
            //更新数据库
            medicalPromiseRepository.save(context.getMedicalPromise());
        }
        return true;
    }


    /**
     * 批量作废
     * @param batchInvalidContext
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean invalidMedicalPromiseBatch(MedicalPromiseBatchInvalidContext batchInvalidContext) {
        MedicalPromiseListQuery query = MedicalPromiseDomainConvert.INSTANCE.convert(batchInvalidContext);
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.listByPatientAndService(query);
        if (CollectionUtils.isEmpty(medicalPromiseList)) {
            return false;
        }

        log.info("MedicalPromiseApplicationImpl->freezeMedicalPromiseStatusBatch,medicalPromises={}", com.jd.medicine.base.common.util.JsonUtil.toJSONString(medicalPromiseList));
        medicalPromiseList.removeIf(p->Objects.equals(CommonConstant.ZERO,p.getYn()) && Objects.isNull(p.getMergeMedicalId()));
        log.info("MedicalPromiseApplicationImpl->freezeMedicalPromiseStatusBatch,medicalPromisesAfter={}", com.jd.medicine.base.common.util.JsonUtil.toJSONString(medicalPromiseList));
        if (CollectionUtils.isEmpty(medicalPromiseList)){
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
        List<Long> mergeIds = medicalPromiseList.stream().filter(p -> Objects.equals(CommonConstant.ZERO, p.getYn())).map(MedicalPromise::getMergeMedicalId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mergeIds)){
            List<MedicalPromise> mergePromiseList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(mergeIds).build());
//            medicalPromiseList.removeIf(p->Objects.equals(CommonConstant.ZERO,p.getYn()));
            medicalPromiseList.addAll(mergePromiseList);
        }

        List<MedicalPromise> result = Lists.newArrayListWithCapacity(medicalPromiseList.size());
        List<MedicalPromise> snapshot = Lists.newArrayListWithCapacity(medicalPromiseList.size());
        for (MedicalPromise medicalPromise : medicalPromiseList) {
            snapshot.add(medicalPromise);
            MedicalPromiseInvalidContext context = new MedicalPromiseInvalidContext();
            context.init(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_INVALID);
            MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(context.getSnapshot().getStatus());
            try {
                medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, context.getTriggerCommand()), context);
            }catch (Exception e){
                log.info("MedicalPromiseApplicationImpl->zuofeI,ERROR",e);
            }
            //更新数据库
            medicalPromiseRepository.save(context.getMedicalPromise());
            result.add(context.getMedicalPromise());
        }
        batchInvalidContext.setMedicalPromiseList(result);
        batchInvalidContext.setMedicalPromiseSnapShotList(snapshot);
        return true;
    }

    /**
     * 履约检测单收样
     *
     * @param context
     * @return
     */
    @Override
    public Boolean medicalPromiseCheck(MedicalPromiseCallbackContext context) {

        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(context.getMedicalPromiseId()).build());
        context.setVerticalCode(medicalPromise.getVerticalCode());
        context.setServiceType(medicalPromise.getServiceType());
        if (StringUtils.isBlank(medicalPromise.getOuterId())){
            medicalPromise.setOuterId(context.getOuterId());
        }
        context.init(medicalPromise,MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE);
        medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus());
        medicalPromise.setTestTime(new Date());
        medicalPromise.setTestStatus(CommonConstant.ONE);
        MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(context.getSnapshot().getStatus());
        medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, context.getTriggerCommand()), context);
        return medicalPromiseRepository.save(context.getMedicalPromise())>0;

    }

    /**
     * 推送结算
     *
     * @param context
     * @return
     */
    @Override
    public Boolean sendSettlementMessage(List<MedPromiseSettlementContext> context) {
        log.info("sendSettlementMessage -> context={}", JsonUtil.toJSONString(context));
        //遍历推送天算，按检测单一条条推


        List<String> black = duccConfig.getTianSuanSettlementStationBlack();

        for (MedPromiseSettlementContext settlementContext : context){

            if (black.contains(settlementContext.getMedicalPromise().getStationId())){
                continue;
            }

            String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.TIAN_SUAN_KEY,settlementContext.getMedicalPromiseId());
            String redisValue = jimClient.get(redisKey);
            if (StringUtils.isBlank(redisValue)){
                //推送天算
                WriteLedgerOrder rpcBO = new WriteLedgerOrder();
                rpcBO.setLedgerId(String.valueOf(settlementContext.getMedicalPromiseId()));
                rpcBO.setAppointmentId(settlementContext.getMedicalPromiseId());
                rpcBO.setUserPin(settlementContext.getUserPin());
                rpcBO.setOrderStartTime(settlementContext.getOrderStartTime());
                rpcBO.setVerifyTime(settlementContext.getVerifyTime());
                rpcBO.setActualPrice(settlementContext.getActualPrice());
                if (Boolean.TRUE.equals(duccConfig.getTestTianSuanMoney())){
                    rpcBO.setPay(BigDecimal.ZERO);
                }else {
                    rpcBO.setPay(settlementContext.getSettlementPrice());
                }
                rpcBO.setVenderId(settlementContext.getProviderId());
                rpcBO.setChannelRuleCode(settlementContext.getChannelRuleCode());
                rpcBO.setDirection(1);
                rpcBO.setOptType(1);
                rpcBO.setSettlementTimePoint("SETTLEMENT_TIME_POINT_1");
                rpcBO.setOrderStatus(1);
                rpcBO.setOperationType(1);
                log.info("sendSettlementMessage -> rpcBO={}", JsonUtil.toJSONString(rpcBO));
                tianSuanSettlementRpc.syncOrder(rpcBO);
            }
        }
        //推送ebs记支出
        return Boolean.TRUE;
    }

    /**
     * @param medicalPromiseBatchFinishContext
     * @return
     */
    @Override
    public Boolean medicalPromiseFinish(MedicalPromiseBatchFinishContext medicalPromiseBatchFinishContext) {
        List<MedicalPromise> snapMedicalPromises = medicalPromiseBatchFinishContext.getSnapMedicalPromises();
        List<MedicalPromise> medicalPromises = Lists.newArrayList();
        for (MedicalPromise medicalPromise : snapMedicalPromises){
            MedicalPromiseFinishContext medicalPromiseFinishContext = new MedicalPromiseFinishContext();
            medicalPromiseFinishContext.init(medicalPromise,MedPromiseEventTypeEnum.MED_PROMISE_COMPLETE);
            MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(medicalPromiseFinishContext.getSnapshot().getStatus());
            medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, medicalPromiseFinishContext.getTriggerCommand()), medicalPromiseFinishContext);
            int save = medicalPromiseRepository.save(medicalPromiseFinishContext.getMedicalPromise());
            if (save<=0){
                throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
            }
            medicalPromises.add(medicalPromiseFinishContext.getMedicalPromise());
        }
        medicalPromiseBatchFinishContext.setMedicalPromises(medicalPromises);
        return Boolean.TRUE;
    }

    /**
     * 检测单配送类型规则匹配
     *
     * @param medicalPromises
     * @param jdhVerticalBusiness
     */
    @Override
    @LogAndAlarm
    public List<MedicalPromise> medicalDeliveryTypeRuleMatch(List<MedicalPromise> medicalPromises, JdhVerticalBusiness jdhVerticalBusiness) {
        //运单配送规则配置
        ShipTypeConfig shipTypeConfig = JsonUtil.parseObject(duccConfig.getCanSelectShipTypeConfig(), ShipTypeConfig.class);
        List<DeliveryTypeExpression> stationDeliveryTypeExpressions = shipTypeConfig.getStationDeliveryTypeExpressions();
        List<MedicalPromise> medicalPromiseList = medicalPromises
                .stream()
                .map(item -> {
                    MedicalPromiseExtendBo extend = item.getExtend();
                    if (Objects.isNull(extend) || CollectionUtils.isEmpty(stationDeliveryTypeExpressions)) {
                        item.setDeliveryType(MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType());
                        return item;
                    }

                    //处理检测配送类型
                    Map<String, Object> ruleParam = Maps.newHashMap();
                    ruleParam.put("angelDelivery", extend.getAngelDelivery());
                    ruleParam.put("businessMode", jdhVerticalBusiness.getBusinessModeCode());
                    ruleParam.put("verticalCode", item.getVerticalCode());
                    for (DeliveryTypeExpression deliveryTypeExpression : stationDeliveryTypeExpressions) {
                        boolean hit = (boolean) AviatorEvaluator.compile(deliveryTypeExpression.getExpression(), true).execute(ruleParam);
                        if (hit) {
                            item.setDeliveryType(Integer.valueOf(deliveryTypeExpression.getDeliveryType()));
                        }
                    }
                    if (Objects.isNull(item.getDeliveryType())) {
                        item.setDeliveryType(MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType());
                    }

                    //距离写入扩展字段
                    item.dealDistanceExt();
                    return item;
                })
                .sorted(Comparator.comparing(MedicalPromise::getDistance))
                .collect(Collectors.toList());

        List<MedicalPromise> angelDeliveryList = medicalPromiseList.stream()
                .filter(item -> MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(item.getDeliveryType()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(angelDeliveryList)) {
            return medicalPromiseList;
        }

        String stationId = angelDeliveryList.get(0).getStationId();
        medicalPromiseList.forEach(item -> {
            if(!item.getStationId().equalsIgnoreCase(stationId)) {
                item.setDeliveryType(MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType());
            }
        });

        return medicalPromiseList;
    }
    @Override
    public Boolean delMedicalPromise(DelMedicalPromiseContext delMedicalPromiseContext) {
        MedicalPromise medicalPromise = new MedicalPromise();
        medicalPromise.setPromiseId(delMedicalPromiseContext.getPromiseId());
        return medicalPromiseRepository.remove(medicalPromise)>0;
    }


    /**
     * medicalPromiseReportContext
     *
     * @param medicalPromiseReportContext
     * @return
     */
    @Override
    public Boolean pushMedicalPromiseReport(MedicalPromiseReportContext medicalPromiseReportContext) {
        MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(medicalPromiseReportContext.getSnapshot().getStatus());
        log.info("MedicalPromiseDomainServiceImpl->pushMedicalPromiseReport,curStatus={}", com.jd.medicine.base.common.util.JsonUtil.toJSONString(curStatus));
        try {
            medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, medicalPromiseReportContext.getTriggerCommand()), medicalPromiseReportContext);
        }catch (Exception e){
            log.info("MedicalPromiseDomainServiceImpl->pushMedicalPromiseReport,状态错误，保存原状态",e);
        }
        MedicalPromise medicalPromise = medicalPromiseReportContext.getMedicalPromise();
        medicalPromise.setReportTime(medicalPromiseReportContext.getReportTime());
        medicalPromise.setTestFinishTime(new Date());
        if (Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(),medicalPromise.getStatus())
                && Objects.equals(MedicalPromiseSubStatusEnum.REPORT_CHECK_PASS.getSubStatus(),medicalPromise.getSubStatus())){
            medicalPromise.setReportStatus(CommonConstant.ONE);
        }


        int save = medicalPromiseRepository.save(medicalPromise);
        if (save <=0){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATUS_CHANGE_ERROR);
        }
        return Boolean.TRUE ;
    }

    /**
     * 处理检测扩展信息
     *
     * @param medicalPromise
     * @param sampleType
     */
    @Override
    public Boolean dealMedicalPromiseExt(MedicalPromise medicalPromise, Integer sampleType) {
        List<Integer> angelDeliverySampleConfig = duccConfig.getAngelDeliverySampleConfig();
        if(CollectionUtils.isEmpty(angelDeliverySampleConfig) || !angelDeliverySampleConfig.contains(sampleType)){
            medicalPromise.buildMedicalExt(sampleType, MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType());
            medicalPromise.setDeliveryType(MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType());
        }else {
            medicalPromise.buildMedicalExt(sampleType, MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType());
            medicalPromise.setDeliveryType(MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType());
        }
        return Boolean.TRUE;
    }
}
