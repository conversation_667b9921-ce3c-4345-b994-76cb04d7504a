package com.jdh.o2oservice.core.domain.medpromise.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 检测单es查询query
 * @Author: wangpengfei144
 * @Date: 2024/6/9
 */
@Data
public class MedicalPromiseEsQuery {

    /**
     * <pre>
     * 分页 页码
     * </pre>
     */
    private int pageNum = 1;
    /**
     * <pre>
     * 分页 页大小
     * </pre>
     */
    private int pageSize = 10;
    /**
     * 样本条码
     */
    private String specimenCode;
    /**
     * 供应商ID
     */
    private Long providerId;
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String laboratoryStationId;
    /**
     * <pre>
     * 项目名称
     * </pre>
     */
    private String serviceItemName;
    /**
     * 预约开始时间
     */
    private Date appointmentStartTime;
    /**
     * 预约结束时间
     */
    private Date appointmentEndTime;
    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private Long serviceItemId;
    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本扫码时间
     */
    private Integer reportTimeOutStatus;
    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本送达时间
     */
    private Integer reportCheckTimeOutStatus;
    /**
     * 快检综合状态筛选
     */
    private Integer compositeStatus;

    /**
     * 订单状态列表
     */
    private List<Integer> statusList;

    /**
     * 运单状态列表
     */
    private List<String> shipStatusList;


    private List<Integer> notInShipStatusList;
    /**
     * 到检状态 0未到检 1已到检 详见 CheckStatusEnum
     */
    private Integer checkStatus;
    /**
     * 报告状态列表
     */
    private Integer reportStatus;

    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本扫码时间
     */
    private String reportTimeOutStatusName;

    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本扫码时间
     */
    private String reportNotTimeOutStatusName;

    /**
     * 报告超时时间 出具报告与样本扫码时间
     */
    private Long reportTimeOut;

    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本送达时间
     */
    private String reportCheckTimeOutStatusName;

    /**
     * 报告检测超时状态 0-未超时 1-已超时 出具报告与样本送达时间
     */
    private String reportCheckNotTimeOutStatusName;

    /**
     * 报告超时时间 出具报告与样本送达时间
     */
    private Long reportCheckTimeOut;

    /**
     * 订单状态列表
     */
    private List<Integer> orderStatusNotInList;

    private Long medicalPromiseId;

    private Integer testStatus;

    private List<Integer> subStatusList;

    private List<Integer> notInSubStatusList;

    private List<Integer> workStatusList;

    private List<Integer> inSubStatusList;

    private List<Integer> notInWorkStatusList;

    private String flowCode;

    /**
     * 是否用新端
     */
    private Boolean heartSmart;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

}
