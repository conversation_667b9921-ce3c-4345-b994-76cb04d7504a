package com.jdh.o2oservice.core.domain.angelpromise.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName:JdhAngelTaskExtVo
 * @Description: 扩展值对象
 * @Author: yaoqinghai
 * @Date: 2024/4/18 20:45
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhAngelTaskExtVo {

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者性别
     */
    private Integer patientGender;

    /**
     * 电子签名图片
     */
    private Long electSignatureImg;

    /**
     * 就诊记录图片
     */
    private List<Long> visitRecordImg;

    /**
     * 知情同意书文件ID
     */
    private Long letterOfConsentFileId;

}
