package com.jdh.o2oservice.core.domain.angelpromise.context;

import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName:DeliverContext
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/24 02:22
 * @Vserion: 1.0
 **/
@Data
@Builder
public class DeliverContext {

    /**
     * 工单id
     */
    private String workId;

    /**
     * 实验室id
     */
    private String LaboratoryId;

    /**
     * 运单id
     */
    private String shipId;

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 经度
     */
    private Double angelLongitude;

    /**
     * 纬度
     */
    private Double angelLatitude;

    /**
     * 实验室经度
     */
    private Double laboratoryLongitude;

    /**
     * 实验室纬度
     */
    private Double laboratoryLatitude;

    /**
     * work
     */
    private AngelWork angelWork;
}
