package com.jdh.o2oservice.core.domain.angelpromise.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * AngelWorkTypeEnum
 *
 * <AUTHOR>
 * @date 2024-05-13 18:33
 */

@Getter
@AllArgsConstructor
public enum AngelWorkDeliveryTypeEnum {

    /**
     * 标准
     */
    STANDER_DELIVERY(0, "标准配送"),

    /**
     * 自配
     */
    SELF_DELIVERY(1, "自配送"),

    /**
     * 部分自配
     */
    ANY_SELF_DELIVERY(2, "部分自配送"),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

    /**
     * 根据类型查询枚举详情
     * @param type
     * @return
     */
    public static AngelWorkDeliveryTypeEnum getEnumByCode(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (AngelWorkDeliveryTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
