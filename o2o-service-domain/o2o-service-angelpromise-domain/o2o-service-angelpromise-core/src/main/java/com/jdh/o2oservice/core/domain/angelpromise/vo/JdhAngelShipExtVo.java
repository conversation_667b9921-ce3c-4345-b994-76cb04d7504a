package com.jdh.o2oservice.core.domain.angelpromise.vo;

import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkCreateShipExtCmd;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName:JdhAngelShipExtVo
 * @Description: 扩展值对象
 * @Author: yaoqinghai
 * @Date: 2024/4/18 20:45
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhAngelShipExtVo {

    /**
     * 运单备注
     */
    private String shipRemark;

    /**
     * 被服务人id
     */
    private List<ShipTask> shipTaskList;

    /**
     * 扩展信息
     */
    private AngelWorkCreateShipExtCmd angelWorkCreateShipExt;

    /**
     * 运力异常信息
     */
    private UavResult uavResult;


    private String angelStationId;//服务站id

    private List<Long> medicalPromiseIds;//检测单id



}
