package com.jdh.o2oservice.core.domain.user.auth.uim.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 9:37 上午
 * @Description:
 */
@Data
public class Uim2MenuBO implements Serializable {
    private static final long serialVersionUID = 3582883399528795024L;

    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 资源吗
     */
    private String resCode;
    /**
     * 链接
     */
    private String url;
    /**
     * 图标
     */
    private String icon;
    /**
     * 类型
     */
    private String target;
    /**
     * 父id
     */
    private Long parentId;
    /**
     * 父资源码
     */
    private String parentResCode;
    /**
     *
     */
    private String trackingCode;
    /**
     * desc
     */
    private String desc;
    /**
     * 子菜单数量
     */
    private Integer subMenuCount;
    /**
     * 子菜单列表
     */
    private List<Uim2MenuBO> subMenuList;
}
