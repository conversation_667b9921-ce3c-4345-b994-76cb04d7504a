package com.jdh.o2oservice.core.domain.user.auth.uim.bo;

import lombok.Data;

import java.util.Map;

@Data
public class Uim2DimRowBO {
    private String keyValue;
    private String presentationValue;
    private Boolean positive;
    private String operator;
    private String levelCode;
    private String primaryKeyVal;
    private String representKeyVal;
    private String parentLevelCode;
    private String parentPrimaryKeyVal;
    private Map<String, Object> extKeyValMap;
}