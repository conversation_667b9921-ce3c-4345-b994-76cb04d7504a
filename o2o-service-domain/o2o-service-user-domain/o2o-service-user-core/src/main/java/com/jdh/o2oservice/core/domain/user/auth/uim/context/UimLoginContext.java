package com.jdh.o2oservice.core.domain.user.auth.uim.context;

import lombok.Data;

/**
 * 用户登录信息上下文
 *
 * <AUTHOR>
 * @date 2023-10-07 11:01
 */
@Data
public class UimLoginContext {
    /**
     * 员工工号,工卡上的那串数字
     */
    private String personId;
    /**
     * 用户erp账号
     */
    private String pin;
    /**
     * 用户昵称
     */
    private String nick;
    /**
     * 用户所属部门编号
     */
    private String orgId;
    /**
     * 用户所属部门名称
     */
    private String orgName;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 租户编号
     */
    private String tenantCode;
    /**
     * 使用语言
     */
    private String lang;
    /**
     * 用户鉴权过期时间
     */
    private long expires;
    /**
     * 用户动态鉴权token
     */
    private String accessToken;
    /**
     * 用户登录设备ip
     */
    private String ip;
}
