package com.jdh.o2oservice.core.domain.report.model;

import lombok.Data;

import java.util.Date;

/**
 * 报告指标实体
 * <AUTHOR>
 * @date 2024-11-13 13:32
 */
@Data
public class MedicalReportIndicator {
    /**
     * 主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 报告id
     */
    private String reportId;

    /**
     * 实验室ID
     */
    private String stationId;

    /**
     * 用户省，京标
     */
    private Long patientProvince;

    /**
     * 用户市，京标
     */
    private Long patientCity;

    /**
     * 用户区，京标
     */
    private Long patientCounty;

    /**
     * 用户镇，京标
     */
    private Long patientTown;

    /**
     * 用户全地址
     */
    private String patientAddress;

    /**
     * 患者Id
     */
    private Long patientId;

    /**
     * 预约患者ID
     */
    private Long promisePatientId;

    /**
     * 指标id
     */
    private Long indicatorId;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 到检时间
     */
    private Date checkTime;

    /**
     * 检测项目信息
     */
    private String serviceItemId;

    /**
     * 指标范围
     */
    private String normalRangeValue;

    /**
     * 指标正常范围下限
     */
    private String normalRangeMin;

    /**
     * 指标正常范围上限
     */
    private String normalRangeMax;

    /**
     * 指标单位
     */
    private String unit;

    /**
     * 指标检查结果
     */
    private String indicatorResult;

    /**
     * 指标异常标记类型，未知：-1，正常：0，低于正常值：1，高于正常值：2，异常：3
     */
    private Integer abnormalMarkType;

    /**
     * 异常内容
     */
    private String abnormalContent;

    /**
     * 是否有效
     */
    private Boolean yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * ct值
     */
    private String ctValue;

}
