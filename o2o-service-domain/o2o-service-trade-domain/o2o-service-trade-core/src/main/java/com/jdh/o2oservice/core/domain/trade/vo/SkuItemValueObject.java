package com.jdh.o2oservice.core.domain.trade.vo;

import com.jdh.o2oservice.base.model.ValueObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * SkuItemValueObject sku 相关信息
 * <AUTHOR>
 * @version 2024/3/1 10:50
 **/
@Data
public class SkuItemValueObject implements ValueObject<SkuItemValueObject> {

    private static final long serialVersionUID = 6840163668775598394L;

    /**
     * 商品skuId
     *
     * @see #id
     */
    @Deprecated
    private String skuId;

    /**
     * Id 后续会替代skuId
     */
    private String id;

    /**
     * 商品名称
     *
     * @see #name
     */
    @Deprecated
    private String skuName;

    /**
     * 名称 后续会替代skuName
     */
    private String name;

    /**
     * 虚skuId
     */
    private String vskuId;

    /**
     * 套装id
     */
    private String suitPromoId;

    /**
     * 总价促销套装id
     */
    private String manSuitPromoId;

    /**
     * 商品skuUuid
     */
    private String skuUuid;

    /**
     * 购买数量(必传)
     */
    private Integer buyNum;

    /**
     * sku 图片
     */
    private String skuPic;

    /**
     * 商品金额
     */
    private String price;

    /**
     * 商品抵扣金额
     */
    private String discount;

    /**
     * 商品返现金额
     */
    private String reprice;

    /**
     * 商品类型
     */
    private Integer itemType;

    /**
     * 门店id(药急送必传)
     */
    private Long storeId;

    /**
     * 一级类目
     */
    private Integer firstCid;

    /**
     * 二级类目
     */
    private Integer secondCid;

    /**
     * 三级类目
     */
    private Integer thirdCid;

    /**
     * 商家id
     */
    private Long venderId;

    /**
     * 商家名称
     */
    private String venderName;

    /**
     * 商品重量
     */
    private BigDecimal weight;

    /**
     * 主sku绑定关系sku集合
     * 虚拟组套加垂车时, 需要传虚sku下的实sku信息
     */
    private List<SkuItemValueObject> relationSkuItemList;

    /**
     * 获取是否可以勾选。0未勾选，1选中
     */
    private Integer checkType;

    /**
     * 商品其他属性
     */
    private SkuItemFieldsValueObject skuItemFieldsVo;

    /**
     * 赠品集合
     */
    private List<SkuGiftItemValueObject> skuGiftItemVoList;

    /**
     * 促销列表
     */
    private List<SkuPromotionValueObject> promotionList;

    /**
     * 可选的促销列表
     */
    private List<SkuPromotionValueObject> selectPromotionList;

    /**
     * extInfos 扩展信息，包括处方标、处方信息、自营标等
     */
    private Map<String, Object> extInfoMap;

    // ------------------------------------ 以下非购物车属性
    /**
     * 库存状态
     */
    private String stockState;

    /**
     * sku唯一标识（在交易流程中）
     */
    private String skuObtainUUID;

    /**
     * 购物篮（子单的概念）的集合
     */
    private String basketId;

    /**
     * 包裹id
     */
    private String bundleId;

    /**
     * 整单的唯一标识
     */
    private String orderObtainUUID;

    /**
     * 附属促销信息
     */
    private ExtPromotionValueObject extPromotion;

    /**
     * 是否为赠品
     */
    private Boolean isGift;

    /**
     * 是否为自营
     */
    private Boolean isJd;

    /**
     * 是否选中
     */
    private Boolean isSelected = true;

    @Override
    public boolean sameValueAs(SkuItemValueObject other) {
        return false;
    }
}
