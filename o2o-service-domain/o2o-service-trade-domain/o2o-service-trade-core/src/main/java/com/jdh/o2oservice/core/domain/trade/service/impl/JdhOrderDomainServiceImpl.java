package com.jdh.o2oservice.core.domain.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.DiscountFeeConfig;
import com.jdh.o2oservice.base.ducc.model.DiscountFeeRule;
import com.jdh.o2oservice.base.ducc.model.fee.*;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.TimeIntervalIntersection;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.ServiceTypeNewEnum;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FixedSkuConfig;
import com.jdh.o2oservice.core.domain.support.feeConfig.repository.FixedSkuConfigRepository;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhServiceTypeCategoryRelation;
import com.jdh.o2oservice.core.domain.support.vertical.repository.JdhServiceTypeCategoryRelationRepository;
import com.jdh.o2oservice.core.domain.trade.bo.BusinessCodeConfig;
import com.jdh.o2oservice.core.domain.trade.bo.BusinessCodeExpressionConfig;
import com.jdh.o2oservice.core.domain.trade.context.CalcOrderServiceFeeContext;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderContext;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceFeeExtendEnum;
import com.jdh.o2oservice.core.domain.trade.model.AppointTimeServiceFeeDetail;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderServiceFeeInfo;
import com.jdh.o2oservice.core.domain.trade.model.ServiceFeeDetail;
import com.jdh.o2oservice.core.domain.trade.rpc.TradeInfoRpc;
import com.jdh.o2oservice.core.domain.trade.service.JdhOrderDomainService;
import com.jdh.o2oservice.core.domain.trade.vo.AddressInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.export.product.enums.ProductSaleChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:JdhOrderDomainService
 * @Description: 交易域订单模块
 * @Author: yaoqinghai
 * @Date: 2024/1/8 22:23
 * @Vserion: 1.0
 **/
@Slf4j
@Service
public class JdhOrderDomainServiceImpl implements JdhOrderDomainService {

    @Resource
    private DuccConfig duccConfig;

    @Autowired
    private FixedSkuConfigRepository fixedSkuConfigRepository;

    @Resource
    private JdhServiceTypeCategoryRelationRepository jdhServiceTypeCategoryRelationRepository;

    private static Map<String, Expression> expressionMap = Maps.newConcurrentMap();

    /**
     *
     */
    @Resource
    private TradeInfoRpc tradeInfoRpc;


    /**
     * 缓存
     */
    @Resource
    private Cluster jimClient;

    /**
     * tradeFeeCacheKey
     */
    private final String tradeFeeCacheKey = "jdh-o2o-service:trade:fee:{0}";

    /**
     * 获取业务身份编码
     *
     * @param jdOrderContext
     * @param serviceType
     * @return
     */
    @Override
    public String getVerticalCode(JdOrderContext jdOrderContext, String serviceType) {
        log.info("[TradeEventSubscriber->getVerticalCode],jdOrderContext={},serviceType={}", JSON.toJSONString(jdOrderContext), serviceType);

        //不考虑一单多品的情况
        List<JdOrderItem> jdOrderItemList = jdOrderContext.getJdOrderItemList();
        JdOrderItem jdOrderItem = jdOrderItemList.get(0);

        //获取业务渠道的配置
        BusinessCodeConfig jmiOrderUrlRedirectConfig = JSONUtil.toBean(duccConfig.getBusinessVerticalCodeConfig(), BusinessCodeConfig.class);
        log.info("[TradeEventSubscriber->getVerticalCode],jmiOrderUrlRedirectConfig={}", JSON.toJSONString(jmiOrderUrlRedirectConfig));

        Map<String, Object> matchChannelMap = Maps.newHashMap();
        matchChannelMap.put("cid1", jdOrderItem.getCid1());
        matchChannelMap.put("cid2", jdOrderItem.getCid2());
        matchChannelMap.put("cid3", jdOrderItem.getCid3());
        matchChannelMap.put("sendPay294", getSendPay294(jdOrderContext.getSendPay()));
        matchChannelMap.put("orderType", jdOrderContext.getOrderType());
        matchChannelMap.put("xfyl", getXfyl(jdOrderContext.getExtend()));
        matchChannelMap.put("serviceType", serviceType);
        matchChannelMap.put("orderTotalAmount", jdOrderContext.getJdOrder().getOrderTotalAmount());

        for (BusinessCodeExpressionConfig businessCodeExpressionConfig : jmiOrderUrlRedirectConfig.getBusinessCodeExpressionConfigList()) {
            String expressionStr = businessCodeExpressionConfig.getExpression();
            log.info("[JdhOrderDomainServiceImpl->getVerticalCode],expressionStr={}", expressionStr);
            /**
             * 参考文档https://github.com/killme2008/aviatorscript
             */
            Expression expression = expressionMap.get(expressionStr);
            if(Objects.isNull(expression)){
                expression = AviatorEvaluator.compile(expressionStr, true);
                expressionMap.put(expressionStr, expression);
            }

            Boolean executeResult = (Boolean) expression.execute(matchChannelMap);
            log.info("[JdhOrderDomainServiceImpl->getVerticalCode],executeResult={}", JSON.toJSONString(executeResult));
            if(executeResult){
                log.info("[JdhOrderDomainServiceImpl->getVerticalCode],execute true!");
                return businessCodeExpressionConfig.getVerticalCode();
            }
        }
        return null;
    }


    /**
     * 获取消费医疗标
     *
     * @return
     */
    private String getXfyl(String orderExtend) {
        if(StringUtils.isBlank(orderExtend)){
            return null;
        }
        JdOrderExtendVo jdOrderExtendVo = JSON.parseObject(orderExtend, JdOrderExtendVo.class);
        return jdOrderExtendVo.getXfyl();
    }

    /**
     * 获取sendpay294位
     *
     * @param sendPay
     * @return
     */
    private String getSendPay294(String sendPay) {
        if(StringUtils.isBlank(sendPay) || sendPay.length() < 294){
            return null;
        }
        return String.valueOf(sendPay.charAt(293));
    }

    /**
     * 获取serviceType
     *
     * @param jdOrderContext
     * @return
     */
    @Override
    public String getServiceType(JdOrderContext jdOrderContext) {
        //查询商品信息
        List<JdOrderItem> jdOrderItemList = jdOrderContext.getJdOrderItemList();
        if(CollectionUtils.isEmpty(jdOrderItemList)){
            throw new BusinessException(BusinessErrorCode.ORDER_LACK_SKU_INFO);
        }
        JdOrderItem jdOrderItem = jdOrderItemList.get(0);
        //查询商品服务类型
        JdhServiceTypeCategoryRelation categoryRelation = jdhServiceTypeCategoryRelationRepository.findCategoryRelation(jdOrderItem.getCid3(), NumConstant.NUM_3);
        if(Objects.isNull(categoryRelation)){
            throw new BusinessException(BusinessErrorCode.SKU_SERVICE_TYPE_NOT_EXIST);
        }
        return categoryRelation.getServiceType();
    }

    /**
     * 计算时段费用
     *
     * @param context              上下文
     * @param serviceFeeDetailList 服务费详细列表
     */
    private void calcTimePeriodFee(CalcOrderServiceFeeContext context,List<ServiceFeeDetail> serviceFeeDetailList){
        log.info("JdhOrderDomainServiceImpl -> calcTimePeriodFee, context={}", JSON.toJSONString(context));
        //时段费
        Map<String, TimePeriodFeeConfig> timePeriodFeeConfigMap= duccConfig.getTimePeriodFeeConfigMap();
        log.info("JdhOrderDomainServiceImpl.calcTimePeriodFee.timePeriodFeeConfigMap={}",JSON.toJSONString(timePeriodFeeConfigMap));
        TimePeriodFeeConfig timePeriodFeeConfig = timePeriodFeeConfigMap.get(context.getVerticalCode());
        if (Objects.isNull(timePeriodFeeConfig)){
            timePeriodFeeConfig=timePeriodFeeConfigMap.get("common");
        }
        BigDecimal holidayFee = new BigDecimal("0");
        // 法定节假日（一个订单记一笔） 法定节假日+20元
        if(context.getAppointmentTime() != null && context.getAppointmentTime().appointmentDayIsHoliday() != null
                && context.getAppointmentTime().appointmentDayIsHoliday()){
            holidayFee = timePeriodFeeConfig.getWorkTypeFee().getHoliday();
        }
        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(holidayFee)
                .type(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getType())
                .aggregateType(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getAggregateTypeEnum().getType())
                .aggregateSubType(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getAggregateTypeEnum().getSubType())
                .tips(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getCode()).build());

        // 即时/预约（一个订单记一笔）  即时预约+20元
        BigDecimal immediatelyFee = new BigDecimal("0");
        if(context.getAppointmentTime() != null && context.getAppointmentTime().getIsImmediately() != null
                && context.getAppointmentTime().getIsImmediately()){
            immediatelyFee = timePeriodFeeConfig.getAppointmentTimeTypeFee().getImmediately();
        }
        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(immediatelyFee)
                .type(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getType())
                .aggregateType(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getAggregateTypeEnum().getType())
                .aggregateSubType(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getAggregateTypeEnum().getSubType())
                .tips(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getCode()).build());
    }

    /**
     * 计算升级护士服务费用
     *
     * @param context              上下文
     * @param serviceFeeDetailList 服务费详细列表
     */
    private void calcTimegetUpgrageAngelFee(CalcOrderServiceFeeContext context,List<ServiceFeeDetail> serviceFeeDetailList){
        log.info("JdhOrderDomainServiceImpl -> calcTimegetUpgrageAngelFee, context={}", JSON.toJSONString(context));
        String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.TRADE_ACTION_SERVICE_UPGRADE_KEY, context.getUserPin());
        String serviceUpgradeSelectedStr = jimClient.get(cacheKey);
        if (context.getSearchServiceUpgrade() == null){
            context.setSearchServiceUpgrade(false);
        }
        log.info("JdhOrderDomainServiceImpl -> calcTimegetUpgrageAngelFee serviceUpgradeSelectedStr={},searchServiceUpgrade={}", serviceUpgradeSelectedStr, context.getSearchServiceUpgrade());
        if (StringUtils.isNotBlank(serviceUpgradeSelectedStr) || context.getSearchServiceUpgrade()){
            // 护士升级费
            BigDecimal upgrageAngelFee = getUpgrageAngelFee(context,context.getSkuIds());
            log.info("JdhOrderDomainServiceImpl -> calcTimegetUpgrageAngelFee context={}, upgrageAngelFee={}", JSON.toJSONString(context), upgrageAngelFee);
            serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(upgrageAngelFee)
                    .type(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getType())
                    .aggregateType(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getAggregateTypeEnum().getType())
                    .aggregateSubType(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getAggregateTypeEnum().getSubType())
                    .tips(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getCode()).build());
        }
    }

    /**
     * TODO 王雨 计算上门费和时段费
     *
     * @param context              上下文
     * @param serviceFeeDetailList 服务费详细列表
     */
    private void calcTimePeriodAndHomeFee(CalcOrderServiceFeeContext context,List<ServiceFeeDetail> serviceFeeDetailList){
//        AddressInfoValueObject addressInfo = context.getAddressInfo();
        //Map<String, String> homeVisitFeeConfigMap = getTimePeriodAndHomeFeeConfig(context);
        //HomeAndTimeFeeConfig feeConfig = getHomeAndTimeFeeConfig(addressInfo,homeVisitFeeConfigMap);
        HomeAndTimeFeeConfig feeConfig = getHomeAndTimeFeeConfigOptimize(context);
        log.info("[JdhOrderDomainServiceImpl#calcTimePeriodAndHomeFee],feeConfig={}",JSON.toJSONString(feeConfig));
        if(Objects.isNull(feeConfig)){
            calcTimePeriodFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcTimePeriodAndHomeFee calcTimePeriodFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
            //上门费
            calcHomeVisitFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcTimePeriodAndHomeFee calcHomeVisitFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
            return;
        }

        String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.TRADE_ACTION_SERVICE_UPGRADE_KEY, context.getUserPin());
        String serviceUpgradeSelectedStr = jimClient.get(cacheKey);
        if (context.getSearchServiceUpgrade() == null){
            context.setSearchServiceUpgrade(false);
        }
        log.info("JdhOrderDomainServiceImpl -> calcTimePeriodAndHomeFee serviceUpgradeSelectedStr={},searchServiceUpgrade={}", serviceUpgradeSelectedStr, context.getSearchServiceUpgrade());
        DiscountFeeRule rule = preDiscountFeeActive(context);
        if(Objects.nonNull(rule) && Objects.nonNull(rule.getAction()) && BooleanUtils.isTrue(rule.getAction().getAutoSelected())){
            log.info("JdhOrderDomainServiceImpl -> calcTimePeriodAndHomeFee preDiscountFeeActive true");
            serviceUpgradeSelectedStr = "autoSelected";
        }
        if (StringUtils.isNotBlank(serviceUpgradeSelectedStr) || context.getSearchServiceUpgrade()){
            // 护士升级费
            BigDecimal upgrageAngelFee = getUpgrageAngelFeeByConfigId(feeConfig,context.getSkuIds());
            log.info("JdhOrderDomainServiceImpl -> calcTimePeriodAndHomeFee feeConfig={}, skuIds={}, upgrageAngelFee={}", JSON.toJSONString(feeConfig), JSON.toJSONString(context.getSkuIds()), upgrageAngelFee);
            serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(upgrageAngelFee)
                    .type(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getType())
                    .aggregateType(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getAggregateTypeEnum().getType())
                    .aggregateSubType(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getAggregateTypeEnum().getSubType())
                    .tips(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getAggregateTypeEnum().getCode())
                    .build());
        }

        // 上门费
        BigDecimal homeVisitFee = feeConfig.getOnSiteFee();
        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(homeVisitFee)
                .type(JdOrderFeeTypeEnum.HOME_VISIT.getType())
                .aggregateType(JdOrderFeeTypeEnum.HOME_VISIT.getAggregateTypeEnum().getType())
                .aggregateSubType(JdOrderFeeTypeEnum.HOME_VISIT.getAggregateTypeEnum().getSubType())
                .tips(JdOrderFeeTypeEnum.HOME_VISIT.getAggregateTypeEnum().getCode())
                .build());

        // 夜间服务费
        Map<String, JdhFeeTimeConfig> nightServiceTimeMap = duccConfig.getNightServiceTimeMap();
        if(CollUtil.isNotEmpty(nightServiceTimeMap)){
            JdhFeeTimeConfig jdhFeeTimeConfig = JSONUtil.toBean(
                    JSONUtil.toJsonStr(nightServiceTimeMap.get(context.getVerticalCode())), JdhFeeTimeConfig.class);
            if(Objects.nonNull(jdhFeeTimeConfig) && jdhFeeTimeConfig.isEnable() && Objects.nonNull(context.getAppointmentTime()) && StringUtils.isNotBlank(context.getAppointmentTime().getAppointmentStartTime())){
                String appointmentStartTime = context.getAppointmentTime().getAppointmentStartTime();
                Boolean inTimeSlot = isInTimeSlot(jdhFeeTimeConfig,appointmentStartTime);
                if(inTimeSlot){
                    BigDecimal nightDoorFee = feeConfig.getNightDoorFee();
                    if(Objects.nonNull(nightDoorFee) && nightDoorFee.compareTo(BigDecimal.ZERO) > 0){
                        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(nightDoorFee)
                                .type(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getType())
                                .aggregateType(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getAggregateTypeEnum().getType())
                                .aggregateSubType(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getAggregateTypeEnum().getSubType())
                                .tips(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getCode())
                                .build());
                    }
                }
            }
        }

        // 高峰时段服务费
        Map<String, JdhFeeTimeConfig> peakServiceTimeMap = duccConfig.getPeakServiceTimeMap();
        if(CollUtil.isNotEmpty(peakServiceTimeMap)){
            JdhFeeTimeConfig peakServiceTime = JSONUtil.toBean(
                    JSONUtil.toJsonStr(peakServiceTimeMap.get(context.getVerticalCode())), JdhFeeTimeConfig.class);
            if(Objects.nonNull(peakServiceTime) && peakServiceTime.isEnable() && Objects.nonNull(context.getAppointmentTime()) && StringUtils.isNotBlank(context.getAppointmentTime().getAppointmentStartTime())){
                String appointmentStartTime = context.getAppointmentTime().getAppointmentStartTime();
                Boolean inTimeSlot = isInTimeSlot(peakServiceTime,appointmentStartTime);
                if(inTimeSlot){
                    BigDecimal peakServiceFee = feeConfig.getPeakServiceFee();
                    if(Objects.nonNull(peakServiceFee) && peakServiceFee.compareTo(BigDecimal.ZERO) > 0){
                        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(peakServiceFee)
                                .type(JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getType())
                                .aggregateType(JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getAggregateTypeEnum().getType())
                                .aggregateSubType(JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getAggregateTypeEnum().getSubType())
                                .tips(JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getCode())
                                .build());
                    }
                }
            }
        }

        // 节假日
        BigDecimal holidayFee = new BigDecimal("0");
        // 法定节假日（一个订单记一笔） 法定节假日+20元
        if(context.getAppointmentTime() != null && context.getAppointmentTime().appointmentDayIsHoliday() != null
                && context.getAppointmentTime().appointmentDayIsHoliday() && Objects.nonNull(feeConfig.getHoliday())){
            holidayFee = feeConfig.getHoliday();
        }
        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(holidayFee)
                .type(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getType())
                .aggregateType(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getAggregateTypeEnum().getType())
                .aggregateSubType(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getAggregateTypeEnum().getSubType())
                .tips(JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getCode()).build());

        // 即时/预约（一个订单记一笔）  即时预约+20元
        BigDecimal immediatelyFee = new BigDecimal("0");
        if(context.getAppointmentTime() != null && context.getAppointmentTime().getIsImmediately() != null
                && context.getAppointmentTime().getIsImmediately() && Objects.nonNull(feeConfig.getImmediately())){
            immediatelyFee = feeConfig.getImmediately();
        }
        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(immediatelyFee)
                .type(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getType())
                .aggregateType(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getAggregateTypeEnum().getType())
                .aggregateSubType(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getAggregateTypeEnum().getSubType())
                .tips(JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getCode()).build());
    }


    /**
     * getHomeAndTimeFeeConfigOptimize
     * @param context
     * @return
     */
    @Override
    @LogAndAlarm
    public HomeAndTimeFeeConfig getHomeAndTimeFeeConfigOptimize(CalcOrderServiceFeeContext context){
        AddressInfoValueObject addressInfo = context.getAddressInfo();
        log.info("[JdhOrderDomainServiceImpl.getHomeAndTimeFeeConfigOptimize],addressInfo={}",JSON.toJSONString(addressInfo));

        //Step2.遍历redis中的所有keys
        String cacheKeysPrefix = context.getChannelId() + "*" + context.getServiceType();
        //FIXME 康复师需求，康复师的商品默认取护士护理的时段费配置
        if (Objects.equals(context.getServiceType(), ServiceTypeNewEnum.KFS_CARE.getType())) {
            cacheKeysPrefix = context.getChannelId() + "*" + ServiceTypeNewEnum.ANGEL_CARE.getType();
        }
        log.info("JdhOrderDomainServiceImpl.getHomeAndTimeFeeConfigOptimize.cacheKeysPrefix={}",JSON.toJSONString(cacheKeysPrefix));

        String townId = addressInfo.getTownId() == null ? "" : String.valueOf(addressInfo.getTownId());
        String townFeeStr = jimClient.hGet(cacheKeysPrefix, townId);
        log.info("JdhOrderDomainServiceImpl.getHomeAndTimeFeeConfigOptimize.townFeeStr={}",townFeeStr);
        HomeAndTimeFeeConfig feeConfig = null;
        HomeAndTimeFeeConfig tempFeeConfig = null;
        if(StringUtil.isNotBlank(townFeeStr)){
            feeConfig = JSON.parseObject(townFeeStr, HomeAndTimeFeeConfig.class);
            if(Objects.nonNull(feeConfig.getAreaFeeConfigId())){
                return feeConfig;
            }
            tempFeeConfig = feeConfig;
        }

        String countyId = addressInfo.getCountyId() == null ? "" : String.valueOf(addressInfo.getCountyId());
        String countyFeeStr = jimClient.hGet(cacheKeysPrefix, countyId);
        log.info("JdhOrderDomainServiceImpl.getHomeAndTimeFeeConfigOptimize.countyFeeStr={}",countyFeeStr);
        if(StringUtil.isNotBlank(countyFeeStr)){
            feeConfig = JSON.parseObject(countyFeeStr, HomeAndTimeFeeConfig.class);
            tempFeeConfig = getFeeConfig(tempFeeConfig,feeConfig);
            if(Objects.nonNull(tempFeeConfig) && Objects.nonNull(tempFeeConfig.getAreaFeeConfigId())){
                return tempFeeConfig;
            }
        }

        String cityId = addressInfo.getCityId() == null ? "" : String.valueOf(addressInfo.getCityId());
        String cityFeeStr = jimClient.hGet(cacheKeysPrefix, cityId);
        log.info("JdhOrderDomainServiceImpl.getHomeAndTimeFeeConfigOptimize.cityFeeStr={}",cityFeeStr);
        if(StringUtil.isNotBlank(cityFeeStr)){
            feeConfig = JSON.parseObject(cityFeeStr, HomeAndTimeFeeConfig.class);
            tempFeeConfig = getFeeConfig(tempFeeConfig,feeConfig);
            if(Objects.nonNull(tempFeeConfig) && Objects.nonNull(tempFeeConfig.getAreaFeeConfigId())){
                return tempFeeConfig;
            }
        }

        //根据用户地址的省、市、区/县、乡镇 id，分别查询自己的fee配置，查询顺序，从乡镇 -》区县-》市-》省-》顺序看是否有配置fee,如果有直接取命中的fee
        String provinceId = addressInfo.getProvinceId() == null ? "" : String.valueOf(addressInfo.getProvinceId());
        String provinceFeeStr = jimClient.hGet(cacheKeysPrefix, provinceId);
        log.info("JdhOrderDomainServiceImpl.getHomeAndTimeFeeConfigOptimize.provinceFeeStr={}",provinceFeeStr);
        if(StringUtil.isNotBlank(provinceFeeStr)){
            feeConfig = JSON.parseObject(provinceFeeStr, HomeAndTimeFeeConfig.class);
            tempFeeConfig = getFeeConfig(tempFeeConfig,feeConfig);
            if(Objects.nonNull(tempFeeConfig)){
                return tempFeeConfig;
            }
        }
        return tempFeeConfig;
    }

    /**
     * 是否在夜间时间段
     * @param jdhFeeTimeConfig
     * @param appointmentStartTime
     * @return
     */
    private Boolean isInTimeSlot(JdhFeeTimeConfig jdhFeeTimeConfig,String appointmentStartTime){
        if(StringUtil.isNotBlank(appointmentStartTime)){
            String[] times = appointmentStartTime.split(" ");
            if(times.length == 2){
                String time = times[1];
                if(StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightStart())){
                    if(time.compareTo(jdhFeeTimeConfig.getMidnightStart()) >= 0 && time.compareTo(jdhFeeTimeConfig.getMidnightEnd()) <= 0){
                        return Boolean.TRUE;
                    }
                }
                if(StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart())){
                    if(time.compareTo(jdhFeeTimeConfig.getAfterMidnightStart()) >= 0 && time.compareTo(jdhFeeTimeConfig.getAfterMidnightEnd()) < 0){
                        return Boolean.TRUE;
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     *
     * @param addressInfo
     * @param homeVisitFeeConfigMap
     * @return
     */
    private HomeAndTimeFeeConfig getHomeAndTimeFeeConfig(AddressInfoValueObject addressInfo,Map<String, String> homeVisitFeeConfigMap){
        log.info("[JdhOrderDomainServiceImpl#getHomeAndTimeFeeConfig],addressInfo={}",JSON.toJSONString(addressInfo));
        String townId = addressInfo.getTownId() == null ? "" : String.valueOf(addressInfo.getTownId());
        String townFeeStr = homeVisitFeeConfigMap.get(townId);
        HomeAndTimeFeeConfig feeConfig = null;
        HomeAndTimeFeeConfig tempFeeConfig = null;
        if(StringUtil.isNotBlank(townFeeStr)){
            feeConfig = JSON.parseObject(townFeeStr, HomeAndTimeFeeConfig.class);
            if(Objects.nonNull(feeConfig.getAreaFeeConfigId())){
                return feeConfig;
            }
            tempFeeConfig = feeConfig;
        }

        String countyId = addressInfo.getCountyId() == null ? "" : String.valueOf(addressInfo.getCountyId());
        String countyFeeStr = homeVisitFeeConfigMap.get(countyId);
        if(StringUtil.isNotBlank(countyFeeStr)){
            feeConfig = JSON.parseObject(countyFeeStr, HomeAndTimeFeeConfig.class);
            tempFeeConfig = getFeeConfig(tempFeeConfig,feeConfig);
            if(Objects.nonNull(tempFeeConfig) && Objects.nonNull(tempFeeConfig.getAreaFeeConfigId())){
                return tempFeeConfig;
            }
        }

        String cityId = addressInfo.getCityId() == null ? "" : String.valueOf(addressInfo.getCityId());
        String cityFeeStr = homeVisitFeeConfigMap.get(cityId);
        if(StringUtil.isNotBlank(cityFeeStr)){
            feeConfig = JSON.parseObject(cityFeeStr, HomeAndTimeFeeConfig.class);
            tempFeeConfig = getFeeConfig(tempFeeConfig,feeConfig);
            if(Objects.nonNull(tempFeeConfig) && Objects.nonNull(tempFeeConfig.getAreaFeeConfigId())){
                return tempFeeConfig;
            }
        }

        //根据用户地址的省、市、区/县、乡镇 id，分别查询自己的fee配置，查询顺序，从乡镇 -》区县-》市-》省-》顺序看是否有配置fee,如果有直接取命中的fee
        String provinceId = addressInfo.getProvinceId() == null ? "" : String.valueOf(addressInfo.getProvinceId());
        String provinceFeeStr = homeVisitFeeConfigMap.get(provinceId);
        if(StringUtil.isNotBlank(provinceFeeStr)){
            feeConfig = JSON.parseObject(provinceFeeStr, HomeAndTimeFeeConfig.class);
            tempFeeConfig = getFeeConfig(tempFeeConfig,feeConfig);
            if(Objects.nonNull(tempFeeConfig)){
                return tempFeeConfig;
            }
        }
        return tempFeeConfig;
    }

    /**
     *
     * @param tempFeeConfig
     * @param feeConfig
     * @return
     */
    private HomeAndTimeFeeConfig getFeeConfig(HomeAndTimeFeeConfig tempFeeConfig,HomeAndTimeFeeConfig feeConfig){
        if(Objects.nonNull(feeConfig.getAreaFeeConfigId())){
            if(Objects.isNull(tempFeeConfig)){
                return feeConfig;
            }else {
                tempFeeConfig.setAreaFeeConfigId(feeConfig.getAreaFeeConfigId());
                return tempFeeConfig;
            }
        }else{
            if(Objects.isNull(tempFeeConfig)){
                tempFeeConfig = feeConfig;
            }
        }
        return tempFeeConfig;
    }
    /**
     *
     * @param feeConfig
     * @param skuIds
     * @return
     */
    @Override
    @LogAndAlarm
    public BigDecimal getUpgrageAngelFeeByConfigId(HomeAndTimeFeeConfig feeConfig,Set<String> skuIds) {
        Long areaFeeConfigId = feeConfig.getAreaFeeConfigId();
        if(Objects.nonNull(areaFeeConfigId)){
            String upgrageAngelFee = tradeInfoRpc.getUpgrageAngelFee(skuIds,areaFeeConfigId);
            if(StringUtil.isNotBlank(upgrageAngelFee)){
                return new BigDecimal(upgrageAngelFee);
            }
        }
        return BigDecimal.ZERO;
    }
    /**
     *
     * @param cityFeeStr
     * @return
     */
    private Boolean getAreaFeeConfigId(String cityFeeStr) {
        HomeAndTimeFeeConfig feeConfig = JSON.parseObject(cityFeeStr, HomeAndTimeFeeConfig.class);
        Long areaFeeConfigId = feeConfig.getAreaFeeConfigId();
        if(Objects.nonNull(areaFeeConfigId)){
            return true;
        }
        return false;
    }

    /**
     *
     * @param context
     * @param skuIds
     * @return
     */
    private BigDecimal getUpgrageAngelFee(CalcOrderServiceFeeContext context,Set<String> skuIds) {
        AddressInfoValueObject addressInfo = context.getAddressInfo();
        String provinceCode = String.valueOf(addressInfo.getProvinceId());
        String channelId = String.valueOf(context.getChannelId());
        Integer serviceType = context.getServiceType();
        String upgrageAngelFee = tradeInfoRpc.getUpgrageAngelFeeByParam(skuIds,provinceCode,channelId,serviceType);
        if(StringUtil.isNotBlank(upgrageAngelFee)){
            return new BigDecimal(upgrageAngelFee);
        }
        return BigDecimal.ZERO;
    }

    /**
     *
     * @param context
     * @return
     */
    private Map<String, String> getTimePeriodAndHomeFeeConfig(CalcOrderServiceFeeContext context) {
        //Step2.遍历redis中的所有keys
        String cacheKeysPrefix = context.getChannelId() + "*" + context.getServiceType();
        log.info("JdhOrderDomainServiceImpl#getTimePeriodAndHomeFeeConfig.cacheKeysPrefix={}",JSON.toJSONString(cacheKeysPrefix));

        Map<String, String> maps = jimClient.hGetAll(cacheKeysPrefix);
        return maps;
//        ScanOptions.ScanOptionsBuilder scanOptions = ScanOptions.scanOptions();
//        //count:每次最多取多少个元素(近似值)---类似分页机制，建议1000之内,server默认10
//        scanOptions.count(100);
//        scanOptions.match(cacheKeysPrefix);
//        KeyScanResult<String> scan = new KeyScanResult();
//        scan.setCursor("");
//        //判断是否遍历结束（即当扫描完集群的所有分片后，scan.isFinished()会返回true）
//        Map<String, HomeAndTimeFeeConfig> homeAndTimeFeeConfigAllMap = new HashMap<>();
//        while (scan == null || !scan.isFinished()) {
//            try {
//
//                scan = jimClient.scan(scan.getCursor(), scanOptions.build()); //为""表示从头开始遍历key
//                List<String> keys = scan.getResult();
//                Optional.ofNullable(keys).map(List::stream).orElseGet(Stream::empty).forEach(key -> {
//                    Map<String, HomeAndTimeFeeConfig> map = JSON.parseObject(jimClient.get(key), Map.class);
//                    homeAndTimeFeeConfigAllMap.putAll(map);
//                    log.info("JdhOrderDomainServiceImpl.getTimePeriodAndHomeFeeConfig.map={}", JSON.toJSONString(map));
//                });
//            } catch (Exception e) {
//                log.error("FeeConfigurationApplicationImpl.deleteFeeConfig has error", e);
//            }
//        }
//         log.info("JdhOrderDomainServiceImpl#getTimePeriodAndHomeFeeConfig.homeAndTimeFeeConfigAllMap={}",JSON.toJSONString(homeAndTimeFeeConfigAllMap));
//        return homeAndTimeFeeConfigAllMap;
    }


    /**
     * calc上门费
     *
     * @param context              上下文
     * @param serviceFeeDetailList 服务费详细列表
     */
    private void calcHomeVisitFee(CalcOrderServiceFeeContext context,List<ServiceFeeDetail> serviceFeeDetailList){
        AddressInfoValueObject addressInfo = context.getAddressInfo();
        Map<String, Map<String, HomeVisitFeeConfig>> homeVisitFeeConfigMap = duccConfig.getHomeVisitFeeConfigMap();
        log.info("JdhOrderDomainServiceImpl.calcHomeVisitFee.homeVisitFeeConfigMap={}",JSON.toJSONString(homeVisitFeeConfigMap));
        Map<String, HomeVisitFeeConfig> homeVisitFeeConfigMapItem = homeVisitFeeConfigMap.get(context.getVerticalCode());
        if (CollUtil.isEmpty(homeVisitFeeConfigMapItem)){
            homeVisitFeeConfigMapItem=homeVisitFeeConfigMap.get("common");
        }
        //根据用户地址的省、市、区/县、乡镇 id，分别查询自己的fee配置，查询顺序，从乡镇 -》区县-》市-》省-》顺序看是否有配置fee,如果有直接取命中的fee
        Integer provinceId = addressInfo.getProvinceId() == null ? 0 : addressInfo.getProvinceId();
        Integer cityId = addressInfo.getCityId() == null ? 0 : addressInfo.getCityId();
        Integer countyId = addressInfo.getCountyId() == null ? 0 : addressInfo.getCountyId();
        Integer townId = addressInfo.getTownId() == null ? 0 : addressInfo.getTownId();
        HomeVisitFeeConfig townFee = homeVisitFeeConfigMapItem.get(String.valueOf(townId));
        HomeVisitFeeConfig countyFee = homeVisitFeeConfigMapItem.get(String.valueOf(countyId));
        HomeVisitFeeConfig cityFee = homeVisitFeeConfigMapItem.get(String.valueOf(cityId));
        HomeVisitFeeConfig provinceFee = homeVisitFeeConfigMapItem.get(String.valueOf(provinceId));
        BigDecimal homeVisitFee;
        if(Objects.nonNull(townFee)){
            homeVisitFee = townFee.getFee();
        }else if(Objects.nonNull(countyFee)){
            homeVisitFee = countyFee.getFee();
        }else if (Objects.nonNull(cityFee)) {
            homeVisitFee = cityFee.getFee();
        }else if(Objects.nonNull(provinceFee)){
            homeVisitFee = provinceFee.getFee();
        }else{
            homeVisitFee = new BigDecimal("0");
            UmpUtil.showWarnMsg(UmpKeyEnum.CALC_SERVICE_FEE_WARN, MessageFormat.format("provinceId:{0},cityId:{1},countyId:{2},townId:{3}"
                    ,String.valueOf(provinceId), String.valueOf(cityId), String.valueOf(countyId),townId));
        }

        serviceFeeDetailList.add(ServiceFeeDetail.builder().fee(homeVisitFee)
                .type(JdOrderFeeTypeEnum.HOME_VISIT.getType())
                .aggregateType(JdOrderFeeTypeEnum.HOME_VISIT.getAggregateTypeEnum().getType())
                .aggregateSubType(JdOrderFeeTypeEnum.HOME_VISIT.getAggregateTypeEnum().getSubType())
                .tips(JdOrderFeeTypeEnum.HOME_VISIT.getAggregateTypeEnum().getCode())
                .build());
    }


    /**
     * 计算动态费用
     *
     * @param serviceFeeDetailList 服务费详细列表
     */
    private void calcDynamicFee(List<ServiceFeeDetail> serviceFeeDetailList) {
        List<DynamicFee> dynamicFeeConfig = duccConfig.getDynamicFeeConfig();
        if(CollUtil.isNotEmpty(dynamicFeeConfig)){
            for (DynamicFee dynamicFee : dynamicFeeConfig) {
                serviceFeeDetailList.add(ServiceFeeDetail.builder()
                        .fee(dynamicFee.getFee())
                        .type(JdOrderFeeTypeEnum.DYNAMIC.getType())
                        .aggregateType(JdOrderFeeTypeEnum.DYNAMIC.getAggregateTypeEnum().getType())
                        .aggregateSubType(JdOrderFeeTypeEnum.DYNAMIC.getAggregateTypeEnum().getSubType())
                        .tips(dynamicFee.getFeeTips())
                        .build());
            }
        }
    }

    /**
     * 计算订单服务费项
     *
     * @param context 上下文
     * @return {@link List}<{@link JdOrderServiceFeeInfo}>
     */
    @Override
    @SuppressWarnings("all")
    public List<JdOrderServiceFeeInfo> calcOrderServiceFee(CalcOrderServiceFeeContext context) {
        log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee context:{}",JSON.toJSONString(context));

        //入参校验，报错返回空，
        try {
            context.paramCheck();
        }catch (Exception e){
            log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee 算费入参不完整");
            return Collections.EMPTY_LIST;
        }

        List<FixedSkuConfig> fixedSkuConfigList = fixedSkuConfigRepository.queryAllFixedSkuConfig();
        Set<String> skuList = new HashSet<>();
        //出参转换
        if (CollUtil.isNotEmpty(fixedSkuConfigList)) {
            skuList = fixedSkuConfigList.stream().map(FixedSkuConfig::getSkuId).collect(Collectors.toSet());
        }
        log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee skuList:{}",JSON.toJSONString(skuList));

        // 一口价
        if (ProductSaleChannelEnum.XFYL.getChannelId().equals(context.getChannelId())){
            if (StringUtils.isNotBlank(context.getMainSkuId()) && skuList.contains(context.getMainSkuId())){// 消医c端主品sku，命中减免逻辑
                log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee XFYL 命中减免逻辑");
                return Collections.EMPTY_LIST;
            }
        }else {
            for (String skuId : context.getSkuIds()) {
                if (skuList.contains(skuId)){
                    log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee {} 命中减免逻辑", context.getChannelId());
                    return Collections.EMPTY_LIST;
                }
            }
        }

        List<ServiceFeeDetail> serviceFeeDetailList = new ArrayList<>();
        if (duccConfig.getFeeConfigDataSourceSwitch()) {
            //上门费+时段费 新数据源
            calcTimePeriodAndHomeFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee calcTimePeriodAndHomeFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
        } else {
            //时段费
            calcTimePeriodFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee calcTimePeriodFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
            //上门费
            calcHomeVisitFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee calcHomeVisitFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
            calcTimegetUpgrageAngelFee(context, serviceFeeDetailList);
        }
        //动态调整项
        calcDynamicFee(serviceFeeDetailList);
        log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee calcDynamicFee serviceFeeDetailList:{}",JSON.toJSONString(serviceFeeDetailList));

        // 费项减免
        discountFeeActive(context, serviceFeeDetailList);
        log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee discountFeeActive serviceFeeDetailList:{}",JSON.toJSONString(serviceFeeDetailList));

        // 组装返回
        List<JdOrderServiceFeeInfo> result = new ArrayList<>();
        serviceFeeDetailList.stream().collect(Collectors.groupingBy(ServiceFeeDetail::getAggregateSubType)).forEach((key, feeDetails) -> {
            JdOrderServiceFeeInfo feeInfo = JdOrderServiceFeeInfo.builder()
                    .aggregateType(feeDetails.get(0).getAggregateType())
                    .aggregateSubType(key)
                    .serviceFeeDetailList(feeDetails)
                    .serviceFee(feeDetails.stream().map(ServiceFeeDetail::getFee).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .build();
            if (Objects.nonNull(context.getAppointmentTime()) && Objects.nonNull(context.getAddressInfo())){
                feeInfo.setIsFinish(true);
            }
            result.add(feeInfo);
        });
        log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee result:{}",JSON.toJSONString(result));
        return result;
    }

    /**
     * 预执行费项减免
     * @param context
     */
    @LogAndAlarm
    private DiscountFeeRule preDiscountFeeActive(CalcOrderServiceFeeContext context){
        DiscountFeeConfig discountFeeConfig = duccConfig.getDiscountFeeConfig();
        if(Objects.isNull(context) || Objects.isNull(context.getAddressInfo())){
            log.warn("JdhOrderDomainServiceImpl -> discountFeeActive context or address empty:{}",JSON.toJSONString(context));
            return null;
        }
        if(Objects.isNull(discountFeeConfig) || MapUtils.isEmpty(discountFeeConfig.getRuleMap())){
            log.warn("JdhOrderDomainServiceImpl -> discountFeeActive discountFeeConfig or rule empty:{}",JSON.toJSONString(discountFeeConfig));
            return null;
        }

        Map<String, DiscountFeeRule> ruleMap = discountFeeConfig.getRuleMap();
        String provinceIdStr = String.valueOf(context.getAddressInfo().getProvinceId());
        String cityIdStr = String.valueOf(context.getAddressInfo().getCityId());
        String countyIdStr = String.valueOf(context.getAddressInfo().getCountyId());
        String townIdStr = null;
        if(Objects.nonNull(context.getAddressInfo().getTownId())) {
            townIdStr = String.valueOf(context.getAddressInfo().getTownId());
        }
        DiscountFeeRule rule = null;
        if(Objects.nonNull(townIdStr) && ruleMap.containsKey(townIdStr)){
            rule = ruleMap.get(townIdStr);
        } else if (ruleMap.containsKey(countyIdStr)) {
            rule = ruleMap.get(countyIdStr);
        } else if (ruleMap.containsKey(cityIdStr)) {
            rule = ruleMap.get(cityIdStr);
        } else if (ruleMap.containsKey(provinceIdStr)) {
            rule = ruleMap.get(provinceIdStr);
        }
        if(Objects.isNull(rule)){
            log.warn("JdhOrderDomainServiceImpl -> discountFeeActive rule is null:{}",JSON.toJSONString(context.getAddressInfo()));
            return null;
        }

        // action 兜底逻辑
        if(Objects.isNull(rule.getAction())){
            rule.setAction(discountFeeConfig.getAction());
        }

        List<Long> blackSkuIds = rule.getBlackSkuIds();
        if(CollectionUtils.isEmpty(blackSkuIds)){
            log.info("JdhOrderDomainServiceImpl -> discountFeeActive non blackSkuIds 命中费项减免逻辑");
            return rule;
        } else if (ProductSaleChannelEnum.XFYL.getChannelId().equals(context.getChannelId())){
            if (StringUtils.isNotBlank(context.getMainSkuId()) && !blackSkuIds.contains(Long.parseLong(context.getMainSkuId()))){// 消医c端主品sku不在黑名单
                log.info("JdhOrderDomainServiceImpl -> discountFeeActive XFYL 命中费项减免逻辑");
                return rule;
            }
        } else if (ProductSaleChannelEnum.NET_HP.getChannelId().equals(context.getChannelId())){
            if (!blackSkuIds.containsAll(context.getSkuIds())) { //互医任意一个sku不在黑名单
                log.info("JdhOrderDomainServiceImpl -> discountFeeActive NET_HP 命中费项减免逻辑");
                return rule;
            }
        }
        return null;
    }

    @LogAndAlarm
    private void discountFeeActive(CalcOrderServiceFeeContext context, List<ServiceFeeDetail> serviceFeeDetailList){
        long upgradeAngelCount = serviceFeeDetailList.stream().filter(s -> JdOrderFeeTypeEnum.UPGRADE_ANGEL.getType().equals(s.getType())).count();
        if(upgradeAngelCount == 0){
            log.warn("JdhOrderDomainServiceImpl -> discountFeeActive UPGRADE_ANGEL empty:{}",JSON.toJSONString(serviceFeeDetailList));
            return;
        }
        DiscountFeeRule rule = preDiscountFeeActive(context);
        if(Objects.nonNull(rule)) {
            calcDiscountFee(context, serviceFeeDetailList, rule);
        }
    }

    /**
     * 计算费项减免
     * @param context
     * @param serviceFeeDetailList
     * @param rule
     */
    private void calcDiscountFee(CalcOrderServiceFeeContext context, List<ServiceFeeDetail> serviceFeeDetailList, DiscountFeeRule rule) {
        if(Objects.isNull(rule.getDiscountType())){
            return ;
        }
        serviceFeeDetailList.stream().filter(s -> JdOrderFeeTypeEnum.UPGRADE_ANGEL.getType().equals(s.getType())).forEach(s -> {
            if(rule.getDiscountType().intValue() == 1){
                if(Objects.nonNull(s.getFee()) && Objects.nonNull(rule.getDiscountRebate()) && s.getFee().compareTo(rule.getDiscountRebate()) >= 0) {
                    s.setOriginalFee(s.getFee());
                    s.setDiscountFee(s.getFee().subtract(rule.getDiscountRebate()));
                    s.setFee(rule.getDiscountRebate());
                }
            } else if (rule.getDiscountType().intValue() == 2) {
                if(Objects.nonNull(s.getFee()) && Objects.nonNull(rule.getDiscountRebate()) && s.getFee().compareTo(rule.getDiscountRebate()) >= 0) {
                    s.setOriginalFee(s.getFee());
                    s.setDiscountFee(rule.getDiscountRebate());
                    s.setFee(s.getFee().subtract(rule.getDiscountRebate()));
                }
            } else if (rule.getDiscountType().intValue() == 3) {
                if(Objects.nonNull(s.getFee()) && Objects.nonNull(rule.getDiscountRebate()) && rule.getDiscountRebate().compareTo(BigDecimal.ZERO) >= 0 && rule.getDiscountRebate().compareTo(BigDecimal.ONE) <= 1) {
                    s.setOriginalFee(s.getFee());
                    BigDecimal discountMoeny = s.getFee().multiply(rule.getDiscountRebate()).setScale(2, RoundingMode.HALF_UP);;
                    s.setDiscountFee(s.getFee().subtract(discountMoeny));
                    s.setFee(discountMoeny);
                }
            }
            if(Objects.nonNull(s.getDiscountFee()) && s.getDiscountFee().compareTo(BigDecimal.ZERO) == 0){
                s.setOriginalFee(BigDecimal.ZERO);
            }
            s.setExtendMap(ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getExtType(), JSON.toJSONString(rule.getAction()));
        });
    }

    /**
     * 计算订单服务费项(无减免)
     *
     * @param context 上下文
     * @return {@link List}<{@link JdOrderServiceFeeInfo}>
     */
    @Override
    public List<ServiceFeeDetail> calcNoFreeOrderServiceFee(CalcOrderServiceFeeContext context) {
        log.info("JdhOrderDomainServiceImpl -> calcNoFreeOrderServiceFee context:{}",JSON.toJSONString(context));
        List<ServiceFeeDetail> serviceFeeDetailList = new ArrayList<>();
        if (duccConfig.getFeeConfigDataSourceSwitch()) {
            //上门费+时段费 新数据源
            calcTimePeriodAndHomeFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee calcTimePeriodAndHomeFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
        } else {
            //时段费
            calcTimePeriodFee(context, serviceFeeDetailList);
            //上门费
            calcHomeVisitFee(context, serviceFeeDetailList);
            log.info("JdhOrderDomainServiceImpl -> calcOrderServiceFee calcTimePeriodFee serviceFeeDetailList:{}", JSON.toJSONString(serviceFeeDetailList));
            calcTimegetUpgrageAngelFee(context, serviceFeeDetailList);
        }
        //动态调整项
        calcDynamicFee(serviceFeeDetailList);
        log.info("JdhOrderDomainServiceImpl -> calcNoFreeOrderServiceFee serviceFeeDetailList:{}",JSON.toJSONString(serviceFeeDetailList));
        return serviceFeeDetailList;
    }

    /**
     * 计算预约时间服务费项
     *
     * @param context 上下文
     * @return {@link List}<{@link JdOrderServiceFeeInfo}>
     */
    @Override
    @LogAndAlarm
    public AppointTimeServiceFeeDetail calcOrderAppointTimeServiceFee(CalcOrderServiceFeeContext context) {
        try {
            log.info("JdhOrderDomainServiceImpl -> calcOrderAppointTimeServiceFee context:{}",JSON.toJSONString(context));
            HomeAndTimeFeeConfig feeConfig = getHomeAndTimeFeeConfigOptimize(context);
            if (Objects.isNull(feeConfig)) {
                return null;
            }
            JdhFeeTimeConfig jdhFeeTimeConfig = null;
            // 夜间服务费
            if (Objects.equals(context.getFeeType(), JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getType())) {
                log.info("JdhOrderDomainServiceImpl -> calcOrderAppointTimeServiceFee 夜间服务费");
                Map<String, JdhFeeTimeConfig> nightServiceTimeMap = duccConfig.getNightServiceTimeMap();
                if (CollUtil.isEmpty(nightServiceTimeMap)) {
                    return null;
                }
                jdhFeeTimeConfig = JSONUtil.toBean(
                        JSONUtil.toJsonStr(nightServiceTimeMap.get(context.getVerticalCode())), JdhFeeTimeConfig.class);
                if(Objects.isNull(jdhFeeTimeConfig) || !Objects.equals(jdhFeeTimeConfig.isEnable(), true)){
                    return null;
                }

                ArrayList<TimeIntervalIntersection.TimeInterval> timeIntervalList = Lists.newArrayList();
                if (StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightEnd())) {
                    timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                }
                if (StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd())) {
                    timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                }
                return AppointTimeServiceFeeDetail.builder()
                        .fee(feeConfig.getNightDoorFee())
                        .type(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getType())
                        .tips(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getAggregateTypeEnum().getCode())
                        .timeIntervalList(timeIntervalList)
                        .build();
            }
            else if (Objects.equals(context.getFeeType(), JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getType())) {
                log.info("JdhOrderDomainServiceImpl -> calcOrderAppointTimeServiceFee 高峰时段费");
                Map<String, JdhFeeTimeConfig> peakServiceTimeMap = duccConfig.getPeakServiceTimeMap();
                if (CollUtil.isEmpty(peakServiceTimeMap)) {
                    return null;
                }
                jdhFeeTimeConfig = JSONUtil.toBean(
                        JSONUtil.toJsonStr(peakServiceTimeMap.get(context.getVerticalCode())), JdhFeeTimeConfig.class);
                if(Objects.isNull(jdhFeeTimeConfig) || !Objects.equals(jdhFeeTimeConfig.isEnable(), true)){
                    return null;
                }

                ArrayList<TimeIntervalIntersection.TimeInterval> timeIntervalList = Lists.newArrayList();
                if (StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getMidnightEnd())) {
                    timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                }
                if (StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) && StringUtils.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd())) {
                    timeIntervalList.add(new TimeIntervalIntersection.TimeInterval(
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightStart()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightStart(), DateTimeFormatter.ofPattern("HH:mm")) : null,
                            StringUtil.isNotBlank(jdhFeeTimeConfig.getAfterMidnightEnd()) ? LocalTime.parse(jdhFeeTimeConfig.getAfterMidnightEnd(), DateTimeFormatter.ofPattern("HH:mm")) : null));
                }
                return AppointTimeServiceFeeDetail.builder()
                        .fee(feeConfig.getPeakServiceFee())
                        .type(JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getType())
                        .tips(JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getAggregateTypeEnum().getCode())
                        .timeIntervalList(timeIntervalList)
                        .build();
            }
        } catch (Exception e) {
            log.error("JdhOrderDomainServiceImpl -> calcOrderAppointTimeServiceFee error", e);
        }
        return null;
    }


}
