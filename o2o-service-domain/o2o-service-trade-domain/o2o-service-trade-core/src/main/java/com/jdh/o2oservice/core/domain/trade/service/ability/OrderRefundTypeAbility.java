package com.jdh.o2oservice.core.domain.trade.service.ability;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import com.jdh.o2oservice.core.domain.trade.context.OrderRefundContext;
import com.jdh.o2oservice.core.domain.trade.enums.ReFundAmountTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderMoney;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderMoneyRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.TradeOrderRefundRpc;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmount;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmountExpand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName:退款Ability
 * @Description: 订单退款类型拆单
 * @Author: liwenming
 * @Date: 2024/5/10 20:35
 * @Vserion: 1.0
 **/
@Slf4j
@Component
public class OrderRefundTypeAbility implements DomainAbility<TradeAbilityCode, OrderRefundContext> {

    /**
     * jdOrderMoneyRepository
     */
    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;

    /**
     * tradeOrderRefundRpc
     */
    @Resource
    private TradeOrderRefundRpc tradeOrderRefundRpc;


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public TradeAbilityCode getAbilityCode() {
        return TradeAbilityCode.ORDER_REFUND_TYPE_SPLIT;
    }

    /**
     * 执行
     *
     * @param orderRefundContext R
     */
    @Override
    public void execute(OrderRefundContext orderRefundContext) {
        log.info("OrderRefundTypeAbility -> execute orderRefundContext={}", JSON.toJSONString(orderRefundContext));
        BigDecimal refundAmout = orderRefundContext.getRefundAmount();
        if(refundAmout.compareTo(BigDecimal.ZERO) == 0){
            Map<Integer,BigDecimal> lastRefundAmountAndTypeMap = new HashMap<>();
            lastRefundAmountAndTypeMap.put(ReFundAmountTypeEnum.ONLINE_PAYMENT.getType(),refundAmout);
            orderRefundContext.setLastRefundAmountAndTypeMap(lastRefundAmountAndTypeMap);
            log.info("OrderRefundTypeAbility -> refundAmout=0, orderId={}",orderRefundContext.getOrderId());
            return;
        }

        if(RefundTypeEnum.AMOUNT_REFUND.getType().equals(orderRefundContext.getRefundType())){
            BigDecimal orderRefundAmouted = tradeOrderRefundRpc.getOrderRefundAmount(orderRefundContext.getOrderId());
            log.info("OrderRefundTypeAbility -> execute orderRefundAmouted={}", orderRefundAmouted);
            orderRefundContext.setOrderRefundAmouted(orderRefundAmouted);
        }
        finalAmountRefund(orderRefundContext);
    }

    /**
     * 最终退款金额拆分
     * @param orderRefundContext
     */
    private void finalAmountRefund(OrderRefundContext orderRefundContext){
        log.info("OrderRefundTypeAbility -> finalAmountRefund start orderRefundContext={}", JsonUtil.toJSONString(orderRefundContext));
        // 支付优惠券
        List<JdOrderMoney> jdOrderMoneyList = getRefundDiscountAmount(orderRefundContext.getOrderId());
        if(CollUtil.isNotEmpty(jdOrderMoneyList)){
            jdOrderMoneyList = jdOrderMoneyList.stream().sorted((o1, o2) -> Integer.valueOf(o1.getMoneyType()).compareTo(o2.getMoneyType())).collect(Collectors.toList());

            BigDecimal refundAmout = orderRefundContext.getRefundAmount();
            BigDecimal orderRefundAmouted = orderRefundContext.getOrderRefundAmouted();
            // 退款金额拆分到不同支付类型上
            Map<Integer,BigDecimal> lastRefundAmountAndTypeMap = lastRefundAmountAndType(jdOrderMoneyList,refundAmout,orderRefundAmouted);
            orderRefundContext.setRefundAmount(refundAmout);
            orderRefundContext.setLastRefundAmountAndTypeMap(lastRefundAmountAndTypeMap);
        }
        log.info("OrderRefundTypeAbility -> finalAmountRefund orderRefundContext={}", JsonUtil.toJSONString(orderRefundContext));
    }

    /**
     * 获取支付优惠券列表
     * @param orderId
     * @return
     */
    private List<JdOrderMoney> getRefundDiscountAmount(Long orderId){
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(orderId);
        if(CollectionUtil.isEmpty(jdOrderMoneyList)){
            jdOrderMoneyList = createOrderMoneyInfo(orderId);
        }
        List<JdOrderMoney> jdOrderMoneyNewList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            jdOrderMoneyList.forEach(jdOrderMoney -> {
                if(Objects.nonNull(ReFundAmountTypeEnum.getRefundTypeOfType(jdOrderMoney.getMoneyType()))
                        && !ReFundAmountTypeEnum.PAY_YX_BEAN.getType().equals(jdOrderMoney.getMoneyType())){
                    jdOrderMoneyNewList.add(jdOrderMoney);
                }
            });
        }
        return jdOrderMoneyNewList;
    }

    /**
     * 退款金额拆分到不同支付类型上
     * @param jdOrderMoneyList
     * @param refundOrderItemAmount
     * @return
     */
    private Map<Integer,BigDecimal> lastRefundAmountAndType(List<JdOrderMoney> jdOrderMoneyList,BigDecimal refundOrderItemAmount,BigDecimal orderRefundAmouted){
        Map<Integer,BigDecimal> lastRefundAmountAndTypeMap = new HashMap<>();
        for(JdOrderMoney jdOrderMoney : jdOrderMoneyList){
            BigDecimal amount = jdOrderMoney.getAmount();
            if(amount.compareTo(orderRefundAmouted) > 0){
                amount = amount.subtract(orderRefundAmouted);
                orderRefundAmouted = BigDecimal.ZERO;
            }else{
                orderRefundAmouted = orderRefundAmouted.subtract(amount);
                continue;
            }
            if(amount.compareTo(refundOrderItemAmount) >= 0){
                lastRefundAmountAndTypeMap.put(jdOrderMoney.getMoneyType(),refundOrderItemAmount);
            }else{
                lastRefundAmountAndTypeMap.put(jdOrderMoney.getMoneyType(),amount);
                refundOrderItemAmount = refundOrderItemAmount.subtract(amount);
            }
        }
        log.info("OrderRefundTypeAbility -> lastRefundAmountAndType lastRefundAmountAndTypeMap={}", JsonUtil.toJSONString(lastRefundAmountAndTypeMap));
        return lastRefundAmountAndTypeMap;
    }

    /**
     * 生成订单金额信息列表
     *
     * @param orderId
     * @return
     */
    private static List<JdOrderMoney> createOrderMoneyInfo(Long orderId) {
        OrderCalculationQueryServiceRpc orderCalculationQueryServiceRpc = SpringUtil.getBean(OrderCalculationQueryServiceRpc.class);
        String amountAndExpand = orderCalculationQueryServiceRpc.queryOrderSplitAmountAndExpand(orderId);
        AssertUtils.hasText(amountAndExpand,TradeErrorCode.QUERY_REFUND_PAY_TYPE_NULL);

        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        List<OrderAmount> orderAmountList = JSON.parseArray(amountAndExpand, OrderAmount.class);
        log.info("JdOrderFactory -> createOrderMoneyInfo, orderId={}, orderAmountList={}", orderId, JSON.toJSONString(orderAmountList));
        if (CollectionUtils.isEmpty(orderAmountList)){
            log.info("JdOrderFactory -> createOrderMoneyInfo 获取订单金额明细失败, orderId={}", orderId);
            return jdOrderMoneyList;
        }
        for (OrderAmount orderAmount : orderAmountList) {
            if (null == orderAmount.getSkuId()){
                log.info("JdOrderFactory -> createOrderMoneyInfo, skuid is null,  orderAmount={}", JSON.toJSONString(orderAmount));
                continue;
            }
            Map<Integer,List<OrderAmountExpand>> orderAmountExpandMap = orderAmount.getAmountExpands().stream().collect(Collectors.groupingBy(b -> b.getType()));
            for(Map.Entry<Integer, List<OrderAmountExpand>> orderAmountExpand: orderAmountExpandMap.entrySet()){
                JdOrderMoney entity = new JdOrderMoney();
                List<OrderAmountExpand> orderAmountExpandList = orderAmountExpand.getValue();
                Integer amountType = orderAmountExpand.getKey();
                BigDecimal amount = BigDecimal.ZERO;
                for(OrderAmountExpand amountExpand : orderAmountExpandList){
                    amount = amount.add(amountExpand.getAmount());
                }
                if(amount.compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                entity.setOrderId(orderAmount.getOrderId());
                entity.setSkuId(orderAmount.getSkuId());
                entity.setMoneyType(amountType);
                entity.setAmount(amount);
                entity.setVersion(NumConstant.NUM_1);
                entity.setYn(YnStatusEnum.YES.getCode());
                entity.setCreateTime(new Date());
                entity.setUpdateTime(new Date());
                jdOrderMoneyList.add(entity);
            }
        }

        return jdOrderMoneyList;
    }

}
