package com.jdh.o2oservice.core.domain.trade.service.ability;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundStatusRatioMapping;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.core.domain.trade.context.QueryOrderRefundAmountContext;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderMoney;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderMoneyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * @ClassName:查询建议退款金额Ability
 * @Description: 查询建议退款金额
 * @Author: liwenming
 * @Date: 2024/5/10 20:35
 * @Vserion: 1.0
 **/
@Slf4j
@Component
public class QueryRefundSuggestAmountAbility implements DomainAbility<TradeAbilityCode, QueryOrderRefundAmountContext> {

    /**
     * jdOrderMoneyRepository
     */
    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public TradeAbilityCode getAbilityCode() {
        return TradeAbilityCode.QUERY_SUGGEST_REFUND_AMOUNT;
    }

    /**
     * 执行
     *
     * @param context R
     */
    @Override
    public void execute(QueryOrderRefundAmountContext context) {
        log.info("QueryRefundSuggestAmountAbility -> execute start context={}", JSON.toJSONString(context));
        RefundStatusRatioMapping refundStatusRatioMapping = calcOrderRefundAmount(context);

        JdOrder orderDetail = context.getOrderDetail();
        BigDecimal orderRefundAmount = BigDecimal.ZERO;
        // 订单实付款
        if(Objects.nonNull(orderDetail)){
            orderRefundAmount = orderDetail.getOrderAmount();
        }
        if(orderRefundAmount.compareTo(BigDecimal.ZERO) == 0){
            context.setSuggestRefundAmount(BigDecimal.ZERO);
            return;
        }
        BigDecimal feeAmount = BigDecimal.ZERO;
        BigDecimal orderSkuTotalAmount = BigDecimal.ZERO;
        // 商品建议退款金额
        if(RefundTypeEnum.ORDER_REFUND.getType().equals(context.getQueryAmountType())){
            feeAmount = getRefundFeeAmount(context.getOrderId(),507);
            // sku金额
            orderSkuTotalAmount = orderRefundAmount.subtract(feeAmount);
        }else if(RefundTypeEnum.AMOUNT_REFUND.getType().equals(context.getQueryAmountType())){
            orderSkuTotalAmount = context.getQuerySkuRefundAmount();
        }

        BigDecimal queryServiceRefundAmount = getOrderItemAmount(orderSkuTotalAmount,refundStatusRatioMapping);
        // fee建议退款金额
        if(context.getLastChildOrder()){
            feeAmount = getRefundFeeAmount(context.getOrderId(),507);
            feeAmount = getOrderFeeAmount(feeAmount,refundStatusRatioMapping);
        }
        context.setSuggestRefundAmount(queryServiceRefundAmount.add(feeAmount));
        log.info("QueryRefundSuggestAmountAbility -> execute after context={}", JSON.toJSONString(context));
    }

    /**
     *
     * @param orderSkuTotalAmount
     * @param refundStatusRatioMapping
     * @return
     */
    private BigDecimal getOrderItemAmount(BigDecimal orderSkuTotalAmount,RefundStatusRatioMapping refundStatusRatioMapping){
        BigDecimal refundOrderItemAmountRatio = BigDecimal.ZERO;
        BigDecimal queryServiceRefundAmount = BigDecimal.ZERO;
        String refundOrderItemAmountRatioStr = refundStatusRatioMapping.getServiceAmountRefundRatio();
        if(StringUtil.isNotBlank(refundOrderItemAmountRatioStr)){
            refundOrderItemAmountRatio = new BigDecimal(refundOrderItemAmountRatioStr);
        }

        if(Objects.nonNull(refundOrderItemAmountRatio) && refundOrderItemAmountRatio.compareTo(BigDecimal.ZERO) > 0){
            queryServiceRefundAmount = orderSkuTotalAmount.multiply(refundOrderItemAmountRatio).setScale(2,BigDecimal.ROUND_DOWN);
        }
        return queryServiceRefundAmount;
    }

    /**
     *
     * @param feeAmount
     * @param refundStatusRatioMapping
     * @return
     */
    private BigDecimal getOrderFeeAmount(BigDecimal feeAmount,RefundStatusRatioMapping refundStatusRatioMapping){
        BigDecimal feeAmountRefundRatio = BigDecimal.ZERO;
        String feeAmountRefundRatioStr = refundStatusRatioMapping.getFeeAmountRefundRatio();
        if(StringUtil.isNotBlank(feeAmountRefundRatioStr)){
            feeAmountRefundRatio = new BigDecimal(feeAmountRefundRatioStr);
        }
        if(Objects.nonNull(feeAmountRefundRatio) && feeAmountRefundRatio.compareTo(BigDecimal.ZERO) > 0){
            return feeAmount.multiply(feeAmountRefundRatio).setScale(2,BigDecimal.ROUND_DOWN);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算退款金额比例
     * @param context
     */
    private RefundStatusRatioMapping calcOrderRefundAmount(QueryOrderRefundAmountContext context){
        // 退款比例
        Map<String, RefundStatusRatioMapping> refundStatusAmoutRatioMap = duccConfig.getRefundStatusAmoutRatio();
        RefundStatusRatioMapping refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusAmoutRatioMap.get(String.valueOf(context.getWorkStatus()))), RefundStatusRatioMapping.class);
        if(Objects.isNull(refundStatusRatioMapping)){
            log.error("[OrderRefundAmountAbility.execute],refundStatusRatioMapping is null, orderId={}",context.getOrderId());
            throw new BusinessException(TradeErrorCode.ORDER_REFUND_FAIL);
        }
        return refundStatusRatioMapping;
    }

    /**
     * 时段费，上门费，动态调整费
     * @param orderId
     * @return
     */
    private BigDecimal getRefundFeeAmount(Long orderId,Integer feeMoneyType){
        BigDecimal feeAmount = BigDecimal.ZERO;
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(orderId);
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoney jdOrderMoney : jdOrderMoneyList){
                if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                    feeAmount = feeAmount.add(jdOrderMoney.getAmount());
                }
            }
        }
        log.info("JdOrderRefundApplicationImpl -> getRefundFeeAmount feeAmount={}", feeAmount);
        return feeAmount;
    }

}
