//package com.jdh.o2oservice.core.domain.trade.service.ability;
//
//
//import cn.hutool.core.collection.CollUtil;
//import com.jd.medicine.base.common.util.JsonUtil;
//import com.jdh.o2oservice.base.constatnt.CommonConstant;
//import com.jdh.o2oservice.base.exception.BusinessException;
//import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
//import com.jdh.o2oservice.base.model.DomainAbility;
//import com.jdh.o2oservice.core.domain.trade.context.JdOrderContext;
//
//import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
//import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
//import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
//import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
//import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
//import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
//import com.jdh.o2oservice.core.domain.trade.rpc.TradeInfoRpc;
//import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.assertj.core.util.Lists;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
//
///**
// * @ClassName:SelfHomeOrderCreateVoucherAbility
// * @Description: TODO
// * @Author: yaoqinghai
// * @Date: 2024/1/27 18:15
// * @Vserion: 1.0
// **/
//@Slf4j
//@Component
//public class CalcOrderAndVerticalCodeAbility implements DomainAbility<TradeAbilityCode, JdOrderContext> {
//
//    /**
//     * jdOrderExtRepository
//     */
//    @Resource
//    private JdOrderExtRepository jdOrderExtRepository;
//    /**
//     * tradeInfoRpc
//     */
//    @Resource
//    private TradeInfoRpc tradeInfoRpc;
//
//
//    /**
//     * 当前能力点匹配的能力编码
//     *
//     * @return
//     */
//    @Override
//    public TradeAbilityCode getAbilityCode() {
//        return TradeAbilityCode.SELF_ORDER_VERTICALCODE;
//    }
//
//    /**
//     *
//     * @param jdOrderContext
//     */
//    @Override
//    public void execute(JdOrderContext jdOrderContext) {
//        dealVerticalCodeAndServiceType(jdOrderContext.getJdOrder(),jdOrderContext.getDbOrder());
//    }
//
//    /**
//     * 处理VerticalCodeAndServiceType
//     * @param childOrder
//     * @param parentIdOrder
//     */
//    private void dealVerticalCodeAndServiceType(JdOrder childOrder, JdOrder parentIdOrder){
//        childOrder.setPartnerSourceOrderId(parentIdOrder.getPartnerSourceOrderId());
//        childOrder.setPartnerSource(parentIdOrder.getPartnerSource());
//        childOrder.setRemark(parentIdOrder.getRemark());
//
//        StringBuilder stringBuilder = new StringBuilder();
//        Integer partnerSource = childOrder.getPartnerSource();
//        if(Objects.nonNull(partnerSource) && partnerSource != 0){
//            stringBuilder.append(partnerSource);
//        }
//        List<JdOrderItem> jdOrderItemList = childOrder.getJdOrderItemList();
//        if(CollectionUtils.isEmpty(jdOrderItemList)){
//            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
//        }
//        Long skuId = jdOrderItemList.get(0).getSkuId();
//        Integer serviceType = tradeInfoRpc.querySkuServiceType(skuId);
//        if(Objects.isNull(serviceType)){
//            log.error("[JdOrderApplicationImpl->dealVerticalCodeAndServiceType],未查询到商品信息!order={}，skuId={}",childOrder.getOrderId(), skuId);
//            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
//        }
//        stringBuilder.append(serviceType);
//        String isImmediatelyFlag = CommonConstant.ZERO_STR;
//        JdOrderExt jdOrderExt = jdOrderExtRepository.findJdOrderExtDetail(parentIdOrder.getOrderId(), JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType());
//        if(Objects.nonNull(jdOrderExt)){
//            String extContext = jdOrderExt.getExtContext();
//            log.info("[JdOrderApplicationImpl->dealVerticalCodeAndServiceType] extContext={}",extContext);
//            if(StringUtils.isNotBlank(extContext)){
//                OrderAppointmentInfoValueObject orderAppointmentInfoValueObject = JsonUtil.parseObject(extContext,OrderAppointmentInfoValueObject.class);
//                if(Objects.nonNull(orderAppointmentInfoValueObject) && orderAppointmentInfoValueObject.getAppointmentTime().getIsImmediately()){
//                    isImmediatelyFlag = CommonConstant.ONE_STR;
//                }
//            }
//        }
//
//        copyParentExt(childOrder,parentIdOrder,jdOrderExt);
//        stringBuilder.append(isImmediatelyFlag);
//        ServiceHomeTypeEnum serviceHomeTypeEnum = ServiceHomeTypeEnum.getServiceHomeTypeEnum(stringBuilder.toString());
//        log.info("[JdOrderApplicationImpl->dealVerticalCodeAndServiceType] stringBuilder={}",stringBuilder.toString());
//        if(Objects.nonNull(serviceHomeTypeEnum)){
//            childOrder.setVerticalCode(serviceHomeTypeEnum.getVerticalCode());
//            childOrder.setServiceType(serviceHomeTypeEnum.getServiceType());
//            parentIdOrder.setVerticalCode(serviceHomeTypeEnum.getVerticalCode());
//            parentIdOrder.setServiceType(serviceHomeTypeEnum.getServiceType());
//        }
//    }
//
//    /**
//     *
//     * @param childOrder
//     * @param parentIdOrder
//     */
//    private void copyParentExt(JdOrder childOrder,JdOrder parentIdOrder,JdOrderExt jdOrderExt){
//        if(!childOrder.getOrderId().equals(parentIdOrder.getOrderId())){
//            List<JdOrderExt> jdOrderExtList = Lists.newArrayList();
//            if(CollUtil.isNotEmpty(childOrder.getJdOrderExtList())){
//                jdOrderExtList.addAll(childOrder.getJdOrderExtList());
//            }
//            if(Objects.nonNull(jdOrderExt)){
//                jdOrderExt.setOrderId(childOrder.getOrderId());
//                jdOrderExt.setCreateTime(new Date());
//                jdOrderExt.setUpdateTime(new Date());
//                jdOrderExtList.add(jdOrderExt);
//            }
//            JdOrderExt jdOrderExt2 = jdOrderExtRepository.findJdOrderExtDetail(parentIdOrder.getOrderId(), JdOrderExtTypeEnum.SERVICE_FEE_INFO.getExtType());
//            if(Objects.nonNull(jdOrderExt2)){
//                jdOrderExt2.setOrderId(childOrder.getOrderId());
//                jdOrderExt2.setCreateTime(new Date());
//                jdOrderExt2.setUpdateTime(new Date());
//                jdOrderExtList.add(jdOrderExt2);
//            }
//            childOrder.setJdOrderExtList(jdOrderExtList);
//        }
//    }
//}
