package com.jdh.o2oservice.core.domain.trade.service.ability;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.trade.context.QueryOrderRefundAmountContext;
import com.jdh.o2oservice.core.domain.trade.enums.ReFundAmountTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderMoney;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderMoneyRepository;
import com.jdh.o2oservice.core.domain.trade.repository.query.JdOrderMoneyQuery;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmount;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmountExpand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:查询最大退款金额Ability
 * @Description: 查询最大退款金额
 * @Author: liwenming
 * @Date: 2024/5/10 20:35
 * @Vserion: 1.0
 **/
@Slf4j
@Component
public class QueryRefundMostAmountNoActualPaymentAbility implements DomainAbility<TradeAbilityCode, QueryOrderRefundAmountContext> {

    /**
     * jdOrderMoneyRepository
     */
    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;
    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public TradeAbilityCode getAbilityCode() {
        return TradeAbilityCode.QUERY_MOST_REFUND_AMOUNT_NO_ACTUAL_PAY;
    }

    /**
     * 执行
     *
     * @param context R
     */
    @Override
    public void execute(QueryOrderRefundAmountContext context) {
        JdOrder orderDetail = context.getOrderDetail();
        BigDecimal orderRefundAmount = BigDecimal.ZERO;
        // 订单实付款
        if(Objects.nonNull(orderDetail)){
            orderRefundAmount = orderDetail.getOrderAmount();
        }

        if(orderRefundAmount.compareTo(BigDecimal.ZERO) == 0){
            BigDecimal orderAmount = getOrderActualAmount(context.getOrderId());
            if(orderAmount.compareTo(BigDecimal.ZERO) == 0){
                context.setMostRefundAmount(BigDecimal.ZERO);
                log.info("QueryRefundMostAmountNoActualPaymentAbility -> context={}", context);
                return;
            }else{
                orderRefundAmount = orderAmount;
            }
        }

        if(RefundTypeEnum.ORDER_REFUND.getType().equals(context.getQueryAmountType())){
            context.setMostRefundAmount(orderRefundAmount);
        }else if(RefundTypeEnum.AMOUNT_REFUND.getType().equals(context.getQueryAmountType())){
            BigDecimal refundAmount = calcOrderRefundAmount(context,orderRefundAmount);
            context.setMostRefundAmount(refundAmount);
        }
        log.info("QueryRefundMostAmountNoActualPaymentAbility -> context={}", context);
    }

    /**
     * 计算退款金额比例
     * @param context
     */
    private BigDecimal calcOrderRefundAmount(QueryOrderRefundAmountContext context,BigDecimal orderRefundAmount){
        // ext金额
        BigDecimal feeAmount = getRefundFeeAmount(context.getOrderId(),507);
        // sku金额
        BigDecimal orderSkuTotalAmount = orderRefundAmount.subtract(feeAmount);
        // 服务费:可以退款的钱
        List<JdOrderItem> jdOrderItems = getJdOrderItemList(context.getOrderId());
        AssertUtils.isNotEmpty(jdOrderItems,TradeErrorCode.REFUND_ORDER_ITEM_NULL);
        Integer size = context.getPromisePatientIdList().size();
        JdOrderItem jdOrderItem = jdOrderItems.get(0);
        // 单个sku金额
        BigDecimal orderSkuAvgAmount = orderSkuTotalAmount.divide(BigDecimal.valueOf(jdOrderItem.getSkuNum()),2,BigDecimal.ROUND_DOWN);
        // 本次最大可退款金额
        BigDecimal totalAmount = orderSkuAvgAmount.multiply(BigDecimal.valueOf(size));
        BigDecimal querySkuRefundAmount = totalAmount;
        if(context.getLastChildOrder()){
            BigDecimal lastSkuAmount = orderSkuTotalAmount.subtract(orderSkuAvgAmount.multiply(BigDecimal.valueOf(jdOrderItem.getSkuNum() - 1)));
            querySkuRefundAmount = lastSkuAmount.add(orderSkuAvgAmount.multiply(BigDecimal.valueOf(size - 1)));
            totalAmount = querySkuRefundAmount.add(feeAmount);
        }

        context.setQueryFeeRefundAmount(feeAmount);
        context.setQuerySkuRefundAmount(querySkuRefundAmount);
        return totalAmount;
    }

    /**
     * 时段费，上门费，动态调整费
     * @param orderId
     * @return
     */
    private BigDecimal getRefundFeeAmount(Long orderId,Integer feeMoneyType){
        BigDecimal feeAmount = BigDecimal.ZERO;
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(orderId);
        if(CollectionUtil.isEmpty(jdOrderMoneyList)){
            jdOrderMoneyList = createOrderMoneyInfo(orderId);
        }
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoney jdOrderMoney : jdOrderMoneyList){
                if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                    feeAmount = feeAmount.add(jdOrderMoney.getAmount());
                }
            }
        }
        log.info("QueryRefundMostAmountNoActualPaymentAbility -> getRefundFeeAmount feeAmount={}", feeAmount);
        return feeAmount;
    }

    /**
     * 时段费，上门费，动态调整费
     * @param orderId
     * @return
     */
    private BigDecimal getOrderActualAmount(Long orderId){
        BigDecimal orderAmount = BigDecimal.ZERO;
        List<Integer> moneyTypeList = ReFundAmountTypeEnum.getRefundTypeList();
        JdOrderMoneyQuery jdOrderMoneyQuery = new JdOrderMoneyQuery();
        jdOrderMoneyQuery.setOrderId(orderId);
        jdOrderMoneyQuery.setMoneyTypeList(moneyTypeList);
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findOrderMoneyList(jdOrderMoneyQuery);
        if(CollectionUtil.isEmpty(jdOrderMoneyList)){
            jdOrderMoneyList = createOrderMoneyInfo(orderId);
        }
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoney jdOrderMoney : jdOrderMoneyList){
                if(moneyTypeList.contains(jdOrderMoney.getMoneyType())){
                    orderAmount = orderAmount.add(jdOrderMoney.getAmount());
                }
            }
        }
        log.info("QueryRefundMostAmountNoActualPaymentAbility -> getOrderActualAmount orderAmount={}", orderAmount);
        return orderAmount;
    }

    /**
     *
     * @param orderId
     * @return
     */
    private List<JdOrderItem> getJdOrderItemList(Long orderId){
        List<JdOrderItem> jdOrderItems = jdOrderItemRepository.itemListByOrderId(orderId);
        return jdOrderItems;
    }
    /**
     * 生成订单金额信息列表
     *
     * @param orderId
     * @return
     */
    private static List<JdOrderMoney> createOrderMoneyInfo(Long orderId) {
        OrderCalculationQueryServiceRpc orderCalculationQueryServiceRpc = SpringUtil.getBean(OrderCalculationQueryServiceRpc.class);
        String amountAndExpand = orderCalculationQueryServiceRpc.queryOrderSplitAmountAndExpand(orderId);
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        if(StringUtils.isBlank(amountAndExpand)){
            return jdOrderMoneyList;
        }
        List<OrderAmount> orderAmountList = JSON.parseArray(amountAndExpand, OrderAmount.class);
        log.info("QueryRefundMostAmountNoActualPaymentAbility -> createOrderMoneyInfo, orderId={}, orderAmountList={}", orderId, JSON.toJSONString(orderAmountList));

        if (CollectionUtils.isEmpty(orderAmountList)){
            log.info("QueryRefundMostAmountNoActualPaymentAbility -> createOrderMoneyInfo 获取订单金额明细失败, orderId={}", orderId);
            return jdOrderMoneyList;
        }
        for (OrderAmount orderAmount : orderAmountList) {
            if (null == orderAmount.getSkuId()){
                log.info("QueryRefundMostAmountNoActualPaymentAbility -> createOrderMoneyInfo, skuid is null,  orderAmount={}", JSON.toJSONString(orderAmount));
                continue;
            }
            Map<Integer,List<OrderAmountExpand>> orderAmountExpandMap = orderAmount.getAmountExpands().stream().collect(Collectors.groupingBy(b -> b.getType()));
            for(Map.Entry<Integer, List<OrderAmountExpand>> orderAmountExpand: orderAmountExpandMap.entrySet()){
                JdOrderMoney entity = new JdOrderMoney();
                List<OrderAmountExpand> orderAmountExpandList = orderAmountExpand.getValue();
                Integer amountType = orderAmountExpand.getKey();
                BigDecimal amount = new BigDecimal(0);
                for(OrderAmountExpand amountExpand : orderAmountExpandList){
                    amount = amount.add(amountExpand.getAmount());
                }
                if(amount.compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                entity.setOrderId(orderAmount.getOrderId());
                entity.setSkuId(orderAmount.getSkuId());
                entity.setMoneyType(amountType);
                entity.setAmount(amount);
                entity.setVersion(NumConstant.NUM_1);
                entity.setYn(YnStatusEnum.YES.getCode());
                entity.setCreateTime(new Date());
                entity.setUpdateTime(new Date());
                jdOrderMoneyList.add(entity);
            }
        }

        return jdOrderMoneyList;
    }

}
