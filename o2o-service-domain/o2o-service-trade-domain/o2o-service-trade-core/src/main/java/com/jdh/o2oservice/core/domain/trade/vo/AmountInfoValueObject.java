package com.jdh.o2oservice.core.domain.trade.vo;

import com.jdh.o2oservice.base.model.ValueObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * AmountInfoValueObject 金额信息
 * <AUTHOR>
 * @version 2024/3/1 14:42
 **/
@Data
public class AmountInfoValueObject implements ValueObject<AmountInfoValueObject> {

    /**
     * 订单应付总金额
     */
    private BigDecimal factOrderAmount;

    /**
     * 清单应付总金额
     */
    private BigDecimal factShoppingListAmount;

    /**
     * 商品金额
     */
    private BigDecimal goodsAmount;

    /**
     * 立减金额
     */
    private BigDecimal totalReprice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 总运费
     */
    private BigDecimal totalFreight;

    /**
     * 已优惠金额（优惠券+单品促销+plus95折+店铺新人礼金等）
     */
    private BigDecimal discountAmount;

    /**
     * 官方立减金额
     */
    private BigDecimal totalOfficialDiscount;

    /**
     * 跨店满减金额
     */
    private BigDecimal overlaySumReward;

    /**
     * 促销优惠金额(单品促销+plus95折+店铺新人礼金等)
     */
    private BigDecimal promotionDiscountAmount;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmount;

    /**
     * 店铺新人礼金
     */
    private BigDecimal giftCashDiscountAmount;

    /**
     * 并行促销
     */
    private BigDecimal totalParallelDiscount;

    @Override
    public boolean sameValueAs(AmountInfoValueObject other) {
        return false;
    }
}
