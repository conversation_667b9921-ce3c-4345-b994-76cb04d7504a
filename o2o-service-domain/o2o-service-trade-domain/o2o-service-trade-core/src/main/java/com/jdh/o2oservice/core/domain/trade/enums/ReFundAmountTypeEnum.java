package com.jdh.o2oservice.core.domain.trade.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 退款金额类型
 * @Date 2020/3/18 16:00
 */
public enum ReFundAmountTypeEnum {
    /**
     * 退款金额类型
     */
    GIFT_CARD(101, "E卡",-4),
    BALANCE(110, "余额",1),
    ONLINE_PAYMENT(112, "在线支付",9),
    EPT_BEAN(123, "京豆",14),
    PAY_YX_BEAN(126, "支付有礼",0),
    SUPER_REDBAO_PAY(151, "超级红包支付",-107),
    EPT_BEAN2(173, "营销京豆",101),
    BAITIAO_FENQI(1017, "白条分期",9),
    ;

    /**
     *
     * @param type
     * @param desc
     * @param refundType
     */
    ReFundAmountTypeEnum(Integer type, String desc, Integer refundType) {
        this.type = type;
        this.desc = desc;
        this.refundType = refundType;
    }

    /**
     *
     */
    private Integer type;
    /**
     *
     */
    private String desc;
    /**
     *
     */
    private Integer refundType;

    public Integer getType() {
        return type;
    }
    public void setType(Integer type) {
        this.type = type;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
    public Integer getRefundType() {
        return refundType;
    }
    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    /**
     * 支付类型转退款类型
     * @param type
     * @return
     */
    public static Integer getRefundTypeOfType(Integer type){
        if (type == null){
            return null;
        }
        for (ReFundAmountTypeEnum reFundAmountTypeEnum:ReFundAmountTypeEnum.values()){
            if(reFundAmountTypeEnum.getType().toString().equals(String.valueOf(type))){
                return reFundAmountTypeEnum.getRefundType();
            }
        }
        return null;
    }

    /**
     * 支付类型转退款类型
     * @return
     */
    public static List<Integer> getRefundTypeList(){
        List<Integer> list = new ArrayList<>();
        list.add(ReFundAmountTypeEnum.GIFT_CARD.getType());
        list.add(ReFundAmountTypeEnum.BALANCE.getType());
        list.add(ReFundAmountTypeEnum.ONLINE_PAYMENT.getType());
        list.add(ReFundAmountTypeEnum.EPT_BEAN.getType());
        list.add(ReFundAmountTypeEnum.SUPER_REDBAO_PAY.getType());
        list.add(ReFundAmountTypeEnum.EPT_BEAN2.getType());
        list.add(ReFundAmountTypeEnum.BAITIAO_FENQI.getType());
        //list.add(507);
        return list;
    }

    /**
     * @param type
     * @return
     */
    public static String getDescOfType(Integer type){
        for (ReFundAmountTypeEnum amountEnum:ReFundAmountTypeEnum.values()){
            if(amountEnum.getType().equals(type)){
                return amountEnum.getDesc();
            }
        }
        return "退款";
    }

}
