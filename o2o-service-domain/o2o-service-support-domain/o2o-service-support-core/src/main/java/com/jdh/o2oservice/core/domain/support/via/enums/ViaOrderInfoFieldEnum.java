package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 订单信息字段枚举
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaOrderInfoFieldEnum {

    /**
     * 订单信息枚举字段
     */
    ORDER_AMOUNT("orderAmount","实付金额"),

    ORDER_ID("orderId","订单号"),

    ORDER_CREATE_TIME("orderCreateTime","下单时间"),

    PAY_TYPE_DESC("payTypeDesc","支付方式(中文)"),
    ORDER_USER_PHONE("orderUserPhone","下单用户电话"),
    ORDER_USER_NAME("orderUserName","下单用户姓名"),
    ADDRESS_DETAIL("addressDetail","下单地址"),
    ORDER_REMARK("remark","备注"),
    ORDER_STATUS_DESC("orderStatusDesc","订单状态描述"),
    ORDER_DISCOUNT("orderDiscount","优惠金额"),
    ORDER_COUPON("orderCoupon","优惠券金额"),
    APPOINTMENT_TIME("appointmentTime","预约时间（期望时间）"),
    ORDER_SKU_INFO_LIST("orderSkuInfoList","订单商品列表"),
    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    ViaOrderInfoFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
