package com.jdh.o2oservice.core.domain.support.basic.dict.repository;

import com.jdh.o2oservice.core.domain.support.basic.dict.model.DictInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 词典仓库管理
 *
 * @date 2024-04-21 20:37
 * <AUTHOR>
 */
public interface DictRepository {

    /**
     * 获取多个词典数据
     *
     * @param keys keys
     * @return map
     */
    Map<String, List<DictInfo>> queryCommonDictList(Set<String> keys);

    /**
     * 获取多个词典数据
     *
     * @param groups groups
     * @return map
     */
    Map<String, List<DictInfo>> queryMultiDictList(Set<String> groups);
    
    /**
     * 获取多个词典数据
     *
     * @param group group
     * @param parentLevel parentLevel
     * @return list
     */
    List<DictInfo> querySubDictList(String group, String parentValue, Integer parentLevel);

}
