package com.jdh.o2oservice.core.domain.support.file.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ContentTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.HttpUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.basic.model.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.file.context.PdfAddSignatureBO;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.enums.FileSignatureRelationEnum;
import com.jdh.o2oservice.core.domain.support.file.factory.JdhFileTaskFactory;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 京东云OSS操作工具类
 *
 * @author: yangxiyu
 * @date: 2023/4/18 8:10 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class FileManageServiceImpl implements FileManageService {

    /**
     * 文件过期时间标记
     */
    private static final String EXPIRE_TAG_PATTERN = "tag_expireTime_{0}";
    /**
     * 默认过期时间
     */
    private static final Long DEFAULT_EXPIRE_TIME = 30L;
    private static final Integer MAX_DAY = 6;
    /**
     *
     */
    @Value("${oss.defaultBucket}")
    private String fileBucket;
    /**
     * 内网上传、下载文件使用，不收费
     */
    @Resource
    private AmazonS3 internalAmazonS3;
    /**
     * 生成需要公网访问的链接时候使用，收费
     */
    @Resource
    private AmazonS3 publicAmazonS3;
    /**
     * V2版本，支持长时过期时间
     */
    @Resource
    private AmazonS3 publicAmazonS3V2;
    /**
     * V2版本，支持长时过期时间
     */
    @Resource
    private AmazonS3 internalAmazonS3V2;
    /**
     * PDF文件追加签名的文件类型配置
     */
    @LafValue("file_pdf_add_signature")
    private String filePdfAddSignature;

    @Autowired
    private DuccConfig duccConfig;

    /**
     * 用于执行HTTP请求的工具类实例。
     */
    @Autowired
    private HttpUtil httpUtil;

    /**
     * 获取 PDF追加签名的配置
     *
     * @param domainCode
     * @param fileBizType
     * @return
     */
    public PdfAddSignatureBO getConfig(String domainCode, String fileBizType) {
        if (StringUtils.isBlank(filePdfAddSignature)) {
            return null;
        }
        List<PdfAddSignatureBO> array = JSON.parseArray(filePdfAddSignature, PdfAddSignatureBO.class);
        for (PdfAddSignatureBO pdfAddSignatureBO : array) {
            if (StringUtils.equals(domainCode, pdfAddSignatureBO.getDomainCode()) && StringUtils.equals(pdfAddSignatureBO.getFileBizType(), fileBizType)) {
                return pdfAddSignatureBO;
            }
        }
        return null;
    }


    /**
     * @param fileName
     * @param inputStream
     * @param filePath
     * @param contentType 如果不传contentType在生成查询的预签名链接时会直接下载文件，传了contentType才会有预览功能
     * @return
     */
    @Override
    public PutFileResult put(String fileName, InputStream inputStream, FolderPathEnum filePath, String contentType, Boolean isPublic) {
        log.info("AmazonS3Util->put start fileName={}, filePath={}", fileName, filePath.path);
        AssertUtils.nonNull(filePath, "filePath is null");

        String key = filePath.getPath() + fileName;
        //获取输入流
        ObjectMetadata objectMetadata = new ObjectMetadata();
        if (StringUtils.isNotBlank(contentType)) {
            // 添加文件类型校验
            if(!httpUtil.jdExtCheck(fileName)){
                log.info("AmazonS3Util->put start fileName={},ext error", fileName);
//                throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
            }
            objectMetadata.setContentType(contentType);
        }
        PutObjectRequest req = new PutObjectRequest(fileBucket, key, inputStream, objectMetadata);

        // 设置标签
        List<Tag> tagSet = Lists.newArrayList();
        if (filePath.expireTime > 0) {
            String tagKey = MessageFormat.format(EXPIRE_TAG_PATTERN, filePath.expireTime);
            tagSet.add(new Tag(tagKey, String.valueOf(filePath.expireTime)));
        }
        ObjectTagging tagging = new ObjectTagging(tagSet);
        req.setTagging(tagging);
        //上传文件流
        try {
            log.info("AmazonS3Util->putObject start ");
            if (Boolean.TRUE.equals(isPublic)) {
                publicAmazonS3.putObject(req);
            } else {
                internalAmazonS3.putObject(req);
            }
            log.info("AmazonS3Util->putObject end ");
        } catch (AmazonServiceException e) {
            log.error("AmazonS3Util->put error key={}, key={} msg{}", key, filePath.path, e.getErrorMessage());
            throw new SystemException(SystemErrorCode.OSS_PUT_ERROR);
        }

        PutFileResult result = new PutFileResult(fileBucket, LocalDateTime.now().plusDays(filePath.getExpireTime()), key);
        log.info("AmazonS3Util->put end ");
        return result;
    }


    @Override
    public InputStream get(String fileName, FolderPathEnum filePath) {
        log.info("AmazonS3Util->get fileName={}, filePath={}", fileName, filePath.path);
        AssertUtils.nonNull(filePath, "filePath is null");

        String key = filePath.getPath() + fileName;
        return get(key);
    }

    /**
     * 获取文件
     *
     * @param allPathFileName 全路径文件名称 如 fileManage/import.xlsx
     * @return
     */
    @Override
    public InputStream get(String allPathFileName) {
        GetObjectRequest request = new GetObjectRequest(fileBucket, allPathFileName);
        try {
            S3Object s3Object = internalAmazonS3.getObject(request);
            return s3Object.getObjectContent();
        } catch (Exception e) {
            log.info("AmazonS3Util->get fail ", e);
            throw new SystemException(SystemErrorCode.OSS_GET_ERROR);
        }
    }

    /**
     * 获取元数据
     *
     * @param allPathFileName
     * @return
     */
    @Override
    public ObjectMetadata getObjectMetadata(String allPathFileName) {
        GetObjectMetadataRequest request = new GetObjectMetadataRequest(fileBucket, allPathFileName);
        try {
            return internalAmazonS3.getObjectMetadata(request);
        } catch (Exception e) {
            log.info("AmazonS3Util->getObjectMetadata fail ", e);
            throw new SystemException(SystemErrorCode.OSS_GET_ERROR);
        }
    }

    /**
     * @param fileKey
     * @param isPublic
     * @param expiration
     * @return
     */
    @Override
    public String getPublicUrl(String fileKey, Boolean isPublic, Date expiration) {
        if (Objects.isNull(expiration)) {
            LocalDateTime time = LocalDateTime.now().plus(DEFAULT_EXPIRE_TIME, ChronoUnit.MINUTES);
            expiration = TimeUtils.localDateTimeToDate(time);
        }

        URL url = getUrl(fileKey, isPublic, expiration);
        return url.toString();
    }

    /**
     * 生产查询的预签名链接
     *
     * @param jdhFiles
     * @param isPublic
     * @param expiration
     * @return
     */
    @Override
    public Map<Long, String> generateGetUrl(List<JdhFile> jdhFiles, Boolean isPublic, Date expiration) {
        if (Objects.isNull(expiration)) {
            LocalDateTime time = LocalDateTime.now().plus(DEFAULT_EXPIRE_TIME, ChronoUnit.MINUTES);
            expiration = TimeUtils.localDateTimeToDate(time);
        }

        Map<Long, String> fileMapUrl = Maps.newHashMap();
        for (JdhFile jdhFile : jdhFiles) {
            URL url = getUrl(jdhFile.getFilePath(), isPublic, expiration);
            fileMapUrl.put(jdhFile.getFileId(), url.toString());
        }

        return fileMapUrl;
    }

    @Override
    public URL generateGetUrl(JdhFile jdhFile, Boolean isPublic, Date expiration) {
        if (Objects.isNull(expiration)) {
            LocalDateTime time = LocalDateTime.now().plus(DEFAULT_EXPIRE_TIME, ChronoUnit.MINUTES);
            expiration = TimeUtils.localDateTimeToDate(time);
        }
        return getUrl(jdhFile.getFilePath(), isPublic, expiration);
    }

    /**
     * 默认的生成预签名链接
     *
     * @return
     */
    @SuppressWarnings("JdJDMethodParamsCount")
    @Override
    public URL generatePutUrl(JdhFile file, Boolean isPublic, Date expiration, String contentType) {
        AssertUtils.hasText(contentType, "contentType is require");
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(fileBucket, file.getFilePath(), HttpMethod.PUT);
        request.setExpiration(expiration);
        request.addRequestParameter("x-amz-storage-class", "STANDARD_IA");

        /**
         * 文档：https://docs.jdcloud.com/cn/object-storage-service/object-management#user-content-4
         * 如果不指定content-type后续生成的预签名链接会是下载，指定content-type可以实现预览
         */
        request.setContentType(contentType);
        try {
            if (isPublic) {
                return publicAmazonS3.generatePresignedUrl(request);
            } else {
                return internalAmazonS3.generatePresignedUrl(request);
            }
        } catch (Throwable e) {
            log.error("AmazonS3Util -> generateUrl fileName={}", file.getFilePath(), e);
            throw new SystemException(SystemErrorCode.OSS_GENERATE_URL_ERROR);
        }
    }

    /**
     * 公网链接转化京东云链接文件上传
     *
     * @param key      文件名称
     * @param outUrl   公网链接
     * @param filePath 文件路径
     * @return PutFileResult
     */
    @Override
    public PutFileResult transferPut(String key, String outUrl, FolderPathEnum filePath, String contentType) {
        log.info("FileManageServiceImpl->transferPut start");
        URL url = null;
        try {
            url = new URL(outUrl);
        }catch (Exception e){
            log.error("FileManageServiceImpl->transferPut fail ", e);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);

        }
        if (!httpUtil.jdSsrfCheck(url)){
            log.error("FileManageServiceImpl->transferPut url jdSsrfCheck  fail，url={} ", outUrl);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
        }
        byte[] bytes = null;
        try {
            HttpResponse response = HttpRequest.get(outUrl)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                    .execute();
            bytes = response.bodyBytes();
            if (Objects.isNull(bytes) || bytes.length == 0 ){
                throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
            }else {
                log.info("FileManageServiceImpl->response bytes.length={}", bytes.length);
                if (Objects.nonNull(duccConfig.getFileMinByteLength())){
                    if (bytes.length < duccConfig.getFileMinByteLength()){
                        throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
                    }
                }
            }
            log.info("FileManageServiceImpl->response end");
        } catch (Exception e) {
            //文件下载失败
            log.info("transferPut,error", e);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        return put(key, inputStream, filePath, contentType, Boolean.FALSE);
    }

    /**
     * 公网链接转化京东云链接文件上传
     *
     * @param key      文件名称
     * @param outUrl   公网链接
     * @param filePath 文件路径
     * @return PutFileResult
     */
    @Override
    public PutFileResult transferPutV2(String key, String outUrl, FolderPathEnum filePath, String contentType) {
        log.info("FileManageServiceImpl->transferPutV2 start");
        URL url = null;
        try {
            url = new URL(outUrl);
        }catch (Exception e){
            log.error("FileManageServiceImpl->transferPutV2 fail ", e);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);

        }
        if (!httpUtil.jdSsrfCheck(url)){
            log.error("FileManageServiceImpl->transferPutV2 url jdSsrfCheck  fail，url={} ", outUrl);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
        }
        byte[] bytes = null;
        try {
            // 创建HttpClient实例
            HttpClient httpClient = HttpClients.createDefault();
            // 创建HttpGet实例，设置请求的URL
            HttpGet httpGet = new HttpGet(outUrl);
            // 执行GET请求
            org.apache.http.HttpResponse httpResponse = httpClient.execute(httpGet);
            bytes = EntityUtils.toByteArray(httpResponse.getEntity());
            log.info("FileManageServiceImpl->transferPutV2 response end");
        } catch (Exception e) {
            //文件下载失败
            log.info("transferPutV2,error", e);
            throw new BusinessException(BaseDomainErrorCode.FILE_DOWNLOAD_ERROR);
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        return put(key, inputStream, filePath, contentType, Boolean.FALSE);
    }

    /**
     * PDF文件追加签名信息，签名信息追加后将文件上传到OSS
     *
     * @param
     * @return
     */
    @Override
    public JdhFile pdfAddSignature(JdhFile signatureImage, String domainCode, String fileBizType) {
        log.info("FileManageServiceImpl->pdfAddSignature , signatureImage={}", signatureImage.toString());
        PdfStamper pdfStamper = null;
        try {
            PdfAddSignatureBO config = getConfig(domainCode, fileBizType);
            if (Objects.isNull(config)) {
                log.error("FileManageServiceImpl->pdfAddSignature config is null");
                throw new SystemException(SystemErrorCode.CONFIG_ERROR);
            }

            LocalDateTime expireTime = LocalDateTime.now().plus(DEFAULT_EXPIRE_TIME, ChronoUnit.MINUTES);
            Date expire = TimeUtils.localDateTimeToDate(expireTime);
            // pdf文件 url
            JdhFile templateFile = new JdhFile();
            templateFile.setFilePath(config.getTemplateFilePath());
            URL url = generateGetUrl(templateFile, true, expire);
            log.info("FileManageServiceImpl->pdfAddSignature sourcePdf url={}", url);
            PdfReader pdfReader = new PdfReader(url);

            // 输出流（二进制数组，存放签名后的PDF文件）
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            pdfStamper = new PdfStamper(pdfReader, outputStream);

            // 签名的图片url
            URL imageUrl = generateGetUrl(signatureImage, true, expire);
//            ObjectMetadata metadata = internalAmazonS3.getObjectMetadata(fileBucket, signatureImage.getFilePath());


            // 设置图片的大小和位置
            Image image = Image.getInstance(imageUrl);
            image.setAbsolutePosition(config.getPosition()[0], config.getPosition()[1]);
            image.scaleToFit(config.getScale()[0], config.getScale()[1]);
            PdfContentByte content = pdfStamper.getUnderContent(config.getPageNum());
            // 追加图片
            content.addImage(image);
            /**
             * 关闭 PdfStamper：确保 pdfStamper.close() 在写入文件之前被调用，以保证所有的修改都被应用。
             */
            pdfStamper.close();

            // 上传文件
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

            JdhFile file = JdhFileTaskFactory.pdfAddSignatureFile(domainCode, fileBizType, signatureImage.getCreateUser());

            put(file.getFileName(), inputStream, FolderPathEnum.FILE_MANAGE, ContentTypeEnum.PDF.getValue(), Boolean.FALSE);
            return file;
        } catch (Exception e) {
            log.error("FileManageServiceImpl->pdfAddSignature error", e);
            throw new SystemException(SystemErrorCode.OSS_PUT_ERROR);
        }
    }

    @Override
    public JdhFile pdfSignature(JdhFile signatureImage, JdhFile wImage, JdhFile templateFile, PdfAddSignatureBO config) {
        log.info("FileManageServiceImpl->pdfSignature , signatureImage={}", signatureImage.toString());
        PdfStamper pdfStamper = null;
        // 输出流（二进制数组，存放签名后的PDF文件）
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            LocalDateTime expireTime = LocalDateTime.now().plus(DEFAULT_EXPIRE_TIME, ChronoUnit.MINUTES);
            Date expire = TimeUtils.localDateTimeToDate(expireTime);
            URL url = generateGetUrl(templateFile, true, expire);
            log.info("FileManageServiceImpl->pdfAddSignature sourcePdf url={}", url);
            PdfReader pdfReader = new PdfReader(url);
            pdfStamper = new PdfStamper(pdfReader, outputStream);


            if(config.getNursePosition() != null && config.getNursePosition().length > 0){
                // 签名的图片url
                URL imageUrl = generateGetUrl(signatureImage, true, expire);
                // 设置图片的大小和位置
                Image image = Image.getInstance(imageUrl);
                int pageNum = config.getNursePosition()[0].intValue();
                float pageHeight = pdfReader.getPageSize(pageNum).getHeight();
                image.setAbsolutePosition(config.getNursePosition()[1], pageHeight - config.getNursePosition()[2]);
                image.scaleToFit(config.getScale()[0], config.getScale()[1]);
                PdfContentByte content = pdfStamper.getOverContent(pageNum);
                // 追加图片
                content.addImage(image);
            }else {
                if (config.getRelation().equals(FileSignatureRelationEnum.SELF.getRelation())) {
                    if(config.getAgentPosition() != null){
                        // 签名的图片url
                        URL imageUrl1 = generateGetUrl(wImage, true, expire);
                        // 设置图片的大小和位置
                        Image image1 = Image.getInstance(imageUrl1);
                        int pageNum1 = config.getAgentPosition()[0].intValue();
                        float pageHeight1 = pdfReader.getPageSize(pageNum1).getHeight();
                        image1.setAbsolutePosition(config.getAgentPosition()[1], pageHeight1 - config.getAgentPosition()[2]);
                        image1.scaleToFit(config.getScale()[0], config.getScale()[1]);
                        PdfContentByte content1 = pdfStamper.getOverContent(pageNum1);
                        // 追加图片
                        content1.addImage(image1);
                    }
                    // 签名的图片url
                    URL imageUrl = generateGetUrl(signatureImage, true, expire);
                    // 设置图片的大小和位置
                    Image image = Image.getInstance(imageUrl);
                    int pageNum = config.getPosition()[0].intValue();
                    float pageHeight = pdfReader.getPageSize(pageNum).getHeight();
                    image.setAbsolutePosition(config.getPosition()[1], pageHeight - config.getPosition()[2]);
                    image.scaleToFit(config.getScale()[0], config.getScale()[1]);
                    PdfContentByte content = pdfStamper.getOverContent(pageNum);
                    // 追加图片
                    content.addImage(image);
                } else {
                    if(config.getPosition() != null){
                        // 签名的图片url
                        URL imageUrl1 = generateGetUrl(wImage, true, expire);
                        // 设置图片的大小和位置
                        Image image1 = Image.getInstance(imageUrl1);
                        int pageNum1 = config.getPosition()[0].intValue();
                        float pageHeight1 = pdfReader.getPageSize(pageNum1).getHeight();
                        image1.setAbsolutePosition(config.getPosition()[1], pageHeight1 - config.getPosition()[2]);
                        image1.scaleToFit(config.getScale()[0], config.getScale()[1]);
                        PdfContentByte content1 = pdfStamper.getOverContent(pageNum1);
                        // 追加图片
                        content1.addImage(image1);
                    }
                    // 签名的图片url
                    URL imageUrl = generateGetUrl(signatureImage, true, expire);
                    // 设置图片的大小和位置
                    Image image = Image.getInstance(imageUrl);
                    int pageNum = config.getAgentPosition()[0].intValue();
                    float pageHeight = pdfReader.getPageSize(pageNum).getHeight();
                    image.setAbsolutePosition(config.getAgentPosition()[1], pageHeight - config.getAgentPosition()[2]);
                    image.scaleToFit(config.getScale()[0], config.getScale()[1]);
                    PdfContentByte content = pdfStamper.getOverContent(pageNum);
                    // 追加图片
                    content.addImage(image);
                }
            }

            // 追加文字
            int fontSize = 12;
            BaseFont bf = BaseFont.createFont("font/SimSun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            if(config.getRelationPosition() != null && config.getRelationPosition().length > 0 ) {
                float pageHeight = pdfReader.getPageSize(config.getRelationPosition()[0].intValue()).getHeight();
                PdfContentByte content1 = pdfStamper.getOverContent(config.getRelationPosition()[0].intValue());
                content1.setFontAndSize(bf, fontSize);
                float x = config.getRelationPosition()[1];
                float y = pageHeight - config.getRelationPosition()[2];
                content1.setTextMatrix(x, y);
                StringBuilder relation = new StringBuilder();
                for (FileSignatureRelationEnum value : FileSignatureRelationEnum.values()) {
                    if (!value.getRelation().equals(FileSignatureRelationEnum.SELF.getRelation())) {
                        if (!value.getRelation().equals(FileSignatureRelationEnum.SPOUSE.getRelation())) {
                            relation.append(" ");
                        }
                        if (config.getRelation().equals(value.getRelation())) {
                            relation.append("[√]").append(value.getDesc());
                        } else {
                            relation.append("[ ]").append(value.getDesc());
                        }
                    }
                }
                relation.append(":");
                StringBuilder relationStr = new StringBuilder();
                relationStr.append(" ").append(StringUtils.isNotEmpty(config.getRelationStr()) && config.getRelation().equals(FileSignatureRelationEnum.OTHER.getRelation()) ? config.getRelationStr() : "   ").append(" ");
                float textWidth = bf.getWidthPoint(relationStr.toString(), fontSize);
                content1.saveState();
                float rectHeight = fontSize + 4; // 稍大于字体高度，避免文字被裁剪
                // 绘制白色背景矩形
                content1.setColorFill(BaseColor.WHITE);
                content1.rectangle(x, y - 4, bf.getWidthPoint(relation.toString(), fontSize) + textWidth + (fontSize * 6), rectHeight); // 注意：y - rectHeight 是矩形顶部
                content1.fill();
                // 恢复图形状态（可选）
                content1.restoreState();
                content1.newlineShowText(relation.toString() + relationStr.toString());
                // 计算文本宽度
                // 绘制下划线（位于文本下方 2pt）
                content1.setLineWidth(1f);
                content1.moveTo(x + bf.getWidthPoint(relation.toString(), fontSize), y - 3); // 下移 3pt
                content1.lineTo(x + bf.getWidthPoint(relation.toString(), fontSize) + textWidth, y - 3);
                content1.stroke();
            }
            if(config.getTimePosition() != null && config.getTimePosition().length > 0) {
                float pageHeight = pdfReader.getPageSize(config.getTimePosition()[0].intValue()).getHeight();
                // 追加日期
                PdfContentByte content2 = pdfStamper.getOverContent(config.getTimePosition()[0].intValue());
                content2.setFontAndSize(bf, fontSize);
                content2.setTextMatrix(config.getTimePosition()[1], pageHeight-config.getTimePosition()[2]);
                String date = DateUtils.format(new Date(),"yyyy年MM月dd日");
                // 计算文本宽度
                float textWidth = bf.getWidthPoint(date, fontSize);
                float rectHeight = fontSize + 4; // 稍大于字体高度，避免文字被裁剪
                // 保存当前图形状态
                content2.saveState();
                // 绘制白色背景矩形
                content2.setColorFill(BaseColor.WHITE);
                content2.rectangle(config.getTimePosition()[1], pageHeight - config.getTimePosition()[2]-4, textWidth + (fontSize * 6), rectHeight); // 注意：y - rectHeight 是矩形顶部
                content2.fill();
                // 恢复图形状态（可选）
                content2.restoreState();
                content2.newlineShowText(date);
            }

        } catch (Exception e) {
            log.error("FileManageServiceImpl->pdfAddSignature error", e);
            throw new SystemException(SystemErrorCode.OSS_PUT_ERROR);
        }finally {
            try {
                /**
                 * 关闭 PdfStamper：确保 pdfStamper.close() 在写入文件之前被调用，以保证所有的修改都被应用。
                 */
                pdfStamper.close();
            } catch (Exception e) {
                log.error("FileManageServiceImpl->pdfAddSignature error", e);
                throw new SystemException(SystemErrorCode.OSS_PUT_ERROR);
            }
        }
        // 上传文件
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());

        JdhFile file = JdhFileTaskFactory.pdfAddSignatureFile(config.getDomainCode(), config.getFileBizType(), signatureImage.getCreateUser());

        put(file.getFileName(), inputStream, FolderPathEnum.FILE_MANAGE, ContentTypeEnum.PDF.getValue(), Boolean.FALSE);
        return file;
    }

    /**
     * 获取签名的合同
     *
     * @param domainEnum
     * @param fileBizType
     * @return
     */
    @Override
    public String getPdfSignatureContract(String domainEnum, String fileBizType) {
        PdfAddSignatureBO config = getConfig(domainEnum, fileBizType);
        if (Objects.isNull(config)) {
            log.error("FileManageServiceImpl->pdfAddSignature config is null");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR);
        }
        return getPublicUrl(config.getTemplateFilePath(), true, null);
    }

    /**
     * 获取元数据（指定环境）
     *
     * @param allPathFileName
     * @param bucket
     * @return
     */
    @Override
    public ObjectMetadata getObjectMetadataWithBucket(String allPathFileName, String bucket) {
        GetObjectMetadataRequest request = new GetObjectMetadataRequest(bucket, allPathFileName);
        try {
            return internalAmazonS3.getObjectMetadata(request);
        } catch (Exception e) {
            log.info("AmazonS3Util->getObjectMetadata fail ", e);
            throw new SystemException(SystemErrorCode.OSS_GET_ERROR);
        }
    }


    /**
     * 判断文件是否存在
     *
     * @param fileName
     * @return
     */
    public Boolean doesObjectExist(String fileName) {
        return internalAmazonS3.doesObjectExist(fileBucket, fileName);
    }

    /**
     * 获取预签名链接
     * @param fileKey
     * @param isPublic
     * @param expiration
     * @return
     */
    private URL getUrl(String fileKey, Boolean isPublic, Date expiration) {
        LocalDateTime maxExpire = LocalDateTime.now().plusDays(MAX_DAY);
        Date maxDate = TimeUtils.localDateTimeToDate(maxExpire);
        URL url;
        if (isPublic) {

            if (expiration.before(maxDate)) {
                url = publicAmazonS3.generatePresignedUrl(fileBucket, fileKey, expiration);
            } else {
                url = publicAmazonS3V2.generatePresignedUrl(fileBucket, fileKey, expiration);
            }
            return url;
        } else {
            if (expiration.before(maxDate)) {
                url = internalAmazonS3.generatePresignedUrl(fileBucket, fileKey, expiration);
            } else {
                url = internalAmazonS3V2.generatePresignedUrl(fileBucket, fileKey, expiration);
            }
        }
        return url;
    }


    /**
     * 文件过期时间，过期后文件在存储会被删除
     */
    public enum FolderPathEnum {
        /**
         * 文件导出存储的文件夹
         */
        FILE_OUT_PUT("FileOutput/", 10),
        /**
         * 文件管理文件夹
         */
        FILE_MANAGE("FileManage/", -1),

        REPORT("report/", -1),

        /**
         * 样本编码记录
         */
        SPECIMEN_CODE("specimenCode/", -1),

        /**
         * 数据清洗文件夹
         */
        DATA_CLEAN("dataClean/", -1),

        /**
         * 业务导出数据
         */
        BUSINESS_DOWNLOAD("businessDown/", -1),

        /**
         * 外呼记录
         */
        CALL_RECORD("callRecord/", -1),
        ;

        FolderPathEnum(String path, Integer expireTime) {
            this.path = path;
            this.expireTime = expireTime;
        }

        /**
         * 文件路径
         */
        private String path;
        /**
         * 文件过期删除时间，为-1表示不过期删除，单位天，这个过期时间只是一个标记，具体的删除规则需要在OSS存储空间配置
         */
        private Integer expireTime;

        /**
         * @return
         */
        public String getPath() {
            return path;
        }

        /**
         * @return
         */
        public Integer getExpireTime() {
            return expireTime;
        }


        private static final Map<String, FolderPathEnum> PATH_ENUM_MAP = new HashMap<>();

        static {
            for (FolderPathEnum folderPathEnum : FolderPathEnum.values()) {
                PATH_ENUM_MAP.put(folderPathEnum.getPath(), folderPathEnum);
            }
        }

        public static FolderPathEnum parse(String path) {

            return PATH_ENUM_MAP.get(path);
        }
    }


}
