package com.jdh.o2oservice.core.domain.support.pdf.repository;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ContentTypeEnum;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.BarcodeUtil;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.pdf.bo.IndicatorPdfBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.MedicalPromisePdfBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.PdfCreateResultBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

/**
 * @Description 自检测pdf生成
 * @Date 2024/12/31 上午11:36
 * <AUTHOR>
 **/
@Component
@Slf4j
public class SelfTestPdfCreateV2 implements PdfCreateProcessor, MapAutowiredKey {

    // 定义全局的字体静态变量
    private static Font font16Bold;
    private static Font font8Bold;
    private static Font font8NormalRed;
    private static Font font8Normal;

    @Resource
    private DuccConfig duccConfig;

    static {
        try {
            // 不同字体（这里定义为同一种字体：包含不同字号、不同style）
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            font16Bold = new Font(bfChinese, 17, Font.BOLD);
            font8Bold = new Font(bfChinese, 8, Font.BOLD);
            font8Normal = new Font(bfChinese, 8, Font.NORMAL);
            font8NormalRed = new Font(bfChinese, 8, Font.NORMAL, BaseColor.RED);
        } catch (Exception e) {
            log.error("SelfTestPdfCreateV2 static font error e", e);
        }
    }


    @Resource
    private FileManageService fileManageService;

    @Override
    public String getMapKey() {
        return "pdfCreateSelfTestV2";
    }

    @Test
    public void test(){
        String s = "{\"pdfInfo\":\"{\\\"stationName\\\":\\\"西城绿通实验室\\\",\\\"sampleTime\\\":\\\"2025-01-01 15:10:15\\\",\\\"checkTime\\\":\\\"2025-01-01 16:10:15\\\",\\\"reportTime\\\"" +
                ":\\\"2025-01-01 17:10:15\\\",\\\"specimenCode\\\":\\\"JD0017290596\\\",\\\"age\\\":\\\"10\\\"" +
                ",\\\"userGenderStr\\\":\\\"男\\\",\\\"medicalPromiseId\\\":159303337509289,\\\"serviceItemName\\\":\\\"呼吸道病毒细菌12联检\\\",\\\"userName\\\":\\\"京智康\\\",\\\"indicatorPdfBos\\\":[{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"10\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"新型冠状病毒\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"8\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"人腺病毒\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"13.5\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"肺炎衣原体\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"16\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"人冠状病毒\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"3\\\",\\\"ctValue\\\":\\\"19.5\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"甲流\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阳性\\\",\\\"valueDescription\\\":\\\"阳性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"6\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"人鼻病毒\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"21\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"肺炎链球菌\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"12\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"人副流感病毒\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"13\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"乙流\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"17\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"肺炎支原体\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"ctValue\\\":\\\"11\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"流感嗜血杆菌\\\",\\\"normalRangeValue\\\":\\\"CT=0或CT>40\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"},{\\\"abnormalType\\\":\\\"0\\\",\\\"testingMethod\\\":\\\"RT-PCR检测技术\\\",\\\"indicatorName\\\":\\\"肺炎支原体耐药基因\\\",\\\"value\\\":\\\"阴性\\\",\\\"valueDescription\\\":\\\"阴性\\\"}]}\",\"pdfName\":\"159303337509289_240817220229689.pdf\",\"template\":\"pdfCreateSelfTest\",\"uploadToOss\":true}";
        PdfCreateBo pdfCreateBo = JsonUtil.parseObject(s, PdfCreateBo.class);
        this.createPdf(pdfCreateBo);
        // 采样时间
        String sampleTime = "2025-01-01 16:10:15";
        // 收样时间
        String checkTime = "2025-01-01 17:10:15";
        // 报告时间
        String reportTime = "2025-01-01 18:10:15";
        timeDisplayRule(sampleTime, checkTime, reportTime);
    }


    /**
     * 创建pdf
     * @param pdfCreateBo
     * @return
     */
    @Override
    public PdfCreateResultBo createPdf(PdfCreateBo pdfCreateBo) {
        log.info("SelfTestPdfCreateV2 createPdf pdfCreateBo={}", JsonUtil.toJSONString(pdfCreateBo));
        MedicalPromisePdfBo medicalPromisePdfBo = JsonUtil.parseObject(pdfCreateBo.getPdfInfo(), MedicalPromisePdfBo.class);
        if (Objects.isNull(medicalPromisePdfBo)){
            return null;
        }
        try {
            // 1.新建document对象
            Document document = new Document(PageSize.A4);
            // 2.建立一个书写器(Writer)与document对象关联
            File file = new File(pdfCreateBo.getPdfName());
            //File file = new File("/Users/<USER>/output5.pdf");
            file.createNewFile();
            PdfWriter writer = PdfWriter.getInstance(document, Files.newOutputStream(file.toPath()));
            // 3.打开文档
            document.open();
            document.newPage();
            log.info("SelfTestPdfCreateV2 createPdf paragraph start");
            JSONObject obj = JSON.parseObject(duccConfig.getReportPdfConfig());
            log.info("SelfTestPdfCreateV2 createPdf paragraph obj={}", JSON.toJSONString(obj));

            // --------------------logo 标题 条形码--------------------------
            Paragraph paragraphHead = new Paragraph();
            PdfPTable tableHead = new PdfPTable(3);
            // 设置表格宽度百分比
            tableHead.setWidthPercentage(100);
            // 设置表格列宽度
            float[] tableHeadColumnWidths = {0.7f, 2f, 0.5f};
            tableHead.setWidths(tableHeadColumnWidths);
            // logo
            Image logoImage = Image.getInstance(new java.net.URL(obj.getString("logo")));
            // 条形码
            PdfContentByte cb = writer.getDirectContent();
            Image barcodeImage = BarcodeUtil.generateBarcode(cb, medicalPromisePdfBo.getSpecimenCode(), 180, 60);

            tableHead.addCell(createLogoCell(logoImage));
            tableHead.addCell(createReportTitleCell(obj.getString("reportTitle"), font16Bold));
            if (barcodeImage == null){
                tableHead.addCell(createReportTextCell("", font16Bold));
            }else {
                tableHead.addCell(createBarcodeCell(barcodeImage));
            }
            paragraphHead.add(tableHead);

            // ------------------姓名 性别 年龄 检测项目 样本情况 检测条码 采样时间 收样时间 报告时间---------------------
            Paragraph paragraphPatient = new Paragraph();
            PdfPTable tablePatient = new PdfPTable(3);
            tablePatient.setWidthPercentage(100);
            float[] tablePatientColumnWidths = {0.3f, 0.3f, 0.3f};
            tablePatient.setWidths(tablePatientColumnWidths);

            tablePatient.addCell(createPatientCell("姓名："+medicalPromisePdfBo.getUserName(), font8Normal,null));
            tablePatient.addCell(createPatientCell("性别："+medicalPromisePdfBo.getUserGenderStr(), font8Normal,null));
            tablePatient.addCell(createPatientCell("年龄："+medicalPromisePdfBo.getAge(), font8Normal,null));
            tablePatient.addCell(createPatientCell("检测项目："+medicalPromisePdfBo.getServiceItemName(), font8Normal,null));
            String sampleCharacteristics = StringUtils.isBlank(medicalPromisePdfBo.getSampleCharacteristics()) ? obj.getString("sampleCharacteristics") : medicalPromisePdfBo.getSampleCharacteristics();
            tablePatient.addCell(createPatientCell("样本情况："+sampleCharacteristics, font8Normal,null));
            tablePatient.addCell(createPatientCell("检测条码："+medicalPromisePdfBo.getSpecimenCode(), font8Normal,null));
            Integer cellCount = 6;

            Boolean timeDisplayRuleFlag = timeDisplayRule(medicalPromisePdfBo.getSampleTime(), medicalPromisePdfBo.getCheckTime(), medicalPromisePdfBo.getReportTime());
            if (timeDisplayRuleFlag){
                tablePatient.addCell(createPatientCell("采样时间："+medicalPromisePdfBo.getSampleTime(), font8Normal,null));
                tablePatient.addCell(createPatientCell("收样时间："+medicalPromisePdfBo.getCheckTime(), font8Normal,null));
                tablePatient.addCell(createPatientCell("报告时间："+medicalPromisePdfBo.getReportTime(), font8Normal,null));
                cellCount = cellCount+3;
                if (obj.getBoolean("stationNameSwitch")){
                    tablePatient.addCell(createPatientCell(String.format(obj.getString("stationName"),medicalPromisePdfBo.getStationName()), font8Normal, 8f));
                    cellCount++;
                }
            }else {
                tablePatient.addCell(createPatientCell("报告时间："+medicalPromisePdfBo.getReportTime(), font8Normal,8f));
                cellCount++;
                if (obj.getBoolean("stationNameSwitch")){
                    tablePatient.addCell(createPatientCell(String.format(obj.getString("stationName"),medicalPromisePdfBo.getStationName()), font8Normal, 8f));
                    cellCount++;
                }
            }

            if (StringUtils.isNotBlank(medicalPromisePdfBo.getTestUser())) {
                tablePatient.addCell(createPatientCell("检测人："+medicalPromisePdfBo.getTestUser(), font8Normal,null));
                cellCount++;

            }
            if (StringUtils.isNotBlank(medicalPromisePdfBo.getCheckUser())){
                tablePatient.addCell(createPatientCell("审核人："+medicalPromisePdfBo.getCheckUser(), font8Normal,null));
                cellCount++;
            }


            if (cellCount%3 != 0){
                int num = 3-cellCount%3;
                for (int i = 0; i < num ; i++) {
                    tablePatient.addCell(createPatientCell("", font8Normal,null));
                }
            }

            paragraphPatient.add(tablePatient);

            // ---------------------序号 项目 CT值 结果 参考区间 检测方法----------------------
            Paragraph paragraphIndicator = new Paragraph();
            PdfPTable tableIndicator = new PdfPTable(6);
            tableIndicator.setWidthPercentage(100);
            float[] tableIndicatorColumnWidths = {0.1f, 0.3f, 0.3f, 0.3f, 0.3f, 0.3f};
            tableIndicator.setWidths(tableIndicatorColumnWidths);
            // 表示在当前页放不下表格时，强制换页
            tableIndicator.setSplitLate(false);
            // 表示跨行时允许自动换页
            tableIndicator.setSplitRows(true);

            tableIndicator.addCell(createIndicatorKeyCell("序号", font8Bold));
            tableIndicator.addCell(createIndicatorKeyCell("项目", font8Bold));
            tableIndicator.addCell(createIndicatorKeyCell("CT值", font8Bold));
            tableIndicator.addCell(createIndicatorKeyCell("参考区间", font8Bold));
            tableIndicator.addCell(createIndicatorKeyCell("结果", font8Bold));
            tableIndicator.addCell(createIndicatorKeyCell("检测方法", font8Bold));

            List<IndicatorPdfBo> indicatorPdfBos = medicalPromisePdfBo.getIndicatorPdfBos();
            for (int i = 0; i < indicatorPdfBos.size(); i++) {
                Font font = font8Normal;
                IndicatorPdfBo indicatorPdfBo = indicatorPdfBos.get(i);
                if (!StringUtil.equals(indicatorPdfBo.getAbnormalType(), CommonConstant.ZERO_STR)){
                    font = font8NormalRed;
                }
                if (Objects.equals(i, indicatorPdfBos.size() - 1)) {
                    tableIndicator.addCell(createIndicatorValueCell(String.valueOf((i+1)), font));
                    tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getIndicatorName(), font));
                    tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getCtValue(), font));
                    tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getReferenceRangeValue(), font));
                    tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getValue(), font));
                    tableIndicator.addCell(createIndicatorValueCell(obj.getString("testingMethod"), font));

                    // 单元格已合并，并居中
                    PdfPCell cell1 = new PdfPCell(new Paragraph(obj.getString("suggestDescribeTitle"), font8Bold));
                    cell1.setColspan(6);
                    cell1.setBorder(PdfPCell.NO_BORDER);
                    cell1.setHorizontalAlignment(Element.ALIGN_LEFT);
                    cell1.setLeading(20f,0f);
                    tableIndicator.addCell(cell1);

                    List<String> suggestDescribeCommonList = JSON.parseArray(obj.getString("suggestDescribeCommon"), String.class);
                    boolean hasLungsDrug = indicatorPdfBos.stream().anyMatch(a -> obj.getString("lungsDrug").equals(a.getIndicatorName()));
                    if (hasLungsDrug){
                        suggestDescribeCommonList.add(2,obj.getString("suggestDescribeLung"));
                    }
                    List<String> newSuggestDescribeCommonList = new ArrayList<>();
                    for (int m = 0; m < suggestDescribeCommonList.size(); m++) {
                        newSuggestDescribeCommonList.add((m+1)+suggestDescribeCommonList.get(m));
                    }
                    StringBuilder sb = new StringBuilder();
                    for (String str : newSuggestDescribeCommonList) {
                        sb.append(str);
                    }
                    String newSuggestDescribeCommonStr = sb.toString();

                    // 单元格已合并，并居中
                    PdfPCell cell2 = new PdfPCell(new Paragraph(newSuggestDescribeCommonStr, font8Normal));
                    cell2.setColspan(6);
                    cell2.setBorder(PdfPCell.BOTTOM);
                    cell2.setHorizontalAlignment(Element.ALIGN_LEFT);
                    cell2.setLeading(10f,0f);
                    tableIndicator.addCell(cell2);
                    continue;
                }

                tableIndicator.addCell(createIndicatorValueCell(String.valueOf((i+1)), font));
                tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getIndicatorName(), font));
                tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getCtValue(), font));
                tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getReferenceRangeValue(), font));
                tableIndicator.addCell(createIndicatorValueCell(indicatorPdfBo.getValue(), font));
                tableIndicator.addCell(createIndicatorValueCell(obj.getString("testingMethod"), font));
            }
            paragraphIndicator.add(tableIndicator);

            // ---------------------声明----------------------
            Paragraph paragraphDeclare = new Paragraph();
            PdfPTable tableDeclare = new PdfPTable(1);
            tableDeclare.setWidthPercentage(100);
            float[] tableCheckTimeColumnWidths = {1f};
            tableDeclare.setWidths(tableCheckTimeColumnWidths);
            tableDeclare.addCell(createDeclareCell(obj.getString("declare"), font8Normal, Element.ALIGN_LEFT));
            paragraphDeclare.add(tableDeclare);

            // 添加段落
            document.add(paragraphHead);
            document.add(paragraphPatient);
            document.add(paragraphIndicator);
            document.add(paragraphDeclare);
            log.info("SelfTestPdfCreateV2 createPdf paragraph document add end");

            // 5.关闭文档
            document.close();
            writer.close();

            // 返回结果
            PdfCreateResultBo pdfCreateResultBo = new PdfCreateResultBo();
            pdfCreateResultBo.setResult(Boolean.TRUE);
            pdfCreateResultBo.setPdfName(pdfCreateBo.getPdfName());
            if (pdfCreateBo.getUploadToOss()){
                PutFileResult put = fileManageService.put(pdfCreateBo.getPdfName(), Files.newInputStream(file.toPath()), FileManageServiceImpl.FolderPathEnum.REPORT, ContentTypeEnum.PDF.getValue(),Boolean.FALSE);
                pdfCreateResultBo.setOssPath(put.getFilePath());
            }
            file.delete();
            return pdfCreateResultBo;
        }catch (Exception e){
            log.info("SelfTestPdfCreateV2 createPdfError pdfCreateBo={}", JsonUtil.toJSONString(pdfCreateBo), e);
        }
        return null;
    }


    /**
     * 创建单元格-指标value
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createIndicatorValueCell(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        value = StringUtil.isNotBlank(value) ? value : "-";
        if ("0".equals(value) || "0.00".equals(value) || "0.0".equals(value)){
            value = "-";
        }
        cell.setPhrase(new Phrase(value, font));
        cell.setLeading(15f,0f);
        return cell;
    }

    /**
     * 创建单元格-patient
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createPatientCell(String value, Font font, Float paddingBottom) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setColspan(1);
        //value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        cell.setPadding(3.0f);
        cell.setLeading(15f,0f);
        cell.setBorder(0);
        cell.setPaddingTop(0.0f);
        if (paddingBottom != null){
            cell.setPaddingBottom(paddingBottom);
        }
        return cell;
    }

    /**
     * 创建单元格-声明
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createDeclareCell(String value, Font font, Integer horizontalAlignment) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(horizontalAlignment);
        cell.setColspan(1);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        cell.setPadding(3.0f);
        cell.setBorder(0);
        // 设置距底部的距离
        cell.setPaddingTop(6);
        return cell;
    }

    /**
     * 创建单元格-指标key
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createIndicatorKeyCell(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.TOP | PdfPCell.BOTTOM);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        cell.setLeading(15f,0f);
        cell.setPaddingBottom(8);
        return cell;
    }

    /**
     * 创建单元格-报告标题
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createReportTitleCell(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.BOTTOM);
        value = StringUtil.isNotBlank(value) ? value : "-";
        cell.setPhrase(new Phrase(value, font));
        cell.setPaddingBottom(15);
        return cell;
    }

    /**
     * 创建单元格-文本
     * @param value
     * @param font
     * @return
     */
    public PdfPCell createReportTextCell(String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.BOTTOM);
        cell.setPhrase(new Phrase(value, font));
        cell.setPaddingBottom(15);
        return cell;
    }

    /**
     * 创建单元格-logo
     * @param image
     * @return
     */
    public PdfPCell createLogoCell(Image image) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
        cell.setHorizontalAlignment(Element.ALIGN_BOTTOM);
        cell.setBorder(PdfPCell.BOTTOM);
        cell.setImage(image);
        cell.setPaddingBottom(3);
        return cell;
    }

    /**
     * 创建单元格-条形码
     * @param image
     * @return
     */
    public PdfPCell createBarcodeCell(Image image) {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
        cell.setHorizontalAlignment(Element.ALIGN_BOTTOM);
        cell.setBorder(PdfPCell.BOTTOM);
        cell.setImage(image);
        return cell;
    }

    public Boolean timeDisplayRule(String sampleTime, String checkTime, String reportTime){
        try {
            if (StringUtils.isBlank(sampleTime) || StringUtils.isBlank(checkTime)){
                return false;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date sampleTimeDate = sdf.parse(sampleTime);
            Date checkTimeDate = sdf.parse(checkTime);
            Date reportTimeDate = sdf.parse(reportTime);
            // 如果存在采样时间，收样时间，报告时间先后顺序不对的情况，仅展示报告时间
            if ((sampleTimeDate.compareTo(checkTimeDate))> 0
                    || (checkTimeDate.compareTo(reportTimeDate))> 0
                    || (sampleTimeDate.compareTo(reportTimeDate))> 0){
                return false;
            }
            // 如果三个时间存在时间间隔过小，时间差在10分钟以内，则仅展示报告时间
            if ((DateUtil.between(sampleTimeDate, checkTimeDate, DateUnit.MINUTE)) <= 10L
                    || (DateUtil.between(checkTimeDate, reportTimeDate, DateUnit.MINUTE)) <= 10L
                    || (DateUtil.between(sampleTimeDate, reportTimeDate, DateUnit.MINUTE)) <= 10L){
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("SelfTestPdfCreateV2 timeDisplayRule error e", e);
            return false;
        }
    }

}
