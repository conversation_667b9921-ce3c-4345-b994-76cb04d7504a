package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @ClassName PricingServiceFactObjectEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/5/2 00:39
 **/
@Getter
public enum PricingServiceFactObjectEnum {

    ANGEL_INFO("jdhAngelDto","护士信息"),

    ORDER_INFO("jdOrder","订单信息"),

    PROMISE_INFO("promiseDto","履约单信息"),

    DISPATCH_INFO("dispatchDto","派单信息"),

    MEDICAL_PROMISE_LIST("medicalPromiseDtoList","检测单信息"),

    AREA_FEE_CONFIG("jdhSettlementAreaFeeConfig","地区对应费项配置"),

    MATERIAL_FEE_CONFIG("materialFeeConfig","耗材费项配置"),

    SKU_FEE_CONFIG("skuFeeConfig","商品费项配置"),

    SKU_SERVICE_AMOUNT_MAP("skuServiceAmountMap","商品服务费map"),

    ITEM_FEE_CONFIG("itemFeeConfig","项目费项配置"),

    CITY_LEVEL_CONFIG("cityLevelConfig","城市等级配置"),

    ADDRESS_INFO("addressInfo","三级地址信息"),

    HOLIDAY_CONFIG("holidayConfig","节假日信息"),

    ANGEL_SETTLEMENT_FEE_RATIO_CONFIG("angelSettlementFeeRatioConfig","护士结算费项比例配置"),

    IS_LAST_SERVICE("isLastService","是否是最后一个服务"),

    HISTORY_SETTLE_AMOUNT("historySettleAmount","历史已经结算过的金额"),
    ;

    /** */
    PricingServiceFactObjectEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /** */
    private String code;
    /** */
    private String description;

    private static final Map<String, PricingServiceFactObjectEnum> CODE_MAP = Maps.newHashMap();

    static {
        for (PricingServiceFactObjectEnum value : values()) {
            CODE_MAP.put(value.code, value);
        }
    }

    /**
     *
     * @param code
     * @return
     */
    public static PricingServiceFactObjectEnum getEnumByCode(String code){
        if (StringUtils.isEmpty(code)){
            return null;
        }
        return CODE_MAP.get(code);
    }
}