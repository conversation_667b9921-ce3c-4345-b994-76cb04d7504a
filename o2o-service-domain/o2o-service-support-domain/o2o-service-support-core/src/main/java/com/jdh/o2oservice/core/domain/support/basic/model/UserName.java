package com.jdh.o2oservice.core.domain.support.basic.model;

import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.RSACode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 值对象 用户姓名抽象，封装了加解密，掩码的能力
 * @author: yang<PERSON>yu
 * @date: 2023/12/22 10:15 上午
 * @version: 1.0
 */
@Getter
@Setter
@Slf4j
public class UserName implements Serializable {
    /** 加密前缀 */
    private static final String PRE_NAME = "123456";
    /**
     *
     */
    private String name;

    /**
     *
     * @param name
     */
    public UserName(String name) {
        this.name = name;
    }

    /**
     *
     */
    public UserName() {
    }

    /**
     * 获取完整姓名
     */
    public String parseRealName(){
        if(this.verify() && !this.isMask()){
            return this.name;
        }

        return null;
    }

    /**
     * 解密
     * @return
     */
    public boolean decrypt() {
        if (StringUtils.isNotBlank(name)) {
            try {
                this.name = RSACode.decryptByPrivateKey(this.name);
            } catch (Exception e) {
                log.error("UserName -> decrypt, 姓名解密失败 ,msg={}", e.getMessage());
                throw new BusinessException(BaseDomainErrorCode.USER_NAME_ILLEGAL);
            }
        }
        return false;
    }

    public static void main(String[] args) throws Exception {
        UserName userName = new UserName();
        userName.setName("张三");
        System.out.println(userName.encrypt());

        String name= "RVeO31JLOPE6/Ze1yr3mI3JD4TeYzocUQGT+/GYEOL6yS2JnrH2IHKp930vidZhIdyT2ET0vijXNA0rcFEwMv1HC5+oLzwyaWwqFeFaE1i1/Li/GF59nm2NBWRVp4JsWcugeKMW4ceHucMLrLz+4owbUgmylkkgrWY+AUUaaWPU=";
        UserName deName = new UserName();
        deName.setName(name);
        System.out.println(deName.decrypt());
        System.out.println(deName.getName());


    }

    /**
     * 加密
     */
    public String encrypt() {
        if (StringUtils.isNotBlank(name)) {
            try {
                return RSACode.encryptByPublicKey(PRE_NAME + this.name);
            } catch (Exception e) {
                log.error("UserName -> ncrypt, 姓名加密失败 ,msg={}", e.getMessage());
                throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
            }
        }
        return null;
    }

    /**
     * 是否包含星号
     * @return
     */
    public Boolean isMask(){
        return StringUtils.isNotBlank(name) && name.contains(CommonConstant.MASK_STR);
    }

    /**
     * 是否包含星号
     * @return
     */
    public String mask(){
        if (StringUtils.isBlank(name)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(name);
        return sb.replace(0, 1,"*").toString();
    }


    /**
     * 是否包含星号
     * @return
     */
    public static String maskTool(String name){
        if (StringUtils.isBlank(name)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(name);
        return sb.replace(0, 1,"*").toString();
    }
    /**
     *  名脱敏
     * @return
     */
    public String maskPersonal(){
        if (StringUtils.isBlank(name)) {
            return null;
        }

        return name.charAt(0) + "*";
    }

    /**
     * 是否包含星号
     * @return
     */
    public static String mask(String name){
        if (StringUtils.isBlank(name)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(name);
        return sb.replace(0, 1,"*").toString();
    }


    /**
     *
     * @return
     */
    public Boolean verify(){
        if (StringUtils.isBlank(name)) {
            return Boolean.TRUE;
        }
        if (name.length() < 2 || name.length() > 20) {
            return false;
        }
        return true;
        /*if (name.matches("^[\\u4e00-\\u9fa5.·\\u36c3\\u4DAEa-zA-Z]{2,}$")){
            return true;
        }else {
            return false;
        }*/
    }

    /**
     * 不校验敏感词
     *
     * @return boolean
     */
    public Boolean verifyNotCheckSensitiveWord(){
        if (StringUtils.isBlank(name)) {
            return Boolean.TRUE;
        }
        return name.length() >= 2 && name.length() <= 20;
    }

    /**
     * 加密
     */
    public static String encrypt(String name) {
        if (StringUtils.isNotBlank(name)) {
            try {
                return RSACode.encryptByPublicKey(PRE_NAME + name);
            } catch (Exception e) {
                log.error("UserName -> ncrypt, 姓名加密失败 ,msg={}", e.getMessage());
                throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
            }
        }
        return null;
    }

    /**
     * 解密
     * @return
     */
    public static String decrypt(String name) {
        if (StringUtils.isNotBlank(name)) {
            try {
                return RSACode.decryptByPrivateKey(name);
            } catch (Exception e) {
                log.error("UserName -> decrypt, 姓名解密失败 ,msg={}", e.getMessage());
                throw new BusinessException(BaseDomainErrorCode.USER_NAME_ILLEGAL);
            }
        }
        return "";
    }
}
