package com.jdh.o2oservice.core.domain.settlement.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementUpdateContext;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementDetail;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementIdentifier;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:29 下午
 * @Description:
 */
public interface AngelSettlementRepository  {


    /** */
    AngelSettlement find(AngelSettlementIdentifier angelSettlementIdentifier);
    /**
     * 保存护士结算信息
     *
     * @param angelSettlement
     * @return
     */
    Long save(AngelSettlement angelSettlement);

    /**
     * 批量保存护士结算+明细
     * @param angelSettlementList
     * @param angelSettlementDetailList
     * @return
     */
    Long batchSaveAngelSettlementAndDetail(List<AngelSettlement> angelSettlementList,List<AngelSettlementDetail> angelSettlementDetailList);

    /**
     * 查询护士结算信息
     *
     * @param queryContext
     * @return
     */
    List<AngelSettlement> querySettlementList(AngelSettlementQueryContext queryContext);

    /**
     * 根据条件汇总金额
     *
     * @param queryContext
     * @return
     */
    BigDecimal querySettlementAmountTot(AngelSettlementQueryContext queryContext);

    /**
     * 分页查询护士结算信息
     *
     * @param queryContext
     * @return
     */
    Page<AngelSettlement> querySettlementPage(AngelSettlementQueryContext queryContext);

    /**
     *
     * @param queryContext
     * @return
     */
    Integer querySettlementCount(AngelSettlementQueryContext queryContext);
    /**
     * 查询账单明细
     *
     * @param queryContext
     * @return
     */
    List<AngelSettlementDetail> querySettlementDetailList(AngelSettlementQueryContext queryContext);

    /**
     * 批量更新结算状态
     * @param angelSettlementUpdateContext
     * @return
     */
    Integer updateSettleStatusBySettleIdList(AngelSettlementUpdateContext angelSettlementUpdateContext);

    /**
     * 更新同步互医状态
     * @param angelSettlementUpdateContext
     * @return
     */
    Integer updateSyncStatusBySettleId(AngelSettlementUpdateContext angelSettlementUpdateContext);
}
