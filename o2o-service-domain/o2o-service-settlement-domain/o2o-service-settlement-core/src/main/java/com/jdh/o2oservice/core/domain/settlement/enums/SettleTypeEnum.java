package com.jdh.o2oservice.core.domain.settlement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 3:35 下午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SettleTypeEnum {

    INCOME(1, "收入"),
    EXPEND(2, "支出"),
    ;


    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;

    /**
     * @param type
     * @return
     */
    public static SettleTypeEnum getSettleTypeEnumByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (SettleTypeEnum settleTypeEnum : SettleTypeEnum.values()) {
            if (Objects.equals(settleTypeEnum.getType(), type)) {
                return settleTypeEnum;
            }
        }
        return null;
    }
}
