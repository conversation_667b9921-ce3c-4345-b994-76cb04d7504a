package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.Entity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 护士结算明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 05:53:18
 */
@Data
public class AngelSettlementDetail implements Entity<AngelSettlementDetailIdentifier> {


    /**
     * ID主键
     */
    private Long id;


    /**
     * 主键id
     */
    private Long settleDetailId;

    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 费用项目名称
     */
    private String feeName;

    /**
     * 金额
     */
    private BigDecimal settleAmount;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 个税代缴
     */
    private BigDecimal tax;

    /**
     * 到账金额
     */
    private BigDecimal receivedAmount;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link ID}
     */
    @Override
    public AngelSettlementDetailIdentifier getIdentifier() {
        return new AngelSettlementDetailIdentifier(this.settleDetailId);
    }
}
