package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchTeamRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchTeamRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchTeamStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TeamAngelCircleSelectionService
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 18:28
 **/
@Service("teamAngelCircleSelectionService")
@Slf4j
public class TeamAngelCircleSelectionService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * 地址服务
     */
    @Resource
    DispatchCircleSelectionService dispatchCircleSelectionService;

    /**
     *
     */
    @Resource
    private DispatchTeamRepository dispatchTeamRepository;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("TeamAngelCircleSelectionService -> call, 圈选逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("TeamAngelCircleSelectionService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));

        //用户订单所需技能
        List<String> angelSkillCodeList = context.getAngelSkillCodeList();
        log.info("DispatchFilterService -> call, 用户订单所需技能 angelSkillCodeList={}", JSON.toJSONString(angelSkillCodeList));

        //如果有技能，则需要圈出符合技能的派单小队，以及对应的护士
        if (CollectionUtils.isNotEmpty(angelSkillCodeList)) {
            List<JdhDispatchTeamSkillRel> dispatchTeamSkillRelList = dispatchTeamRepository.findDispatchTeamSkillRelList(DispatchTeamRepQuery.builder().angelSkillCodeList(angelSkillCodeList).build());
            //根据小队归堆
            Map<Long, Set<String>> teamId2SkillMap = dispatchTeamSkillRelList.stream()
                    .collect(Collectors.groupingBy(JdhDispatchTeamSkillRel::getDispatchTeamId,
                            Collectors.mapping(JdhDispatchTeamSkillRel::getAngelSkillCode, Collectors.toSet())));

            List<Long> teamIdList = new ArrayList<>();

            teamId2SkillMap.forEach((teamId, teamSkillCodeList) -> {
                if (CollectionUtils.isNotEmpty(teamSkillCodeList) && teamSkillCodeList.containsAll(angelSkillCodeList)) {
                    teamIdList.add(teamId);
                }
            });
            //如果没有命中的小队，则流程结束，无小队护士可派
            if (CollectionUtils.isEmpty(teamIdList)) {
                log.info("TeamAngelCircleSelectionService -> call, 没有命中的小队，流程终止 context={}", JSON.toJSONString(context));
                OutputMessage outputMessage = new OutputMessage();
                outputMessage.setBlock(true);
                return outputMessage;
            }
            List<JdhDispatchTeam> list = dispatchTeamRepository.findList(DispatchTeamRepQuery.builder().dispatchTeamIdList(teamIdList).dispatchTeamStatusList(Lists.newArrayList(JdhDispatchTeamStatusEnum.DISPATCH_TEAM_STATUS_ENABLE.getStatus())).build());
            //如果没有启用中的小队，则流程结束，无小队护士可派
            if (CollectionUtils.isEmpty(list)) {
                log.info("TeamAngelCircleSelectionService -> call, 没有启用中的小队，流程终止 context={}", JSON.toJSONString(context));
                OutputMessage outputMessage = new OutputMessage();
                outputMessage.setBlock(true);
                return outputMessage;
            }

            List<JdhDispatchTeamAngelRel> dispatchTeamAngelRelList = dispatchTeamRepository.findDispatchTeamAngelRelList(DispatchTeamRepQuery.builder().dispatchTeamIdList(list.stream().map(JdhDispatchTeam::getDispatchTeamId).collect(Collectors.toList())).build());
            if (CollectionUtils.isEmpty(dispatchTeamAngelRelList)) {
                log.info("TeamAngelCircleSelectionService -> call, 没有命中的小队护士，流程终止 context={}", JSON.toJSONString(context));
                OutputMessage outputMessage = new OutputMessage();
                outputMessage.setBlock(true);
                return outputMessage;
            }
            //命中的护士ID列表
            Set<Long> angelIdSet = dispatchTeamAngelRelList.stream().map(JdhDispatchTeamAngelRel::getAngelId).collect(Collectors.toSet());
            log.info("TeamAngelCircleSelectionService -> call, 命中的护士ID列表 angelIdSet={}", JSON.toJSONString(angelIdSet));
            context.setSelectAngelIds(Lists.newArrayList(angelIdSet));
        } else {//如果无技能，则所有派单小队的护士都会被圈出
            List<JdhDispatchTeam> list = dispatchTeamRepository.findList(DispatchTeamRepQuery.builder().dispatchTeamStatusList(Lists.newArrayList(JdhDispatchTeamStatusEnum.DISPATCH_TEAM_STATUS_ENABLE.getStatus())).build());
            //如果没有启用中的小队，则流程结束，无小队护士可派
            if (CollectionUtils.isEmpty(list)) {
                log.info("TeamAngelCircleSelectionService -> call, 没有启用中的小队，流程终止 context={}", JSON.toJSONString(context));
                OutputMessage outputMessage = new OutputMessage();
                outputMessage.setBlock(true);
                return outputMessage;
            }
            List<JdhDispatchTeamAngelRel> dispatchTeamAngelRelList = dispatchTeamRepository.findDispatchTeamAngelRelList(DispatchTeamRepQuery.builder().dispatchTeamIdList(list.stream().map(JdhDispatchTeam::getDispatchTeamId).collect(Collectors.toList())).build());
            if (CollectionUtils.isEmpty(dispatchTeamAngelRelList)) {
                log.info("TeamAngelCircleSelectionService -> call, 没有命中的小队护士，流程终止 context={}", JSON.toJSONString(context));
                OutputMessage outputMessage = new OutputMessage();
                outputMessage.setBlock(true);
                return outputMessage;
            }
            //命中的护士ID列表
            Set<Long> angelIdSet = dispatchTeamAngelRelList.stream().map(JdhDispatchTeamAngelRel::getAngelId).collect(Collectors.toSet());
            log.info("TeamAngelCircleSelectionService -> call, 命中的护士ID列表 angelIdSet={}", JSON.toJSONString(angelIdSet));
            context.setSelectAngelIds(Lists.newArrayList(angelIdSet));
        }
        //对服务者地址、技能进行校验过滤
        dispatchCircleSelectionService.call(inputMessage);
        log.info("TeamAngelCircleSelectionService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));
        log.info("TeamAngelCircleSelectionService -> call, 圈选逻辑 END");
        return new OutputMessage();
    }

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    /**
     *
     * @return
     */
    @Override
    public String getCode() {
        return DispatchFlowEnum.TEAM_ANGEL_CIRCLE_SELECTION.getFlowCode();
    }

    /**
     *
     * @return
     */
    @Override
    public String getName() {
        return DispatchFlowEnum.TEAM_ANGEL_CIRCLE_SELECTION.getFlowDesc();
    }

    /**
     *
     * @param inputMessage
     */
    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======TeamAngelCircleSelectionService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}