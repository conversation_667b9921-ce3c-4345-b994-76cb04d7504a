package com.jdh.o2oservice.core.domain.dispatch.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName DispatchFlowEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/10/18 22:21
 **/
@AllArgsConstructor
@Getter
public enum DispatchFlowEnum {

    /**
     * 业务校验逻辑
     */
    BIZ_CHECK("bizCheck", "业务校验逻辑"),

    /**
     * 圈选逻辑
     */
    CIRCLE_SELECTION("circleSelection", "圈选逻辑"),

    /**
     * 意向护士圈选逻辑
     */
    INTENDED_CIRCLE_SELECTION("intendedCircleSelection", "意向护士圈选逻辑"),

    /**
     * 小队服务者圈选逻辑
     */
    TEAM_ANGEL_CIRCLE_SELECTION("teamAngelCircleSelection", "小队服务者圈选逻辑"),

    /**
     * 通用过滤逻辑，过滤接单开关和技能
     */
    FILTER("filter", "通用过滤逻辑"),

    /**
     * 优先排序逻辑
     */
    PRIORITY_SORT("prioritySort", "优先排序逻辑"),

    /**
     * 通用派单决策逻辑
     */
    DISPATCH_DECISION("dispatchDecision", "通用派单决策逻辑"),
    /**
     * 意向护士派单决策逻辑
     */
    INTENDED_DISPATCH_DECISION("intendedDispatchDecision", "意向护士派单决策逻辑"),
    MULTI_SELECTION("multiSelection", "多轮派单圈选护士"),
    MULTI_GROUP("multiGroup", "多轮派单分组逻辑"),

    /**
     * lbs圈选护士
     */
    LBS_STATION_ANGEL_CIRCLE_SELECTION("lbsStationAngelCircleSelectService", "lbs查询服务站"),

    /**
     * 护士技能过滤逻辑
     */
    ANGEL_SKILL_FILTER("angelSkillFilter", "护士技能过滤"),

    /**
     * 护士排班过滤逻辑
     */
    ANGEL_SCHEDULE_FILTER("angelScheduleFilter", "护士排班过滤逻辑"),

    /**
     * 护士日程过滤逻辑
     */
    ANGEL_DAILY_WORK_FILTER("angelDailyWorkFilter", "护士日程过滤逻辑"),

    /**
     * 因子计算优先排序逻辑
     */
    FACTOR_PRIORITY_SORT("factorPrioritySort", "优先排序逻辑"),

    /**
     * 派单决策逻辑-直接指定派给排名第一的护士，无需护士手动接受
     */
    DISPATCH_ASSIGN_DECISION("dispatchAssignDecision", "派单决策逻辑"),

    ;

    private String flowCode;

    private String flowDesc;
}