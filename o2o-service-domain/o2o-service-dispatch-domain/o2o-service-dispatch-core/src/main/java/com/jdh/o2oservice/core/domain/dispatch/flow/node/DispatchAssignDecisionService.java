package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.DispatchDetailTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchAssignDecisionService
 * @Description
 * <AUTHOR>
 * @Date 2025/3/9 17:23
 **/
@Service("dispatchAssignDecisionService")
@Slf4j
public class DispatchAssignDecisionService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchAssignDecisionService -> call, 派单决策 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchAssignDecisionService -> call, 派单决策 context={}", JSON.toJSONString(context));
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("DispatchAssignDecisionService -> call, 派单决策 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }

        Date now = DateUtil.parseDateTime(DateUtil.now());
        Long redispatchTime = 30L;
        log.info("DispatchAssignDecisionService -> call, redispatchTime={}", redispatchTime);
        context.setDispatchDecisionExecuteTime(now);
        context.setRedispatchTime(redispatchTime);
        context.setAssignType(DispatchDetailTypeEnum.ASSIGN.getType());
        //如果最终还是有并列第一（排序权重分相同、到用户家距离相同），随机派一名护士
        DispatchAngelBO dispatchAngelBO = angelList.get(0);
        Double sortScore = dispatchAngelBO.getSortScore();
        Double distance = dispatchAngelBO.getDistance();
        List<DispatchAngelBO> list = angelList.stream().filter(bo -> Objects.equals(sortScore, bo.getSortScore()) && Objects.equals(distance, bo.getDistance())).collect(Collectors.toList());
        //打乱护士集合达到随机效果
        Collections.shuffle(list);
        context.setSelectionAngelList(Lists.newArrayList(list.get(0)));
        log.info("DispatchAssignDecisionService -> call, 派单决策 context={}", JSON.toJSONString(context));
        log.info("DispatchAssignDecisionService -> call, 派单决策 END");
        return new OutputMessage();
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.DISPATCH_ASSIGN_DECISION.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.DISPATCH_ASSIGN_DECISION.getFlowDesc();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchAssignDecisionService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}