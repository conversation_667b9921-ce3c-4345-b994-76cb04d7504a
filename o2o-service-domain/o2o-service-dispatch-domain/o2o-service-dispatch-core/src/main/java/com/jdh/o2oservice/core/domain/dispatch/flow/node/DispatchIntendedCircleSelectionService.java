package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchFilterConfig;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceLocation;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelStationBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelStationParam;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 意向护士圈选
 * @ClassName CircleSelectionService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/18 22:16
 **/
@Service("dispatchIntendedCircleSelectionService")
@Slf4j
public class DispatchIntendedCircleSelectionService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * 地址服务
     */
    @Resource
    AddressRpc addressRpc;

    /**
     *
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     * jdhDispatchDomainService
     */
    @Resource
    private JdhDispatchDomainService jdhDispatchDomainService;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchIntendedCircleSelectionService -> call, 圈选逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchIntendedCircleSelectionService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));
        if (CollectionUtils.isEmpty(context.getIntendedAngelIds())) {
            log.info("DispatchIntendedCircleSelectionService -> call, 无意向护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        //获取用户上门地址省市区信息
        ServiceLocation serviceLocation = context.getJdhDispatch().getServiceLocation();
        //根据用户上门地址获取京标地址
        if (Objects.isNull(serviceLocation.getServiceLocationProvinceId()) || Objects.isNull(serviceLocation.getServiceLocationCityId())) {
            //上门地址省市区未存，调用地址服务根据详细地址查询省市区信息
            BaseAddressBo baseAddressBo = addressRpc.getJDAddressFromAddress(serviceLocation.getServiceLocationDetail());
            serviceLocation.setServiceLocationProvinceId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getProvinceCode() : null);
            serviceLocation.setServiceLocationCityId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getCityCode() : null);
            serviceLocation.setServiceLocationDistrictId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getDistrictCode() : null);
            serviceLocation.setServiceLocationTownId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getTownCode() : null);
        }
        //未拿到城市，默认圈选护士失败
        if (Objects.isNull(serviceLocation.getServiceLocationProvinceId()) || Objects.isNull(serviceLocation.getServiceLocationCityId())) {
            log.info("DispatchIntendedCircleSelectionService -> call, 未拿到城市，默认圈选护士失败，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        DispatchFilterConfig filterConfig = jdhDispatchDomainService.parseFilterStrategy(context.getJdhDispatch().getServiceLocation());
        if (Objects.isNull(filterConfig)) {
            //配置为空，默认按照城市id查询护士派单
            filterConfig = DispatchFilterConfig.builder().skillFilterDispatch(false).skillFilterPush(false).provinceCode(serviceLocation.getServiceLocationProvinceId()).cityCode(serviceLocation.getServiceLocationCityId()).build();
        }
        log.info("DispatchIntendedCircleSelectionService -> call, filterConfig={}", JsonUtil.toJSONString(filterConfig));
        //查询与用户上门地址相同城市的服务站信息
        List<DispatchAngelStationBO> angelStationList = dispatchFlowDependRpc.queryAngelStationList(DispatchQueryAngelStationParam.builder().angelStationStatus(1)
                .provinceCode(Objects.nonNull(filterConfig.getProvinceCode()) ? filterConfig.getProvinceCode().toString() : null)
                .cityCode(Objects.nonNull(filterConfig.getCityCode()) ? filterConfig.getCityCode().toString() : null)
                .districtCode(Objects.nonNull(filterConfig.getDistrictCode()) ? filterConfig.getDistrictCode().toString() : null)
                .pageNum(1)
                .pageSize(1000)
                .build());
        if (CollectionUtils.isEmpty(angelStationList)) {
            log.info("DispatchIntendedCircleSelectionService -> call, 未查到与用户上门地址相同城市的服务站信息，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        //根据服务站查询关联的护士信息
        List<Long> stationIdList = angelStationList.stream().map(DispatchAngelStationBO::getAngelStationId).collect(Collectors.toList());
        log.info("DispatchIntendedCircleSelectionService -> call, 根据服务站查询关联的护士信息 stationIdList={}", JSON.toJSONString(stationIdList));
        List<DispatchAngelBO> angelList = dispatchFlowDependRpc.queryAngelByStationId(
                DispatchQueryAngelParam.builder().auditProcessStatus(1).stationIdList(stationIdList).intendedAngelIds(context.getIntendedAngelIds()).build()
        );
        context.setSelectionAngelList(angelList);
        log.info("DispatchIntendedCircleSelectionService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));
        log.info("DispatchIntendedCircleSelectionService -> call, 圈选逻辑 END");
        return new OutputMessage();
    }

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    /**
     *
     * @return
     */
    @Override
    public String getCode() {
        return DispatchFlowEnum.INTENDED_CIRCLE_SELECTION.getFlowCode();
    }

    /**
     *
     * @return
     */
    @Override
    public String getName() {
        return DispatchFlowEnum.INTENDED_CIRCLE_SELECTION.getFlowDesc();
    }

    /**
     *
     * @param inputMessage
     */
    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======CircleSelectionService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}