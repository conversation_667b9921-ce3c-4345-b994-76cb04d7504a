package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.jim.cli.Cluster;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.dispatch.DispatchMultiRoundsConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchFlowTask;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 多轮派单圈选护士
 *
 * @ClassName CircleSelectionService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/18 22:16
 **/
@Service("multiDispatchAngelGroupService")
@Slf4j
public class MultiDispatchAngelGroupService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * 0:flowId
     * 1:轮次 executionNum
     */
    private static final String DISPATCH_MULTI_ANGEL_GROUP_KEY = "dispatch:multi:group:{0}:{1}";
    @Resource
    private Cluster jimClient;
    @Resource
    private DuccConfig duccConfig;
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("MultiDispatchAngelGroupService -> call, 过滤逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("MultiDispatchAngelGroupService -> call, 过滤逻辑 context={}", JSON.toJSONString(context));
        if (CollectionUtils.isEmpty(context.getSelectionAngelList())) {
            log.info("MultiDispatchAngelGroupService -> call, 派单决策 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        DispatchMultiRoundsConfig multiRoundsConfig = duccConfig.getDispatchMultiRoundsConfig();
        log.info("MultiDispatchAngelGroupService -> call, 获取多轮派单配置 multiRoundsConfig={}", JSON.toJSONString(multiRoundsConfig));
        //护士结果集
        List<DispatchAngelBO> angelList = new ArrayList<>();
        //是否走了圈选逻辑。
        //true-经过了正常圈选、过滤、排序之后的命中护士结果集，需要分组后存入缓存，调用findByExecutionNum取派单护士
        //false-使用多轮派单上下文中的护士列表派单
        Boolean multiDispatchSelect = context.getMultiDispatchSelect();
        if (Objects.equals(multiDispatchSelect, Boolean.TRUE)) {
            //打乱护士集合
            Collections.shuffle(context.getSelectionAngelList());
            JdhDispatchFlowTask dispatchFlowTask = context.getDispatchFlowTask();
            // 意向护士派单，当前轮次已经执行三轮时，进去多轮派单，首次进行护士圈选
            if (dispatchFlowTask.isIntendedDispatch()) {
                if (dispatchFlowTask.getExecutionNum() == 3) {
                    firstGroup(context, 4);
                }
            } else if (dispatchFlowTask.isStandardAssignDispatch()) {
                // 常规派单轮，当前轮次已经执行两轮时，进去多轮派单，首次进行护士圈选
                if (dispatchFlowTask.getExecutionNum() == 2) {
                    firstGroup(context, 3);
                }
            } else if (dispatchFlowTask.isDefaultDispatch()) {
                if (dispatchFlowTask.getExecutionNum() == 1) {
                    firstGroup(context, 2);
                }
            }
            //使用多轮派单上下文中的护士列表派单
            angelList = findByExecutionNum(dispatchFlowTask);
        } else {
            //使用多轮派单上下文中的护士列表派单
            log.info("MultiDispatchAngelGroupService -> call, 使用多轮派单上下文中的护士列表派单");
            angelList = context.getSelectionAngelList();
        }

        context.setSelectionAngelList(angelList);
        //设置抢单持续时间
        //-------------------------------------------
        // 1分钟
        //-------------------------------------------
        //获取派单持续时间配置
        Long redispatchTime = multiRoundsConfig.getRoundTimeoutSecond().longValue();
        Date now = DateUtil.parseDateTime(DateUtil.now());
        log.info("MultiDispatchAngelGroupService -> call, redispatchTime={}", redispatchTime);
        context.setDispatchDecisionExecuteTime(now);
        context.setRedispatchTime(redispatchTime);
        attachPlanTime(context);
        log.info("MultiDispatchAngelGroupService -> call, 派单决策 context={}", JSON.toJSONString(context));
        log.info("MultiDispatchAngelGroupService -> call, 派单决策 END");
        return new OutputMessage();

    }

    /**
     * 设置计划出门时间和计划服务完成时间
     * @param context
     */
    private void attachPlanTime(AngelDispatchContext context) {
        //预约上门开始时间、预约上门结束时间
        LocalDateTime appointmentStartTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentStartTime();
        LocalDateTime appointmentEndTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentEndTime();

        //常规派单轮时间可用性配置
        Map<String, Integer> dispatchTimeBufferDuration = duccConfig.getDispatchTimeBufferDuration();
        Integer planStartBufferMinute = dispatchTimeBufferDuration.getOrDefault("planStartBufferMinute", 60);//a
        Integer planServiceTimeMinute = dispatchTimeBufferDuration.getOrDefault("planServiceTimeMinute", 60);//b
        Integer dailyTimeBufferMinute = dispatchTimeBufferDuration.getOrDefault("dailyTimeBufferMinute", 20);//d
        //当前时间
        LocalDateTime now = LocalDateTime.now();
        //预计上门时间
        Date planDoorTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(-planStartBufferMinute));
        //预计服务完成时间
        Date planServiceDoneTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(planServiceTimeMinute));

        //如果预计上门时间比当前时间还早，则预计上门时间重新计算（用户预约时间往后+）
        if (now.isAfter(appointmentStartTime.plusMinutes(-planStartBufferMinute))) {
            planDoorTime = TimeUtils.localDateTimeToDate(now);
            planServiceDoneTime = TimeUtils.localDateTimeToDate(now.plusMinutes(planStartBufferMinute).plusMinutes(planServiceTimeMinute));
        }
        context.setPlanOutTime(planDoorTime);
        context.setPlanFinishTime(planServiceDoneTime);
    }

    /**
     * 护士分组逻辑 todo @一凡
     *
     * @param context
     * @param startNum
     * @return
     */
    private void firstGroup(AngelDispatchContext context, Integer startNum) {
        JdhDispatchFlowTask dispatchFlowTask = context.getDispatchFlowTask();
        List<DispatchAngelBO> selectionAngelList = context.getSelectionAngelList();
        // 缓存护士
        DispatchMultiRoundsConfig multiRoundsConfig = duccConfig.getDispatchMultiRoundsConfig();
        int timeOut = 600;
        if (Objects.nonNull(multiRoundsConfig.getMaxMultiRound()) && Objects.nonNull(multiRoundsConfig.getRoundTimeoutSecond())) {
            timeOut = multiRoundsConfig.getMaxMultiRound() * multiRoundsConfig.getRoundTimeoutSecond() + 300;
        }
        List<List<DispatchAngelBO>> partitions = Lists.partition(selectionAngelList, multiRoundsConfig.getMinAngelCount());

        // 圈选的护士存入缓存
        for (int i = 0; i < partitions.size(); i++) {
            if(i >= multiRoundsConfig.getMaxMultiRound()) {
                log.info("MultiDispatchAngelGroupService -> firstGroup, 派单决策 超过多轮派单最大轮次，不处理 i={}, maxMultiRound={}", i, multiRoundsConfig.getMaxMultiRound());
                return;
            }
            List<DispatchAngelBO> angels = partitions.get(i);
            if (CollectionUtils.isNotEmpty(angels)) {
                List<Long> angelIds = angels.stream().map(DispatchAngelBO::getAngelId).collect(Collectors.toList());
                String key = MessageFormat.format(DISPATCH_MULTI_ANGEL_GROUP_KEY, dispatchFlowTask.getFlowTaskId(), (i + startNum));
                jimClient.setEx(key, JSON.toJSONString(angelIds), timeOut, TimeUnit.MINUTES);
                log.info("multiDispatchAngelGroupService -> firstGroup, set key={}, size={}", key, angels.size());
            }
        }
    }


    /**
     * 获取当前轮次的护士
     * @param dispatchFlowTask
     * @return
     */
    public List<DispatchAngelBO> findByExecutionNum(JdhDispatchFlowTask dispatchFlowTask) {
        String key = MessageFormat.format(DISPATCH_MULTI_ANGEL_GROUP_KEY, dispatchFlowTask.getFlowTaskId(), (dispatchFlowTask.getExecutionNum() + 1));

        String json = jimClient.get(key);
        log.info("MultiDispatchAngelGroupService->findByExecutionNum key={}", key);
        if (StringUtils.isBlank(json)){
            log.info("MultiDispatchAngelGroupService->findByExecutionNum angelIds is empty");
            return Collections.emptyList();
        }
        log.info("MultiDispatchAngelGroupService->findByExecutionNum angelIds={}", json);
        List<Long> angelIds = JSON.parseArray(json, Long.class);
        return dispatchFlowDependRpc.queryAngelByAngelIds(DispatchQueryAngelParam.builder().auditProcessStatus(1).intendedAngelIds(angelIds).build());
    }

    /**
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    /**
     * @return
     */
    @Override
    public String getCode() {
        return DispatchFlowEnum.MULTI_GROUP.getFlowCode();
    }

    /**
     * @return
     */
    @Override
    public String getName() {
        return DispatchFlowEnum.MULTI_GROUP.getFlowDesc();
    }

    /**
     * @param inputMessage
     */
    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======multiDispatchAngelGroupService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}