package com.jdh.o2oservice.core.domain.dispatch.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @ClassName DispatchAppointmentPatient
 * @Description
 * <AUTHOR>
 * @Date 2024/4/17 20:29
 **/
@Data
public class AppointmentPatient {

    /**
     * 顾客ID
     */
    private Long promisePatientId;

    /**
     * 健康档案ID
     */
    private Long patientId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String userPhone;

    /**
     * 性别
     */
    private Integer patientGender;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 服务项目列表
     */
    private List<ServiceItem> serviceItems;

    /**
     * 冻结状态 0 正常 1 冻结
     */
    private Integer freeze;

    /**
     * 姓名脱敏
     * 将 name 字符串中除了第一个字符以外的所有字符替换为 "**"
     * @return
     */
    public String nameMask(){
        if (StringUtils.isBlank(name)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(name);
        return sb.replace(0, 1,"*").toString();
    }

    /**
     * 姓名脱敏
     * 将 name 字符串中除了第一个字符以外的所有字符替换为 "**"
     * @return
     */
    public String nameMaskSuffix(){
        if (StringUtils.isBlank(name) || name.length() == 1) {
            return name;
        }
        StringBuilder maskedName = new StringBuilder();
        maskedName.append(name.charAt(0));
        for (int i = 1; i < name.length(); i++) {
            maskedName.append("*");
        }
        return maskedName.toString();
    }
}