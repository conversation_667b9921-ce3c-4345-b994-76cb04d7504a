package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceItem;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceLocation;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryLbsStationAngelParam;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName LbsDispatchStationCircleSelectService
 * @Description
 * <AUTHOR>
 * @Date 2025/3/7 13:46
 **/
@Service("lbsDispatchStationCircleSelectService")
@Slf4j
public class LbsDispatchStationCircleSelectService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * 地址服务
     */
    @Resource
    AddressRpc addressRpc;

    /**
     *
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     * jdhDispatchDomainService
     */
    @Resource
    private JdhDispatchDomainService jdhDispatchDomainService;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("LbsDispatchStationCircleSelectService -> call, 圈选逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("LbsDispatchStationCircleSelectService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));
        //获取用户上门地址省市区信息
        ServiceLocation serviceLocation = context.getJdhDispatch().getServiceLocation();
        //未获取上门地址，默认圈选护士失败
        if (StringUtils.isBlank(serviceLocation.getServiceLocationDetail())) {
            log.info("LbsDispatchStationCircleSelectService -> call, 未获取上门地址，默认圈选护士失败，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        //sku列表
        if (Objects.isNull(context.getJdhDispatch().getServiceInfo()) || CollectionUtils.isEmpty(context.getJdhDispatch().getServiceInfo().getPatients())) {
            log.info("LbsDispatchStationCircleSelectService -> call, 未获取服务商品信息，默认圈选护士失败，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        Set<Long> skuNoList = context.getJdhDispatch().getServiceInfo().getPatients().stream().flatMap(patient -> patient.getServiceItems().stream()).map(ServiceItem::getServiceId).collect(Collectors.toSet());
        log.info("LbsDispatchStationCircleSelectService -> call, skuNoList={}", JSON.toJSONString(skuNoList));
        if (CollectionUtils.isEmpty(skuNoList)) {
            log.info("LbsDispatchStationCircleSelectService -> call, 未获取服务商品信息，默认圈选护士失败，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        List<DispatchAngelBO> angelList = dispatchFlowDependRpc.queryAngelListByGeo(DispatchQueryLbsStationAngelParam.builder().address(serviceLocation.getServiceLocationDetail()).skuNos(skuNoList).build());
        context.setSelectionAngelList(angelList);
        log.info("LbsDispatchStationCircleSelectService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));
        log.info("LbsDispatchStationCircleSelectService -> call, 圈选逻辑 END");
        return new OutputMessage();
    }

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    /**
     *
     * @return
     */
    @Override
    public String getCode() {
        return DispatchFlowEnum.LBS_STATION_ANGEL_CIRCLE_SELECTION.getFlowCode();
    }

    /**
     *
     * @return
     */
    @Override
    public String getName() {
        return DispatchFlowEnum.LBS_STATION_ANGEL_CIRCLE_SELECTION.getFlowDesc();
    }

    /**
     *
     * @param inputMessage
     */
    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======LbsDispatchStationCircleSelectService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}