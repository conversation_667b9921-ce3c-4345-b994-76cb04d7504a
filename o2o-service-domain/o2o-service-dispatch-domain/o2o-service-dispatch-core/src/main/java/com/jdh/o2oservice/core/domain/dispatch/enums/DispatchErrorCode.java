package com.jdh.o2oservice.core.domain.dispatch.enums;

import com.jdh.o2oservice.base.enums.AlarmLevelEnum;
import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

import java.util.Objects;

/**
 * @ClassName DispatchErrorCode
 * @Description
 * <AUTHOR>
 * @Date 2024/4/24 21:36
 **/
@ToString
public enum DispatchErrorCode implements AbstractErrorCode {

    /**
     * 派单域错误码
     */
    DISPATCH_CREATE_BUSY(DomainEnum.DISPATCH, "70001", "创建派单任务繁忙"),
    DISPATCH_CALLBACK_BUSY(DomainEnum.DISPATCH, "70002", "当前派单任务繁忙，请稍后再试"),
    DISPATCH_DETAIL_ANGEL_ID_NOT_EXIST(DomainEnum.DISPATCH, "70003", "派单任务明细服务者编码为空"),
    DISPATCH_NOT_EXIST(DomainEnum.DISPATCH, "70004", "派单任务不存在"),
    DISPATCH_STATUS_NOT_ALLOW_OPERATION(DomainEnum.DISPATCH, "70030", "当前单据状态不允许此操作"),
    DISPATCH_DETAIL_NOT_EXIST(DomainEnum.DISPATCH, "70036", "派单任务已过期，如您已接单可前往日程页查询"),
    DISPATCH_NETHP_DIAG_ORDER_SUBMIT_ERROR(DomainEnum.DISPATCH, "70037", "调用互医下单失败"),
    DISPATCH_NETHP_DIAG_ORDER_END_ERROR(DomainEnum.DISPATCH, "70038", "调用互医订单完成失败"),
    DISPATCH_RECEIVE_ASSIGN_ORDER_ERROR(DomainEnum.DISPATCH, "70039", "抱歉，您未能在规定时间内完成接单操作，请留意下一个订单的到来~"),
    DISPATCH_RECEIVE_GRAB_ORDER_ERROR(DomainEnum.DISPATCH, "70040", "抱歉，手慢了，此单已被其他人抢走，请留意下一个订单的到来~"),
    DISPATCH_NETHP_DIAG_ORDER_MODIFY_ERROR(DomainEnum.DISPATCH, "70041", "调用互医修改失败"),
    DISPATCH_NETHP_DIAG_ORDER_SEARCH_ERROR(DomainEnum.DISPATCH, "70042", "调用互医查询失败"),
    DISPATCH_RECEIVE_ORDER_NOT_PROCESS_ERROR(DomainEnum.DISPATCH, "70043", "订单退款中/已退款，暂不可接单"),
    DISPATCH_TARGET_ANGEL_TYPE_ERROR(DomainEnum.DISPATCH, "70044", "定向派单,资源类型或者资源类型明细为空"),
    RIDER_HAS_NO_PROVIDER_ERROR(DomainEnum.DISPATCH, "70045", "无骑手供应商配置信息,无法派供应商"),
    DISPATCH_SKILL_NOT_ALLOW_OPERATION(DomainEnum.DISPATCH, "70049", "您暂未开通服务项目中所需的{}，暂不能接单。请联系客服为您开通技能"),
    DISPATCH_TIME_TO_VISIT_REMIND(DomainEnum.DISPATCH, "70048", "当前距离用户预约的上门时间较近，请确保可以准时履约。如不能请尽快将订单退回，迟到可能产生处罚"),
    DISPATCH_TIME_TO_VISIT_CONFLICT_REMIND(DomainEnum.DISPATCH, "70047", "系统检测到您刚接的订单前后有其他临近订单，请妥善安排时间，确保可以正常履约。如不能请尽快将订单退回，迟到可能产生处罚"),
    DISPATCH_SETTLE_PRICE_ERROR(DomainEnum.DISPATCH, "70048", "获取护士结算价失败，请检查配置后重试"),
    DISPATCH_MODIFY_DATE_FAIL(DomainEnum.DISPATCH, "70049", "更新服务时间失败"),
    DISPATCH_MODIFY_DATE_ANGEL_FAIL(DomainEnum.DISPATCH, "70050", "更新服务时间重新分派服务者失败"),
    DISPATCH_MODIFY_DATE_EXIST(DomainEnum.DISPATCH, "70050", "更新服务时间派单任务执行中"),
    DISPATCH_ANGEL_SKILL_EMPTY(DomainEnum.DISPATCH, "70051", "派单护士的技能信息为空"),
    DISPATCH_ANGEL_SKILL_NOT_MATCH_ITEM_SKILL(DomainEnum.DISPATCH, "70052","目标护士不具备相关技能" ),
    DISPATCH_CHECK_RISK_FAIL(DomainEnum.DISPATCH, "70053","派单失败，请尝试其他护士" ),
    RECEIVE_CHECK_RISK_FAIL(DomainEnum.DISPATCH, "70054","当前订单存在风险，暂时无法接单" ),
    ;

    /**
     * PromiseErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    DispatchErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    DispatchErrorCode(DomainEnum domainEnum, String code, String description, AlarmLevelEnum alarmLevelEnum) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
        this.alarmLevelEnum = alarmLevelEnum;
    }

    /** */
    private DomainEnum domainEnum;

    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 报警级别
     */
    private AlarmLevelEnum alarmLevelEnum;

    /**
     * getCode
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * getDescription
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return this.description;
    }

    /**
     * 获取报警级别
     *
     * @return
     */
    public AlarmLevelEnum getAlarmLevel() {
        if(Objects.isNull(alarmLevelEnum)) {
            return AlarmLevelEnum.WARING;
        }
        return alarmLevelEnum;
    }
}