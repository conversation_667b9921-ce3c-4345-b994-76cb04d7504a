package com.jdh.o2oservice.application.support.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.ActivityConfigRewardTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelAggregateEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelAuditProcessStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelRecruitmentActivity;
import com.jdh.o2oservice.core.domain.angel.model.ext.ActivityRewardProgress;
import com.jdh.o2oservice.core.domain.angel.repository.db.ActivityRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelActivityRepQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepPageQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawAccountVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawDetailVo;
import com.jdh.o2oservice.core.domain.support.basic.enums.ActivityRewardStatusEnum;
import com.jdh.o2oservice.core.domain.support.file.context.FileExportContext;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileExportHandler;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAbstractAngelActivity;
import com.jdh.o2oservice.export.angel.dto.AngelRecruitmentActivityManDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.infrastructure.repository.db.po.AngelSettlementPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * @ClassName AngelSettlementDiffDataExportHandlerImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/25 10:06
 **/
@Slf4j
@Service
public class AngelSettlementDiffDataExportHandlerImpl extends AbstractFileExportHandler {

    /**
     *
     */
    @Resource
    private AngelSettlementRepository angelSettlementRepository;

    /**
     *
     */
    @Resource
    private HySettleRpc hySettleRpc;

    /**
     * angelRepository
     */
    @Resource
    private AngelRepository angelRepository;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     *
     * @param ctx
     */
    @Override
    protected void preHandle(FileExportContext ctx) {

    }

    /**
     *
     * @param ctx ctx
     */
    @Override
    protected void getData(FileExportContext ctx) {
        Map<String, Object> queryParam = ctx.getQueryParam();
        log.info("AngelSettlementDiffDataExportHandlerImpl -> getData queryParam:{}",JSON.toJSONString(queryParam));
        String queryMonth = Convert.convert(String.class, queryParam.get("queryMonth"));

        //查询所有审核通过的护士
        List<JdhAngel> jdhAngelList = angelRepository.findList(JdhAngelRepQuery.builder().auditProcessStatus(AngelAuditProcessStatusEnum.AUDIT_PASS.getCode()).build());

        CompletableFuture<Map<Long, BigDecimal>> nethpAmountCompletableFuture = CompletableFuture.supplyAsync(() -> getNethpAmount(jdhAngelList, queryMonth), executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        CompletableFuture<Map<Long, BigDecimal>> homeAmountCompletableFuture = CompletableFuture.supplyAsync(() -> getHomeAmount(jdhAngelList, queryMonth), executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));

        CompletableFuture.allOf(nethpAmountCompletableFuture, homeAmountCompletableFuture).join();

        Map<Long, BigDecimal> angel2NethpAmount = new HashMap<>();
        Map<Long, BigDecimal> angel2HomeAmount = new HashMap<>();
        try {
            //获取互医的金额数据
            angel2NethpAmount = nethpAmountCompletableFuture.get();
            //获取到家的金额数据
            angel2HomeAmount = homeAmountCompletableFuture.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        //遍历比较金额
        BlockingQueue<Map<String, Object>> queue = ctx.getQueue();
        if (queue == null) {
            queue = new LinkedBlockingQueue<>();
            ctx.setQueue(queue);
        }
        for (JdhAngel angel : jdhAngelList) {
            BigDecimal homeAmount = angel2HomeAmount.getOrDefault(angel.getAngelId(), BigDecimal.ZERO);
            BigDecimal nethpAmount = angel2NethpAmount.getOrDefault(angel.getAngelId(), BigDecimal.ZERO);

            boolean loggedOut = BigDecimal.ONE.negate().equals(nethpAmount);
            //双方金额不一致记录
            if (homeAmount.compareTo(nethpAmount) != 0) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("angelId",angel.getAngelId());
                jsonObject.put("angelName",angel.getAngelName());
                jsonObject.put("nethpDocId",angel.getNethpDocId());
                jsonObject.put("homeSettlementAmount", homeAmount);
                jsonObject.put("hySettlementAmount", loggedOut ? BigDecimal.ZERO : nethpAmount);
                jsonObject.put("loggedOut", loggedOut);

                Map<String, Object> column = new HashMap<>();
                column.put(AngelAggregateEnum.ANGEL.getCode(), jsonObject);
                queue.add(column);
            }
        }
        log.info("AngelSettlementDiffDataExportHandlerImpl getData 结束, queue={}", JSON.toJSONString(queue));
    }

    /**
     *
     * @param jdhAngelList 互医护士id列表
     * @param queryMonth 查询范围
     * @return
     */
    private Map<Long, BigDecimal> getHomeAmount(List<JdhAngel> jdhAngelList, String queryMonth) {
        //查询护士全量累计收入
        if (StringUtils.isNotBlank(queryMonth)) {
            Date month = null;
            try {
                month = new SimpleDateFormat("yyyy-MM-dd").parse(queryMonth);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            Date settleTimeStart = DateUtil.beginOfDay(month);
            Date settleTimeEnd = DateUtil.endOfMonth(month);
            Map<Long, BigDecimal> result = new HashMap<>();

            for (JdhAngel jdhAngel : jdhAngelList) {
                try {
                    AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
                    queryContext.setAngelId(jdhAngel.getAngelId());
                    queryContext.setSettlementType(SettleTypeEnum.INCOME.getType());
                    queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
                    queryContext.setCreateTimeStart(settleTimeStart);
                    queryContext.setCashTimeEnd(settleTimeEnd);
                    BigDecimal bigDecimal = angelSettlementRepository.querySettlementAmountTot(queryContext);
                    result.put(jdhAngel.getAngelId(), bigDecimal);
                } catch (Throwable e) {
                    log.info("AngelSettlementDiffDataExportHandlerImpl getHomeAmount, jdhAngel={}", JSON.toJSONString(jdhAngel), e);
                }
            }
            return result;
        }
        // 按月查护士全量收入
        Map<Long, BigDecimal> result = new HashMap<>();

        //护士ID列表
        List<Long> angelIdList = jdhAngelList.stream().map(JdhAngel::getAngelId).collect(Collectors.toList());

        List<AngelSettlement> list = new ArrayList<>();

        AngelSettlementQueryContext context = new AngelSettlementQueryContext();
        context.setAngelIdList(angelIdList);
        context.setSettlementType(SettleTypeEnum.INCOME.getType());
        context.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        context.setPageSize(NumConstant.NUM_20000);
        boolean hasNextPage = true;
        int pageNum = 1;
        do {
            context.setPageNum(pageNum);
            log.info("AngelSettlementDiffDataExportHandlerImpl -> getHomeAmount context:{}",JSON.toJSONString(context));
            Page<AngelSettlement> page = angelSettlementRepository.querySettlementPage(context);
            log.info("AngelSettlementDiffDataExportHandlerImpl -> getHomeAmount page:{}",JSON.toJSONString(page));
            if(Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())){
                hasNextPage = false;
                continue;
            }
            list.addAll(page.getRecords());
            pageNum++;
        }while (hasNextPage);

        Map<Long, List<AngelSettlement>> collect = list.stream().collect(Collectors.groupingBy(AngelSettlement::getAngelId));

        for (Map.Entry<Long, List<AngelSettlement>> entry : collect.entrySet()) {
            BigDecimal tot = CollectionUtils.isEmpty(entry.getValue()) ? BigDecimal.ZERO : entry.getValue().stream().map(AngelSettlement::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            result.put(entry.getKey(), tot);
        }
        return result;
    }

    /**
     *
     * @param jdhAngelList 互医护士id列表
     * @param queryMonth 查询范围
     * @return
     */
    private Map<Long, BigDecimal> getNethpAmount(List<JdhAngel> jdhAngelList, String queryMonth) {
        //查询护士全量累计收入
        if (StringUtils.isBlank(queryMonth)) {
            Map<Long, BigDecimal> result = new HashMap<>();

            for (JdhAngel jdhAngel : jdhAngelList) {
                try {
                    AngelSettlementQueryContext context = new AngelSettlementQueryContext();
                    context.setUserPin(jdhAngel.getAngelPin());
                    context.setAngelId(jdhAngel.getAngelId());
                    WithdrawAccountVo withdrawAccountVo = hySettleRpc.queryWithdrawAccount(context);
                    result.put(jdhAngel.getAngelId(), Objects.nonNull(withdrawAccountVo) && Objects.nonNull(withdrawAccountVo.getCumulativeAcquisitionAmount()) ? withdrawAccountVo.getCumulativeAcquisitionAmount() : BigDecimal.ZERO);
                } catch (BusinessException e) {
                    log.info("AngelSettlementDiffDataExportHandlerImpl getNethpAmount, jdhAngel={}", JSON.toJSONString(jdhAngel), e);
                    if ("未找到账户信息，请联系客服人员".equals(e.getErrorCode().getDescription())) {
                        result.put(jdhAngel.getAngelId(), BigDecimal.ONE.negate());
                    }
                } catch (SystemException e) {
                    log.info("AngelSettlementDiffDataExportHandlerImpl getNethpAmount, jdhAngel={}", JSON.toJSONString(jdhAngel), e);
                    if ("未找到账户信息，请联系客服人员".equals(e.getErrorCode().getDescription())) {
                        result.put(jdhAngel.getAngelId(), BigDecimal.ONE.negate());
                    }
                } catch (Throwable e) {
                    log.info("AngelSettlementDiffDataExportHandlerImpl getNethpAmount, jdhAngel={}", JSON.toJSONString(jdhAngel), e);
                }
            }
            return result;
        }
        // 按月查护士全量收入
        Map<Long, BigDecimal> result = new HashMap<>();

        for (JdhAngel jdhAngel : jdhAngelList) {
            try {
                List<WithdrawDetailVo> list = new ArrayList<>();
                AngelSettlementQueryContext context = new AngelSettlementQueryContext();
                context.setUserPin(jdhAngel.getAngelPin());
                context.setAngelId(jdhAngel.getAngelId());
                context.setPageSize(NumConstant.NUM_3000);
                boolean hasNextPage = true;
                int pageNum = 1;
                do {
                    context.setPageNum(pageNum);
                    log.info("AngelSettlementDiffDataExportHandlerImpl -> getData context:{}",JSON.toJSONString(context));
                    PageDto<WithdrawDetailVo> pageDto = hySettleRpc.queryWithdrawDetailList(context);
                    log.info("AngelSettlementDiffDataExportHandlerImpl -> pageDto:{}",JSON.toJSONString(pageDto));
                    if(Objects.isNull(pageDto) || CollectionUtils.isEmpty(pageDto.getList())){
                        hasNextPage = false;
                        continue;
                    }
                    list.addAll(pageDto.getList());
                    pageNum++;
                }while (hasNextPage);

                BigDecimal reduce = list.stream().filter(withdrawDetailVo -> Objects.nonNull(withdrawDetailVo) && Objects.nonNull(withdrawDetailVo.getAmount())).map(WithdrawDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                result.put(jdhAngel.getAngelId(), reduce);
            } catch (Throwable e) {
                log.info("AngelSettlementDiffDataExportHandlerImpl getNethpAmount, jdhAngel={}", JSON.toJSONString(jdhAngel), e);
            }
        }
        return result;
    }

    /**
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return FileExportTypeEnum.ANGEL_SETTLEMENT_DIFF_DATA_EXPORT.getType();
    }

    /**
     *
     * @param page
     * @return
     */
    private PageDto<AngelRecruitmentActivityManDto> convertAngelRecruitmentActivityManDto(Page<JdhAbstractAngelActivity> page) {
        if (Objects.isNull(page)) {
            return null;
        }
        List<JdhAbstractAngelActivity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageDto.getEmptyPage();
        }
        List<JdhAngelRecruitmentActivity> list = Convert.toList(JdhAngelRecruitmentActivity.class, records);
        Set<Long> angelSet = list.stream().map(JdhAbstractAngelActivity::getAngelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> beInviteAngelSet = list.stream().map(recruitmentActivity -> recruitmentActivity.getActivityRuleProgress().getAcceptAngelId()).filter(Objects::nonNull).collect(Collectors.toSet());
        angelSet.addAll(beInviteAngelSet);
        List<Long> angelIdList = Lists.newArrayList(angelSet);

        JdhAngelRepPageQuery angelRepPageQuery = JdhAngelRepPageQuery.builder().angelIdList(angelIdList).build();
        angelRepPageQuery.setPageNum(1);
        angelRepPageQuery.setPageSize(100000);
        angelRepPageQuery.setSearchDel(true);
        Page<JdhAngel> jdhAngelPage = angelRepository.page(angelRepPageQuery);
        Map<Long, JdhAngel> id2Angel = new HashMap<>();
        if (Objects.nonNull(jdhAngelPage)) {
            id2Angel = jdhAngelPage.getRecords().stream().collect(Collectors.toMap(JdhAngel::getAngelId, angel -> angel));
        };

        List<AngelRecruitmentActivityManDto> dtoList = new ArrayList<>();
        for (JdhAngelRecruitmentActivity recruitmentActivity : list) {
            JdhAngel angel = id2Angel.get(recruitmentActivity.getAngelId());
            JdhAngel beInviteAngel = id2Angel.get(recruitmentActivity.getActivityRuleProgress().getAcceptAngelId());
            AngelRecruitmentActivityManDto dto = new AngelRecruitmentActivityManDto();
            dto.setAngelId(String.valueOf(beInviteAngel.getAngelId()));
            dto.setAngelPin(beInviteAngel.getAngelPin());
            dto.setAngelName(beInviteAngel.getAngelName());
            dto.setInviteAngelId(String.valueOf(recruitmentActivity.getAngelId()));
            dto.setInviteAngelPin(angel.getAngelPin());
            dto.setInviteAngelName(angel.getAngelName());
            dto.setProfessionTitleCode(CollectionUtils.isNotEmpty(beInviteAngel.getJdhAngelProfessionRelList()) ? beInviteAngel.getJdhAngelProfessionRelList().get(0).getProfessionTitleCode() : null);
            dto.setProfessionTitleName(CollectionUtils.isNotEmpty(beInviteAngel.getJdhAngelProfessionRelList()) ? beInviteAngel.getJdhAngelProfessionRelList().get(0).getProfessionTitleName() : null);
            dto.setInstitutionName(CollectionUtils.isNotEmpty(beInviteAngel.getJdhAngelProfessionRelList()) ? beInviteAngel.getJdhAngelProfessionRelList().get(0).getInstitutionName() : null);
            dto.setAuditProcessStatus(beInviteAngel.getAuditProcessStatus());
            dto.setAuditProcessStatusDesc(AngelAuditProcessStatusEnum.getDescByCode(beInviteAngel.getAuditProcessStatus()));
            dto.setRewardStatus(recruitmentActivity.getRewardStatus());
            dto.setRewardStatusDesc(ActivityRewardStatusEnum.getDescByStatus(recruitmentActivity.getRewardStatus()));
            List<ActivityRewardProgress> cashRewardList = Optional.ofNullable(recruitmentActivity.getActivityRewardProgress()).orElse(new ArrayList<>()).stream().filter(activityRewardProgress -> Objects.equals(activityRewardProgress.getConfigRewardType(), ActivityConfigRewardTypeEnum.CASH.getType())).collect(Collectors.toList());
            dto.setRewardValue(CollectionUtils.isNotEmpty(cashRewardList) ?
                    cashRewardList.stream().map(ActivityRewardProgress::getConfigRewardValue).filter(StringUtils::isNotBlank).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add).toPlainString() : "");
            dto.setRegisterTime(DateUtil.formatDateTime(beInviteAngel.getCreateTime()));
            dto.setAuditProcessDateTime(beInviteAngel.getAuditProcessDate());
            dtoList.add(dto);
        }
        PageDto<AngelRecruitmentActivityManDto> resultPage = new PageDto<>();
        resultPage.setList(dtoList);
        resultPage.setTotalPage(page.getPages());
        resultPage.setPageNum(page.getCurrent());
        resultPage.setPageSize(page.getSize());
        resultPage.setTotalCount(page.getTotal());
        return resultPage;
    }
}