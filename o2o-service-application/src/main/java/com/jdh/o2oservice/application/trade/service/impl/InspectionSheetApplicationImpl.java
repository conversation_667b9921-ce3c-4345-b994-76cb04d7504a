package com.jdh.o2oservice.application.trade.service.impl;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.trade.service.InspectionSheetApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.common.enums.VerticalEnum;
import com.jdh.o2oservice.core.domain.product.bo.UserAddressDetailBO;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.product.service.ProductDomainService;
import com.jdh.o2oservice.core.domain.trade.bo.InspectProjectChildDetailBO;
import com.jdh.o2oservice.core.domain.trade.bo.InspectSheetInfoBO;
import com.jdh.o2oservice.core.domain.trade.bo.PartnerSourceOrderBO;
import com.jdh.o2oservice.core.domain.trade.bo.PartnerSourceOrderQuery;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.rpc.InspectionQueryRpc;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.StationGeoForManQuery;
import com.jdh.o2oservice.export.product.dto.InspectProjectChildDetailDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuServiceItemRelRequest;
import com.jdh.o2oservice.export.trade.dto.InspectionSheetButtonDTO;
import com.jdh.o2oservice.export.trade.query.InspectionSheetButtonParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/25 14:57
 **/
@Slf4j
@Service
public class InspectionSheetApplicationImpl implements InspectionSheetApplication {

    @Resource
    private JdhSkuRepository jdhSkuRepository;
    @Resource
    private StationApplication stationApplication;
    @Resource
    private ProductApplication productApplication;
    @Resource
    private InspectionQueryRpc inspectionQueryRpc;

    @Resource
    private ProductDomainService productDomainService;
    /**
     * 互医跳转地址
     */
    @Value("${h5SettleUrl}")
    private String h5SettleUrl;
    /**
     * 互医跳转地址
     */
    @Value("${miniSettleUrl}")
    private String miniSettleUrl;

    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.trade.service.impl.InspectionSheetApplicationImpl.queryInspectionSheetButton")
    @Override
    public InspectionSheetButtonDTO queryInspectionSheetButton(InspectionSheetButtonParam param) {
        InspectionSheetButtonDTO inspectionSheetButtonDTO = new InspectionSheetButtonDTO();
        // sku 和地址获取服务站；
        log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton 通过检验单查询是否可立即预约 param:{}", JSON.toJSONString(param));
        List<JdhSkuDto> jdhSkuDtos = null;
        if (param.getSheetId() != null) {
            InspectionSheetButtonParam paramInspectionSheetButtonParam = new InspectionSheetButtonParam();
            paramInspectionSheetButtonParam.setSheetId(param.getSheetId());
            paramInspectionSheetButtonParam.setUserPin(param.getUserPin());
            if(Objects.nonNull(param.getAddressId())){
                paramInspectionSheetButtonParam.setAddressId(param.getAddressId());
            }
            if(Objects.nonNull(param.getFullAddress())){
                paramInspectionSheetButtonParam.setFullAddress(param.getFullAddress());
            }
            // addressId场景下补全fullAddress
            if(param.getAddressId() != null && param.getUserPin() != null && StringUtils.isBlank(param.getFullAddress())){
                UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(param.getUserPin(), param.getAddressId());
                if(userAddressDetail != null){
                    param.setFullAddress(userAddressDetail.getFullAddress());
                    paramInspectionSheetButtonParam.setFullAddress(userAddressDetail.getFullAddress());
                }
            }
            jdhSkuDtos = querySettlementSku(paramInspectionSheetButtonParam);
            if (CollectionUtils.isEmpty(jdhSkuDtos)) {
                log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton 通过检验单查询是否可立即预约 兑换商品出商品列表为空，返回不可立即预约 param:{}", JSON.toJSONString(param));
                inspectionSheetButtonDTO.setShow(true);
                inspectionSheetButtonDTO.setIsClick(false);
                return inspectionSheetButtonDTO;
            }
            Long addressId = param.getAddressId();
            inspectionSheetButtonDTO.setH5JumpLink(String.format(h5SettleUrl, addressId == null ? "" : addressId, PartnerSourceEnum.JDH_NETDIAG.getCode(), param.getSheetId()));
            inspectionSheetButtonDTO.setMiniJumpLink(String.format(miniSettleUrl, addressId == null ? "" : addressId, PartnerSourceEnum.JDH_NETDIAG.getCode(), param.getSheetId(), param.getSheetId()));
        }

        if(param.getAddressId() != null && StringUtils.isBlank(param.getFullAddress())){
            log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton getUserLastAddress start");
            UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(param.getUserPin(), param.getAddressId());
            log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton getUserLastAddress end");
            if(userAddressDetail != null){
                param.setFullAddress(userAddressDetail.getFullAddress());
            }
        }

        //如果 addressId 为空，用检验单 id 作为入参，下游地址会用这个做缓存；
        if (param.getAddressId() == null) {
            param.setAddressId(param.getSheetId());
        }

        StationGeoForManQuery stationGeoForManQuery = new StationGeoForManQuery();
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setAddressId(String.valueOf(param.getAddressId()));
        addressDetail.setFullAddress(param.getFullAddress());
        List<AddressDetail> addressDetails = Arrays.asList(addressDetail);
        stationGeoForManQuery.setAddressDetailList(addressDetails);
        if (CollectionUtils.isNotEmpty(jdhSkuDtos)) {
            stationGeoForManQuery.setSkuNos(jdhSkuDtos.stream().map(JdhSkuDto::getSkuId).collect(Collectors.toSet()));
        }
        log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton queryJdhStationGeoForMan start");
        List<JdhStationDto> jdhStationDtos = stationApplication.queryJdhStationGeoForMan(stationGeoForManQuery);
        log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton queryJdhStationGeoForMan end");

        if (CollectionUtils.isEmpty(jdhStationDtos)) {
            inspectionSheetButtonDTO.setShow(true);
            inspectionSheetButtonDTO.setIsClick(false);
            log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton 通过检验单查询是否可立即预约 匹配到服务站为空，返回不可立即预约 param:{}，result:{}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(jdhStationDtos));
            return inspectionSheetButtonDTO;
        }
        log.info("InspectionSheetApplicationImpl.queryInspectionSheetButton 通过检验单查询是否可立即预约 返回可立即预约 param:{}，result:{}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(jdhStationDtos));
        inspectionSheetButtonDTO.setShow(true);
        inspectionSheetButtonDTO.setIsClick(true);
        return inspectionSheetButtonDTO;
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.trade.service.impl.InspectionSheetApplicationImpl.querySettlementSku")
    @Override
    public List<JdhSkuDto> querySettlementSku(InspectionSheetButtonParam param) {
        log.info("InspectionSheetApplicationImpl.querySettlementSku 通过检验单获取 sku 信息 param:{}", JSON.toJSONString(param));
        //根据检验单查询，检验单查询检查项
        InspectSheetInfoBO inspectSheetInfoBO = inspectionQueryRpc.queryInspectSheetBySheetId(param.getUserPin(), param.getSheetId());
        List<InspectProjectChildDetailBO> childProjects = inspectSheetInfoBO.getChildProjects();
        JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest = new JdhSkuServiceItemRelRequest();
        jdhSkuServiceItemRelRequest.setServiceItemIds(childProjects.stream().map(InspectProjectChildDetailBO::getChildId).collect(Collectors.toSet()));
        jdhSkuServiceItemRelRequest.setVerticalCode(VerticalEnum.NET_HOSPITAL_HOME_TEST.getCode());
        jdhSkuServiceItemRelRequest.setUserPin(param.getUserPin());
        if(Objects.nonNull(param.getAddressId())){
            jdhSkuServiceItemRelRequest.setAddressId(param.getAddressId());
        }
        if(Objects.nonNull(param.getFullAddress())){
            jdhSkuServiceItemRelRequest.setFullAddress(param.getFullAddress());
        }
        // 检查项查询 sku
        Map<Long, List<JdhSkuDto>> longListMap = productApplication.queryMultiSkuByServiceItem(jdhSkuServiceItemRelRequest);
        log.info("InspectionSheetApplicationImpl.querySettlementSku 通过检查项获取sku信息 param:{}，result:{}", JSON.toJSONString(jdhSkuServiceItemRelRequest), JSON.toJSONString(longListMap));
        List<JdhSkuDto> jdhSkuDtos = longListMap.values().stream()
                .filter(s -> s.size() > 0)
                .map(item -> item.get(0))
                .collect(Collectors.toList());
        if (param.getAddressId() == null) {
            return jdhSkuDtos;
        }
        // sku和地址获取服务站；只有 sku 没有地址；地址非必填
        List<JdhSkuDto> jdhSkuDtos1 = getJdhSkuDtos(param, jdhSkuDtos);
        if (CollectionUtils.isEmpty(jdhSkuDtos1)) {
            return new ArrayList<>();
        }
        return jdhSkuDtos;
    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.trade.service.impl.InspectionSheetApplicationImpl.querySettlementProjectChild")
    @Override
    public List<InspectProjectChildDetailDTO> querySettlementProjectChild(InspectionSheetButtonParam param) {
        //根据检验单查询，检验单查询检查项
        InspectSheetInfoBO inspectSheetInfoBO = inspectionQueryRpc.queryInspectSheetBySheetId(param.getUserPin(), param.getSheetId());
        List<InspectProjectChildDetailBO> childProjects = inspectSheetInfoBO.getChildProjects();
        JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest = new JdhSkuServiceItemRelRequest();
        jdhSkuServiceItemRelRequest.setServiceItemIds(childProjects.stream().map(InspectProjectChildDetailBO::getChildId).collect(Collectors.toSet()));
        jdhSkuServiceItemRelRequest.setVerticalCode(VerticalEnum.NET_HOSPITAL_HOME_TEST.getCode());
        jdhSkuServiceItemRelRequest.setUserPin(param.getUserPin());
        if(Objects.nonNull(inspectSheetInfoBO.getAddressId())){
            jdhSkuServiceItemRelRequest.setAddressId(inspectSheetInfoBO.getAddressId());
        }
        if(Objects.nonNull(param.getAddressId())){
            jdhSkuServiceItemRelRequest.setAddressId(param.getAddressId());
        }
        if(Objects.nonNull(param.getFullAddress())){
            jdhSkuServiceItemRelRequest.setFullAddress(param.getFullAddress());
        }
        // 检查项查询 sku，key 是检查项的 id
        Map<Long, List<JdhSkuDto>> longListMap = productApplication.queryMultiSkuByServiceItem(jdhSkuServiceItemRelRequest);
        log.info("InspectionSheetApplicationImpl.querySettlementProjectChild 通过检查项获取sku信息 childProjects:{}，result:{}", JSON.toJSONString(childProjects), JSON.toJSONString(longListMap));

        List<InspectProjectChildDetailDTO> result = new ArrayList<>();
        for (InspectProjectChildDetailBO childProject : childProjects) {
            InspectProjectChildDetailDTO childDetailDTO = getInspectProjectChildDetailDTO(longListMap, childProject);
            childDetailDTO.setPatientId(inspectSheetInfoBO.getPatientId());
            result.add(childDetailDTO);
        }

        if(param.getAddressId() != null && StringUtils.isBlank(param.getFullAddress())){
            UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(param.getUserPin(), param.getAddressId());
            if(userAddressDetail != null){
                param.setFullAddress(userAddressDetail.getFullAddress());
            }
        }

        if (param.getAddressId() == null || param.getFullAddress() == null) {
            log.info("InspectionSheetApplicationImpl.querySettlementProjectChild 通过检查项获取检查项和sku关系信息 没有地址信息返回数据 param:{}，result:{}", JSON.toJSONString(param), JSON.toJSONString(longListMap));
            return result;
        }

        List<JdhSkuDto> jdhSkuDtos = longListMap.values().stream()
                .filter(s -> s.size() > 0)
                .map(item -> item.get(0))
                .collect(Collectors.toList());

        List<JdhSkuDto> jdhSkuDtosDb = getJdhSkuDtos(param, jdhSkuDtos);
        if (CollectionUtils.isEmpty(jdhSkuDtosDb)) {
            result.forEach(childDetailDTO -> childDetailDTO.setSkuId(null));
            log.info("InspectionSheetApplicationImpl.querySettlementProjectChild 通过检查项获取检查项和sku关系信息 服务站没有匹配到sku信息返回数据 param:{}，result:{}", JSON.toJSONString(param), JSON.toJSONString(longListMap));
            return result;
        }
        Set<Long> skuIdSet = jdhSkuDtosDb.stream().map(JdhSkuDto::getSkuId).collect(Collectors.toSet());
        for (InspectProjectChildDetailDTO inspectProjectChildDetailDTO : result) {
            boolean contains = skuIdSet.contains(inspectProjectChildDetailDTO.getSkuId());
            if (!contains) {
                inspectProjectChildDetailDTO.setSkuId(null);
            }
        }
        log.info("InspectionSheetApplicationImpl.querySettlementProjectChild 通过检查项获取检查项和sku关系信息 param:{}，result:{}", JSON.toJSONString(param), JSON.toJSONString(longListMap));
        return result;

    }

    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.trade.service.impl.InspectionSheetApplicationImpl.queryInspectionSheet")
    @Override
    public InspectSheetInfoBO queryInspectionSheet(InspectionSheetButtonParam param) {
        return inspectionQueryRpc.queryInspectSheetBySheetId(param.getUserPin(), param.getSheetId());
    }

    @Override
    @LogAndAlarm
    public PartnerSourceOrderBO queryOuterOrder(PartnerSourceOrderQuery query) {
        PartnerSourceOrderBO partnerSourceOrderBO = inspectionQueryRpc.queryPartnerSourceOrderInfo(query);
        log.info("InspectionSheetApplicationImpl.queryOuterOrder 通过外部订单查询外部订单信息 query:{}，result:{}", JSON.toJSONString(query), JSON.toJSONString(partnerSourceOrderBO));
        if(Objects.isNull(partnerSourceOrderBO) || CollectionUtils.isEmpty(partnerSourceOrderBO.getServiceItemSet())){
            log.error("InspectionSheetApplicationImpl.queryOuterOrder 通过外部订单查询外部订单信息 未查询到外部订单信息 query:{}", JSON.toJSONString(query));
            return null;
        }
        JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest = new JdhSkuServiceItemRelRequest();
        jdhSkuServiceItemRelRequest.setServiceItemIds(partnerSourceOrderBO.getServiceItemSet().stream().map(Long::valueOf).collect(Collectors.toSet()));
        jdhSkuServiceItemRelRequest.setVerticalCode(VerticalEnum.HOSPITAL_PAID_GUIDANCE_A.getCode());
        jdhSkuServiceItemRelRequest.setUserPin(query.getUserPin());
        // 置换sku不带地址
        /*if(Objects.nonNull(query.getAddressId())){
            jdhSkuServiceItemRelRequest.setAddressId(query.getAddressId());
        }*/
        // 检查项查询 sku，key 是检查项的 id
        Map<Long, List<JdhSkuDto>> longListMap = productApplication.queryMultiSkuByServiceItem(jdhSkuServiceItemRelRequest);
        log.info("InspectionSheetApplicationImpl.queryOuterOrder 通过外部订单查询外部订单信息 通过检查项获取sku信息 param:{}，result:{}", JSON.toJSONString(jdhSkuServiceItemRelRequest), JSON.toJSONString(longListMap));

        List<JdhSkuDto> jdhSkuDtos = longListMap.values().stream()
                .filter(s -> s.size() > 0)
                .map(item -> item.get(0))
                .collect(Collectors.toList());

        Set<Long> skuIdSet = jdhSkuDtos.stream().filter(s -> Objects.nonNull(s.getServiceType())).map(JdhSkuDto::getSkuId).collect(Collectors.toSet());
        partnerSourceOrderBO.setSkuIds(skuIdSet);
        return partnerSourceOrderBO;
    }

    private static InspectProjectChildDetailDTO getInspectProjectChildDetailDTO(Map<Long, List<JdhSkuDto>> longListMap, InspectProjectChildDetailBO childProject) {
        InspectProjectChildDetailDTO childDetailDTO = new InspectProjectChildDetailDTO();
        childDetailDTO.setChildId(childProject.getChildId());
        childDetailDTO.setChildName(childProject.getChildName());
        childDetailDTO.setServiceTypeName(childProject.getServiceTypeName());
        childDetailDTO.setRelateDetails(childProject.getRelateDetails());
        childDetailDTO.setProjectId(childProject.getProjectId());
        childDetailDTO.setProjectName(childProject.getProjectName());

        List<JdhSkuDto> skuList = longListMap.get(childProject.getChildId());
        if (CollectionUtils.isNotEmpty(skuList)) {
            childDetailDTO.setSkuId(skuList.get(0).getSkuId()); // 护士上门只有一个 sku
        }
        return childDetailDTO;
    }

    private List<JdhSkuDto> getJdhSkuDtos(InspectionSheetButtonParam param, List<JdhSkuDto> jdhSkuDtos) {
        StationGeoForManQuery stationGeoForManQuery = new StationGeoForManQuery();
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setAddressId(String.valueOf(param.getAddressId()));
        addressDetail.setFullAddress(param.getFullAddress());
        List<AddressDetail> addressDetails = Arrays.asList(addressDetail);
        stationGeoForManQuery.setAddressDetailList(addressDetails);
        stationGeoForManQuery.setSkuNos(jdhSkuDtos.stream().map(JdhSkuDto::getSkuId).collect(Collectors.toSet()));
        List<JdhStationDto> jdhStationDtos = stationApplication.queryJdhStationGeoForMan(stationGeoForManQuery);
        log.info("通过sku和地址信息获取地址信息 param:{}，result:{}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(jdhStationDtos));
        if (CollectionUtils.isEmpty(jdhStationDtos)) {
            return new ArrayList<>();
        }
        return jdhSkuDtos;
    }
}
