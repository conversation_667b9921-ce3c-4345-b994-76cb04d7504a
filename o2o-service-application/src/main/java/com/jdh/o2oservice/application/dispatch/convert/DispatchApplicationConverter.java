package com.jdh.o2oservice.application.dispatch.convert;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelWorkStatusEventEnum;
import com.jdh.o2oservice.common.enums.DispatchDetailTypeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelTaskStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.*;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchDetailListRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchDetailPageRepQuery;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemAngelSkillRel;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.*;
import com.jdh.o2oservice.export.dispatch.query.DispatchDetailForManRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchWaitingReceiveListRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchWaitingReceiveOverviewRequest;
import com.jdh.o2oservice.export.product.cmd.ItemMaterialPackageRelCmd;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchApplicationConverter
 * @Description
 * <AUTHOR>
 * @Date 2024/4/21 16:06
 **/
@Mapper
public interface DispatchApplicationConverter {

    /**
     * log
     */
    Logger log = LoggerFactory.getLogger(DispatchApplicationConverter.class);

    DispatchApplicationConverter INS = Mappers.getMapper(DispatchApplicationConverter.class);

    /**
     * cmd2SubmitContext
     * @param cmd
     * @return
     */
    @Mapping(target = "serviceInfo.appointmentTime", expression = "java(new com.jdh.o2oservice.core.domain.dispatch.model.DispatchCtxAppointmentTime(submitDispatchCmd.getAppointmentTime().getDateType(), submitDispatchCmd.getAppointmentTime().getAppointmentStartTime(),submitDispatchCmd.getAppointmentTime().getAppointmentEndTime(), submitDispatchCmd.getAppointmentTime().getIsImmediately()))")
    @Mapping(target = "serviceInfo.patients", source = "patients")
    @Mapping(target = "serviceInfo.remark", source = "remark")
    @Mapping(target = "serviceInfo.sourceVoucherId", source = "sourceVoucherId")
    @Mapping(target = "serviceInfo.intendedAngelIds", source = "intendedAngelIds")
    SubmitDispatchContext cmd2SubmitContext(SubmitDispatchCmd cmd);

    /**
     * jdhSku2DispatchSku
     * @param jdhSkuDtoMap
     * @return
     */
    Map<Long, DispatchSku> jdhSku2DispatchSku(Map<Long, JdhSkuDto> jdhSkuDtoMap);

    /**
     * skillRel2DispatchItemSkillRel
     * @param list
     * @return
     */
    List<JdhDispatchServiceItemSkillRel> skillRel2DispatchItemSkillRel(List<ServiceItemAngelSkillRel> list);

    /**
     * cmd2AngelDispatchContext
     * @param cmd
     * @return
     */
    AngelDispatchContext cmd2AngelDispatchContext(AngelDispatchCmd cmd);

    /**
     * cmd2FreezeDispatchContext
     * @param cmd
     * @return
     */
    FreezeDispatchContext cmd2FreezeDispatchContext(FreezeDispatchCmd cmd);

    /**
     * cmd2InvalidDispatchContext
     * @param cmd
     * @return
     */
    InvalidDispatchContext cmd2InvalidDispatchContext(CancelDispatchCmd cmd);

    /**
     * cmd2DispatchCallbackContext
     * @param cmd
     * @return
     */
    @Mapping(target = "dispatchType", source = "dispatchType.type")
    DispatchCallbackContext cmd2DispatchCallbackContext(DispatchCallbackCmd cmd);

    /**
     * cmd2DispatchCallbackContext
     * @param cmd
     * @return
     */
    DispatchCallbackContext cmd2DispatchCallbackContext(RecoverDispatchCmd cmd);

    /**
     * cmd2ReDispatchContext
     * @param cmd
     * @return
     */
    ReDispatchContext cmd2ReDispatchContext(ReDispatchCmd cmd);

    /**
     * cmd2TargetDispatchCmd
     * @param cmd
     * @return
     */
    TargetDispatchContext cmd2TargetDispatchCmd(TargetDispatchCmd cmd);

    /**
     *
     * @param angelPlanChargeDto
     * @return
     */
    DispatchAngelPlanCharge dto2DispatchAngelPlanCharge(AngelPlanChargeDto angelPlanChargeDto);

    /**
     *
     * @param list
     * @return
     */
    List<DispatchAngelPlanCharge> dto2DispatchAngelPlanCharge(List<AngelPlanChargeDto> list);

    /**
     *
     * @param request
     * @return
     */
    DispatchDetailListRepQuery request2DispatchDetailListRepQuery(DispatchWaitingReceiveListRequest request);

    /**
     *
     * @param request
     * @return
     */
    DispatchDetailListRepQuery request2DispatchDetailListRepQuery(DispatchWaitingReceiveOverviewRequest request);

    /**
     * cmd2AngelRefuseDispatchContext
     * @param cmd
     * @return
     */
    AngelRefuseDispatchContext cmd2AngelRefuseDispatchContext(AngelDispatchOperationCmd cmd);

    /**
     *
     * @param detail
     * @return
     */
    JdhDispatchDetailDto dispatchDetail2Dto(JdhDispatchDetail detail);

    /**
     * angelWorkStatus2DispatchFreezeStatus
     * @param angelWorkStatusDto
     * @return
     */
    DispatchFreezeStatusDto angelWorkStatus2DispatchFreezeStatus(AngelWorkStatusDto angelWorkStatusDto);

    /**
     * angelWorkStatus2DispatchInvalidStatus
     * @param angelWorkStatusDto
     * @return
     */
    DispatchInvalidStatusDto  angelWorkStatus2DispatchInvalidStatus(AngelWorkStatusDto angelWorkStatusDto);

    /**
     * dispatchAngelPlanCharge2AngelChargeInfo
     * @param planCharge
     * @return
     */
    AngelChargeInfo dispatchAngelPlanCharge2AngelChargeInfo(DispatchAngelPlanCharge planCharge);

    /**
     *
     * @param serviceItems
     * @return
     */
    List<DispatchServiceItemDto> dispatchServiceItem2DispatchServiceItemDto(List<ServiceItem> serviceItems);

    /**
     * JdhDispatchHistory2Dto
     * @param list
     * @return
     */
    List<JdhDispatchHistoryDto> jdhDispatchHistory2Dto(List<JdhDispatchHistory> list);

    /**
     *
     * @param jdhDispatch
     * @return
     */
    JdhDispatchDto JdhDispatch2Dto(JdhDispatch jdhDispatch);

    /**
     * convert
     * @param jdhDispatchDetail
     * @return
     */
    JdhDispatchForManDTO convert(JdhDispatchDetail jdhDispatchDetail);


    @Mapping(source = "records" ,target = "list")
    @Mapping(source = "total" ,target = "totalCount")
    @Mapping(source = "current" ,target = "pageNum")
    PageDto<JdhDispatchForManDTO> convert(Page<JdhDispatchDetail> jdhDispatchDetailPage);

    /**
     * convert
     * @param jdhDispatchDetails
     * @return
     */
    List<JdhDispatchForManDTO> convert(List<JdhDispatchDetail> jdhDispatchDetails);

    /**
     *
     * @param detail
     * @return
     */
    DispatchDetailPageRepQuery convert(DispatchDetailForManRequest detail);

    /**
     *
     * @param detail
     * @return
     */
    default JdhDispatchDetailDto dispatchDetail2Dto(JdhDispatchDetail detail, JdhDispatch dispatch, Map<Long, ServiceItemDto> itemDtoMap, PromiseDto promiseDto){
        return dispatchDetail2Dto(detail, dispatch, itemDtoMap, new HashMap<>(), promiseDto);
    }

    /**
     *
     * @param detail
     * @return
     */
    default JdhDispatchDetailDto dispatchDetail2Dto(JdhDispatchDetail detail, JdhDispatch dispatch, Map<Long, ServiceItemDto> itemDtoMap,Map<Long, JdhStationServiceItemRel> serviceItem2StationItemMap, PromiseDto promiseDto){
        if (Objects.isNull(detail)) {
            return null;
        }
        JdhDispatchDetailDto.JdhDispatchDetailDtoBuilder jdhDispatchDetailDto = JdhDispatchDetailDto.builder();
        jdhDispatchDetailDto.dispatchDetailId(detail.getDispatchDetailId());
        jdhDispatchDetailDto.dispatchId(dispatch.getDispatchId());
        jdhDispatchDetailDto.dispatchType(dispatch.getDispatchType());
        jdhDispatchDetailDto.verticalCode(detail.getVerticalCode() );
        jdhDispatchDetailDto.serviceType(detail.getServiceType() );
        String serviceTypeDesc = "";
        if (Objects.equals(detail.getServiceType(), ServiceTypeEnum.TEST.getServiceType())) {
            serviceTypeDesc = "上门检测";
        } else if (Objects.equals(detail.getServiceType(), ServiceTypeEnum.CARE.getServiceType())) {
            serviceTypeDesc = "上门护理";
        }
        jdhDispatchDetailDto.serviceTypeDesc(serviceTypeDesc);
        jdhDispatchDetailDto.promiseId(detail.getPromiseId());
        jdhDispatchDetailDto.voucherId(detail.getVoucherId());
        //派单、抢单类型
        jdhDispatchDetailDto.dispatchDetailType(detail.getDispatchDetailType());
        jdhDispatchDetailDto.sourceVoucherId(dispatch.getServiceInfo().getSourceVoucherId());
        jdhDispatchDetailDto.dispatchDetailStatus(detail.getDispatchDetailStatus());
        jdhDispatchDetailDto.dispatchDetailStatusDesc(JdhDispatchDetailStatusEnum.getDescByType(detail.getDispatchDetailStatus()));
        jdhDispatchDetailDto.businessModeCode(dispatch.getServiceInfo().getBusinessModeCode());

        //预估收入
        if (Objects.nonNull(detail.getDetailExtend()) && Objects.nonNull(detail.getDetailExtend().getPlanCharge())) {
            DispatchAngelPlanCharge planCharge = detail.getDetailExtend().getPlanCharge();
            String plainString = planCharge.getAllSettlementPrice().setScale(2, RoundingMode.DOWN).toPlainString();
            jdhDispatchDetailDto.angelCharge(plainString);
            jdhDispatchDetailDto.angelChargeDesc(String.format("￥%s",plainString));
        }
        jdhDispatchDetailDto.angelId(detail.getAngelId());
        jdhDispatchDetailDto.angelName(detail.getAngelName());
        jdhDispatchDetailDto.outAngelId(detail.getOutAngelId());

        //组装预约人列表
        List<AppointmentPatient> patients = dispatch.getServiceInfo().getPatients();
        //服务项目聚合描述、耗材聚合描述
        Set<String> itemNameList = new HashSet<>();
        List<String> materialPackageNameList = new ArrayList<>();

        List<DispatchAppointmentPatientDto> patientList = new ArrayList<>();
        jdhDispatchDetailDto.patients(patientList);
        for (AppointmentPatient patient : patients) {
            DispatchAppointmentPatientDto build = DispatchAppointmentPatientDto.builder()
                    .promisePatientId(patient.getPromisePatientId())
                    .patientId(patient.getPatientId())
                    //姓名掩码展示
                    .name(patient.nameMask())
                    .patientGender(patient.getPatientGender())
                    .patientAge(patient.getPatientAge())
                    .serviceItems(dispatchServiceItem2DispatchServiceItemDto(patient.getServiceItems()))
                    .build();
            if (CollectionUtils.isNotEmpty(build.getServiceItems())) {
                //组装采样教程和耗材信息
                for (DispatchServiceItemDto serviceItem : build.getServiceItems()) {
                    //项目名称列表add
                    itemNameList.add(serviceItem.getItemName());
                    //项目基础数据获取，耗材信息及教程url获取
                    ServiceItemDto serviceItemDto = itemDtoMap.get(serviceItem.getItemId());
                    if (Objects.nonNull(serviceItemDto) && CollectionUtils.isNotEmpty(serviceItemDto.getMaterialList())) {
                        List<String> materialNames = serviceItemDto.getMaterialList().stream().map(ItemMaterialPackageRelCmd::getMaterialPackageName).distinct().collect(Collectors.toList());
                        serviceItem.setMaterialNames(Joiner.on("、").join(materialNames));
                        materialPackageNameList.addAll(materialNames);
                    }
                    JdhStationServiceItemRel serviceItemRel = serviceItem2StationItemMap.get(serviceItem.getItemId());
                    if (Objects.nonNull(serviceItemRel)) {
                        serviceItem.setSamplingWay(serviceItemRel.getSpecimenWay());
                        serviceItem.setSampleType(serviceItemRel.getSpecimenType());
                        serviceItem.setTestWay(serviceItemRel.getTestWay());
                        serviceItem.setSimplePreserveDuration(serviceItemRel.getSpecimenPreserveDuration());
                        serviceItem.setSimpleNum(serviceItemRel.getSpecimenNum());
                        serviceItem.setServiceDuration(serviceItemRel.getServiceDuration());
                        serviceItem.setSimplePreserveCondition(serviceItemRel.getSpecimenPreserveCondition());
                        serviceItem.setTestDuration(serviceItemRel.getTestDuration());
                        serviceItem.setServiceCondition(serviceItemRel.getServiceCondition());
                        serviceItem.setRemark(serviceItemRel.getRemark());
                        serviceItem.setStationId(serviceItemRel.getStationId());
                    }
                    serviceItem.setTutorialPicUrl(Objects.nonNull(serviceItemDto) ? serviceItemDto.getTongUrl() : "");
                }
            }
            patientList.add(build);
        }


        Map<String, Long> materialNameMap = materialPackageNameList.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        List<String> materialNameList = materialNameMap.entrySet().stream()
                .map(entry -> entry.getKey() + "×" + entry.getValue())
                .collect(Collectors.toList());

        jdhDispatchDetailDto.serviceItemSize(itemNameList.size());
        jdhDispatchDetailDto.serviceItemAggregatedDesc(Joiner.on("、").join(itemNameList));
        jdhDispatchDetailDto.materialPackageAggregatedDesc(Joiner.on("、").join(materialNameList));

        //组装预约时间
        jdhDispatchDetailDto.appointmentTime(DispatchAppointmentTimeDto.builder()
                .dateType(promiseDto.getAppointmentTime().getDateType())
                .appointmentStartTime(TimeUtils.dateTimeToStr(promiseDto.getAppointmentTime().getAppointmentStartTime(), TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS))
                .appointmentEndTime(TimeUtils.dateTimeToStr(promiseDto.getAppointmentTime().getAppointmentEndTime(), TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS))
                .isImmediately(promiseDto.getAppointmentTime().getIsImmediately())
                .appointmentTimeDesc(TimeUtils.dateTimeToStr(promiseDto.getAppointmentTime().getAppointmentStartTime(), TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS))
                .serviceDuration(dispatch.getServiceInfo().getServiceDuration())
                .build());

        //组装服务地址
        jdhDispatchDetailDto.serviceLocation(DispatchServiceLocationDto.builder()
                //.distance()
                //.duration()
                .serviceLocationCityId(dispatch.getServiceLocation().getServiceLocationCityId())
                //服务详细地址
                .serviceLocationDetail(dispatch.getServiceLocation().getServiceLocationDetail())
                .serviceLocationDistrictId(dispatch.getServiceLocation().getServiceLocationDistrictId())
                .serviceLocationId(dispatch.getServiceLocation().getServiceLocationId())
                .serviceLocationProvinceId(dispatch.getServiceLocation().getServiceLocationProvinceId())
                .latitude(dispatch.getServiceInfo().getGisPoint().getLatitude())
                .longitude(dispatch.getServiceInfo().getGisPoint().getLongitude())
                .build());

        //接单如果有过期时间，计算剩余接单时长返回（考虑系统处理、接口耗时，减2s返回）
        if (Objects.nonNull(detail.getExpireDate()) && detail.getExpireDate().after(new Date())) {
            long remainingDuration = (detail.getExpireDate().getTime() - System.currentTimeMillis()) / 1000;
            //剩余时间小于两秒，默认返回1秒；剩余时间大于两秒，减两秒返回
            remainingDuration = Math.max(1, remainingDuration - 2);
            jdhDispatchDetailDto.remainingDuration((int) remainingDuration);
        }

        //备注提示
        jdhDispatchDetailDto.remark(dispatch.getServiceInfo().getRemark());

        return jdhDispatchDetailDto.build();
    }

    /**
     *
     * @param detailList
     * @param angelDtoList
     * @return
     */
    default List<JdhDispatchDetailDto> dispatchDetail2Dto(List<JdhDispatchDetail> detailList, List<JdhAngelDto> angelDtoList){
        if (CollectionUtils.isEmpty(detailList)) {
            return new ArrayList<>();
        }
        Map<Long, JdhAngelDto> angelDtoMap = angelDtoList.stream().collect(Collectors.toMap(JdhAngelDto::getAngelId, jdhAngelDto -> jdhAngelDto, (t, t2) -> t2));
        List<JdhDispatchDetailDto> result = new ArrayList<>();
        for (JdhDispatchDetail detail : detailList) {
            JdhDispatchDetailDto jdhDispatchDetailDto = dispatchDetail2Dto(detail);
            JdhAngelDto jdhAngelDto = angelDtoMap.get(detail.getAngelId());
            if (Objects.nonNull(jdhAngelDto)) {
                jdhDispatchDetailDto.setAngelId(jdhAngelDto.getAngelId());
                jdhDispatchDetailDto.setAngelName(jdhAngelDto.getAngelName());
                jdhDispatchDetailDto.setAngelPin(jdhAngelDto.getAngelPin());
                jdhDispatchDetailDto.setPhone(jdhAngelDto.getPhone());
                jdhDispatchDetailDto.setOutAngelId(Objects.nonNull(jdhAngelDto.getNethpDocId()) ? jdhAngelDto.getNethpDocId().toString() : "");
            }
            result.add(jdhDispatchDetailDto);
        }
        return result;
    }

    /**
     *
     * @param jdhDispatch
     * @return
     */
    default JdhAngelWorkSaveCmd dispatch2AngelWorkSaveCmd(JdhDispatch jdhDispatch) {
        JdhAngelWorkSaveCmd.JdhAngelWorkSaveCmdBuilder saveCmdBuilder = JdhAngelWorkSaveCmd.builder()
                .verticalCode(jdhDispatch.getVerticalCode())
                .serviceType(jdhDispatch.getServiceType())
                .orderId(StringUtils.isNotBlank(jdhDispatch.getServiceInfo().getSourceVoucherId()) ? Long.valueOf(jdhDispatch.getServiceInfo().getSourceVoucherId()) : null)
                //.workType()
                //.angelStationId()
                .sourceId(jdhDispatch.getDispatchId())
                .promiseId(jdhDispatch.getPromiseId());

        if (CollectionUtils.isNotEmpty(jdhDispatch.getAngelDetailList())) {
            Optional<JdhDispatchDetail> optional = jdhDispatch.getAngelDetailList().stream().filter(detail -> Objects.equals(detail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_RECEIVED.getStatus())).findFirst();
            if (optional.isPresent()){
                JdhDispatchDetail dispatchDetail = optional.get();
                saveCmdBuilder.angelId(dispatchDetail.getAngelId()).angelName(dispatchDetail.getAngelName());
                saveCmdBuilder.chargeInfo(dispatchAngelPlanCharge2AngelChargeInfo(dispatchDetail.getDetailExtend().getPlanCharge()));
            }
        }
        if (CollectionUtils.isNotEmpty(jdhDispatch.getServiceInfo().getPatients())) {
            List<Appointment> appointmentList = new ArrayList<>();
            for (AppointmentPatient patient : jdhDispatch.getServiceInfo().getPatients()) {
                appointmentList.add(Appointment.builder()
                        .promisePatientId(patient.getPromisePatientId())
                        .patientName(patient.getName())
                        .patientGender(patient.getPatientGender())
                        .patientAge(patient.getPatientAge())
                        .patientFullAddress(jdhDispatch.getServiceLocation().getServiceLocationDetail())
                        .serviceStartTime(Date.from(jdhDispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant()))
                        .serviceEndTime(Date.from(jdhDispatch.getServiceInfo().getAppointmentTime().getAppointmentEndTime().atZone(ZoneId.systemDefault()).toInstant()))
                        .build());
            }

            saveCmdBuilder.appointmentList(appointmentList);
        }
        saveCmdBuilder.planOutTime(jdhDispatch.getServiceInfo().getPlanOutTime());
        saveCmdBuilder.planFinishTime(jdhDispatch.getServiceInfo().getPlanFinishTime());
        //如果派单未记录，兜底设置计划上门时间、计划完成时间
        if (Objects.isNull(jdhDispatch.getServiceInfo().getPlanOutTime()) || Objects.isNull(jdhDispatch.getServiceInfo().getPlanFinishTime())) {
            //预约上门开始时间、预约上门结束时间
            LocalDateTime appointmentStartTime = jdhDispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime();
            //常规派单轮时间可用性配置
            Map<String, Integer> dispatchTimeBufferDuration = SpringUtil.getBean(DuccConfig.class).getDispatchTimeBufferDuration();
            Integer planStartBufferMinute = dispatchTimeBufferDuration.getOrDefault("planStartBufferMinute", 60);//a
            Integer planServiceTimeMinute = dispatchTimeBufferDuration.getOrDefault("planServiceTimeMinute", 60);//b
            //预计上门时间
            Date planDoorTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(-planStartBufferMinute));
            //预计服务完成时间
            Date planServiceDoneTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(planServiceTimeMinute));
            saveCmdBuilder.planOutTime(planDoorTime);
            saveCmdBuilder.planFinishTime(planServiceDoneTime);
            log.warn("DispatchApplicationConverter -> dispatch2AngelWorkSaveCmd, 派单未记录，兜底设置计划上门时间、计划完成时间, planDoorTime={}, planServiceDoneTime={}", planDoorTime ,planServiceDoneTime);
        }
        return saveCmdBuilder.build();
    }

    /**
     * cmd2AngelWorkExecuteCmd
     * @param cmd
     * @return
     */
    default AngelWorkExecuteCmd cmd2AngelWorkExecuteCmd(FreezeDispatchCmd cmd){
        List<AngelTaskCmd> list = new ArrayList<>();
        for (DispatchAppointmentPatient patient : cmd.getCancelPatients()) {
            list.add(AngelTaskCmd.builder()
                    .serviceId(patient.getServiceItems().stream().map(dispatchServiceItem -> String.valueOf(dispatchServiceItem.getServiceId())).collect(Collectors.toList()))
                    .patientId(patient.getPromisePatientId())
                    .build());
        }

        return AngelWorkExecuteCmd.builder()
                .eventCode(AngelWorkStatusEventEnum.ANGEL_WORK_REFUND_FREEZE_SERVE.getEventCode())
                //.reason()
                .promiseId(cmd.getPromiseId())
                //.sourceId()
                .angelTaskCmdList(list)
                .build();
    }

    /**
     * cmd2AngelWorkExecuteCmd
     * @param cmd
     * @return
     */
    default AngelWorkExecuteCmd cmd2AngelWorkExecuteCmd(CancelDispatchCmd cmd){
        List<AngelTaskCmd> list = new ArrayList<>();
        for (DispatchAppointmentPatient patient : cmd.getCancelPatients()) {
            list.add(AngelTaskCmd.builder()
                    .serviceId(patient.getServiceItems().stream().map(dispatchServiceItem -> String.valueOf(dispatchServiceItem.getServiceId())).collect(Collectors.toList()))
                    .patientId(patient.getPromisePatientId())
                    .build());
        }

        return AngelWorkExecuteCmd.builder()
                .eventCode(AngelWorkStatusEventEnum.ANGEL_WORK_REFUND_SERVE.getEventCode())
                //.reason()
                .promiseId(cmd.getPromiseId())
                //.sourceId()
                .angelTaskCmdList(list)
                .build();
    }

    /**
     * dispatch2AngelWorkExecuteCmd
     * @param jdhDispatch
     * @return
     */
    default AngelWorkExecuteCmd dispatch2AngelWorkExecuteCmd(JdhDispatch jdhDispatch, AngelWorkStatusEventEnum eventEnum){
        List<AngelTaskCmd> list = new ArrayList<>();
        for (AppointmentPatient patient : jdhDispatch.getServiceInfo().getPatients()) {
            list.add(AngelTaskCmd.builder()
                    .serviceId(patient.getServiceItems().stream().map(dispatchServiceItem -> String.valueOf(dispatchServiceItem.getServiceId())).collect(Collectors.toList()))
                    .patientId(patient.getPromisePatientId())
                    .build());
        }

        return AngelWorkExecuteCmd.builder()
                .eventCode(eventEnum.getEventCode())
                //.reason()
                .promiseId(jdhDispatch.getPromiseId())
                //.sourceId()
                .angelTaskCmdList(list)
                .build();
    }

    /**
     * dispatch2AngelWorkDto
     * @param detailDto
     * @return
     */
    default AngelWorkDto dispatch2AngelWorkDto(JdhDispatchDetailDto detailDto, JdhDispatch dispatch, DoctorAdviceFloorDto doctorAdviceFloorDto){
        AngelWorkDto dto = new AngelWorkDto();
        if(Objects.nonNull(doctorAdviceFloorDto) && CollectionUtils.isNotEmpty(doctorAdviceFloorDto.getFileUrlDtos())) {
            dto.setDoctorAdviceFloorDto(doctorAdviceFloorDto);
        }

        //dto.setId();
        dto.setWorkId(detailDto.getDispatchDetailId());
        dto.setJdOrderId(StringUtils.isNotBlank(dispatch.getServiceInfo().getSourceVoucherId()) ? Long.valueOf(dispatch.getServiceInfo().getSourceVoucherId()) : null);
        dto.setPromiseId(detailDto.getPromiseId());
        dto.setAngelId(String.valueOf(detailDto.getAngelId()));
        dto.setAngelPin(detailDto.getAngelPin());
        dto.setAngelName(detailDto.getAngelName());
        dto.setAngelPhone(detailDto.getPhone());
        dto.setAngelCharge(StringUtils.isNotBlank(detailDto.getAngelCharge()) ? new BigDecimal(detailDto.getAngelCharge()) : BigDecimal.ZERO);
        dto.setAngelChargeDesc(detailDto.getAngelChargeDesc());

        //派单域（已接单） ->  服务者履约域（待服务）
        if (Objects.equals(JdhDispatchStatusEnum.DISPATCH_INVALID.getStatus(), dispatch.getDispatchStatus())) {
            //服务者履约域（已过期）
            dto.setStatus(AngelWorkStatusEnum.EXPIRED.getType());
            dto.setStatusDesc(AngelWorkStatusEnum.EXPIRED.getShowDesc());
        } else if (Objects.equals(JdhDispatchDetailStatusEnum.DISPATCH_RECEIVED.getStatus(), detailDto.getDispatchDetailStatus())) {
            dto.setStatus(AngelWorkStatusEnum.RECEIVED.getType());
            dto.setStatusDesc(AngelWorkStatusEnum.RECEIVED.getShowDesc());
        } else if (Objects.equals(JdhDispatchDetailStatusEnum.DISPATCH_COMPLETED.getStatus(), detailDto.getDispatchDetailStatus()) && (Objects.nonNull(detailDto.getRemainingDuration()) && detailDto.getRemainingDuration() > 0)) {
            //派单域（已派单） ->  服务者履约域（待接单）
            dto.setStatus(AngelWorkStatusEnum.WAIT_RECEIVE.getType());
            dto.setStatusDesc(AngelWorkStatusEnum.WAIT_RECEIVE.getShowDesc());
        } else {
            //服务者履约域（已过期）
            dto.setStatus(AngelWorkStatusEnum.EXPIRED.getType());
            dto.setStatusDesc(AngelWorkStatusEnum.EXPIRED.getShowDesc());
        }
        dto.setDisabled(false);
        //dto.setAngelstationId();
        //dto.setClothingPicUrls();
        //dto.setMedicalWastePicUrls();
        //dto.setExtend();
        //dto.setInsureId();
        //dto.setInsureStatus();
        dto.setMaterialNames(detailDto.getMaterialPackageAggregatedDesc());
        //dto.setAngelVirtualStatusList();
        //dto.setAngelWorkInsure();
        if (Objects.equals(detailDto.getServiceType(), ServiceTypeEnum.TEST.getServiceType())) {
            dto.setWorkType(2);
            dto.setWorkTypeDesc("上门检测");
        } else if (Objects.equals(detailDto.getServiceType(), ServiceTypeEnum.CARE.getServiceType())) {
            dto.setWorkType(3);
            dto.setWorkTypeDesc("上门护理");
        }
        //派单返回，抢单不返回
        dto.setRemainingDuration(Objects.equals(detailDto.getDispatchDetailType(), DispatchDetailTypeEnum.ASSIGN.getType()) ? detailDto.getRemainingDuration() : null);
        dto.setAngelWorkOrder(AngelWorkOrderDto.builder()
                .orderId(dto.getJdOrderId())
                .serviceStartTime(Objects.isNull(dispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime()) ? "" : dispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime())
                .userFullAddress(detailDto.getServiceLocation().getServiceLocationDetail())
                .userPhone(detailDto.getOrderUserPhone())
                .remark(detailDto.getRemark())
                .duration(Objects.nonNull(detailDto.getAppointmentTime().getServiceDuration()) ? detailDto.getAppointmentTime().getServiceDuration().doubleValue() : null)
                //距离除以1000，转换为km
                .kmDistance(Objects.nonNull(detailDto.getServiceLocation().getDistance()) ? BigDecimal.valueOf(detailDto.getServiceLocation().getDistance()).divide(new BigDecimal(1000), 1, RoundingMode.HALF_UP).doubleValue() : null)
                .userName(detailDto.getOrderUserName())
                .userLat(detailDto.getServiceLocation().getLatitude().doubleValue())
                .userLng(detailDto.getServiceLocation().getLongitude().doubleValue())
                .createTime(TimeUtils.dateTimeToStr(detailDto.getSubmitOrderTime(), TimeFormat.LONG_PATTERN_LINE))
                .build());
        if (CollectionUtils.isNotEmpty(detailDto.getPatients())) {
            List<AngelTaskDto> angelTaskDtoList = Lists.newArrayListWithCapacity(detailDto.getPatients().size());
            Date serviceEndTime = Date.from(dispatch.getServiceInfo().getAppointmentTime().getAppointmentEndTime().atZone(ZoneId.systemDefault()).toInstant());
            Date serviceStartTime = Date.from(dispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant());
            for (DispatchAppointmentPatientDto dispatchAppointmentPatientDto : detailDto.getPatients()) {
                AngelTaskDto.AngelTaskDtoBuilder builder = AngelTaskDto.builder();
                builder.serviceEndTime(serviceEndTime);
                builder.patientId(Objects.nonNull(dispatchAppointmentPatientDto.getPatientId()) ? dispatchAppointmentPatientDto.getPatientId().toString() : "");
                builder.patientName(dispatchAppointmentPatientDto.getName());
                builder.patientAge(Objects.nonNull(dispatchAppointmentPatientDto.getPatientAge()) ? dispatchAppointmentPatientDto.getPatientAge() + "岁" : "");
                builder.patientGender(GenderEnum.getDescOfType(dispatchAppointmentPatientDto.getPatientGender()));
                builder.workId(detailDto.getDispatchDetailId());
                builder.patientAddressLat(detailDto.getServiceLocation().getLatitude().doubleValue());
                builder.patientAddressLng(detailDto.getServiceLocation().getLongitude().doubleValue());
                builder.serviceStartTime(serviceStartTime);
                builder.patientFullAddress(detailDto.getServiceLocation().getServiceLocationDetail());
                builder.disabled(false);
                builder.taskId(dispatchAppointmentPatientDto.getPromisePatientId());
                //builder.serviceDuration(Objects.nonNull(detailDto.getAppointmentTime().getServiceDuration()) ? detailDto.getAppointmentTime().getServiceDuration() : null);
                builder.status(AngelTaskStatusEnum.INIT.getType());

                List<String> doctorsAdvicePicUrls = dispatchAppointmentPatientDto.getDoctorsAdvicePicUrls();
                if(CollectionUtils.isNotEmpty(doctorsAdvicePicUrls)) {
                    List<PatientPicDetailDto> patientPicDetailDtos = Lists.newArrayList();
                    for (String doctorsAdvicePicUrl : doctorsAdvicePicUrls) {
                        PatientPicDetailDto patientPicDetailDto = new PatientPicDetailDto();
                        patientPicDetailDto.setPatientsPicUrl(doctorsAdvicePicUrl);
                        patientPicDetailDtos.add(patientPicDetailDto);
                    }
                    builder.fileUrlDtos(patientPicDetailDtos);
                }
                if (CollectionUtils.isNotEmpty(dispatchAppointmentPatientDto.getServiceItems())) {
                    String planConsumeTime = Objects.nonNull(detailDto.getAppointmentTime().getServiceDuration()) ? detailDto.getAppointmentTime().getServiceDuration().toString() : "";
                    List<AngelWorkMedPromiseDto> angelWorkServiceItems = dispatchAppointmentPatientDto.getServiceItems().stream().map(dispatchServiceItemDto -> {
                        AngelWorkMedPromiseDto.AngelWorkMedPromiseDtoBuilder promiseDtoBuilder = AngelWorkMedPromiseDto.builder();
                        promiseDtoBuilder.serviceItemId(dispatchServiceItemDto.getItemId());
                        promiseDtoBuilder.serviceItemName(dispatchServiceItemDto.getItemName());
                        //promiseDtoBuilder.specimenCode();
                        //promiseDtoBuilder.medicalPromiseId();
                        if (CollectionUtils.isNotEmpty(dispatchServiceItemDto.getMaterialPackages())) {
                            DispatchMaterialPackageDto packageDto = dispatchServiceItemDto.getMaterialPackages().stream().findFirst().get();
                            promiseDtoBuilder.medicalPromiseId(packageDto.getMaterialPackageId());
                            promiseDtoBuilder.materialPackageName(packageDto.getMaterialPackageName());
                            //promiseDtoBuilder.materialNames(Joiner.on("、").join(materialNames));
                        }
                        promiseDtoBuilder.planConsumeTime(planConsumeTime);
                        promiseDtoBuilder.materialNames(dispatchServiceItemDto.getMaterialNames());
                        promiseDtoBuilder.tutorialPicUrl(dispatchServiceItemDto.getTutorialPicUrl());
                        promiseDtoBuilder.samplingWay(dispatchServiceItemDto.getSamplingWay());
                        promiseDtoBuilder.sampleType(dispatchServiceItemDto.getSampleType());
                        promiseDtoBuilder.testWay(dispatchServiceItemDto.getTestWay());
                        promiseDtoBuilder.simplePreserveDuration(dispatchServiceItemDto.getSimplePreserveDuration());
                        promiseDtoBuilder.simpleNum(dispatchServiceItemDto.getSimpleNum());
                        promiseDtoBuilder.serviceDuration(dispatchServiceItemDto.getServiceDuration());
                        promiseDtoBuilder.simplePreserveCondition(dispatchServiceItemDto.getSimplePreserveCondition());
                        promiseDtoBuilder.testDuration(dispatchServiceItemDto.getTestDuration());
                        promiseDtoBuilder.serviceCondition(dispatchServiceItemDto.getServiceCondition());
                        promiseDtoBuilder.remark(dispatchServiceItemDto.getRemark());
                        promiseDtoBuilder.stationId(dispatchServiceItemDto.getStationId());
                        return promiseDtoBuilder.build();
                    }).collect(Collectors.toList());
                    builder.angelWorkServiceItems(angelWorkServiceItems);
                }
                ;
                angelTaskDtoList.add(builder.build());
            }
            dto.setAngelTasks(angelTaskDtoList);
        }
        /*dto.setAngelWorkMaterialPackages(Lists.newArrayList(AngelWorkMaterialPackageDto.builder().build()));
        dto.setAngelWorkSpecimens();*/

        return dto;
    }

    /**
     * dispatch2DispatchDetailForManDto
     * @param dispatch
     * @return
     */
    @Mapping(target = "appointmentTime.appointmentStartTime", expression = "java(jdhDispatchServiceInfo.getAppointmentTime().formatAppointmentStartTime())")
    @Mapping(target = "appointmentTime.appointmentEndTime", expression = "java(jdhDispatchServiceInfo.getAppointmentTime().formatAppointmentEndTime())")
    @Mapping(target = "appointmentTime.dateType", source = "serviceInfo.appointmentTime.dateType")
    @Mapping(target = "appointmentTime.isImmediately", source = "serviceInfo.appointmentTime.isImmediately")
    @Mapping(target = "patients", source = "serviceInfo.patients")
    @Mapping(target = "remark", source = "serviceInfo.remark")
    @Mapping(target = "sourceVoucherId", source = "serviceInfo.sourceVoucherId")
    DispatchDetailForManDto dispatch2DispatchDetailForManDto(JdhDispatch dispatch);

    /**
     *
     * @param dispatchList
     * @param dispatchDetailList
     * @return
     */
    default DispatchDetailForManDto dispatch2DispatchDetailForManDto(List<JdhDispatch> dispatchList, List<JdhDispatchDetail> dispatchDetailList) {
        if (CollectionUtils.isEmpty(dispatchList)) {
            return DispatchDetailForManDto.builder().build();
        }
        DispatchDetailForManDto build = dispatch2DispatchDetailForManDto(dispatchList.get(0));
        if (CollectionUtils.isEmpty(dispatchDetailList)) {
            return build;
        }
        List<DispatchDetailRoundForManDto> list = new ArrayList<>();
        Map<Long, List<JdhDispatchDetail>> dispatchId2Details = dispatchDetailList.stream().collect(Collectors.groupingBy(JdhDispatchDetail::getDispatchId));
        TreeMap<Long, JdhDispatch> id2Dispatch = dispatchList.stream().collect(Collectors.toMap(JdhDispatch::getDispatchId, jdhDispatch -> jdhDispatch, (t, t2) -> t2, TreeMap::new));
        for (Map.Entry<Long, JdhDispatch> entry: id2Dispatch.entrySet()) {
            List<JdhDispatchDetail> detailList = dispatchId2Details.get(entry.getKey());
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            TreeMap<Integer, List<JdhDispatchDetail>> round2Details = detailList.stream().collect(Collectors.groupingBy(JdhDispatchDetail::getDispatchRound, TreeMap::new, Collectors.toList()));
            for (Map.Entry<Integer, List<JdhDispatchDetail>> listEntry : round2Details.entrySet()) {
                List<JdhDispatchDetail> value = listEntry.getValue();
                StringBuilder sb = new StringBuilder(value.size() * 30);
                value.forEach(jdhDispatchDetail -> sb.append(jdhDispatchDetail.getAngelId()).append("-").append(jdhDispatchDetail.getAngelName()).append(";"));
                list.add(DispatchDetailRoundForManDto.builder().dispatchRound(listEntry.getKey()).angelDesc(sb.toString()).build());
            }
        }
        build.setList(list);
        return build;
    }

    public static void main(String[] args) {
        List<JdhDispatch> dispatchList = JSON.parseArray("[{\"aggregateCode\":\"DISPATCH\",\"branch\":\"yfb\",\"createUser\":\"德古拉的复兴\",\"dispatchId\":160703450710218,\"dispatchRound\":3,\"dispatchStatus\":3,\"dispatchType\":2,\"domainCode\":\"DISPATCH\",\"freeze\":0,\"id\":1384,\"identifier\":{\"dispatchId\":160703450710218},\"outDispatchId\":\"809313648388829\",\"promiseId\":160703376785546,\"serviceInfo\":{\"appointmentTime\":{\"appointmentEndTime\":\"2024-09-17T20:00:00\",\"appointmentStartTime\":\"2024-09-17T19:00:00\",\"dateType\":2,\"isImmediately\":false},\"businessModeCode\":\"angelCare\",\"currentDispatchExpireTime\":1726559715000,\"currentDispatchTime\":1726559115000,\"currentGrabNum\":1,\"gisPoint\":{\"address\":\"北京朝阳区垡头街道欢乐谷景区\",\"analysisType\":\"ELASTIC_SEARCH\",\"latitude\":39.862086,\"longitude\":116.501722,\"reliability\":95},\"patients\":[{\"name\":\"京东\",\"patientAge\":36,\"patientGender\":2,\"patientId\":13175206739677,\"promisePatientId\":160703376785449,\"serviceItems\":[{\"itemId\":160075600101847,\"itemName\":\"品类渗透测试919\",\"itemSkillList\":[{\"angelSkillCode\":\"156351525814426\",\"itemId\":160075600101847}],\"materialPackages\":[{\"materialPackageId\":155729385227546,\"requiredFlag\":1}],\"serviceId\":100110717513}],\"userPhone\":\"***********\"}],\"serviceDuration\":30,\"sourceVoucherId\":\"301101941412\"},\"serviceLocation\":{\"serviceLocationDetail\":\"北京朝阳区垡头街道欢乐谷景区\"},\"serviceType\":\"care\",\"updateUser\":\"liuchunmei25\",\"userPin\":\"德古拉的复兴\",\"version\":4,\"verticalCode\":\"xfylVtpHomeCare\",\"voucherId\":160703376261129}]",JdhDispatch.class);
        List<JdhDispatchDetail> dispatchDetailList = JSON.parseArray("[{\"aggregateCode\":\"DISPATCH_DETAIL\",\"angelId\":155134956929049,\"angelName\":\"测试口口凡\",\"branch\":\"yfb\",\"detailExtend\":{\"planCharge\":{\"allSettlementPrice\":0.26,\"angelId\":\"155134956929049\",\"dispatchMarkupPrice\":0,\"orderSettleAmount\":0.26}},\"dispatchDetailId\":160703455428617,\"dispatchDetailStatus\":5,\"dispatchDetailType\":2,\"dispatchId\":160703450710218,\"dispatchRound\":2,\"domainCode\":\"DISPATCH\",\"expireDate\":1726559715000,\"freeze\":0,\"id\":4211,\"identifier\":{\"dispatchDetailId\":160703455428617},\"outAngelId\":\"408813998412\",\"promiseId\":160703376785546,\"serviceType\":\"care\",\"updateUser\":\"liuchunmei25\",\"version\":2,\"verticalCode\":\"xfylVtpHomeCare\",\"voucherId\":160703376261129},{\"aggregateCode\":\"DISPATCH_DETAIL\",\"angelId\":160163207577785,\"angelName\":\"测试王雪源\",\"branch\":\"yfb\",\"detailExtend\":{\"planCharge\":{\"allSettlementPrice\":0.26,\"angelId\":\"160163207577785\",\"dispatchMarkupPrice\":0,\"orderSettleAmount\":0.26}},\"dispatchDetailId\":160703455428618,\"dispatchDetailStatus\":5,\"dispatchDetailType\":2,\"dispatchId\":160703450710218,\"dispatchRound\":2,\"domainCode\":\"DISPATCH\",\"expireDate\":1726559715000,\"freeze\":0,\"id\":4212,\"identifier\":{\"dispatchDetailId\":160703455428618},\"outAngelId\":\"417029606404\",\"promiseId\":160703376785546,\"serviceType\":\"care\",\"updateUser\":\"liuchunmei25\",\"version\":2,\"verticalCode\":\"xfylVtpHomeCare\",\"voucherId\":160703376261129},{\"aggregateCode\":\"DISPATCH_DETAIL\",\"angelId\":160198295552442,\"angelName\":\"测试杨凯华\",\"branch\":\"yfb\",\"detailExtend\":{\"planCharge\":{\"allSettlementPrice\":0.26,\"angelId\":\"160198295552442\",\"dispatchMarkupPrice\":0,\"orderSettleAmount\":0.26}},\"dispatchDetailId\":160703455428615,\"dispatchDetailStatus\":5,\"dispatchDetailType\":2,\"dispatchId\":160703450710218,\"dispatchRound\":2,\"domainCode\":\"DISPATCH\",\"expireDate\":1726559715000,\"freeze\":0,\"id\":4213,\"identifier\":{\"dispatchDetailId\":160703455428615},\"outAngelId\":\"417129296006\",\"promiseId\":160703376785546,\"serviceType\":\"care\",\"updateUser\":\"liuchunmei25\",\"version\":2,\"verticalCode\":\"xfylVtpHomeCare\",\"voucherId\":160703376261129},{\"aggregateCode\":\"DISPATCH_DETAIL\",\"angelId\":156715582488583,\"angelName\":\"测试梅小小\",\"branch\":\"yfb\",\"detailExtend\":{\"planCharge\":{\"allSettlementPrice\":0.26,\"angelId\":\"156715582488583\",\"dispatchMarkupPrice\":0,\"orderSettleAmount\":0.26}},\"dispatchDetailId\":160703458574375,\"dispatchDetailStatus\":3,\"dispatchDetailType\":1,\"dispatchId\":160703450710218,\"dispatchRound\":3,\"domainCode\":\"DISPATCH\",\"freeze\":0,\"id\":4214,\"identifier\":{\"dispatchDetailId\":160703458574375},\"outAngelId\":\"409414017203\",\"promiseId\":160703376785546,\"serviceType\":\"care\",\"version\":1,\"verticalCode\":\"xfylVtpHomeCare\",\"voucherId\":160703376261129}]",JdhDispatchDetail.class);
        DispatchDetailForManDto dispatchDetailForManDto = DispatchApplicationConverter.INS.dispatch2DispatchDetailForManDto(dispatchList, dispatchDetailList);
    }
}