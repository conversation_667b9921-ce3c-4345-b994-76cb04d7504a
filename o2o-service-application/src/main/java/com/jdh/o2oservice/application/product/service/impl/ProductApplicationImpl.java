package com.jdh.o2oservice.application.product.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.convert.StationApplicationConverter;
import com.jdh.o2oservice.application.angel.service.AngelEcologyApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.product.ProductExtApplication;
import com.jdh.o2oservice.application.product.convert.ProductApplicationConverter;
import com.jdh.o2oservice.application.product.convert.ProductServiceIndicatorConvertor;
import com.jdh.o2oservice.application.product.listener.ImportProductSkuListener;
import com.jdh.o2oservice.application.product.listener.ImportProductSkuListenerContext;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.product.service.ProductPoolApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.support.service.FeeConfigurationApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.config.AbTestConfiguration;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.DiscountFeeAction;
import com.jdh.o2oservice.base.ducc.model.MergeMedicalPromiseCheckConfig;
import com.jdh.o2oservice.base.ducc.model.UsePromiseGoSwitch;
import com.jdh.o2oservice.base.ducc.model.settlement.ShareGetCouponConfig;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.AbTestExp;
import com.jdh.o2oservice.base.model.BusinessModeParam;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationSkuRel;
import com.jdh.o2oservice.core.domain.product.bo.*;
import com.jdh.o2oservice.core.domain.product.bo.vo.CommentUgcListResult;
import com.jdh.o2oservice.core.domain.product.context.JdhItemAngelSkillRelQueryContext;
import com.jdh.o2oservice.core.domain.product.context.JdhItemListQueryContext;
import com.jdh.o2oservice.core.domain.product.context.JdhItemMaterialPackageRelQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceItemQueryContext;
import com.jdh.o2oservice.core.domain.product.enums.CouponUseStatusEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.enums.ProductEventTypeEnum;
import com.jdh.o2oservice.core.domain.product.event.ProductSkuSaleStatusEventBody;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.ProductDetailPropertiesRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.ProductServiceGoodsQuery;
import com.jdh.o2oservice.core.domain.product.rpc.CouponServiceRpc;
import com.jdh.o2oservice.core.domain.product.rpc.ProductLimitbuyRpc;
import com.jdh.o2oservice.core.domain.product.rpc.bo.CouponInfoBO;
import com.jdh.o2oservice.core.domain.product.rpc.bo.GetCouponResultBO;
import com.jdh.o2oservice.core.domain.product.rpc.param.*;
import com.jdh.o2oservice.core.domain.product.service.ProductDomainService;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.settlement.bo.ExternalDomainFeeConfigSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.SettlementFeeDetailSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.query.SettlementConfigDomainQuery;
import com.jdh.o2oservice.core.domain.settlement.context.SettlementConfigContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementSubjectTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ExternalDomainFeeConfig;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementFeeDetailConfig;
import com.jdh.o2oservice.core.domain.settlement.service.SettlementConfigDomainService;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.DictInfo;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.FeeAggregateTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.CommentPageParam;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.record.model.JdhApplyOpenServiceRecord;
import com.jdh.o2oservice.core.domain.support.record.repository.JdhApplyOpenServiceRecordRepository;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhServiceTypeCategoryRelation;
import com.jdh.o2oservice.core.domain.support.vertical.repository.JdhServiceTypeCategoryRelationRepository;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceFeeExtendEnum;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.*;
import com.jdh.o2oservice.export.product.cmd.*;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.enums.ProductSaleChannelEnum;
import com.jdh.o2oservice.export.product.query.*;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.trade.cmd.CalcTradeServiceFeeCmd;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.AddressUpdateParam;
import com.jdh.o2oservice.export.trade.query.AvaiableAppointmentTimeParam;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuItemRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuItemRelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.operation.union.UnaryUnionOp;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toSet;

/**
 * @ClassName ProductApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 11:40
 **/
@Service
@Slf4j
public class ProductApplicationImpl implements ProductApplication, ProductExtApplication {

    /**
     * productDetailPropertiesRepository
     */
    @Resource
    private ProductDetailPropertiesRepository productDetailPropertiesRepository;
    /**
     * productDomainService
     */
    @Resource
    private ProductDomainService productDomainService;

    /**
     * 结算配置
     */
    @Resource
    private SettlementConfigDomainService settlementConfigDomainService;
    /**
     * 健康地址rpc
     */
    @Resource
    private StationApplication stationApplication;
    /**
     * 商家
     */
    @Resource
    private ProviderRepository providerRepository;
    /**
     * productLimitbuyRpc
     */
    @Resource
    private ProductLimitbuyRpc productLimitbuyRpc;

    /**
     * jdhSkuRepository
     */
    @Resource
    private JdhSkuRepository jdhSkuRepository;

    /**
     * skuInfoRpc
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * 健康地址rpc
     */
    @Resource
    private JdhAddressRpc jdhAddressRpc;
    /**
     * 项目
     */
    @Resource
    JdhServiceItemRepository jdhServiceItemRepository;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * feeConfigurationApplication
     */
    @Resource
    private FeeConfigurationApplication feeConfigurationApplication;
    /**
     * 文件存储地址
     */
    @Resource
    FileManageApplication fileManageApplication;

    /**
     * 商家门店仓库
     */
    @Resource
    ProviderStoreRepository providerStoreRepository;

    /**
     * 词典仓库
     */
    @Resource
    DictRepository dictRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private JdhSkuItemRelPoMapper jdhSkuItemRelPoMapper;
    /**
     * 文件服务仓
     */
    @Resource
    JdhFileRepository jdhFileRepository;

    /**
     * 服务者
     */
    @Resource
    FileManageService fileManageService;

    /**
     * redisUtil
     */
    @Resource
    private RedisUtil redisUtil;

    private static final Integer KNIGHT_SERVICE_TYPE = 1;

    private static final Integer ANGEL_TEST_SERVICE_TYPE =2 ;

    private static final Integer GET_COUPON_SUCCESS_CODE = 999;

    /**
     * 标准项目
     */
    @Resource
    private ProductServiceItemApplication productServiceItemApplication;

    @Resource
    private CouponServiceRpc couponServiceRpc;

    @Resource
    private ProductPoolApplication productPoolApplication;

    @Resource
    private TdeClientUtil tdeClientUtil;

    @Resource
    private Cluster jimClient;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * 门店信息
     */
    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Resource
    private AbTestConfiguration abTestConfiguration;

    /**
     * 商品类目模式
     */
    @Resource
    private JdhServiceTypeCategoryRelationRepository jdhServiceTypeCategoryRelationRepository;

    @Resource
    private JdhApplyOpenServiceRecordRepository jdhApplyOpenServiceRecordRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private AngelEcologyApplication angelEcologyApplication;


    /**
     * 查询套餐列表
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryServiceGoodsList")
    public List<ProviderServiceGoodsDto> queryServiceGoodsList(ProductServiceGoodsListRequest request) {
        log.info("ProductApplicationImpl -> queryServiceGoodsList, request={}", JSONUtil.toJsonStr(request));
        ProductServiceGoodsQuery serviceGoodsQuery = ProductApplicationConverter.instance.serviceGoodsListRequest2Query(request);
        serviceGoodsQuery.initVertical();
        //需要查询sku信息
        if (serviceGoodsQuery.needSkuData()) {
            RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(serviceGoodsQuery.getServiceId());
            serviceGoodsQuery.setCrsSku(crsSku);
        }
        //查询商家信息
        if (serviceGoodsQuery.getCrsSku() != null && serviceGoodsQuery.getCrsSku().getVenderId() != null) {
            Provider provider = providerRepository.findByVender(String.valueOf(serviceGoodsQuery.getCrsSku().getVenderId()));
            serviceGoodsQuery.setDockingType(Objects.nonNull(provider) ? provider.getDockingType() : null);
            serviceGoodsQuery.setChannelType(Objects.nonNull(provider) ? provider.getChannelNo() : null);
        }
        List<ProductServiceGoods> productServiceGoodsList = productDomainService.queryServiceGoodsList(serviceGoodsQuery);
        return ProductApplicationConverter.instance.serviceGoodsEntity2Dto(productServiceGoodsList);
    }

    /**
     * 分页查询健康商品主数据
     *
     * @param request request
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryPageJdhSkuInfo")
    public PageDto<JdhSkuDto> queryPageJdhSkuInfo(JdhSkuPageRequest request) {
        log.info("ProductApplicationImpl.queryPageJdhSkuInfo, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuPageRequest"));
        JdhSku reqSku = ProductApplicationConverter.instance.pageRequestToJdhSku(request);
        // 查询项目id,通过关联关系反查出skuId,放入查询商品入参
        if(StringUtils.isNotBlank(request.getItemName())) {
            ServiceItemQuery serviceItemQuery = new ServiceItemQuery();
            serviceItemQuery.setItemName(request.getItemName().trim());
            serviceItemQuery.setPageNum(1);
            serviceItemQuery.setPageSize(100);
            PageDto<ServiceItemDto> pageDto = productServiceItemApplication.queryServiceItemPage(serviceItemQuery);
            if (pageDto == null || CollUtil.isEmpty(pageDto.getList())) {
                PageDto<JdhSkuDto> retDto = new PageDto<>();
                retDto.setTotalPage(0);
                retDto.setPageNum(request.getPageNum());
                retDto.setPageSize(request.getPageSize());
                retDto.setTotalCount(0);
                return retDto;
            }
            List<Long> itemIdList = pageDto.getList().stream().map(ServiceItemDto::getItemId).collect(Collectors.toList());
            List<JdhSkuItemRel> jdhSkuItemRelList = jdhSkuRepository.queryJdhSkuItemRelList(itemIdList.stream().map(s -> JdhSkuItemRel.builder().skuItemId(String.valueOf(s)).build()).filter(Objects::nonNull)
                    .collect(Collectors.toList()), request.getChannelId(), 1, true);
            if (CollectionUtils.isEmpty(jdhSkuItemRelList)) {
                PageDto<JdhSkuDto> retDto = new PageDto<>();
                retDto.setTotalPage(0);
                retDto.setPageNum(request.getPageNum());
                retDto.setPageSize(request.getPageSize());
                retDto.setTotalCount(0);
                return retDto;
            }
            reqSku.setSkuIdList(jdhSkuItemRelList.stream().map(JdhSkuItemRel::getSkuId).filter(Objects::nonNull).collect(Collectors.toList()));
        }

        Page<JdhSku> page = jdhSkuRepository.queryPageJdkSku(reqSku);
        PageDto<JdhSkuDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            pageDto.setList(Collections.emptyList());
            log.info("ProductApplicationImpl.queryPageJdhSkuInfo, pageDto={}", JSON.toJSONString(pageDto));
            return pageDto;
        }
        pageDto.setList(page.getRecords().stream().map(ProductApplicationConverter.instance::convertJdhSkuToJdhSkuDto).filter(Objects::nonNull).collect(Collectors.toList()));
        Set<Long> skuIds = pageDto.getList().stream().map(JdhSkuDto::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());

        try {
            CompletableFuture<Map<String, RpcSkuBO>> skuFuture = CompletableFuture.supplyAsync(() -> skuInfoRpc.getSkuInfo(skuIds.stream().map(String::valueOf).filter(Objects::nonNull).collect(Collectors.toSet())), executorPoolFactory.get(ThreadPoolConfigEnum.SKU_MAN_HAND_POOL));

            // 查询项目信息
            CompletableFuture<Map<Long, List<ServiceItem>>> serviceItemFuture = CompletableFuture.supplyAsync(() -> getMultiSkuItemList(skuIds, request.getSkuItemType() == null ? 1 : request.getSkuItemType()), executorPoolFactory.get(ThreadPoolConfigEnum.SKU_MAN_HAND_POOL));
            // 查询推荐商品信息
            CompletableFuture<Map<Long, List<JdhSkuRel>>> skuRelFuture = CompletableFuture.supplyAsync(() -> getMultiSkuRelList(skuIds, request.getSkuRelType() == null ? 2 : request.getSkuRelType()), executorPoolFactory.get(ThreadPoolConfigEnum.SKU_MAN_HAND_POOL));
            CompletableFuture.allOf(skuFuture, serviceItemFuture, skuRelFuture).get();
            for (JdhSkuDto jdhSkuDto : pageDto.getList()) {
                // 查询商品主数据
                Map<String, RpcSkuBO> skuMaps = skuFuture.get();
                Map<Long, List<ServiceItem>> skuItemMaps = serviceItemFuture.get();
                Map<Long, List<JdhSkuRel>> skuRelMaps = skuRelFuture.get();
                jdhSkuDto.setSkuName(CollUtil.isNotEmpty(skuMaps) && skuMaps.containsKey(jdhSkuDto.getSkuId().toString()) ? skuMaps.get(jdhSkuDto.getSkuId().toString()).getSkuName() : "");
                jdhSkuDto.setServiceItemList(ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(skuItemMaps.get(jdhSkuDto.getSkuId())));
                jdhSkuDto.setSkuRelSkuIds(CollUtil.isEmpty(skuRelMaps.get(jdhSkuDto.getSkuId())) ? "" : skuRelMaps.get(jdhSkuDto.getSkuId()).stream().map(s -> s.getSkuId().toString()).collect(Collectors.joining("、")));
                jdhSkuDto.setServiceProcessImg(buildSkuServiceProcessImg(jdhSkuDto.getServiceProcessImg()));
                //将底层的fileId，转为可直接访问的url
                convertFileId2Url(jdhSkuDto);
            }
        } catch (Exception e) {
            log.error("queryPageJdhSkuInfo e", e);
        }
        log.info("ProductApplicationImpl.queryPageJdhSkuInfo, pageDto={}", JSON.toJSONString(pageDto));
        return pageDto;
    }

    /**
     * 查询健康商品主数据
     *
     * @param request request
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryJdhSkuInfo")
    public JdhSkuDto queryJdhSkuInfo(JdhSkuRequest request) {
        return queryAggregationJdhSkuInfo(request);
    }

    /**
     * 查询jdh sku信息列表
     *
     * @param request 请求
     * @return {@link Map}<{@link Long}, {@link JdhSkuDto}>
     */
    @Override
    public Map<Long, JdhSkuDto> queryJdhSkuInfoByList(JdhSkuListRequest request) {
        return this.queryJdhSkuInfoList(request);
    }

    /**
     * 查询健康商品主数据
     *
     * @param request request
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryJdhSkuInfoList")
    public Map<Long, JdhSkuDto> queryJdhSkuInfoList(JdhSkuListRequest request) {
        log.info("ProductApplicationImpl.queryJdhSkuInfoList, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuListRequest"));
        //AssertUtils.isNotEmpty(request.getSkuIdList(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuIdList"));
        Set<Long> skuIdList = request.getSkuIdList();
        if(Objects.isNull(skuIdList)){
            skuIdList = new HashSet<>();
        }
        // 查询商品主数据
        Map<Long, JdhSkuDto> jdSkuCoreDataMap = new HashMap<>(1);
        if (Boolean.TRUE.equals(request.getQuerySkuCoreData())) {
            Map<String, RpcSkuBO> skuMaps = skuInfoRpc.getSkuInfo(skuIdList.stream().map(String::valueOf).filter(Objects::nonNull).collect(Collectors.toSet()));
            for (Long skuId : skuIdList) {
                jdSkuCoreDataMap.put(skuId, ProductApplicationConverter.instance.convertToJdhSkuDto(skuMaps.get(skuId.toString())));
            }
        }
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(request);
        log.info("ProductApplicationImpl.queryJdhSkuInfoList, jdhSkus={}", JSON.toJSONString(jdhSkus));
        Map<Long, JdhSku> map = jdhSkus.stream().filter(Objects::nonNull).collect(Collectors.toMap(JdhSku::getSkuId, a -> a, (k1, k2) -> k1));
        Map<Long, JdhSkuDto> jdhSkuDtoHashMap = new HashMap<>(1);
        if(CollectionUtils.isEmpty(skuIdList)){
            skuIdList.addAll(jdhSkus.stream().map(JdhSku::getSkuId).collect(Collectors.toSet()));
        }
        for (Long skuId : skuIdList) {
            JdhSkuDto jdhSkuCustomDto = ProductApplicationConverter.instance.convertJdhSkuToJdhSkuDto(map.get(skuId));
            JdhSkuDto jdSkuCoreData = jdSkuCoreDataMap.get(skuId);
            if (jdhSkuCustomDto != null) {
                jdhSkuCustomDto.setServiceProcessImg(buildSkuServiceProcessImg(jdhSkuCustomDto.getServiceProcessImg()));
                JdhSkuDto ret = buildJdhSkuDtoWithCoreSku(jdhSkuCustomDto, jdSkuCoreData);
                //将底层的fileId，转为可直接访问的url
                convertFileId2Url(ret);
                jdhSkuDtoHashMap.put(skuId, ret);
            } else {
                jdhSkuDtoHashMap.put(skuId, jdSkuCoreData);
            }
        }
        log.info("ProductApplicationImpl.queryJdhSkuInfoList, jdhSkuDtoHashMap={}", JSON.toJSONString(jdhSkuDtoHashMap));
        if (Boolean.TRUE.equals(request.getQueryServiceItem())) {
            List<JdhSkuItemRel> jdhSkuItemRels = jdhSkuRepository.queryJdhSkuItemRelList(skuIdList.stream().map(s -> JdhSkuItemRel.builder().skuId(s).build()).filter(Objects::nonNull).collect(Collectors.toList()), null, 1, false);
            log.info("ProductApplicationImpl.queryJdhSkuInfoList, jdhSkuItemRels={}", JSON.toJSONString(jdhSkuItemRels));
            if (CollUtil.isEmpty(jdhSkuItemRels)) {
                log.info("ProductApplicationImpl.queryJdhSkuInfoList, jdhSkuItemRels is empty, ret={}",
                        JSON.toJSONString(jdhSkuDtoHashMap));
                return jdhSkuDtoHashMap;
            }
            Map<Long, List<JdhSkuItemRel>> groupSkuRel =
                    jdhSkuItemRels.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(JdhSkuItemRel::getSkuId));
            for (Long skuId : groupSkuRel.keySet()) {
                Set<Long> itemIdList = groupSkuRel.get(skuId).stream().filter(Objects::nonNull).map(s -> Long.parseLong(s.getSkuItemId())).collect(Collectors.toSet());
                JdhItemListQueryContext itemListQuery =
                        JdhItemListQueryContext.builder().itemIdList(itemIdList).build();
                List<ServiceItem> serviceItems = jdhServiceItemRepository.queryServiceItemList(itemListQuery);
                if (CollUtil.isEmpty(serviceItems)) {
                    continue;
                }

                //如果查询耗材信息
                if (Boolean.TRUE.equals(request.getQueryItemMaterial())) {
                    //查询耗材信息
                    List<ServiceItemMaterialPackageRel> serviceItemMaterialPackageRels = jdhServiceItemRepository.queryServiceItemMaterialPackageRel(JdhItemMaterialPackageRelQueryContext.builder().serviceItemIds(itemIdList).build());
                    if (CollectionUtils.isNotEmpty(serviceItemMaterialPackageRels)) {
                        Map<Long, List<ServiceItemMaterialPackageRel>> itemIdToMaterial = serviceItemMaterialPackageRels.stream().collect(Collectors.groupingBy(ServiceItemMaterialPackageRel::getItemId));
                        for (ServiceItem serviceItem : serviceItems) {
                            serviceItem.setMaterialList(itemIdToMaterial.get(serviceItem.getItemId()));
                        }
                    }
                }

                //如果查询技能信息
                if (Boolean.TRUE.equals(request.getQueryItemSkill())) {
                    //查询技能信息
                    List<ServiceItemAngelSkillRel> itemAngelSkillRels = jdhServiceItemRepository.queryServiceItemAngelSkillRel(JdhItemAngelSkillRelQueryContext.builder().serviceItemIds(itemIdList).build());
                    if (CollectionUtils.isNotEmpty(itemAngelSkillRels)) {
                        Map<Long, List<ServiceItemAngelSkillRel>> listMap = itemAngelSkillRels.stream().collect(Collectors.groupingBy(ServiceItemAngelSkillRel::getItemId));
                        for (ServiceItem serviceItem : serviceItems) {
                            serviceItem.setAngelSkillCodeList(listMap.getOrDefault(serviceItem.getItemId(), Lists.newArrayList()).stream().map(ServiceItemAngelSkillRel::getAngelSkillCode).collect(Collectors.toSet()));
                        }
                    }
                }

                jdhSkuDtoHashMap.get(skuId).setServiceItemList(ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(serviceItems));
            }
        }
        if (Boolean.TRUE.equals(request.getQueryPriceFeeConfig())) {
            //查询配置的结算价格数据
            List<ExternalDomainFeeConfig> externalDomainFeeConfigs = settlementConfigDomainService.queryExternalDomainFeeConfig(SettlementConfigDomainQuery.builder()
                    .domainCode(DomainEnum.PRODUCT)
                    .aggregateCode(ProductAggregateEnum.PRODUCT_SKU)
                    .aggregateIdList(map.keySet().stream().map(String::valueOf).collect(Collectors.toList()))
                    .settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                    .build());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(externalDomainFeeConfigs)) {
                // 提取结算价格
                Map<String, ExternalDomainFeeConfig> collect = externalDomainFeeConfigs.stream().collect(Collectors.toMap(ExternalDomainFeeConfig::getAggregateId, feeConfig -> feeConfig, (t, t2) -> t2));
                for (Map.Entry<String, ExternalDomainFeeConfig> entry : collect.entrySet()) {
                    Long key = Long.valueOf(entry.getKey());
                    ExternalDomainFeeConfig feeConfig = entry.getValue();
                    JdhSkuDto jdhSkuDto = jdhSkuDtoHashMap.get(key);
                    if (Objects.nonNull(jdhSkuDto)) {
                        jdhSkuDto.setAngelBasicSettlementPrice(feeConfig.getDetailConfigPriceByFeeType(String.valueOf(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getType())));
                    }
                }
            }
        }
        log.info("ProductApplicationImpl.queryJdhSkuInfoList, jdhSkuDtoHashMap={}", JSON.toJSONString(jdhSkuDtoHashMap));
        return jdhSkuDtoHashMap;
    }

    /**
     * 查询健康商品主数据关联sku
     *
     * @param request
     * @return list
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryJdhSkuRel")
    public List<JdhSkuRelDto> queryJdhSkuRel(JdhSkuRelRequest request) {
        log.info("ProductApplicationImpl.queryJdhSkuInfoList, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuRelRequest"));
        AssertUtils.nonNull(request.getParentSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("parentSkuId"));
        AssertUtils.nonNull(request.getSkuRelType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuRelType"));
        List<JdhSkuRelDto> jdhSkuRelDtoList = ProductApplicationConverter.instance.modelToDtoList(getSubSkuList(request.getParentSkuId(), request.getSkuRelType(), request.getSkuItemType()));
        if (CollUtil.isNotEmpty(jdhSkuRelDtoList)) {
            // 子商品查询商品全称
            Map<String, RpcSkuBO> skuMaps = skuInfoRpc.getSkuInfo(jdhSkuRelDtoList.stream().map(s -> String.valueOf(s.getSkuId())).filter(Objects::nonNull).collect(Collectors.toSet()));
            for (JdhSkuRelDto jdhSkuRelDto : jdhSkuRelDtoList) {
                if (skuMaps.containsKey(jdhSkuRelDto.getSkuId().toString())) {
                    RpcSkuBO skuBO = skuMaps.get(jdhSkuRelDto.getSkuId().toString());
                    jdhSkuRelDto.setSkuName(skuBO.getSkuName());
                }
            }
        }
        return jdhSkuRelDtoList;
    }

    /**
     * 批量查询健康商品主数据关联sku
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryJdhSkuRelBatch")
    public Map<Long, List<JdhSkuRelDto>> queryJdhSkuRelBatch(JdhSkuRelRequest request) {
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuRelRequest"));
        AssertUtils.nonNull(request.getParentSkuIdSet(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("parentSkuIdSet"));
        AssertUtils.nonNull(request.getSkuRelType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuRelType"));
        Map<Long, List<JdhSkuRelDto>> jdhSkuRelDtoMap = ProductApplicationConverter.instance.modelToDtoMap(getMultiSkuRelList(request.getParentSkuIdSet(), request.getSkuRelType()));
        if (CollUtil.isNotEmpty(jdhSkuRelDtoMap)) {
            List<JdhSkuRelDto> jdhSkuRelList = new ArrayList<>();
            new ArrayList<>(jdhSkuRelDtoMap.values()).forEach(jdhSkuRelList::addAll);
            Set<String> skuIds = jdhSkuRelList.stream().map(s -> String.valueOf(s.getSkuId())).filter(Objects::nonNull).collect(toSet());
            // 获取SKU信息
            Map<String, RpcSkuBO> skuMaps = skuInfoRpc.getSkuInfo(skuIds);
            jdhSkuRelDtoMap.forEach((k, v) ->{
                for (JdhSkuRelDto jdhSkuRelDto : v) {
                    if (skuMaps.containsKey(jdhSkuRelDto.getSkuId().toString())) {
                        RpcSkuBO skuBO = skuMaps.get(jdhSkuRelDto.getSkuId().toString());
                        jdhSkuRelDto.setSkuName(skuBO.getSkuName());
                    }
                }
            });
        }
        return jdhSkuRelDtoMap;
    }

    /**
     * 保存健康商品主数据
     *
     * @param createJdhSkuCmd cmd
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.addJdhSkuInfo")
    @Transactional(rollbackFor = Exception.class)
    public Boolean addJdhSkuInfo(CreateJdhSkuCmd createJdhSkuCmd) {
        log.info("ProductApplicationImpl.addJdhSkuInfo, createJdhSkuCmd={}", JSON.toJSONString(createJdhSkuCmd));
        AssertUtils.nonNull(createJdhSkuCmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("CreateJdhSkuCmd"));
        AssertUtils.nonNull(createJdhSkuCmd.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        if (ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(createJdhSkuCmd.getServiceType())) {
            createJdhSkuCmd.setSkuType(SkuRelTypeEnum.MAIN_ITEM.getType());
            createJdhSkuCmd.setChannelId(ProductSaleChannelEnum.XFYL.getChannelId());
        } else {
            AssertUtils.nonNull(createJdhSkuCmd.getSkuType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuType"));
        }
        //20250728 确认解业务身份暂时没用到，先放开限制
        /*if (ServiceTypeNewEnum.ANGEL_TEST.getType().equals(createJdhSkuCmd.getServiceType())) {
            AssertUtils.nonNull(createJdhSkuCmd.getBusinessProcessType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("businessProcessType"));
        }*/
        // 校验商品有效性
        checkMainSkuInfo(createJdhSkuCmd.getServiceType(), createJdhSkuCmd.getSkuId());
        JdhSku jdhSku = ProductApplicationConverter.instance.cmdToJdhSku(createJdhSkuCmd);
        // 骑手上门-校验合管逻辑
        if (ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(jdhSku.getServiceType())) {
            checkItemMergeConfig(jdhSku.getSkuId(), jdhSku.getSkuType(), jdhSku.getServiceItemIdList(), jdhSku);
        }
        // 快递上门检测
        if (ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(jdhSku.getServiceType()) && jdhSku.getServiceItemIdList().size() > 1) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_ITEM_CHECK);
        }
        // 校验商品项目
        Boolean ret = checkSkuServiceItemAllInOneStore(jdhSku, null);
        if (!Boolean.TRUE.equals(ret)) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SERVICE_ITEM_ALL_IN_STATION);
        }
        //校验商品配置的每日可约时间范围
        checkDayTimeFrame(jdhSku);
        // 目前sku仅关联项目
        jdhSku.setSkuItemType(createJdhSkuCmd.getSkuItemType() == null ? 1 : createJdhSkuCmd.getSkuItemType());
        jdhSku.setCreateUser(createJdhSkuCmd.getErp());
        jdhSku.setUpdateUser(createJdhSkuCmd.getErp());
        int count = jdhSkuRepository.save(jdhSku);
        log.info("ProductApplicationImpl.addJdhSkuInfo, count={}", count);
        //保存结算价格相关信息
        SettlementConfigContext context = new SettlementConfigContext();
        List<ExternalDomainFeeConfigSaveBo> externalDomainFeeConfigList = Lists.newArrayList(ExternalDomainFeeConfigSaveBo.builder()
                .domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_SKU)
                .aggregateId(String.valueOf(jdhSku.getSkuId())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                .detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getType()).feeAmount(createJdhSkuCmd.getAngelBasicSettlementPrice()).build()))
                .build());
        context.setExternalDomainFeeConfigList(externalDomainFeeConfigList);
        settlementConfigDomainService.batchSaveExternalDomainFeeConfig(context);
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhSku, ProductEventTypeEnum.BASE_SAVE, ProductApplicationConverter.instance.convertProducBaseEventBody(jdhSku)));
        return count > 0;
    }

    /**
     * 更新健康商品主数据
     *
     * @param updateJdhSkuCmd cmd
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.updateJdhSkuInfo")
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateJdhSkuInfo(UpdateJdhSkuCmd updateJdhSkuCmd) {
        log.info("ProductApplicationImpl.updateJdhSkuInfo, updateJdhSkuCmd={}", JSON.toJSONString(updateJdhSkuCmd));
        AssertUtils.nonNull(updateJdhSkuCmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("UpdateJdhSkuCmd"));
        AssertUtils.nonNull(updateJdhSkuCmd.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        if (ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(updateJdhSkuCmd.getServiceType())) {
            updateJdhSkuCmd.setSkuType(SkuRelTypeEnum.MAIN_ITEM.getType());
            updateJdhSkuCmd.setChannelId(ProductSaleChannelEnum.XFYL.getChannelId());
        } else {
            AssertUtils.nonNull(updateJdhSkuCmd.getSkuType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuType"));
        }
        //20250728 确认解业务身份暂时没用到，先放开限制
        /*if (ServiceTypeNewEnum.ANGEL_TEST.getType().equals(updateJdhSkuCmd.getServiceType())) {
            AssertUtils.nonNull(updateJdhSkuCmd.getBusinessProcessType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("businessProcessType"));
        }*/
        JdhSku snapshot = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(updateJdhSkuCmd.getSkuId()).build());
        JdhSku jdhSku = ProductApplicationConverter.instance.cmdToJdhSku(updateJdhSkuCmd);
        // 骑手上门-校验合管逻辑
        if (ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(jdhSku.getServiceType())) {
            checkItemMergeConfig(jdhSku.getSkuId(), jdhSku.getSkuType(), jdhSku.getServiceItemIdList(), jdhSku);
        }
        // 快递上门检测
        if (ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(jdhSku.getServiceType()) && jdhSku.getServiceItemIdList().size() > 1) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_ITEM_CHECK);
        }
        jdhSku.setVersion(snapshot.getVersion());
        // 校验商品项目
        Boolean ret = checkSkuServiceItemAllInOneStore(jdhSku, null);
        if (!Boolean.TRUE.equals(ret)) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SERVICE_ITEM_ALL_IN_STATION);
        }
        //校验是否为主品
        Boolean mainSkuCheck = checkMainSkuLogic(updateJdhSkuCmd);
        if (!Boolean.TRUE.equals(mainSkuCheck)) {
            throw new BusinessException(ProductErrorCode.MAIN_PRODUCT_SKU_CAN_NOT_CHANGE);
        }
        //校验是否为附加品
        Boolean addSkuCheck = checkAddSkuLogic(updateJdhSkuCmd);
        if (!Boolean.TRUE.equals(addSkuCheck)) {
            throw new BusinessException(ProductErrorCode.ADD_PRODUCT_SKU_CAN_NOT_CHANGE);
        }
        //校验商品配置的每日可约时间范围
        checkDayTimeFrame(jdhSku);
        // 目前sku仅关联项目
        jdhSku.setSkuItemType(updateJdhSkuCmd.getSkuItemType() == null ? 1 : updateJdhSkuCmd.getSkuItemType());
        jdhSku.setUpdateUser(updateJdhSkuCmd.getErp());
        int count = jdhSkuRepository.update(jdhSku);
        log.info("ProductApplicationImpl.updateJdhSkuInfo, count={}", count);
        //保存结算价格相关信息
        SettlementConfigContext context = new SettlementConfigContext();
        List<ExternalDomainFeeConfigSaveBo> externalDomainFeeConfigList = Lists.newArrayList(ExternalDomainFeeConfigSaveBo.builder()
                .domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_SKU)
                .aggregateId(String.valueOf(jdhSku.getSkuId())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                .detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getType()).feeAmount(updateJdhSkuCmd.getAngelBasicSettlementPrice()).build()))
                .build());
        context.setExternalDomainFeeConfigList(externalDomainFeeConfigList);
        settlementConfigDomainService.batchSaveExternalDomainFeeConfig(context);
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhSku, ProductEventTypeEnum.BASE_SAVE, ProductApplicationConverter.instance.convertProducBaseEventBody(jdhSku)));
        return count > 0;
    }

    /**
     * 校验是否为附加品修改为主品逻辑 如果该商品被绑定过，不允许修改
     * @param updateJdhSkuCmd
     * @return
     */
    private Boolean checkAddSkuLogic(UpdateJdhSkuCmd updateJdhSkuCmd) {
        if (SkuRelTypeEnum.MAIN_ITEM.getType().equals(updateJdhSkuCmd.getSkuType())){
            List<JdhSkuRel> jdhSkuRels = jdhSkuRepository.queryJdhSkuRelInfo(JdhSkuRel.builder().skuId(updateJdhSkuCmd.getSkuId()).skuRelType(SkuRelTypeEnum.ADD_ITEM.getType()).build());
            if (CollectionUtils.isNotEmpty(jdhSkuRels)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验是否为主品修改为加项品逻辑 如果是主品且其下面有绑定的加项品，不允许修改
     * @param
     * @return
     */
    private Boolean checkMainSkuLogic(UpdateJdhSkuCmd updateJdhSkuCmd) {
        if (SkuRelTypeEnum.ADD_ITEM.getType().equals(updateJdhSkuCmd.getSkuType())) {
            List<JdhSkuRel> jdhSkuRels = jdhSkuRepository.queryJdhSkuRelInfo(JdhSkuRel.builder().parentSkuId(updateJdhSkuCmd.getSkuId()).skuRelType(SkuRelTypeEnum.ADD_ITEM.getType()).build());
            if (CollectionUtils.isNotEmpty(jdhSkuRels)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 更新健康商品主数据爆单开关
     *
     * @param updateJdhSkuSaleStatusCmd cmd
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.updateJdhSkuSaleStatus")
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateJdhSkuSaleStatus(UpdateJdhSkuSaleStatusCmd updateJdhSkuSaleStatusCmd) {
        log.info("ProductApplicationImpl.updateJdhSkuSaleStatus, updateJdhSkuCmd={}", JSON.toJSONString(updateJdhSkuSaleStatusCmd));
        AssertUtils.nonNull(updateJdhSkuSaleStatusCmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("UpdateJdhSkuSaleStatusCmd"));
        AssertUtils.nonNull(updateJdhSkuSaleStatusCmd.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        AssertUtils.nonNull(updateJdhSkuSaleStatusCmd.getSaleStatus(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("saleStatus"));
        JdhSku snapshot = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(updateJdhSkuSaleStatusCmd.getSkuId()).build());
        JdhSku jdhSku = ProductApplicationConverter.instance.cmdToJdhSku(updateJdhSkuSaleStatusCmd);
        jdhSku.setVersion(snapshot.getVersion());
        jdhSku.setUpdateUser(updateJdhSkuSaleStatusCmd.getErp());
        int count = jdhSkuRepository.updateSaleStatus(jdhSku);
        log.info("ProductApplicationImpl.updateJdhSkuSaleStatus, count={}", count);
        Boolean ret = count > 0;
        if (Boolean.TRUE.equals(ret)) {
            ProductSkuSaleStatusEventBody productSkuSaleStatusEventBody = new ProductSkuSaleStatusEventBody();
            productSkuSaleStatusEventBody.setSkuId(updateJdhSkuSaleStatusCmd.getSkuId());
            productSkuSaleStatusEventBody.setSkuSaleStatus(updateJdhSkuSaleStatusCmd.getSaleStatus());
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdhSku, ProductEventTypeEnum.UPDATE_SALE_STATUS, productSkuSaleStatusEventBody));
        }
        return ret;
    }

    /**
     * 查询健康商品主数据
     *
     * @param request request
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.checkJdhSkuInfo")
    public JdhSkuDto checkJdhSkuInfo(JdhSkuRequest request) {
        log.info("ProductApplicationImpl.checkJdhSkuInfo, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuRequest"));
        AssertUtils.nonNull(request.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        // 校验商品有效性
        JdhSkuDto jdhSkuDto = checkMainSkuInfo(request.getServiceType(), request.getSkuId());
        log.info("ProductApplicationImpl.checkJdhSkuInfo, ret={}", JSON.toJSONString(jdhSkuDto));
        return jdhSkuDto;
    }

    /**
     * 查询健康商品主数据关联sku
     *
     * @param request
     * @return list
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.checkSubSku")
    public JdhSkuRelDto checkSubSku(JdhSkuRelRequest request) {
        log.info("ProductApplicationImpl.checkSubSku, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuRelRequest"));
        AssertUtils.nonNull(request.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        AssertUtils.nonNull(request.getParentSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("parentSkuId"));
        AssertUtils.nonNull(request.getSkuRelType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuRelType"));
        if (request.getSkuId().equals(request.getParentSkuId())) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_REPEAT);
        }
        RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(request.getSkuId()));
        JdhSkuDto jdhSkuDto = ProductApplicationConverter.instance.convertToJdhSkuDto(crsSku);
        if (jdhSkuDto == null || jdhSkuDto.getSkuId() == null || StringUtils.isBlank(jdhSkuDto.getSkuName())) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK);
        }
        List<JdhSkuRel> list = jdhSkuRepository.queryJdhSkuRelInfo(JdhSkuRel.builder().parentSkuId(request.getParentSkuId())
                .skuId(request.getSkuId()).skuRelType(request.getSkuRelType()).build());
        if (CollUtil.isNotEmpty(list)) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_EXIST_CHECK);
        }
        JdhSkuRelDto jdhSkuRelDto = new JdhSkuRelDto();
        jdhSkuRelDto.setSkuId(request.getSkuId());
        jdhSkuRelDto.setSkuName(jdhSkuDto.getSkuName());
        log.info("ProductApplicationImpl.checkSubSku, ret={}", JSON.toJSONString(jdhSkuRelDto));
        return jdhSkuRelDto;
    }

    /**
     * 保存健康商品关联子商品
     *
     * @param createJdhSkuRelCmd cmd
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.addJdhSkuInfoRel")
    @Transactional(rollbackFor = Exception.class)
    public Boolean addJdhSkuInfoRel(CreateJdhSkuRelCmd createJdhSkuRelCmd) {
        log.info("ProductApplicationImpl.addJdhSkuInfoRel, request={}", JSON.toJSONString(createJdhSkuRelCmd));
        AssertUtils.nonNull(createJdhSkuRelCmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("CreateJdhSkuRelCmd"));
        AssertUtils.nonNull(createJdhSkuRelCmd.getParentSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("parentSkuId"));
        AssertUtils.nonNull(createJdhSkuRelCmd.getSkuRelType(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuRelType"));
        // 如果子商品为空，直接删除关联关系
        if (CollUtil.isEmpty(createJdhSkuRelCmd.getSubSkuList())) {
            jdhSkuRepository.deleteSkuRelInfo(JdhSkuRel.builder().parentSkuId(createJdhSkuRelCmd.getParentSkuId()).skuRelType(createJdhSkuRelCmd.getSkuRelType()).build());
            return true;
        }

        // 校验商品项目有效性
        checkSkuSubRelInfo(createJdhSkuRelCmd);

        createJdhSkuRelCmd.setCreateUser(createJdhSkuRelCmd.getErp());
        createJdhSkuRelCmd.setUpdateUser(createJdhSkuRelCmd.getErp());
        List<JdhSkuRel> jdhSkuRelList = buildJdhSkuRels(createJdhSkuRelCmd);
        JdhSkuRel jdhSkuRelParent = new JdhSkuRel();
        jdhSkuRelParent.setParentSkuId(createJdhSkuRelCmd.getParentSkuId());
        jdhSkuRelParent.setSkuRelType(createJdhSkuRelCmd.getSkuRelType());
        jdhSkuRelParent.setCreateUser(createJdhSkuRelCmd.getErp());
        jdhSkuRelParent.setUpdateUser(createJdhSkuRelCmd.getErp());
        int count = jdhSkuRepository.batchSaveSkuRelInfo(jdhSkuRelParent, jdhSkuRelList);
        return count > 0;
    }

    /**
     * 查看商品信息
     *
     * @return GoodsInfoDTO
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryProductDetailFloorBySku")
    public ProductDetailDTO queryProductDetailFloorBySku(ProductDetailFloorRequest detailFloorBO) {
        log.info("ProductApplicationImpl queryProductDetailFloorBySku detailFloorBO={}", JSON.toJSONString(detailFloorBO));
        return queryProductDetailFloorBySkuNew(detailFloorBO);
    }

    /**
     * 构建活动楼层
     * @param productDetailDTO
     */
    private void buildActivityFloor(ProductDetailDTO productDetailDTO, JdhSku jdhSku) {
        if (Objects.nonNull(jdhSku) && CollectionUtils.isNotEmpty(jdhSku.getActivityFloors())){
            List<ProductActivityFloorDto> activityFloors = Lists.newArrayList();
            for (ProductActivityFloor activityFloor : jdhSku.getActivityFloors()) {
                ProductActivityFloorDto dto = new ProductActivityFloorDto();
                dto.setJumpUrl(activityFloor.getJumpUrl());
                dto.setImageUrlFileId(activityFloor.getImageUrlFileId());
                activityFloors.add(dto);
            }
            buildActivityFloorUrl(activityFloors);
            productDetailDTO.setActivityFloors(activityFloors);
        }
    }

    /**
     * 查看商品信息
     *
     * @return GoodsInfoDTO
     */
    private ProductDetailDTO queryProductDetailFloorBySkuNew(ProductDetailFloorRequest detailFloorBO) {
        log.info("ProductApplicationImpl queryProductDetailFloorBySkuNew detailFloorBO={}", JSON.toJSONString(detailFloorBO));
        String skuNo = detailFloorBO.getSkuNo();
        String pin = detailFloorBO.getUserPin();
        ProductDetailDTO productDetailDTO = new ProductDetailDTO();
        // 商品运营端配置信息
        CompletableFuture<JdhSku> jdhSkuFuture = CompletableFuture.supplyAsync(() -> {
            JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(Long.valueOf(skuNo)).build());
            if (jdhSku != null && StringUtils.isNotBlank(jdhSku.getServiceProcessImg())) {
                jdhSku.setServiceProcessImg(buildSkuServiceProcessImg(jdhSku.getServiceProcessImg()));
            }
            return jdhSku;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // spu展示Ab实验
        AbTestExp spuAbTestExp = abTestConfiguration.callAbTestResult(pin, "spuAb");
        boolean spuFlag = (Objects.nonNull(spuAbTestExp) && !Arrays.asList("no_popup", "base").contains(spuAbTestExp.getLabel()))
                || (Objects.isNull(detailFloorBO.getUserPin()));
        log.info("ProductApplicationImpl queryProductDetailFloorBySkuNew spuAbTestExp={}, spuFlag={}", JSON.toJSONString(spuAbTestExp), spuFlag);
        CompletableFuture<List<JdhSkuRel>> jdhSkuRelsFuture;
        if (spuFlag){
            jdhSkuRelsFuture = CompletableFuture.supplyAsync(() -> {// 查询sku以及与当前sku绑定的其他sku
                return getSkuGroupList(detailFloorBO);
            },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));
        }else {
            jdhSkuRelsFuture = CompletableFuture.supplyAsync(() -> { // 商详获取升级加购商品信息
                if (Objects.isNull(detailFloorBO.getSkuRelType())){
                    detailFloorBO.setSkuRelType(SkuRelTypeEnum.UPGRADE_ITEM.getType());
                }
                return getSubSkuList(Long.valueOf(skuNo), detailFloorBO.getSkuRelType(), detailFloorBO.getSkuItemType());
            },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));
        }

        //--------------设置轮播图及商品主图视频--------------
        CompletableFuture<SkuMaterialBO> skuMaterialBOFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.queryMaterialForAssembly(skuNo);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 用户默认地址
        Long addressId = StringUtils.isNotBlank(detailFloorBO.getAddressId()) ? Long.valueOf(detailFloorBO.getAddressId()) : null;
        log.info("ProductApplicationImpl queryProductDetailFloorBySku addressId={}",addressId);
        CompletableFuture<UserAddressDetailBO> userAddressDetailFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return productDomainService.getUserLastAddress(detailFloorBO.getUserPin(), addressId);
            } catch (Exception e) {
                log.error("ProductApplicationImpl -> queryProductDetailFloorBySku getUserLastAddress error", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        JdhSku jdhSku = jdhSkuFuture.join();
        List<JdhSkuRel> jdhSkuRels = jdhSkuRelsFuture.join();
        SkuMaterialBO skuMaterialBO = skuMaterialBOFuture.join();
        UserAddressDetailBO userAddressDetail = userAddressDetailFuture.join();
        log.info("ProductApplicationImpl queryProductDetailFloorBySku jdhSku={}", JSON.toJSONString(jdhSku));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku jdhSkuRels={}", JSON.toJSONString(jdhSkuRels));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku skuMaterialBO={}", JSON.toJSONString(skuMaterialBO));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku userAddressDetail={}", JSON.toJSONString(userAddressDetail));

        if(Objects.isNull(jdhSku)){
            log.error("ProductApplicationImpl queryProductDetailFloorBySkuNew jdhSku empty");
            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
        }

        //--------------查询商品主图视频--------------
        VideoMarkResultBO videoMarkResultBO = null;
        if (StringUtils.isNotBlank(skuMaterialBO.getMainVideoId())){
            videoMarkResultBO = skuInfoRpc.getVideoPlayInfoByTraffic(skuMaterialBO.getMainVideoId());
        }
        List<ProductCarouselFileBO> productCarouselFileBos = productDomainService.skuMaterialBO2Dto(skuMaterialBO,videoMarkResultBO);
        productDetailDTO.setCarouselMapDetailList(ProductApplicationConverter.instance.productCarouselFileBO2DTOList(productCarouselFileBos));

        //--------------设置商品主要信息--------------
        // 到家需求配置文件
        Map<String, String> properties = productDetailPropertiesRepository.getQuickCheckProductDetailProperties(skuNo, "newNursesHome");
        Set<String> skuIds = Sets.newHashSet(skuNo);
        if (CollectionUtils.isNotEmpty(jdhSkuRels)) {
            for (JdhSkuRel jdhSkuRel : jdhSkuRels) {
                skuIds.add(String.valueOf(jdhSkuRel.getSkuId()));
            }
        }
        log.info("ProductApplicationImpl queryProductDetailFloorBySku skuIds={}", JSON.toJSONString(skuIds));

        // 批量-领取优惠券
        CompletableFuture<CouponGetResultDTO> batchGetCouponFuture = CompletableFuture.supplyAsync(() -> {
            try {
                BatchGetCouponCmd batchGetCouponCmd = new BatchGetCouponCmd();
                batchGetCouponCmd.setUserPin(pin);
                batchGetCouponCmd.setSkuNo(skuNo);
                return batchGetCoupon(batchGetCouponCmd);
            } catch (Exception e) {
                log.error("ProductApplicationImpl batchGetCoupon error e", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 获取SKU信息
        CompletableFuture<Map<String, RpcSkuBO>> skuInfoFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.getSkuInfo(skuIds);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 判断sku是否是pop商品
        CompletableFuture<Boolean> selfFlagFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.judgeIsPopBySku(skuNo);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 查询sku实时价格
        CompletableFuture<PriceInfoResponseBO> priceInfoResponseBOFuture = CompletableFuture.supplyAsync(() -> {
            AddressDetailBO addressDetail = null;
            if (userAddressDetail != null){
                addressDetail = new AddressDetailBO();
                BeanUtils.copyProperties(userAddressDetail, addressDetail);
            }
            return skuInfoRpc.getSkuPriceAndPromotionByPin(skuIds, pin, addressDetail);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 获取SKU到手价
        CompletableFuture<Map<String, ProductPurchasePriceDto>> productPurchasePriceMapFuture = CompletableFuture.supplyAsync(() -> {
            return productPoolApplication.getProductPurchasePriceMap(skuIds, userAddressDetail, detailFloorBO.getUserPin());
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 获取SKU与服务站关系
        CompletableFuture<Map<String, List<JdhStationDto>>> skuStationMapFuture = CompletableFuture.supplyAsync(() -> {
            return getSkuStationGeoMap(userAddressDetail, skuIds);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //--------------评价--------------
        CompletableFuture<EvaluateInfoDTO> evaluateInfoDTOFuture = CompletableFuture.supplyAsync(() -> {
            try {
                EvaluateInfoBO evaluateInfoBo = productDomainService.getEvaluateInfo(skuNo);
                productDetailDTO.setEvaluateInfoDTO(ProductApplicationConverter.instance.modelToDto(evaluateInfoBo));
                return productDetailDTO.getEvaluateInfoDTO();
            }catch (Exception e){
                log.error("查询评价 error",e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));


        Map<String, RpcSkuBO> skuInfo = skuInfoFuture.join();
        Boolean selfFlag = selfFlagFuture.join();
        PriceInfoResponseBO priceInfoResponseBO = priceInfoResponseBOFuture.join();
        Map<String, ProductPurchasePriceDto> productPurchasePriceMap = productPurchasePriceMapFuture.join();
        Map<String, List<JdhStationDto>> skuStationMap = skuStationMapFuture.join();
        EvaluateInfoDTO evaluateInfoDTO = evaluateInfoDTOFuture.join();

        CouponGetResultDTO batchGetCoupon = batchGetCouponFuture.join();
        log.info("ProductApplicationImpl queryProductDetailFloorBySku skuInfo={}", JSON.toJSONString(skuInfo));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku selfFlag={}", JSON.toJSONString(selfFlag));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku priceInfoResponseBO={}", JSON.toJSONString(priceInfoResponseBO));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku productPurchasePriceMap={}", JSON.toJSONString(productPurchasePriceMap));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku skuStationMap={}", JSON.toJSONString(skuStationMap));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku evaluateInfoDTO={}", JSON.toJSONString(evaluateInfoDTO));

        log.info("ProductApplicationImpl queryProductDetailFloorBySku batchGetCoupon={}", JSON.toJSONString(batchGetCoupon));

        RpcSkuBO skuBO = skuInfo.get(skuNo);

        // 白名单配置的是一口价不展示起字
        Set<String> skuFreeFeeList = feeConfigurationApplication.queryAllFixedSkuConfig();
        // 获取标签
        ProductDefaultSkuBO productDefaultSkuBO = productDomainService.getProductDefaultSkuDTO(skuInfo.get(skuNo), priceInfoResponseBO, selfFlag, jdhSku, productPurchasePriceMap, skuFreeFeeList, spuFlag);
        productDetailDTO.setDefaultSku(ProductApplicationConverter.instance.productDefaultSkuBO2DTO(productDefaultSkuBO));

        //--------------商品详情--------------
        CompletableFuture<SkuInfoAndStyleDTO> skuInfoAndStyleDTOFuture = CompletableFuture.supplyAsync(() -> {
            try {
                SkuInfoAndStyleBO skuInfoAndStyleBO = skuInfoRpc.queryProductBigFieldBySku(skuNo);
                productDetailDTO.setProductDetailAndStyle(ProductApplicationConverter.instance.skuInfoAndStyleBO2DTO(skuInfoAndStyleBO));
                return productDetailDTO.getProductDetailAndStyle();
            } catch (Exception e) {
                log.error("商详获取商品详情 error", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //--------------升级加购--------------
        CompletableFuture<List<ProductDefaultSkuDTO>> productDefaultSkuDTOSFuture = CompletableFuture.supplyAsync(() -> {
            try {
                if (CollectionUtils.isNotEmpty(jdhSkuRels)){
                    List<ProductDefaultSkuBO> productDefaultSkuDTOS = productDomainService.getProductDefaultSkuDTOS(jdhSkuRels, skuInfo, selfFlag, priceInfoResponseBO, skuFreeFeeList, productPurchasePriceMap, skuStationMap, spuFlag);
                    productDetailDTO.setOtherDefaultSku(ProductApplicationConverter.instance.productDefaultSkuBO2DTOList(productDefaultSkuDTOS));
                    return productDetailDTO.getOtherDefaultSku();
                }
            } catch (Exception e) {
                log.error("商详获取升级加购 error", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //--------------优惠券--------------
        CompletableFuture<List<ProductCouponDTO>> productCouponDTOSFuture = CompletableFuture.supplyAsync(() -> {
            try {
                ProductCouponRequest productCouponRequest = new ProductCouponRequest();
                productCouponRequest.setUserPin(detailFloorBO.getUserPin());
                productCouponRequest.setSkuNo(detailFloorBO.getSkuNo());
                productCouponRequest.setQueryGiftCash(true);
                List<ProductCouponDTO> productCouponDTOS = queryProductCouponList(productCouponRequest);
                if (productCouponDTOS.size()>5){
                    productCouponDTOS = productCouponDTOS.subList(0, 5);
                }
                productDetailDTO.setProductCouponDTOList(productCouponDTOS);
                return productDetailDTO.getProductCouponDTOList();
            } catch (Exception e) {
                log.error("查询优惠券 error", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //--------------服务须知楼层&&地址--------------
        CompletableFuture<ProductInfoBO> productInfoFuture = CompletableFuture.supplyAsync(() -> {
            List<JdhStationDto> jdhStationDtos = skuStationMap.get(skuNo);
            try {
                if (userAddressDetail != null){
                    AddressDetail addressDetail = new AddressDetail();
                    addressDetail.setAddressId(String.valueOf(userAddressDetail.getAddressId()));
                    addressDetail.setFullAddress(userAddressDetail.getFullAddress());
                    StationGeoQuery stationGeoQuery = new StationGeoQuery();
                    stationGeoQuery.setSkuNo(Long.valueOf(skuNo));
                    stationGeoQuery.setAddressDetailList(Arrays.asList(addressDetail));
                    jdhStationDtos = stationApplication.queryJdhStationGeo(stationGeoQuery);
                    log.info("商详获取地图服务站信息param:{},result:{}", JSON.toJSONString(stationGeoQuery), JSON.toJSONString(jdhStationDtos));


                    UsePromiseGoSwitch usePromiseGoSwitch = duccConfig.getUsePromiseGoSwitch();
                    BusinessModeEnum businessModeEnum = BusinessModeUtil.getBusinessModeEnum(jdhSku.getServiceType());
                    // 商详支持当前模式eta数据查询
                    if (Objects.nonNull(usePromiseGoSwitch) && usePromiseGoSwitch.supportProductQuery(businessModeEnum)){
                        PreUserPromisegoBo promisegoBo = queryPreUserPromisego(jdhSku, pin, userAddressDetail, jdhStationDtos);
                        // 支商详支持当前模式eta数据的使用展示
                        if (usePromiseGoSwitch.supportProductUse(businessModeEnum) && usePromiseGoSwitch.isProductPageBlankPin(pin)){
                            if (Objects.nonNull(promisegoBo) && Objects.nonNull(promisegoBo.getScript()) && StrUtil.isNotBlank(promisegoBo.getScript().getScriptContent())){
                                try {
                                    //处理 ab逻辑
                                    AbTestExp etaAb = abTestConfiguration.callAbTestResult(pin, "etaAb");
                                    if(Objects.nonNull(etaAb) && !"base".equals(etaAb.getLabel())){
                                        productDetailDTO.setScriptDto(ScriptDto.builder().scriptContent(promisegoBo.getScript().getScriptContent()).build());
                                    }
                                } catch (Exception e) {
                                    log.error("getProductDetailEta callAbTestResult exception",e);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("ProductApplicationImpl -> queryProductDetailFloorBySku 获取地图失败 queryJdhStationGeo error", e);
            }
            ProductInfoBO productInfo = productDomainService.getProductInfoBO(CollectionUtils.isEmpty(jdhStationDtos), detailFloorBO.isLogin(), userAddressDetail, jdhSku);
            productDetailDTO.setProductInfo(ProductApplicationConverter.instance.ProductInfoBO2DTO(productInfo));
            return productInfo;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //--------------限购--------------
        CompletableFuture<ProductLimitBuyDTO> productLimitBuyDTOFuture = CompletableFuture.supplyAsync(() -> {
            List<SkuInfoRequestParam> skuInfoRequestParams = new ArrayList<>();
            SkuInfoRequestParam infoRequestParam = new SkuInfoRequestParam();
            infoRequestParam.setSkuId(skuNo);
            skuInfoRequestParams.add(infoRequestParam);
            List<ProductLimitBuyBO> productLimitStrategy = productLimitbuyRpc.getProductLimitStrategy(skuInfoRequestParams, pin);
            ProductLimitBuyDTO productLimitBuyDTO = ProductApplicationConverter.instance.productLimitBuy2DTO(productLimitStrategy.get(0));
            productDetailDTO.setProductLimitBuyDTO(productLimitBuyDTO);
            return productDetailDTO.getProductLimitBuyDTO();
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //-------------站内分享------------
        CompletableFuture<Map<String, Map<String, String>>> shareInfoFuture = CompletableFuture.supplyAsync(() -> {
            Map<String, Map<String, String>> shareInfo = productDomainService.getShare(skuNo, productCarouselFileBos, productDefaultSkuBO, properties);
            productDetailDTO.setShareInfo(shareInfo);
            return productDetailDTO.getShareInfo();
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //-------------分享领券楼层------------
        ShareGetCouponConfig shareGetCouponConfig = JSON.parseObject(duccConfig.getProductDetailShareGetCouponConfig(), ShareGetCouponConfig.class);
        if (shareGetCouponConfig.getEnvTypeList().contains(detailFloorBO.getEnvType()) && shareGetCouponConfig.getVenderIdList().contains(skuBO.getVenderId())
                && !shareGetCouponConfig.getBlackSkuList().contains(skuNo) && Boolean.TRUE.equals(shareGetCouponConfig.getOpen())){
            CompletableFuture<Void> abTestExpFuture = CompletableFuture.runAsync(() -> {
                abTestConfiguration.shareGetCouponExecute(detailFloorBO.getUserPin(), ()->{
                    ShareGetCouponDTO shareGetCouponDTO = new ShareGetCouponDTO();
                    BeanUtils.copyProperties(shareGetCouponConfig, shareGetCouponDTO);
                    productDetailDTO.setShareGetCouponDTO(shareGetCouponDTO);
                });
            },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        }


        //-------------京东提问------------
        CompletableFuture<QueryQuestionDTO> queryQuestionDTOFuture = CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> showQuestionSkuSet = duccConfig.getShowQuestionSkuSet();
                AtomicReference<SkuQuesttion4Result> skuQuesttion4Result = new AtomicReference<>(new SkuQuesttion4Result());
                if (showQuestionSkuSet.contains(skuNo)){
                    abTestConfiguration.shareSkuQuestionBoxExecute(pin, ()->{
                        String uuid = detailFloorBO.getUuid();
                        if(StringUtil.isBlank(uuid)){
                            uuid = detailFloorBO.getClientIp();
                        }
                        SkuQuesttion4Result tempResult = productDomainService.getSkuQuestionList(uuid,pin,skuNo);
                        if(Objects.nonNull(tempResult)){
                            skuQuesttion4Result.set(tempResult);
                        }
                        skuQuesttion4Result.get().setShowQuestionFloor(Boolean.TRUE);
                    });
                }
                productDetailDTO.setQueryQuesttionDTO(ProductApplicationConverter.instance.modelToDto(skuQuesttion4Result.get()));
                return productDetailDTO.getQueryQuesttionDTO();
            }catch (Exception e){
                log.error("查询提问 error",e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 升级护士费项减免
        CompletableFuture<DiscountFeeDisplayDto> discountFeeFuture = CompletableFuture.supplyAsync(() -> {
            try {
                if(StringUtils.isBlank(detailFloorBO.getUserPin())){
                    return null;
                }
                if(Objects.isNull(userAddressDetail)){
                    return null;
                }

                CalcTradeServiceFeeCmd calcTradeServiceFeeCmd = new CalcTradeServiceFeeCmd();
                calcTradeServiceFeeCmd.setUserPin(detailFloorBO.getUserPin());
                calcTradeServiceFeeCmd.setSkuIds(new HashSet<>(Arrays.asList(detailFloorBO.getSkuNo())));
                // 预约地址
                AddressUpdateParam addressInfo = new AddressUpdateParam();
                addressInfo.setProvinceId(userAddressDetail.getProvinceId());
                addressInfo.setCityId(userAddressDetail.getCityId());
                addressInfo.setCountyId(userAddressDetail.getCountyId());
                if(Objects.nonNull(userAddressDetail.getTownId())) {
                    addressInfo.setTownId(userAddressDetail.getTownId());
                }
                addressInfo.setAddressDetail(userAddressDetail.getAddressDetail());
                calcTradeServiceFeeCmd.setAddressInfo(addressInfo);
                calcTradeServiceFeeCmd.setSearchServiceUpgrade(true);
                List<TradeServiceFeeInfoDTO> tradeServiceFeeList = tradeApplication.calcServiceFee(calcTradeServiceFeeCmd);

                log.info("ProductApplicationImpl discountFeeDisplayDto calcTradeServiceFeeCmd={}, tradeServiceFeeList={}", JSON.toJSONString(calcTradeServiceFeeCmd), JSON.toJSONString(tradeServiceFeeList));
                if (CollectionUtils.isEmpty(tradeServiceFeeList)){
                    return null;
                }
                tradeServiceFeeList = tradeServiceFeeList.stream().filter(t-> FeeAggregateTypeEnum.DYNAMIC_FEE.getSubType().equals(t.getAggregateSubType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(tradeServiceFeeList)){
                    return null;
                }
                TradeServiceFeeInfoDTO tradeServiceFee = tradeServiceFeeList.get(0);
                List<TradeServiceFeeDetailDTO> serviceFeeDetailList = tradeServiceFee.getServiceFeeDetailList();
                serviceFeeDetailList = serviceFeeDetailList.stream().filter(t->t.getType().equals(JdOrderFeeTypeEnum.UPGRADE_ANGEL.getType())).collect(Collectors.toList());
                log.info("ProductApplicationImpl discountFeeDisplayDto filter serviceFeeDetailList={}", JSON.toJSONString(serviceFeeDetailList));
                if (CollectionUtils.isEmpty(serviceFeeDetailList)){
                    log.info("ProductApplicationImpl discountFeeDisplayDto serviceFeeDetailList empty");
                    return null;
                }
                TradeServiceFeeDetailDTO tradeServiceFeeDetailDTO = serviceFeeDetailList.get(0);

               if (Objects.isNull(tradeServiceFeeDetailDTO) || Objects.isNull(tradeServiceFeeDetailDTO.getFee())){
                    log.info("ProductApplicationImpl discountFeeDisplayDto fee null");
                    return null;
                }

                // 升级费为0 并且 升级减免为0 则不处理
                if (tradeServiceFeeDetailDTO.getFee().compareTo(BigDecimal.ZERO) <= 0 && (Objects.isNull(tradeServiceFeeDetailDTO.getDiscountFee()) || tradeServiceFeeDetailDTO.getDiscountFee().compareTo(BigDecimal.ZERO) <= 0)){
                    log.info("ProductApplicationImpl fee none discount");
                    return null;
                }
                
                Map<String, String> extendMap = tradeServiceFeeDetailDTO.getExtendMap();
                if(MapUtils.isNotEmpty(extendMap) && extendMap.containsKey(ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getExtType())){
                    DiscountFeeAction discountFeeAction = JSON.parseObject(extendMap.get(ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getExtType()), ServiceFeeExtendEnum.DISCOUNT_FEE_ACTION.getClazz());
                    if(Objects.nonNull(discountFeeAction) && Objects.nonNull(discountFeeAction.getDisplay())){
                        DiscountFeeDisplayDto discountFeeDisplayDto = ProductApplicationConverter.instance.convertDiscountFeeDisplayDto(discountFeeAction.getDisplay());
                        discountFeeDisplayDto.setAutoSelected(discountFeeAction.getAutoSelected());
                        discountFeeDisplayDto.setInvertSelect(discountFeeAction.getInvertSelect());

                        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.PRODUCT_DISCOUNT_FEE_CONFIRM, detailFloorBO.getUserPin());
                        if(jimClient.exists(redisKey)){
                            discountFeeDisplayDto.setIsShowConfirm(false);
                            discountFeeDisplayDto.setIsShowToast(true);
                        } else{
                            discountFeeDisplayDto.setIsShowConfirm(true);
                            discountFeeDisplayDto.setIsShowToast(false);
                        }
                        jimClient.set(redisKey, "1", RedisKeyEnum.PRODUCT_DISCOUNT_FEE_CONFIRM.getExpireTime(), RedisKeyEnum.PRODUCT_DISCOUNT_FEE_CONFIRM.getExpireTimeUnit(), false);
                        productDetailDTO.setDiscountFeeDisplayDto(discountFeeDisplayDto);
                        return productDetailDTO.getDiscountFeeDisplayDto();
                    }
                }
            }catch (Exception e){
                log.error("查询升级费项减免 error",e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 活动楼层
        CompletableFuture<Void> buildActivityFloorFuture = CompletableFuture.supplyAsync(() -> {
            try {
                buildActivityFloor(productDetailDTO, jdhSku);
            }catch (Exception e){
                log.error("查询评价 error",e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        //--------------商品评价填充好评标签--------------
        CompletableFuture<Void> fullGoodCommentTagFuture = CompletableFuture.supplyAsync(() -> {
            try {
                fullGoodCommentTag(evaluateInfoDTO, skuNo);
            } catch (Exception e) {
                log.error("商品评价填充好评标签 error", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));


        SkuInfoAndStyleDTO skuInfoAndStyleDTO = skuInfoAndStyleDTOFuture.join();
        List<ProductDefaultSkuDTO> otherDefaultSku = productDefaultSkuDTOSFuture.join();
        List<ProductCouponDTO> productCouponDTOS = productCouponDTOSFuture.join();
        ProductInfoBO productInfo = productInfoFuture.join();
        ProductLimitBuyDTO productLimitBuyDTO = productLimitBuyDTOFuture.join();
        Map<String, Map<String, String>> shareInfo = shareInfoFuture.join();
        QueryQuestionDTO queryQuestionDTO = queryQuestionDTOFuture.join();
        DiscountFeeDisplayDto discountFeeDisplayDto  = discountFeeFuture.join();
        fullGoodCommentTagFuture.join();

        //商祥如果命中 自动升级，并且不允许反选，不展示eta 【这里后面可以优化整个异步的任务组装方式】
        if(Objects.nonNull(discountFeeDisplayDto)
                && Boolean.TRUE.equals(discountFeeDisplayDto.getAutoSelected())
                && Boolean.FALSE.equals(discountFeeDisplayDto.getInvertSelect())){
            productDetailDTO.setScriptDto(null);
        }


        buildActivityFloorFuture.join();

        log.info("ProductApplicationImpl queryProductDetailFloorBySku skuInfoAndStyleDTO={}", JSON.toJSONString(skuInfoAndStyleDTO));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku otherDefaultSku={}", JSON.toJSONString(otherDefaultSku));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku productCouponDTOS={}", JSON.toJSONString(productCouponDTOS));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku productInfo={}", JSON.toJSONString(productInfo));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku productLimitBuyDTO={}", JSON.toJSONString(productLimitBuyDTO));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku shareInfo={}", JSON.toJSONString(shareInfo));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku queryQuestionDTO={}", JSON.toJSONString(queryQuestionDTO));
        log.info("ProductApplicationImpl queryProductDetailFloorBySku discountFeeDisplayDto={}", JSON.toJSONString(discountFeeDisplayDto));

        // 商品中台-sku以及与当前sku绑定的其他sku
        convertSkuGroupDTOList(spuFlag, productDetailDTO);

        // 底部浮窗优先级：无登录态、有登录态地址无服务、爆单开关、服务升级、promise预测
        bottomFloatingWindow(productDetailDTO, discountFeeDisplayDto);

        //--------------底部横幅--------------
        ProductDetailBottomBannerBO productDetailBottomBannerDTO = productDomainService.getProductDetailBottomBannerDTO(productInfo.getBuyLimitType());
        productDetailDTO.setBottomBanner(ProductApplicationConverter.instance.productDetailBottomBannerBO2DTO(productDetailBottomBannerDTO));
        log.info("ProductApplicationImpl -> queryProductDetailFloorBySku 底部横幅 bottomBanner={}", JSON.toJSONString(productDetailDTO.getBottomBanner()));

        //--------------配置 底部按钮 --------------
        ProductDetailOrderConfigDTO productsAndOrderConfig = this.getProductDetailOrderConfigDTO(properties, detailFloorBO, productInfo.getBuyLimitType(),String.valueOf(skuBO.getVenderId()), productCouponDTOS, jdhSku);
        productDetailDTO.setProductsAndOrderConfig(productsAndOrderConfig);
        log.info("ProductApplicationImpl -> queryProductDetailFloorBySku productDetailDTO={}", JsonUtil.toJSONString(productDetailDTO));
        return productDetailDTO;
    }

    /**
     * 店铺新人礼金与优惠券合并
     * @param request
     * @param priceInfoResponseBO
     * @param productCouponDTOS
     */
    private List<ProductCouponDTO> cashGiftMoneyCouponMerge(ProductCouponRequest request, PriceInfoResponseBO priceInfoResponseBO, List<ProductCouponDTO> productCouponDTOS){
        log.info("cashGiftMoneyCouponMerge priceInfoResponseBO={}, productCouponDTOS={}", JSON.toJSONString(priceInfoResponseBO), JSON.toJSONString(productCouponDTOS));
        // 店铺新人礼金与优惠券合并
        List<ProductCouponDTO> mergeList = new ArrayList<>();
        try {
            // 合并优惠券
            if (CollectionUtils.isNotEmpty(productCouponDTOS)){
                mergeList.addAll(productCouponDTOS);
            }
            Map<String, InfoResultBO> infoResponse = priceInfoResponseBO.getInfoResponse();
            if (MapUtils.isEmpty(infoResponse)){
                return mergeList;
            }
            InfoResultBO infoResultBO = infoResponse.get(request.getSkuNo());
            if (Objects.isNull(infoResultBO)){
                return mergeList;
            }
            List<PromoInfoFieldBO> promoInfoFieldList = infoResultBO.getPromoInfoFieldList();
            if (CollectionUtils.isEmpty(promoInfoFieldList)){
                return mergeList;
            }
            // 首购礼金配置
            JSONObject cashGiftObj = JSON.parseObject(duccConfig.getProductCouponConfig()).getJSONObject("cashGift");
            String cashGift = "";
            Long startTime = 0L;
            Long endTime = 0L;
            for (PromoInfoFieldBO p : promoInfoFieldList) {
                if (cashGiftObj.getInteger("promoType").equals(p.getPromoType()) && CollectionUtils.isNotEmpty(p.getPromoTags()) && p.getPromoTags().contains(cashGiftObj.getInteger("promoTag"))){
                    Map<String, String> extInfo = p.getExtInfo();
                    if (MapUtils.isEmpty(extInfo)){
                        continue;
                    }
                    cashGift = extInfo.get("cashGift");
                    if (StringUtils.isBlank(cashGift)){
                        continue;
                    }
                    startTime = p.getStartTime();
                    endTime = p.getEndTime();
                    break;
                }
            }
            log.info("storeGiftMoneyCouponMerge cashGift={}, startTime={}, endTime={}", cashGift, startTime, endTime);
            if (StringUtils.isBlank(cashGift)){
                return mergeList;
            }

            // 店铺新人礼金
            ProductCouponDTO cashGiftCoupon = new ProductCouponDTO();
            cashGiftCoupon.setDiscount(new BigDecimal(cashGift));
            cashGiftCoupon.setName(cashGiftObj.getString("name"));
            cashGiftCoupon.setActivityBeginTime(new Date(startTime * 1000L));
            cashGiftCoupon.setActivityEndTime(new Date(endTime * 1000L));
            cashGiftCoupon.setCouponStyle(cashGiftObj.getInteger("couponStyle"));
            cashGiftCoupon.setShortName(cashGiftObj.getString("shortName"));
            mergeList.add(cashGiftCoupon);

            mergeList.forEach(c->{
                c.setSortValue(Objects.isNull(c.getDiscount()) ?  new BigDecimal("0") : c.getDiscount());
                if (Objects.nonNull(c.getActivityBeginTime()) && Objects.nonNull(c.getActivityEndTime())){
                    boolean within24Hours = TimeUtils.isWithin24Hours(c.getActivityBeginTime(), c.getActivityEndTime());
                    c.setWithin24Hours(within24Hours);
                    if (within24Hours){
                        c.setRemainingTime(c.getActivityEndTime().getTime() - c.getActivityBeginTime().getTime());
                    }
                }
            });
            // 按照优惠金额大小排序
            mergeList.sort((a, b) -> b.getSortValue().compareTo(a.getSortValue()));
        } catch (Exception e) {
            log.error("ProductApplicationImpl storeGiftMoneyCouponMerge error e", e);
        }
        return mergeList;
    }

    /**
     * 底部浮窗优先级：无登录态、有登录态地址无服务、爆单开关、服务升级、promise预测
     * @param productDetailDTO
     * @param discountFeeDisplayDto
     */
    private void bottomFloatingWindow(ProductDetailDTO productDetailDTO, DiscountFeeDisplayDto discountFeeDisplayDto) {
        ProductInfoDTO productInfo = productDetailDTO.getProductInfo();
        // 限购提示
        List<Integer> bottomBannerList = Arrays.asList(ProductDetailBottomBannerEnum.NO_LOGIN.getBuyLimitType(), ProductDetailBottomBannerEnum.ADDRESS_NULL.getBuyLimitType(),
                ProductDetailBottomBannerEnum.NO_TESTING_SERVICE.getBuyLimitType(), ProductDetailBottomBannerEnum.TUBE.getBuyLimitType());
        if (Objects.nonNull(productInfo) && Objects.nonNull(productInfo.getBuyLimitType()) && bottomBannerList.contains(productInfo.getBuyLimitType())){
            productDetailDTO.setDiscountFeeDisplayDto(null);// 服务升级
            productDetailDTO.setScriptDto(null);// promise eta 预测
            return;
        }
        //商祥如果命中 自动升级，并且不允许反选，不展示eta 【这里后面可以优化整个异步的任务组装方式】
        if(Objects.nonNull(discountFeeDisplayDto)
                && Boolean.TRUE.equals(discountFeeDisplayDto.getAutoSelected())
                && Boolean.FALSE.equals(discountFeeDisplayDto.getInvertSelect())){
            productDetailDTO.setScriptDto(null);// promise eta 预测
            return;
        }
    }


    private void convertSkuGroupDTOList(Boolean spuFlag, ProductDetailDTO productDetailDTO) {
        List<ProductDefaultSkuDTO> otherDefaultSkuList = productDetailDTO.getOtherDefaultSku();
        if (spuFlag){
            if (CollectionUtils.isNotEmpty(otherDefaultSkuList)){
                List<ProductDefaultSkuDTO> skuGroupDTOList = otherDefaultSkuList.stream()
                        .filter(sku -> Boolean.TRUE.equals(sku.getMatchStationFlag())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(skuGroupDTOList) && skuGroupDTOList.size() > 1){
                    // sku以及与当前sku绑定的其他sku
                    productDetailDTO.setSkuGroupDTOList(skuGroupDTOList);
                }
                productDetailDTO.setOtherDefaultSku(null);
            }
            // 首单特惠标签与plus标签同时存在，只展示plus标签
            ProductDefaultSkuDTO defaultSku = productDetailDTO.getDefaultSku();
            if (Objects.nonNull(defaultSku) && BooleanUtil.isTrue(defaultSku.getMatchPlusFlag())){
                defaultSku.setPromotionTags(null);
                productDetailDTO.setDefaultSku(defaultSku);
            }

        }
    }



    private Map<String, List<JdhStationDto>> getSkuStationGeoMap(UserAddressDetailBO userAddressDetail, Set<String> skuIds) {
        try {
            log.info("ProductApplicationImpl getSkuStationGeoMap start");
            if (Objects.isNull(userAddressDetail)){
                log.info("ProductApplicationImpl -> getSkuStationGeoMap userAddressDetail empty");
                return Collections.emptyMap();
            }
            List<CompletableFuture<Map<String, List<JdhStationDto>>>> futureList = Collections.synchronizedList(Lists.newArrayList());
            UserAddressDetailBO finalUserAddressDetail = userAddressDetail;
            skuIds.forEach(skuId->{
                CompletableFuture<Map<String, List<JdhStationDto>>> future = CompletableFuture.supplyAsync(()->{
                    try {
                        StationGeoQuery stationGeoQuery = new StationGeoQuery();
                        stationGeoQuery.setSkuNo(Long.valueOf(skuId));
                        // 用户地址
                        List<AddressDetail> addressDetailList = new ArrayList<>();
                        AddressDetail addressDetail = new AddressDetail();
                        addressDetail.setAddressId(String.valueOf(finalUserAddressDetail.getAddressId()));
                        addressDetail.setFullAddress(finalUserAddressDetail.getFullAddress());
                        addressDetailList.add(addressDetail);
                        stationGeoQuery.setAddressDetailList(addressDetailList);
                        List<JdhStationDto> stationList = stationApplication.queryJdhStationGeo(stationGeoQuery);
                        log.info("ProductApplicationImpl -> getSkuStationGeoMap station stationGeoQuery={}, stationList={}", JSON.toJSONString(stationGeoQuery), JSON.toJSONString(stationList));
                        if (CollectionUtils.isEmpty(stationList)){
                            return null;
                        }
                        Map<String, List<JdhStationDto>> map = new HashMap<>();
                        map.put(skuId, stationList);
                        return map;
                    } catch (Exception e) {
                        log.error("ProductApplicationImpl -> getSkuStationGeoMap error e", e);
                    }
                    return null;
                }, executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL)).exceptionally(throwable -> {
                    log.error("ProductApplicationImpl -> getSkuStationGeoMap exceptionally error "+throwable.getMessage(),throwable);
                    return null;
                });
                futureList.add(future);
            });
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture<?>[0])).get(NumConstant.NUM_5, TimeUnit.SECONDS);
            List<Map<String, List<JdhStationDto>>> skuStationCollect = futureList.stream().filter(Objects::nonNull).map(e -> e.getNow(null)).filter(Objects::nonNull)
                    .collect(Collectors.toList());

            Map<String, List<JdhStationDto>> skuStationMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skuStationCollect)){
                for (Map<String, List<JdhStationDto>> e : skuStationCollect) {
                    skuStationMap.putAll(e);
                }
            }
            log.info("ProductApplicationImpl -> getSkuStationGeoMap skuStationMap={}", JSON.toJSONString(skuStationMap));
            return skuStationMap;
        } catch (Exception e) {
            log.error("ProductApplicationImpl -> getSkuStationGeoMap error e", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 查询sku以及与当前sku绑定的其他sku
     * @param detailFloorBO
     * @return
     */
    private List<JdhSkuRel> getSkuGroupList(ProductDetailFloorRequest detailFloorBO) {
        log.info("ProductApplicationImpl -> getSkuGroupList detailFloorBO={}", JSON.toJSONString(detailFloorBO));
        List<JdhSkuRel> skuRelList = new ArrayList<>();
        try {
            String skuNo = detailFloorBO.getSkuNo();
            Integer skuItemType = detailFloorBO.getSkuItemType();
            // 子集查兄弟集合（子查兄弟）
            List<String> relSkuIds = queryMapInfoForSkuIds(skuNo);
            if (CollectionUtils.isEmpty(relSkuIds)){
                log.info("ProductApplicationImpl -> getSkuGroupList rpcSkuMap empty");
                return skuRelList;
            }
            // 获取SKU信息
            Map<String, RpcSkuBO> rpcSkuMap = skuInfoRpc.getSkuInfo(new HashSet<>(relSkuIds));
            if (MapUtils.isEmpty(rpcSkuMap)){
                log.info("ProductApplicationImpl -> getSkuGroupList rpcSkuMap empty");
                return skuRelList;
            }

            relSkuIds.forEach(skuId->{
                JdhSkuRel skuRel = new JdhSkuRel();
                skuRel.setSkuId(Long.valueOf(skuId));
                skuRelList.add(skuRel);
            });
            log.info("ProductApplicationImpl -> getSkuGroupList effective skuRelList={}", JSON.toJSONString(skuRelList));
            // 查询多个商品下项目列表信息
            Map<Long, List<ServiceItem>> maps = getMultiSkuItemList(skuRelList.stream().map(JdhSkuRel::getSkuId).collect(Collectors.toSet()), skuItemType == null ? 1 : skuItemType);
            skuRelList.forEach(jdhSkuRel->{
                if (!maps.containsKey(jdhSkuRel.getSkuId()) || CollUtil.isEmpty(maps.get(jdhSkuRel.getSkuId()))) {
                    return;
                }
                jdhSkuRel.setServiceItemList(maps.get(jdhSkuRel.getSkuId()));
            });
            log.info("ProductApplicationImpl -> getSkuGroupList end skuRelList={}", JSON.toJSONString(skuRelList));
        } catch (Exception e) {
            log.error("ProductApplicationImpl -> getSkuGroupList error e", e);
        }
        return skuRelList;
    }

    /**
     * 子集查兄弟集合（子查兄弟）
     * @param skuNo
     * @return
     */
    private List<String> queryMapInfoForSkuIds(String skuNo) {
        List<String> relSkuIds = new ArrayList<>();
        // 示例数据：{"100164437404":"{\"100133417973\":\"100164437360,100133417973,100164437404\"}"}
        // 入参：{父:全部}
        Map<String, String> crsSkuMap = skuInfoRpc.queryMapInfo(Sets.newHashSet(skuNo));
        log.info("ProductApplicationImpl -> queryMapInfoForSkuIds crsSkuMap={}", JSON.toJSONString(crsSkuMap));
        if (MapUtils.isEmpty(crsSkuMap)){
            log.info("ProductApplicationImpl -> queryMapInfoForSkuIds crsSkuMap empty");
            return relSkuIds;
        }
        // {"100133417973":"100164437360,100133417973,100164437404"}
        String crsSkuStr = crsSkuMap.get(skuNo);
        if (StringUtils.isBlank(crsSkuStr)){
            log.info("ProductApplicationImpl -> queryMapInfoForSkuIds crsSkuStr empty");
            return relSkuIds;
        }

        JSONObject allSkuObj = JSON.parseObject(crsSkuStr);
        Map<String,String> allSkuMap = (Map)allSkuObj;
        // 100164437360,100133417973,100164437404
        String allSkuStr = null;
        for (Map.Entry<String,String> entry : allSkuMap.entrySet()) {
            allSkuStr = entry.getValue();
        }
        relSkuIds = Arrays.asList(allSkuStr.split(","));
        log.info("ProductApplicationImpl -> queryMapInfoForSkuIds relSkuIds={}", JSON.toJSONString(relSkuIds));
        return relSkuIds;
    }


    /**
     * 查询售前用户promisego
     *
     * @param jdhSku             jdhSku
     * @param userAddressDetail 用户地址详细信息
     * @param jdhStationDtoList 服务站列表
     */
    private PreUserPromisegoBo queryPreUserPromisego(JdhSku jdhSku,
                                                     String userPin,
                                                     UserAddressDetailBO userAddressDetail,
                                                     List<JdhStationDto> jdhStationDtoList){
        if(CollUtil.isEmpty(jdhStationDtoList)){
            log.info("ProductApplicationImpl -> queryProductDetailFloorBySku 查询时效预估数据，【服务站信息为空】，不满足条件");
            return null;
        }
        if(StrUtil.isBlank(userPin)){
            log.info("ProductApplicationImpl -> queryProductDetailFloorBySku 查询时效预估数据，【非登录态】，不满足条件");
            return null;
        }

        //1、根据sku + 地址
        try {
            XfylAppointDateTimeDTO recentlyAvailableAppointmentTime = findRecentlyAvailableAppointmentTime(userPin, jdhSku, userAddressDetail);
            log.info("ProductApplicationImpl -> queryProductDetailFloorBySku queryPreUserPromisego recentlyAvailableAppointmentTime:{}",JSON.toJSONString(recentlyAvailableAppointmentTime));
            if(Objects.nonNull(recentlyAvailableAppointmentTime)){
                PreUserPromisegoRequestBo requestBo = buildPromisegoRequestBo(jdhSku,recentlyAvailableAppointmentTime, userAddressDetail,jdhStationDtoList);
                if(Objects.nonNull(requestBo)){
                    return promiseGoRpcService.queryPreUserPromisego(requestBo);
                }
            }
            return null;
        } catch (Exception e) {
            log.error("ProductApplicationImpl -> queryPreUserPromisego exception",e);
        }
        return null;
    }

    /**
     * buildPromisegoRequestBo
     *
     * @param jdhSku                           jdh sku
     * @param recentlyAvailableAppointmentTime 最近可用预约时间
     * @param userAddressDetail                用户地址详细信息
     * @return {@link PreUserPromisegoRequestBo }
     */
    private PreUserPromisegoRequestBo buildPromisegoRequestBo(JdhSku jdhSku,
                                                              XfylAppointDateTimeDTO recentlyAvailableAppointmentTime,
                                                              UserAddressDetailBO userAddressDetail,
                                                              List<JdhStationDto> jdhStationDtoList){
        //业务模式
        Integer serviceType = jdhSku.getServiceType();
        BusinessModeEnum businessModeEnum = BusinessModeUtil.getBusinessModeEnum(BusinessModeParam.builder().skuServiceType(jdhSku.getServiceType()).businessProcessType(jdhSku.getBusinessProcessType()).build());

        //预约项目信息
        List<PromisegoRequestAppointmentService> appointmentServiceList = new ArrayList<>();
        PromisegoRequestAppointmentService promisegoRequestAppointmentService = new PromisegoRequestAppointmentService();
        promisegoRequestAppointmentService.setSkuId(jdhSku.getSkuId());
        promisegoRequestAppointmentService.setSkuType(jdhSku.getSkuType());
        promisegoRequestAppointmentService.setServiceType(serviceType);

        List<PromisegoRequestAppointmentServiceItem> itemList = new ArrayList<>();

        List<JdhSkuItemRel> jdhSkuItemRels = jdhSkuRepository.queryJdhSkuItemRelList(Arrays.asList(JdhSkuItemRel.builder().skuId(jdhSku.getSkuId()).build()), null, 1, false);
        List<ServiceItem> serviceItems = jdhServiceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder()
                .itemIdList(jdhSkuItemRels.stream().map(ele -> Long.parseLong(ele.getSkuItemId())).collect(Collectors.toSet()))
                .build());

        for (ServiceItem serviceItem : serviceItems) {
            PromisegoRequestAppointmentServiceItem serviceItemEle = new PromisegoRequestAppointmentServiceItem();
            serviceItemEle.setItemId(serviceItem.getItemId());
            serviceItemEle.setItemName(serviceItem.getItemName());
            itemList.add(serviceItemEle);
        }

        promisegoRequestAppointmentService.setItemList(itemList);
        appointmentServiceList.add(promisegoRequestAppointmentService);
        Map<String, JdhStationDto> jdhStationDtoMap = jdhStationDtoList.stream().collect(Collectors.toMap(JdhStationDto::getAddressId, Function.identity()));

        // 命中的服务者信息
        JdhStationDto jdhStationDto = jdhStationDtoMap.get(userAddressDetail.getAddressId().toString());
        List<AngelStationDto> stationDtoList = jdhStationDto.getStationDtoList();
        if (CollectionUtils.isEmpty(stationDtoList)){
            log.info("ProductApplicationImpl->buildPromisegoRequestBo stationDtoList is empty");
            return null;
        }
        stationDtoList = stationDtoList.stream().filter(e ->  Objects.nonNull(e) && StringUtils.isNotBlank(e.getStationId())).collect(Collectors.toList());
        List<StoreInfoBo> storeInfoBos = providerStoreExportServiceRpc.listByStoreIds(stationDtoList.stream().map(AngelStationDto::getStationId).filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
        if(CollUtil.isEmpty(storeInfoBos)){
            return null;
        }
        Map<String, StoreInfoBo> storeInfoBoMap = storeInfoBos.stream().collect(Collectors.toMap(StoreInfoBo::getJdStoreId, Function.identity()));
        List<PromisegoAngelStation> jdhStationList = new ArrayList<>();
        for (AngelStationDto angelStationDto : stationDtoList) {
            PromisegoAngelStation promisegoAngelStation = station2PromisegoAngelStation(angelStationDto, storeInfoBoMap.get(angelStationDto.getStationId()));
            jdhStationList.add(promisegoAngelStation);
        }

        //预约地址信息
        PromisegoRequestAddress address = PromisegoRequestAddress.builder()
                .provinceId(userAddressDetail.getProvinceId())
                .cityId(userAddressDetail.getCityId())
                .countyId(userAddressDetail.getCountyId())
                .townId(Objects.nonNull(userAddressDetail.getTownId()) && userAddressDetail.getTownId() > 0 ? userAddressDetail.getTownId() : null)
                .provinceName(userAddressDetail.getProvinceName())
                .cityName(userAddressDetail.getCityName())
                .countyName(userAddressDetail.getCountyName())
                .townName(StrUtil.isBlank(userAddressDetail.getTownName()) ? null : userAddressDetail.getTownName())
                .fullAddress(userAddressDetail.getFullAddress())
                .latitude(userAddressDetail.getLatitude())
                .longitude(userAddressDetail.getLongitude())
                .coordType(userAddressDetail.getCoordType())
                .angelStationList(jdhStationList)
                .build();

        //预约时间信息
        PromisegoRequestAppointmentTime appointmentTime = PromisegoRequestAppointmentTime.builder()
                .immediately(recentlyAvailableAppointmentTime.getIsImmediately())
                .dateType(recentlyAvailableAppointmentTime.getDateType())
                .appointmentStartTime(TimeUtils.timeStrToLocalDate(recentlyAvailableAppointmentTime.getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S))
                .appointmentEndTime(TimeUtils.timeStrToLocalDate(recentlyAvailableAppointmentTime.getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE_NO_S))
                .build();

        return PreUserPromisegoRequestBo.builder()
                .scene(PromisegoQuerySceneEnum.PRODUCT_DETAIL.getScene())
                .businessMode(Objects.nonNull(businessModeEnum) ? businessModeEnum.getCode() : "")
                .appointmentAddress(address)
                .appointmentTime(appointmentTime)
                .appointmentServiceList(appointmentServiceList)
                .build();
    }


    /**
     * station2PromisegoAngelStation
     *
     * @param angelStationDto 服务站
     * @return {@link PromisegoAngelStation }
     */
    private PromisegoAngelStation station2PromisegoAngelStation(AngelStationDto angelStationDto,StoreInfoBo storeInfoBo) {
        PromisegoAngelStation jdhStation = new PromisegoAngelStation();
        jdhStation.setAngelStationId(angelStationDto.getAngelStationId());
        jdhStation.setAngelStationName(angelStationDto.getAngelStationName());
        jdhStation.setStationId(angelStationDto.getStationId());
        jdhStation.setStationName(angelStationDto.getStationName());
        jdhStation.setStationProvinceId(storeInfoBo.getProvinceId());
        jdhStation.setStationCityId(storeInfoBo.getCityId());
        jdhStation.setStationCountyId(storeInfoBo.getCountyId());
        jdhStation.setStationTownId(null);
        jdhStation.setStationProvinceName(storeInfoBo.getProvinceName());
        jdhStation.setStationCityName(storeInfoBo.getCityName());
        jdhStation.setStationCountyName(storeInfoBo.getCountyName());
        jdhStation.setStationTownName(null);
        jdhStation.setStationFullAddress(storeInfoBo.getStoreAddr());
        jdhStation.setStationLongitude(Double.valueOf(storeInfoBo.getLng()));
        jdhStation.setStationLatitude(Double.valueOf(storeInfoBo.getLat()));
        jdhStation.setStationCoordType(null);
        return jdhStation;
    }

    /**
     * 查找最近可用预约时间
     *
     * @param userPin           用户pin
     * @param jdhSku             jdhSku
     * @param userAddressDetail 用户地址详细信息
     * @return {@link XfylAppointDateTimeDTO }
     */
    private XfylAppointDateTimeDTO findRecentlyAvailableAppointmentTime(String userPin,JdhSku jdhSku,UserAddressDetailBO userAddressDetail){
        AvaiableAppointmentTimeParam appointmentTimeParam = new AvaiableAppointmentTimeParam();
        appointmentTimeParam.setUserPin(userPin);
        appointmentTimeParam.setSkuIds(Collections.singletonList(jdhSku.getSkuId()));
        appointmentTimeParam.setFullAddress(userAddressDetail.getFullAddress());
        appointmentTimeParam.setAddressId(userAddressDetail.getAddressId().toString());

        AvaiableAppointmentTimeDTO availableAppointmentTime = tradeApplication.queryAvaiableAppointmentTime(appointmentTimeParam);
        if(Objects.nonNull(availableAppointmentTime)){
            List<XfylAppointDateDTO> compatibleGroupDto = availableAppointmentTime.getCompatibleGroupDTO();
            if(CollUtil.isNotEmpty(compatibleGroupDto)){
                Date now = new Date();
                for (XfylAppointDateDTO xfylAppointDateDTO : compatibleGroupDto) {
                    List<XfylAppointDateTimeGroupDTO> appointDateTimeGroupDTOList = xfylAppointDateDTO.getAppointDateTimeGroupDTOList();
                    Integer dateType = xfylAppointDateDTO.getDateType();
                    for (XfylAppointDateTimeGroupDTO xfylAppointDateTimeGroupDTO : appointDateTimeGroupDTOList) {
                        for (XfylAppointDateTimeDTO xfylAppointDateTimeDTO : xfylAppointDateTimeGroupDTO.getDateTimeDTOList()) {
                            Integer status = xfylAppointDateTimeDTO.getStatus();
                            String appointmentStartTime = xfylAppointDateTimeDTO.getAppointmentStartTime();
                            Date appointmentStartTimeDate = TimeUtils.timeStrToDate(appointmentStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                            //如果可约,并且是今天的
                            if(NumConstant.NUM_1.equals(status) && DateUtil.isSameDay(now,appointmentStartTimeDate)){
                                xfylAppointDateTimeDTO.setDateType(dateType);
                                return xfylAppointDateTimeDTO;
                            }
                        }

                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据标准项目集合查询多个商品信息
     *
     * @param jdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest
     * @return maps
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryMultiSkuByServiceItem")
    public Map<Long, List<JdhSkuDto>> queryMultiSkuByServiceItem(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest) {
        log.info("ProductApplicationImpl.queryMultiSkuByServiceItem, jdhSkuServiceItemRelRequest={}", JSON.toJSONString(jdhSkuServiceItemRelRequest));
        AssertUtils.nonNull(jdhSkuServiceItemRelRequest, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuServiceItemRelRequest"));
        AssertUtils.hasText(jdhSkuServiceItemRelRequest.getVerticalCode(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("verticalCode"));
        AssertUtils.isNotEmpty(jdhSkuServiceItemRelRequest.getServiceItemIds(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("serviceItemIds"));
        Set<Long> serviceItemIds = jdhSkuServiceItemRelRequest.getServiceItemIds();
        List<JdhSkuItemRel> queryList = serviceItemIds.stream().filter(Objects::nonNull).map(s -> JdhSkuItemRel.builder().skuItemId(String.valueOf(s)).build()).collect(Collectors.toList());
        // 售卖渠道
        Long saleChannelId = jdhSkuServiceItemRelRequest.getSaleChanelId();
        if (saleChannelId == null && VerticalEnum.NET_HOSPITAL_HOME_TEST.getCode().equalsIgnoreCase(jdhSkuServiceItemRelRequest.getVerticalCode())) {
            // 互医分配渠道
            saleChannelId = ProductSaleChannelEnum.NET_HP.getChannelId();
        } else if (saleChannelId == null || VerticalEnum.HOSPITAL_PAID_GUIDANCE_A.getCode().equalsIgnoreCase(jdhSkuServiceItemRelRequest.getVerticalCode())) {
            // 默认渠道
            saleChannelId = ProductSaleChannelEnum.HOSPITAL_PAID_GUIDANCE.getChannelId();
        }
        List<JdhSkuItemRel> jdhSkuItemRelList = jdhSkuRepository.queryJdhSkuItemRelList(queryList, saleChannelId, 1, true);
        Map<Long, List<JdhSkuDto>> maps = new HashMap<>(1);
        if (CollUtil.isEmpty(jdhSkuItemRelList)) {
            for (Long item : serviceItemIds) {
                maps.put(item, Collections.emptyList());
            }
            return maps;
        }
        Set<String> skuIds = jdhSkuItemRelList.stream().filter(Objects::nonNull).map(s -> String.valueOf(s.getSkuId())).collect(Collectors.toSet());

        CompletableFuture<Map<Long, JdhSku>> skuMapsFuture = CompletableFuture.supplyAsync(() -> {
            List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(skuIds.stream().map(s -> JdhSku.builder().skuId(Long.valueOf(s)).build()).collect(Collectors.toList()));
            return jdhSkus.stream().filter(Objects::nonNull).collect(Collectors.toMap(JdhSku::getSkuId, a -> a, (k1, k2) -> k1));
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        CompletableFuture<Map<String, RpcSkuBO>> skuInfoMapsFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.getSkuInfo(skuIds);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        CompletableFuture<Map<String, PriceResultBO>> skuPricesFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.getSkuPriceByPin(skuIds, jdhSkuServiceItemRelRequest.getUserPin());
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        Map<Long, JdhSku> skuMaps = skuMapsFuture.join();
        Map<String, RpcSkuBO> skuInfoMaps = skuInfoMapsFuture.join();
        Map<String, PriceResultBO> skuPrices = skuPricesFuture.join();

        Map<Long, List<JdhSkuDto>> retMap = new HashMap<>(1);
        for (JdhSkuItemRel jdhSkuItemRel : jdhSkuItemRelList) {
            JdhSkuDto jdhSkuDto = ProductApplicationConverter.instance.convertJdhSkuToJdhSkuDto(skuMaps.get(jdhSkuItemRel.getSkuId()));
            jdhSkuDto.setServiceProcessImg(buildSkuServiceProcessImg(jdhSkuDto.getServiceProcessImg()));
            Set<String> groups = Stream.of(DictKeyEnum.SKU_SERVICE_TYPE.getKey()).collect(Collectors.toSet());
            Map<String, List<DictInfo>> dic = dictRepository.queryMultiDictList(groups);
            if (CollUtil.isNotEmpty(dic)) {
                for (DictInfo dictInfo : dic.get(DictKeyEnum.SKU_SERVICE_TYPE.getKey())) {
                    if (dictInfo.getValue().toString().equalsIgnoreCase(String.valueOf(jdhSkuDto.getServiceType()))) {
                        jdhSkuDto.setServiceTypeName(dictInfo.getLabel());
                        break;
                    }
                }
            }
            jdhSkuDto.setSkuId(jdhSkuItemRel.getSkuId());
            if (skuInfoMaps.containsKey(String.valueOf(jdhSkuItemRel.getSkuId()))) {
                RpcSkuBO rpcSkuBO = skuInfoMaps.get(String.valueOf(jdhSkuItemRel.getSkuId()));
                jdhSkuDto.setSkuName(rpcSkuBO.getSkuName());
            }
            if (skuPrices.containsKey(String.valueOf(jdhSkuItemRel.getSkuId()))) {
                PriceResultBO priceResultBO = skuPrices.get(String.valueOf(jdhSkuItemRel.getSkuId()));
                jdhSkuDto.setJdPrice(priceResultBO.getJdPrice());
            }
            List<JdhSkuDto> list = new ArrayList<>();
            if (retMap.containsKey(Long.parseLong(jdhSkuItemRel.getSkuItemId())) && CollUtil.isNotEmpty(retMap.get(Long.parseLong(jdhSkuItemRel.getSkuItemId())))) {
                List<JdhSkuDto> lastList = retMap.get(Long.parseLong(jdhSkuItemRel.getSkuItemId()));
                lastList.add(jdhSkuDto);
                list.addAll(lastList);
            } else {
                list.add(jdhSkuDto);
            }
            retMap.put(Long.parseLong(jdhSkuItemRel.getSkuItemId()), list);
        }
        // 如果存在地址，把不在服务站的SKU过滤掉
        log.info("ProductApplicationImpl.queryMultiSkuByServiceItem,beforeFilter.retMap={}", JSON.toJSONString(retMap));
        filterSkuByStation(retMap, jdhSkuServiceItemRelRequest);
        log.info("ProductApplicationImpl.queryMultiSkuByServiceItem,sorted.retMap={}", JSON.toJSONString(retMap));
        sortRetMap(retMap);
        //对skuList进行服务类型维度的合并
        log.info("ProductApplicationImpl.queryMultiSkuByServiceItem,beforeMerge.retMap={}", JSON.toJSONString(retMap));
        if(jdhSkuServiceItemRelRequest.isMergeSkuByServiceType()) {
            this.mergeSkuByServiceType(retMap);
            log.info("ProductApplicationImpl.queryMultiSkuByServiceItem,after.retMap={}", JSON.toJSONString(retMap));
        }
        return retMap;
    }

    /**
     * 对返回结果进行排序
     * @param retMap
     */
    private void sortRetMap(Map<Long, List<JdhSkuDto>> retMap) {
        for (Map.Entry<Long, List<JdhSkuDto>> entry : retMap.entrySet()) {
            List<JdhSkuDto> filterSkuList = Optional.ofNullable(entry.getValue())
                    .map(List::stream)
                    .orElseGet(Stream::empty)
                    .sorted(Comparator.comparing(JdhSkuDto::getServiceType, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(JdhSkuDto::getSkuId, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            retMap.put(entry.getKey(), filterSkuList);
        }
    }

    /**
     * 如果存在地址，把不在地址对应服务站的SKU过滤掉
     * @param retMap
     * @param jdhSkuServiceItemRelRequest
     */
    private void filterSkuByStation(Map<Long, List<JdhSkuDto>> retMap, JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest) {
        if(Objects.isNull(jdhSkuServiceItemRelRequest.getAddressId())){
            log.info("ProductApplicationImpl.filterSkuByStation addressId is null");
            return;
        }

        // addressId场景下补全fullAddress
        if(jdhSkuServiceItemRelRequest.getAddressId() != null && jdhSkuServiceItemRelRequest.getUserPin() != null && StringUtils.isBlank(jdhSkuServiceItemRelRequest.getFullAddress())){
            UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(jdhSkuServiceItemRelRequest.getUserPin(), jdhSkuServiceItemRelRequest.getAddressId());
            if(userAddressDetail != null){
                jdhSkuServiceItemRelRequest.setFullAddress(userAddressDetail.getFullAddress());
            }
        }

        StationGeoForManQuery stationGeoForManQuery = new StationGeoForManQuery();
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setAddressId(String.valueOf(jdhSkuServiceItemRelRequest.getAddressId()));
        addressDetail.setFullAddress(jdhSkuServiceItemRelRequest.getFullAddress());
        List<AddressDetail> addressDetails = Arrays.asList(addressDetail);
        stationGeoForManQuery.setAddressDetailList(addressDetails);
        List<JdhStationDto> jdhStationDtos = stationApplication.queryJdhStationGeoForMan(stationGeoForManQuery);
        log.info("ProductApplicationImpl.filterSkuByStation 通过地址信息获取地址信息 param:{}，result:{}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(jdhStationDtos));
        if (CollectionUtils.isEmpty(jdhStationDtos)) {
            log.info("ProductApplicationImpl.filterSkuByStation 未找到可用的服务站，清空SKU列表");
            for (Map.Entry<Long, List<JdhSkuDto>> entry : retMap.entrySet()) {
                List<JdhSkuDto> filterSkuList = Optional.ofNullable(entry.getValue()).map(List::stream).orElseGet(Stream::empty).collect(Collectors.toList());
                filterSkuList.forEach(s -> {
                    s.setServiceType(null);
                    s.setServiceTypeName(null);
                });
                retMap.put(entry.getKey(), filterSkuList);
            }
            return;
        }
        List<JdhStation> jdhStations = StationApplicationConverter.ins.convertToJdhStationList(jdhStationDtos.get(0).getStationDtoList());
        Map<Long, List<JdhStationSkuRel>> skuGroup = stationApplication.filterSkuByStation(jdhStations, retMap.values().stream().flatMap(Collection::stream).map(JdhSkuDto::getSkuId).collect(Collectors.toSet()));
        Set<Long> avaiableSkuList = skuGroup.keySet();

        //按照规则进行过滤
        for (Map.Entry<Long, List<JdhSkuDto>> entry : retMap.entrySet()) {
            List<JdhSkuDto> filterSkuList = Optional.ofNullable(entry.getValue()).map(List::stream).orElseGet(Stream::empty).collect(Collectors.toList());
            filterSkuList.forEach(s -> {
                if(!avaiableSkuList.contains(s.getSkuId())){
                    s.setServiceType(null);
                    s.setServiceTypeName(null);
                }
            });
            retMap.put(entry.getKey(), filterSkuList);
        }
    }

    /**
     * 对skuList进行服务类型维度的合并
     * 合并规则为：
     *  1.如果都有骑手 仅返回骑手
     *  2.如果非都有骑手（a.如果都有护士 仅返回护士  b.如果非都有护士 业务保证不会有这种情况 返回护士）
     * @param retMap
     */
    private void mergeSkuByServiceType(Map<Long, List<JdhSkuDto>> retMap) {
        //判断合并时的过滤规则，isKnight
        Boolean isKnight = true;
        for (List<JdhSkuDto> jdhSkuDtos : retMap.values()) {
            Set<Integer> serviceTypeSet = Optional.ofNullable(jdhSkuDtos).map(List::stream).orElseGet(Stream::empty).
                    map(JdhSkuDto::getServiceType).collect(Collectors.toSet());
            //只要有一个不包含骑手类型 标记位置为false 提前返回
            if (!serviceTypeSet.contains(KNIGHT_SERVICE_TYPE)) {
                isKnight = false;
                break;
            }
        }

        //按照规则进行过滤 骑手情况过滤护士检测 非骑手情况过滤骑手
        for (Map.Entry<Long, List<JdhSkuDto>> entry : retMap.entrySet()) {
            Stream<JdhSkuDto> tmpStream = isKnight ? Optional.ofNullable(entry.getValue()).map(List::stream).orElseGet(Stream::empty).
                    filter(skuItem -> !ANGEL_TEST_SERVICE_TYPE.equals(skuItem.getServiceType())) :
                    Optional.ofNullable(entry.getValue()).map(List::stream).orElseGet(Stream::empty).
                            filter(skuItem -> !KNIGHT_SERVICE_TYPE.equals(skuItem.getServiceType()));

            List<JdhSkuDto> filterSkuList =  tmpStream.sorted(Comparator.comparing(JdhSkuDto::getServiceType, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(JdhSkuDto::getSkuId, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            retMap.put(entry.getKey(), filterSkuList);
        }
    }

    @Resource
    private ProductApplication productApplication;
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.listGroupAddress")
    public Map<String, GroupUserAddressDTO> listGroupAddress(OrderUserAddressQuery request) {
        Map<String, GroupUserAddressDTO> res = Maps.newHashMap();
        GroupUserAddressDTO can = new GroupUserAddressDTO();
        can.setGroupName("可用地址列表");
        GroupUserAddressDTO dis = new GroupUserAddressDTO();
        dis.setGroupName("不可用地址列表");
        res.put("canSelect", can);
        res.put("disSelect", dis);

        List<AddressDetailBO> list = jdhAddressRpc.queryAddressList(request.getUserPin());
        if (CollectionUtil.isEmpty(list)) {
            return res;
        }

        if (CollectionUtils.isNotEmpty(request.getSkuNoList())){
            String skuId = request.getSkuNoList().get(0);
            JdhSkuRequest skuRequest = new JdhSkuRequest();
            skuRequest.setSkuId(Long.valueOf(skuId));
            JdhSkuDto skuDto = productApplication.queryJdhSkuInfo(skuRequest);
            // 对于快递模式，不卡服务地址可用范围，能收货就能使用
            if (Objects.nonNull(skuDto) && Objects.equals(skuDto.getServiceType(), ServiceTypeNewEnum.TRANSPORT_TEST.getType())){
                List<UserAddressDetailDTO> userAddressDetailDTOS = ProductApplicationConverter.instance.convertAddressDetailBO2DTO(list);
                can.setList(userAddressDetailDTOS);
                return res;
            }
        }



        List<AddressDetail> addressDetailList = new ArrayList<>();
        for (AddressDetailBO addressDetailBO : list) {
            AddressDetail addressDetail = new AddressDetail();
            addressDetail.setAddressId(String.valueOf(addressDetailBO.getAddressId()));
            addressDetail.setFullAddress(addressDetailBO.getFullAddress());
            addressDetailList.add(addressDetail);
        }
        StationGeoForManQuery build = new StationGeoForManQuery();
        build.setSkuNos(request.getSkuNoList().stream().map(Long::parseLong).collect(Collectors.toSet()));
        build.setAddressDetailList(addressDetailList);
        List<UserAddressDetailDTO> userAddressDetailDTOS = ProductApplicationConverter.instance.convertAddressDetailBO2DTO(list);
        for (UserAddressDetailDTO userAddressDetailDTO : userAddressDetailDTOS) {
            userAddressDetailDTO.setId(userAddressDetailDTO.getAddressId());
        }
        //返回的数据表示地址是高亮
        List<JdhStationDto> jdhStationDtos = new ArrayList<>();
        try {
            jdhStationDtos = stationApplication.queryJdhStationGeoForMan(build);
            log.info("ProductApplicationImpl.listGroupAddress, param:{},获取服务站地址={}",JSON.toJSONString(build), JSON.toJSONString(jdhStationDtos));
        } catch (Exception e) {
            log.error("ProductApplicationImpl.listGroupAddress, 获取服务站错误={}", JSON.toJSONString(build), e);
        }
        if (CollectionUtils.isEmpty(jdhStationDtos)) {
            dis.setList(userAddressDetailDTOS);
            return res;
        }

        // 提取addressDetailList中所有的ID
        Set<String> addressDetailIds = addressDetailList.stream()
                .map(AddressDetail::getAddressId)
                .collect(Collectors.toSet());
        log.info("addressDetailIds数据:{}",JSON.toJSONString(addressDetailIds));

        // 提取jdhStationDtos中所有的ID
        Set<String> jdhStationDtoIds = jdhStationDtos.stream()
                .map(JdhStationDto::getAddressId)
                .collect(Collectors.toSet());
        log.info("jdhStationDtoIds数据:{}",JSON.toJSONString(jdhStationDtoIds));

        // 计算交集
        Set<String> intersection = addressDetailIds.stream()
                .filter(jdhStationDtoIds::contains)
                .collect(Collectors.toSet());
        log.info("intersection交集数据:{},userAddressDetailDTOS:{}",JSON.toJSONString(intersection),JSON.toJSONString(userAddressDetailDTOS));

        List<UserAddressDetailDTO> canList = new ArrayList<>();
        List<UserAddressDetailDTO> disList = new ArrayList<>();
        for (UserAddressDetailDTO userAddressDetailDTO : userAddressDetailDTOS) {
            Long addressId = userAddressDetailDTO.getId();
            if (intersection.contains(String.valueOf(addressId))) {
                canList.add(userAddressDetailDTO);
            } else {
                disList.add(userAddressDetailDTO);
            }
        }
        can.setList(canList);
        dis.setList(disList);
        return res;
    }

    /**
     * 查询商品信息
     *
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryAggregationJdhSkuInfo")
    public JdhSkuDto queryAggregationJdhSkuInfo(JdhSkuRequest request) {
        log.info("ProductApplicationImpl.queryAggregationJdhSkuInfo, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuRequest"));
        AssertUtils.nonNull(request.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(request.getSkuId()).build());
        JdhSkuDto jdSkuCoreData = null;
        if (Boolean.TRUE.equals(request.getQuerySkuCoreData())) {
            // 主站商品数据
            RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(request.getSkuId()));
            if (crsSku == null || StringUtils.isBlank(crsSku.getSkuId()) || StringUtils.isBlank(crsSku.getSkuName())) {
                throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK);
            }
            jdSkuCoreData = ProductApplicationConverter.instance.convertToJdhSkuDto(crsSku);
        }
        if (jdhSku != null) {
            // 消医以后商品数据
            JdhSkuDto jdhSkuCustomDto = ProductApplicationConverter.instance.convertJdhSkuToJdhSkuDto(jdhSku);
            buildSkuExtend(request, jdhSkuCustomDto);
            JdhSkuDto ret = buildJdhSkuDtoWithCoreSku(jdhSkuCustomDto, jdSkuCoreData);
            // 查询商品下项目数据直接返回
            if (Boolean.TRUE.equals(request.getQueryServiceItem())) {
                ret.setServiceItemList(ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(getSkuItemList(request.getSkuId(), request.getSkuItemType() == null ? 1 : request.getSkuItemType())));
            }
            ret.setServiceProcessImg(buildSkuServiceProcessImg(jdhSku.getServiceProcessImg()));
            //将底层的fileId，转为可直接访问的url
            convertFileId2Url(ret);

            //查询配置的结算价格数据
            List<ExternalDomainFeeConfig> externalDomainFeeConfigs = settlementConfigDomainService.queryExternalDomainFeeConfig(SettlementConfigDomainQuery.builder()
                    .domainCode(DomainEnum.PRODUCT)
                    .aggregateCode(ProductAggregateEnum.PRODUCT_SKU)
                    .aggregateId(String.valueOf(ret.getSkuId()))
                    .settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                    .build());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(externalDomainFeeConfigs)) {
                // 提取结算价格
                JdhSettlementFeeDetailConfig feeDetailConfig = externalDomainFeeConfigs.get(0).getDetailConfigListByFeeType(String.valueOf(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getType()));
                ret.setAngelBasicSettlementPrice(Objects.nonNull(feeDetailConfig) ? feeDetailConfig.getFeeAmount() : null);
            }
            log.info("ProductApplicationImpl.queryAggregationJdhSkuInfo, ret={}", JSON.toJSONString(ret));
            return ret;
        }
        // 消医没有异构数据直接返回商品主数据
        return jdSkuCoreData;
    }

    /**
     * 转换url地址信息
     *
     * @param jdhSkuDto jdh sku dto
     */
    @Override
    public void convertFileId2Url(JdhSkuDto jdhSkuDto){
        if(Objects.isNull(jdhSkuDto)){
            return;
        }
        //采样教程轮播图集合
        List<Long> tutorialCarousel = jdhSkuDto.getTutorialCarousel();
        if(CollUtil.isNotEmpty(tutorialCarousel)){
            try {
                List<String> fileUrlList = getFileUrlList(new HashSet<>(tutorialCarousel));
                if(CollUtil.isNotEmpty(fileUrlList)){
                    jdhSkuDto.setTutorialCarouselUrl(fileUrlList);
                }
            } catch (Exception e) {
                log.error("ProductApplicationImpl 处理 tutorialCarousel convertUrl exception",e);
            }
        }

        //采样教程视频
        String tutorialVideo = jdhSkuDto.getTutorialVideo();
        if(StringUtils.isNotBlank(tutorialVideo)){
            try {
                List<String> fileUrlList = getFileUrlList(CollUtil.newHashSet(Long.parseLong(tutorialVideo)));
                if(CollUtil.isNotEmpty(fileUrlList)){
                    jdhSkuDto.setTutorialVideoUrl(fileUrlList.get(NumConstant.NUM_0));
                }
            } catch (Exception e) {
                log.error("ProductApplicationImpl 处理 tutorialVideo convertUrl exception",e);
            }
        }

        //采样教程视频封面
        String tutorialVideoThumbnail = jdhSkuDto.getTutorialVideoThumbnail();
        if(StringUtils.isNotBlank(tutorialVideoThumbnail)){
            try {
                List<String> fileUrlList = getFileUrlList(CollUtil.newHashSet(Long.parseLong(tutorialVideoThumbnail)));
                if(CollUtil.isNotEmpty(fileUrlList)){
                    jdhSkuDto.setTutorialVideoThumbnailUrl(fileUrlList.get(NumConstant.NUM_0));
                }
            } catch (Exception e) {
                log.error("ProductApplicationImpl 处理 tutorialVideoThumbnail convertUrl exception",e);
            }
        }

        String tutorialMethod = jdhSkuDto.getTutorialMethod();
        if(StringUtils.isNotBlank(tutorialMethod)){
            try {
                List<String> fileUrlList = getFileUrlList(CollUtil.newHashSet(Long.parseLong(tutorialMethod)));
                if(CollUtil.isNotEmpty(fileUrlList)){
                    jdhSkuDto.setTutorialMethodUrl(fileUrlList.get(NumConstant.NUM_0));
                }
            } catch (Exception e) {
                log.error("ProductApplicationImpl 处理 tutorialMethod convertUrl exception",e);
            }
        }

        buildActivityFloorUrl(jdhSkuDto.getActivityFloors());
    }

    private void buildActivityFloorUrl(List<ProductActivityFloorDto> activityFloors){
        if (CollectionUtils.isNotEmpty(activityFloors)){
            for (ProductActivityFloorDto activityFloor : activityFloors) {
                List<FilePreSignedUrlDto> urls = fileManageApplication.generateGetUrl(GenerateGetUrlCommand.builder()
                        .domainCode(DomainEnum.PRODUCT.getCode())
                        .fileIds(Sets.newHashSet(activityFloor.getImageUrlFileId()))
                        .isPublic(Boolean.TRUE)
                        .expireTime(DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, NumConstant.NUM_7))
                        .build());
                activityFloor.setImageUrl(urls.get(0).getUrl());
            }
        }
    }

    /**
     * 获取文件网址
     *
     * @param fileIdSet 文件 ID 集
     * @return {@link List }<{@link String }>
     */
    private List<String> getFileUrlList(Set<Long> fileIdSet){
        List<FilePreSignedUrlDto> urlDtoList = fileManageApplication.generateGetUrl(GenerateGetUrlCommand.builder()
                .domainCode(DomainEnum.PRODUCT.getCode())
                .fileIds(fileIdSet)
                .isPublic(Boolean.TRUE)
                .expireTime(DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, NumConstant.NUM_7))
                .build());
        if(CollUtil.isNotEmpty(urlDtoList)){
            return urlDtoList.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 校验健康商品主数据
     *
     * @param request request
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.checkJdSku")
    public JdhSkuDto checkJdSku(JdhSkuRequest request) {
        log.info("ProductApplicationImpl.checkJdSku, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuRequest"));
        AssertUtils.nonNull(request.getSkuId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(request.getSkuId()));
        JdhSkuDto jdhSkuDto = ProductApplicationConverter.instance.convertToJdhSkuDto(crsSku);
        if (jdhSkuDto == null || jdhSkuDto.getSkuId() == null || StringUtils.isBlank(jdhSkuDto.getSkuName())) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK);
        }
        return jdhSkuDto;
    }

    /**
     * 校验健康商品服务项是否在同一个门店下
     *
     * @param jdhSkuDto jdhSkuDto
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.checkSkuServiceItemAllInOneStore")
    public Boolean checkSkuServiceItemAllInOneStore(JdhSkuDto jdhSkuDto, String stationId) {
        JdhSku jdhSku = JdhSku.builder().serviceType(jdhSkuDto.getServiceType()).serviceItemIdList(jdhSkuDto.getServiceItemList().stream().map(ServiceItemDto::getItemId).collect(Collectors.toList())).build();
        return checkSkuServiceItemAllInOneStore(jdhSku, stationId);
    }

    /**
     * 分页查询商品项目关系
     *
     * @param request request
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryPageJdhSkuItemRel")
    public PageDto<JdhSkuItemRelDto> queryPageJdhSkuItemRel(JdhSkuServiceItemRelPageRequest request) {
        log.info("ProductApplicationImpl.queryJdhSkuItemRelPage, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuServiceItemRelPageRequest"));
        if (request.getSaleChanelId() == null) {
            AssertUtils.hasText(request.getVerticalCode(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("verticalCode"));
        }
        JdhSkuItemRel rel = ProductApplicationConverter.instance.requestToModel(request);
        // 售卖渠道
        Long saleChannelId = request.getSaleChanelId();
        if (saleChannelId == null && VerticalEnum.NET_HOSPITAL_HOME_TEST.getCode().equalsIgnoreCase(request.getVerticalCode())) {
            // 互医分配渠道
            saleChannelId = ProductSaleChannelEnum.NET_HP.getChannelId();
        }
        rel.setChannelId(saleChannelId);
        Page<JdhSkuItemRel> page = jdhSkuRepository.queryJdhSkuItemRelPage(rel);
        PageDto<JdhSkuItemRelDto> pageDto = new PageDto<>();
        if (page == null || CollUtil.isEmpty(page.getRecords())) {
            pageDto.setTotalPage(0);
            pageDto.setPageNum(rel.getPageNum());
            pageDto.setPageSize(rel.getPageSize());
            pageDto.setTotalCount(0);
            pageDto.setList(Collections.emptyList());
            return pageDto;
        }
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        pageDto.setList(ProductApplicationConverter.instance.modelToDto(page.getRecords()));
        return pageDto;
    }

    /**
     * 查询商品项目信息
     *
     * @param jdhServiceQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public List<ServiceItemDto> querySkuAndItemList(JdhServiceQuery jdhServiceQuery) {
        JdhSkuItemRel rel = new JdhSkuItemRel();
        rel.setSkuId(jdhServiceQuery.getSkuId());
        List<JdhSkuItemRel> jdhSkuItemRels = jdhSkuRepository.queryJdhSkuItemRelList(rel);
        log.info("ProductApplicationImpl querySkuAndItemList jdhSkuItemRels={}", JSON.toJSONString(jdhSkuItemRels));
        if(CollectionUtils.isNotEmpty(jdhSkuItemRels)) {
            Set<Long> itemIdSet = jdhSkuItemRels.stream().map(itemRel -> Long.valueOf(itemRel.getSkuItemId())).collect(Collectors.toSet());
            ServiceItemQuery serviceItemQuery = new ServiceItemQuery();
            serviceItemQuery.setItemIds(itemIdSet);
            ServiceItemQueryContext serviceItemQueryContext = ProductServiceIndicatorConvertor.ins.convertToServiceItemQueryContext(serviceItemQuery);
            return ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(jdhServiceItemRepository.queryJdhItemList(serviceItemQueryContext));
        }
        return Collections.emptyList();
    }

    /**
     * 申请开通服务记录
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ProductApplicationImpl.createApplyOpenServiceRecord")
    public ApplyOpenServiceRecordDto createApplyOpenServiceRecord(ApplyOpenServiceRecordCmd cmd) {
        // 用户默认地址
        Long addressId = StringUtils.isNotBlank(cmd.getAddressId()) ? Long.valueOf(cmd.getAddressId()) : null;
        UserAddressDetailBO userLastAddress = productDomainService.getUserLastAddress(cmd.getUserPin(), addressId);
        log.info("ProductApplicationImpl createApplyOpenServiceRecord userPin={}, addressId={}, userLastAddress={}", cmd.getUserPin(), addressId, JSON.toJSONString(userLastAddress));
        if (Objects.isNull(userLastAddress)){
            throw new BusinessException(BusinessErrorCode.ADDRESS_NOT_EXIST);
        }

        Long openServiceRecordId = generateIdFactory.getId();
        JdhApplyOpenServiceRecord applyOpenServiceRecord = new JdhApplyOpenServiceRecord();
        applyOpenServiceRecord.setOpenServiceRecordId(openServiceRecordId);
        applyOpenServiceRecord.setUserPin(cmd.getUserPin());
        applyOpenServiceRecord.setSkuId(Long.valueOf(cmd.getSkuNo()));
        applyOpenServiceRecord.setActivateType(NumConstant.NUM_1);
        applyOpenServiceRecord.setProvinceCode(Objects.isNull(userLastAddress.getProvinceId()) ? null : String.valueOf(userLastAddress.getProvinceId()));
        applyOpenServiceRecord.setCityCode(Objects.isNull(userLastAddress.getCityId()) ? null : String.valueOf(userLastAddress.getCityId()));
        applyOpenServiceRecord.setCountyCode(Objects.isNull(userLastAddress.getCountyId()) ? null : String.valueOf(userLastAddress.getCountyId()));
        applyOpenServiceRecord.setTownCode(Objects.isNull(userLastAddress.getTownId()) ? null : String.valueOf(userLastAddress.getTownId()));
        applyOpenServiceRecord.setProvinceName(userLastAddress.getProvinceName());
        applyOpenServiceRecord.setCityName(userLastAddress.getCityName());
        applyOpenServiceRecord.setCountyName(userLastAddress.getCountyName());
        applyOpenServiceRecord.setTownName(userLastAddress.getTownName());
        applyOpenServiceRecord.setAddressDetail(userLastAddress.getAddressDetail());
        applyOpenServiceRecord.setCreateUser(OperatorRoleTypeEnum.USER_SELF.getDesc());
        applyOpenServiceRecord.setUpdateUser(OperatorRoleTypeEnum.USER_SELF.getDesc());
        jdhApplyOpenServiceRecordRepository.save(applyOpenServiceRecord);

        ApplyOpenServiceRecordDto result = new ApplyOpenServiceRecordDto();
        result.setOpenServiceRecordId(openServiceRecordId);
        return result;
    }

    /**
     * 查询可服务区域
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ProductApplicationImpl.queryServiceArea")
    public ServiceAreaDto queryServiceArea(ServiceAreaRequest request) {
        // 查询商品信息
        JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(Long.valueOf(request.getSkuNo())).build());
        if (Objects.isNull(jdhSku)){
            throw new BusinessException(BusinessErrorCode.SKU_INFO_QUERY_FAIL);
        }

        // 用户默认地址
        Long addressId = StringUtils.isNotBlank(request.getAddressId()) ? Long.valueOf(request.getAddressId()) : null;
        UserAddressDetailBO userLastAddress = productDomainService.getUserLastAddress(request.getUserPin(), addressId);
        log.info("ProductApplicationImpl queryServiceArea userPin={}, addressId={}, userLastAddress={}", request.getUserPin(), addressId, JSON.toJSONString(userLastAddress));
        if (Objects.isNull(userLastAddress)){
            throw new BusinessException(BusinessErrorCode.ADDRESS_NOT_EXIST);
        }

        // 根据地址查询经纬度
        AngelStationAddressRequest addressRequest = new AngelStationAddressRequest();
        addressRequest.setAddress(userLastAddress.getFullAddress());
        AngelStationLatAndLngDto latAndLngDto = stationApplication.queryAddressLatAndLng(addressRequest);
        log.info("ProductApplicationImpl queryServiceArea addressRequest={}, latAndLngDto={}", JSON.toJSONString(addressRequest), JSON.toJSONString(latAndLngDto));
        if (Objects.isNull(latAndLngDto)){
            throw new BusinessException(BusinessErrorCode.ADDRESS_NOT_EXIST);
        }

        // 查询商品关联的服务站
        List<JdhStationSkuRel> stationSkuRelList = stationApplication.queryStationSkuRelBySku(new HashSet<>(Collections.singletonList(Long.valueOf(request.getSkuNo()))));
        log.info("ProductApplicationImpl queryServiceArea stationSkuRelList={}", JSON.toJSONString(stationSkuRelList));
        if (CollectionUtils.isEmpty(stationSkuRelList)){
            throw new BusinessException(BusinessErrorCode.ANGEL_STATION_NOT_EXIST);
        }
        Set<Long> stationIds = stationSkuRelList.stream().map(JdhStationSkuRel::getStationId).collect(toSet());
        log.info("ProductApplicationImpl queryServiceArea stationIds={}", JSON.toJSONString(stationIds));

        // 商详服务区域地图配置
        JSONObject obj = JSON.parseObject(duccConfig.getProductServiceAreaMapConfig());
        // 服务资源类型
        List<Integer> angelTypes = JSON.parseArray(JSON.parseObject(obj.getString("skuServiceTypeMappingAngelType")).getString(String.valueOf(jdhSku.getServiceType())), Integer.class);
        // 直辖市：北京、上海、天津、重庆
        List<Integer> directCityIds = JSON.parseArray(obj.getString("directCityIds"), Integer.class);

        // 查询服务站
        StationQuery stationQuery = new StationQuery();
        stationQuery.setStationIds(stationIds);
        stationQuery.setAngelStationStatus(NumConstant.NUM_1);
        stationQuery.setAngelTypes(angelTypes);
        if (directCityIds.contains(userLastAddress.getProvinceId())){
            stationQuery.setProvinceCode(userLastAddress.getProvinceId());
        }else {
            stationQuery.setCityCode(userLastAddress.getCityId());
        }
        List<AngelStationDto> angelStationList = stationApplication.queryJdhStation(stationQuery);
        log.info("ProductApplicationImpl queryServiceArea search stationQuery={}, angelStationList={}", JSON.toJSONString(stationQuery), JSON.toJSONString(angelStationList));

        // 该城市有服务，则展示该城市的整体围栏
        if (CollectionUtils.isNotEmpty(angelStationList)){
            if (obj.getBoolean("cityFenceIntegrationSwitch")){
                return processAdjacentDuplicatesCityFence(angelStationList, latAndLngDto, userLastAddress);
            }else {
                return processCityFence(angelStationList, latAndLngDto, userLastAddress);
            }
        }

        // 该城市无服务，则展示全国已开通服务的城市图
        return transNationalServiceAreaCoordinates(angelTypes, directCityIds, latAndLngDto, userLastAddress, stationIds);
    }

    /**
     * 该城市无服务，则展示全国已开通服务的城市图
     * @param angelTypes
     * @param directCityIds
     * @param latAndLngDto
     * @param userLastAddress
     * @param stationIds
     * @return
     */
    private ServiceAreaDto transNationalServiceAreaCoordinates(List<Integer> angelTypes, List<Integer> directCityIds, AngelStationLatAndLngDto latAndLngDto, UserAddressDetailBO userLastAddress, Set<Long> stationIds) {
        log.info("ProductApplicationImpl transNationalServiceAreaCoordinates start");
        // 城市坐标点地里数据
        List<Map<String, Object>> features = new ArrayList<>();
        // 城市名称
        List<String> cityNameList = new ArrayList<>();

        // 查询直辖市服务站
        StationQuery directCityStationQuery = new StationQuery();
        directCityStationQuery.setStationIds(stationIds);
        directCityStationQuery.setAngelStationStatus(NumConstant.NUM_1);
        directCityStationQuery.setAngelTypes(angelTypes);
        directCityStationQuery.setProvinceCodeList(directCityIds);
        List<AngelStationDto> directCityStationList = stationApplication.queryJdhStation(directCityStationQuery);
        log.info("transNationalServiceAreaCoordinates directCityStation directCityStationQuery={}, directCityStationList={}", JSON.toJSONString(directCityStationQuery), JSON.toJSONString(directCityStationList));
        if (CollectionUtils.isNotEmpty(directCityStationList)){
            Map<String, List<AngelStationDto>> directCityStationMap = directCityStationList.stream().filter(s->StringUtils.isNotBlank(s.getProvinceCode()))
                    .collect(Collectors.groupingBy(AngelStationDto::getProvinceCode));
            for (Map.Entry<String, List<AngelStationDto>> entry : directCityStationMap.entrySet()) {
                List<AngelStationDto> angelStationList = entry.getValue().stream().filter(s -> StringUtils.isNotBlank(s.getFenceRangeCenterLng())
                        && StringUtils.isNotBlank(s.getFenceRangeCenterLat())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(angelStationList)){
                    continue;
                }
                AngelStationDto s = angelStationList.get(0);
                Map<String, Object> featureMap = new HashMap<>();

                Map<String, Object> geometry = new HashMap<>();
                geometry.put("coordinates", Arrays.asList(Double.valueOf(s.getFenceRangeCenterLng()),Double.valueOf(s.getFenceRangeCenterLat())));
                geometry.put("type","Point");

                Map<String, Object> properties = new HashMap<>();
                properties.put("cityCode",s.getProvinceCode());
                properties.put("cityName",s.getProvinceName());
                properties.put("angelStationId",s.getAngelStationId());
                properties.put("angelStationName",s.getAngelStationName());
                properties.put("fullAddress",s.getFullAddress());


                featureMap.put("geometry",geometry);
                featureMap.put("properties", properties);
                featureMap.put("type","Feature");

                features.add(featureMap);
                cityNameList.add(s.getProvinceName());
            }
        }

        // 全国二级城市（非直辖市）服务站
        StationQuery secondCityStationQuery = new StationQuery();
        secondCityStationQuery.setStationIds(stationIds);
        secondCityStationQuery.setAngelStationStatus(NumConstant.NUM_1);
        secondCityStationQuery.setAngelTypes(angelTypes);
        secondCityStationQuery.setNotProvinceCodeList(directCityIds);
        List<AngelStationDto> secondCityStationList = stationApplication.queryJdhStation(secondCityStationQuery);
        log.info("transNationalServiceAreaCoordinates secondCity secondCityStationQuery={}, secondCityStationList={}", JSON.toJSONString(secondCityStationQuery), JSON.toJSONString(secondCityStationList));
        if (CollectionUtils.isNotEmpty(secondCityStationList)){
            Map<String, List<AngelStationDto>> secondCityStationMap = secondCityStationList.stream().filter(s->StringUtils.isNotBlank(s.getCityCode()))
                    .collect(Collectors.groupingBy(AngelStationDto::getCityCode));
            for (Map.Entry<String, List<AngelStationDto>> entry : secondCityStationMap.entrySet()) {
                List<AngelStationDto> angelStationList = entry.getValue().stream().filter(s -> StringUtils.isNotBlank(s.getFenceRangeCenterLng())
                        && StringUtils.isNotBlank(s.getFenceRangeCenterLat())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(angelStationList)){
                    continue;
                }
                AngelStationDto s = angelStationList.get(0);
                Map<String, Object> featureMap = new HashMap<>();

                Map<String, Object> geometry = new HashMap<>();
                geometry.put("coordinates", Arrays.asList(Double.valueOf(s.getFenceRangeCenterLng()),Double.valueOf(s.getFenceRangeCenterLat())));
                geometry.put("type","Point");

                Map<String, Object> properties = new HashMap<>();
                properties.put("cityCode",s.getCityCode());
                properties.put("cityName",s.getCityName());
                properties.put("angelStationId",s.getAngelStationId());
                properties.put("angelStationName",s.getAngelStationName());
                properties.put("fullAddress",s.getFullAddress());

                featureMap.put("geometry",geometry);
                featureMap.put("properties", properties);
                featureMap.put("type","Feature");

                features.add(featureMap);
                cityNameList.add(s.getCityName());
            }
        }
        log.info("transNationalServiceAreaCoordinates features={}", JSON.toJSONString(features));

        // 返回结果
        ServiceAreaDto result = new ServiceAreaDto();
        result.setUserPositionLng(latAndLngDto.getAngelStationLng());
        result.setUserPositionLat(latAndLngDto.getAngelStationLat());
        result.setAddressDetail(userLastAddress.getFullAddress());
        result.setFeatures(features);
        result.setMapType(NumConstant.NUM_2);
        result.setCityNameList(cityNameList);
        return result;
    }

    /**
     * 判断两个围栏是否重叠
     * @param fence1 围栏1的坐标点集
     * @param fence2 围栏2的坐标点集
     * @return true表示存在重叠
     */
    public boolean polygonOverlap(GeometryFactory gf, Coordinate[] fence1, Coordinate[] fence2) {
        Polygon p1 = gf.createPolygon(fence1);
        Polygon p2 = gf.createPolygon(fence2);
        return p1.intersects(p2);  // 关键重叠判断:ml-citation{ref="7" data="citationList"}
    }

    /**
     * 递归处理相邻相等的元素，直到没有相邻相等元素为止
     * @param input 原始集合
     * @return 处理后的最终集合
     */
    public List<Coordinate[]> processAdjacentDuplicates(GeometryFactory gf, List<Coordinate[]> input) {
        // 基本情况：集合为空或只有一个元素时直接返回
        if (input.size() <= 1) {
            return new ArrayList<>(input);
        }

        boolean isDuplicate = false;
        List<Coordinate[]> result = new ArrayList<>();
        int i = 0;
        while (i < input.size()) {
            // 检查是否有相邻且相等的元素
            if (i < input.size() - 1 && this.polygonOverlap(gf, input.get(i), input.get(i + 1))) {
                // 将firstCoordinate与secondCoordinate进行合并
                List<Coordinate[]> coordinates = new ArrayList<>();
                coordinates.add(input.get(i));
                coordinates.add(input.get(i + 1));

                List<Geometry> geometries = new ArrayList<>();
                coordinates.forEach(c->{
                    // 添加多边形
                    geometries.add(gf.createPolygon(c));
                });

                // 可以对集合中的几何对象进行空间运算
                Geometry geometryUnion = UnaryUnionOp.union(geometries);
                // 合并后的Coordinate
                Coordinate[] mergeCoordinate = geometryUnion.getCoordinates();
                result.add(mergeCoordinate);
                i += 2; // 跳过已处理的下一个元素
                isDuplicate = true;
            } else {
                result.add(input.get(i));
                i += 1;
            }
        }

        // 如果结果与原集合相同，说明没有需要合并的元素了
        if (!isDuplicate) {
            return result;
        } else {
            // 否则递归处理新生成的集合
            return processAdjacentDuplicates(gf, result);
        }
    }

    /**
     * 城市围栏去重
     * @param angelStationList
     * @param latAndLngDto
     * @param userLastAddress
     * @return
     */
    private ServiceAreaDto processAdjacentDuplicatesCityFence(List<AngelStationDto> angelStationList, AngelStationLatAndLngDto latAndLngDto, UserAddressDetailBO userLastAddress) {
        log.info("ProductApplicationImpl processAdjacentDuplicatesCityFence start");
        // 创建GeometryFactory，可指定PrecisionModel和SRID
        GeometryFactory factory = new GeometryFactory(new PrecisionModel(), 4326);

        // 服务站经纬度
        List<Coordinate[]> coordinateList = this.convertStationCoordinateList(angelStationList);

        // 递归处理相邻相等的元素，直到没有相邻相等元素为止
        List<Coordinate[]> coordinateArr = this.processAdjacentDuplicates(factory, coordinateList);

        List<List<Coordinate>> coordinateCollect = new ArrayList<>();
        for (Coordinate[] array : coordinateArr) {
            coordinateCollect.add(Arrays.asList(array));
        }

        // 去重整合后的地里数据
        List<List<List<Double>>> arrayList = new ArrayList<>(1);
        coordinateCollect.forEach(allCoordinates->{
            // 围栏地理数据
            List<List<Double>> fenceBoundaryTotalList  = new ArrayList<>();
            allCoordinates.forEach(coordinate->{
                // 经度
                double x = coordinate.getX();
                // 维度
                double y = coordinate.getY();
                List<Double> fenceBoundaryList = new ArrayList<>();
                fenceBoundaryList.add(x);
                fenceBoundaryList.add(y);
                fenceBoundaryTotalList.add(fenceBoundaryList);
            });

            arrayList.add(fenceBoundaryTotalList);
        });

        Map<String,Object> geometryMap = new HashMap<>();
        geometryMap.put("type","Polygon");
        geometryMap.put("coordinates", arrayList);
        // 返回结果
        ServiceAreaDto result = new ServiceAreaDto();
        result.setUserPositionLng(latAndLngDto.getAngelStationLng());
        result.setUserPositionLat(latAndLngDto.getAngelStationLat());
        result.setAddressDetail(userLastAddress.getFullAddress());
        result.setGeometryMap(geometryMap);
        result.setMapType(NumConstant.NUM_1);
        result.setAngelStationMap(angelStationList.stream().collect(Collectors.toMap(AngelStationDto::getAngelStationId, AngelStationDto::getAngelStationName)));
        return result;
    }

    private List<Coordinate[]> convertStationCoordinateList(List<AngelStationDto> angelStationList) {
        Map<Long, List<List<Double>>> polygonFenceBoundaryMap = new HashMap<>();
        angelStationList.forEach(a->{
            polygonFenceBoundaryMap.put(a.getAngelStationId(), a.getFenceBoundaryList());
        });

        List<Coordinate[]> coordinateList = new ArrayList<>();
        for (Map.Entry<Long, List<List<Double>>> entry : polygonFenceBoundaryMap.entrySet()) {
            List<Coordinate> polygonCoordinateList = new ArrayList<>();
            entry.getValue().forEach(p->{
                Double lng = p.get(0);
                Double lat = p.get(1);
                polygonCoordinateList.add(new Coordinate(lng,lat));
            });
            coordinateList.add(polygonCoordinateList.toArray(new Coordinate[0]));
        }
        log.info("convertStationCoordinateList coordinateList count={}", coordinateList.size());
        log.info("convertStationCoordinateList coordinateList={}", JSON.toJSONString(coordinateList));
        return coordinateList;
    }

    /**
     * 该城市有服务，则展示该城市的整体围栏
     * @param angelStationList
     * @param latAndLngDto
     * @param userLastAddress
     * @return
     */
    private ServiceAreaDto processCityFence(List<AngelStationDto> angelStationList, AngelStationLatAndLngDto latAndLngDto, UserAddressDetailBO userLastAddress) {
        log.info("ProductApplicationImpl processCityFence start");
        // 围栏地理数据
        List<List<Double>> fenceBoundaryTotalList = new ArrayList<>();
        angelStationList.forEach(a->{
            fenceBoundaryTotalList.addAll(a.getFenceBoundaryList());
        });
        log.info("ProductApplicationImpl processCityFence fenceBoundaryTotalList={}", JSON.toJSONString(fenceBoundaryTotalList));

        Map<String,Object> geometryMap = new HashMap<>();
        geometryMap.put("type","Polygon");
        List<List<List<Double>>> arrayList = new ArrayList<>(1);
        arrayList.add(fenceBoundaryTotalList);
        geometryMap.put("coordinates", arrayList);

        // 返回结果
        ServiceAreaDto result = new ServiceAreaDto();
        result.setUserPositionLng(latAndLngDto.getAngelStationLng());
        result.setUserPositionLat(latAndLngDto.getAngelStationLat());
        result.setAddressDetail(userLastAddress.getFullAddress());
        result.setGeometryMap(geometryMap);
        result.setMapType(NumConstant.NUM_1);
        result.setAngelStationMap(angelStationList.stream().collect(Collectors.toMap(AngelStationDto::getAngelStationId, AngelStationDto::getAngelStationName)));
        return result;
    }

    /**
     * 商品评价列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ProductApplicationImpl.getCommentPageList")
    public EvaluateInfoDTO getCommentPageList(CommentPageRequest request) {
        CommentPageParam commentPageParam = ProductApplicationConverter.instance.convertCommentPageParam(request);
        String commentListResult = skuInfoRpc.getCommentPageList(commentPageParam);
        log.info("ProductApplicationImpl getCommentPageList commentPageParam={}, commentListResult={}", JSON.toJSONString(commentPageParam), commentListResult);
        if(StringUtil.isNotBlank(commentListResult)){
            CommentUgcListResult commentUgcListResult = JsonUtil.parseObject(commentListResult, CommentUgcListResult.class);
            if(Objects.nonNull(commentUgcListResult)
                    && Objects.nonNull(commentUgcListResult.getSummary())
                    && StringUtils.isNotBlank(commentUgcListResult.getSummary().getCommentCountStr())
                    && !commentUgcListResult.getSummary().getCommentCountStr().equals("0")
                    && CollectionUtils.isNotEmpty(commentUgcListResult.getComments())){

                // 构建评价数据
                EvaluateInfoBO evaluateInfoBO = productDomainService.buildEvaluateInfoDTO(commentUgcListResult);
                log.info("getCommentPageList evaluateInfoBO={}", JSON.toJSONString(evaluateInfoBO));
                EvaluateInfoDTO evaluateInfoDTO = ProductApplicationConverter.instance.modelToDto(evaluateInfoBO);
                // 填充好评标签
                fullGoodCommentTag(evaluateInfoDTO, request.getSkuNo());
                evaluateInfoDTO.setPageNum(request.getPageNum());
                return evaluateInfoDTO;
            }
        }
        return null;
    }

    /**
     * 填充好评标签
     * @param evaluateInfoDTO
     * @param skuNo
     */
    private void fullGoodCommentTag(EvaluateInfoDTO evaluateInfoDTO, String skuNo){
        try {
            log.info("ProductApplicationImpl fullGoodCommentTag evaluateInfoDTO={}, skuNo={}", JSON.toJSONString(evaluateInfoDTO), skuNo);
            if (Objects.isNull(evaluateInfoDTO) || StringUtils.isBlank(skuNo)){
                log.info("ProductApplicationImpl fullGoodCommentTag param empty");
                return;
            }
            List<CommentUgcDTO> commentUgcList = evaluateInfoDTO.getList();
            if (CollectionUtils.isEmpty(commentUgcList)){
                log.info("ProductApplicationImpl fullGoodCommentTag commentUgcList empty");
                return;
            }

            List<CompletableFuture<Map<String, List<String>>>> futureList = Collections.synchronizedList(Lists.newArrayList());
            commentUgcList.forEach(commentUgc -> {
                CompletableFuture<Map<String, List<String>>> future = CompletableFuture.supplyAsync(()->{
                    try {
                        AngelMainRequest angelMainRequest = new AngelMainRequest();
                        angelMainRequest.setUserPin(commentUgc.getPin());
                        angelMainRequest.setSkuId(Collections.singletonList(Long.valueOf(skuNo)));
                        angelMainRequest.setOnlyBest(true);
                        // 增加服务评价标签展示，展示的标签为客户评价时好评的标签-分数大于等于4分的
                        List<UserValuationDto> userValuationList = angelEcologyApplication.queryUserValuationTag(angelMainRequest);
                        log.info("ProductApplicationImpl fullGoodCommentTag userValuationList={}", JSON.toJSONString(userValuationList));
                        Map<String, List<String>> goodCommentTagMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(userValuationList)){
                            List<String> valuationTags = userValuationList.get(0).getValuationTag();
                            if (CollectionUtils.isNotEmpty(valuationTags)){
                                goodCommentTagMap.put(commentUgc.getGuid(), valuationTags);
                            }
                        }
                        return goodCommentTagMap;
                    } catch (Exception e) {
                        log.error("ProductApplicationImpl fullGoodCommentTag supplyAsync error e", e);
                    }
                    return null;
                },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL)).exceptionally(throwable -> {
                    log.error("ProductApplicationImpl fullGoodCommentTag error:"+throwable.getMessage(),throwable);
                    return null;
                });
                futureList.add(future);
            });

            try {
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture<?>[futureList.size()])).get(500, TimeUnit.MILLISECONDS);
            }catch (Exception e){
                log.error("ProductApplicationImpl fullGoodCommentTag completableFuture allOf error:"+e.getMessage(),e);
            }

            List<Map<String, List<String>>> goodCommentTagMapList = futureList.stream().filter(Objects::nonNull).map(e -> e.getNow(null))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            log.info("ProductApplicationImpl fullGoodCommentTag goodCommentTagMapList={}", JSON.toJSONString(goodCommentTagMapList));

            for (CommentUgcDTO commentUgc : commentUgcList) {
                for (Map<String, List<String>> map : goodCommentTagMapList) {
                    List<String> tagList = map.get(commentUgc.getGuid());
                    if (CollectionUtils.isNotEmpty(tagList)){
                        commentUgc.setGoodCommentTagList(tagList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("ProductApplicationImpl fullGoodCommentTag error e", e);
        }
    }



    /**
     * 导入商品
     *
     * @param cmd cmd
     * @return true
     */
    @Override
    public Boolean importSku(JdhSkuImportCmd cmd) {
        log.info("ProductApplicationImpl#importSku cmd={}", JSON.toJSONString(cmd));
        AssertUtils.nonNull(cmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("JdhSkuImportCmd"));
        AssertUtils.hasText(cmd.getFileId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("文件id"));
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(Long.parseLong(cmd.getFileId())).build());
        if (jdhFile == null) {
            throw new BusinessException(SupportErrorCode.SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST);
        }
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.IMPORT_PRODUCT_SKU_IMPORT_LOCK_KEY, cmd.getErp());
        boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.IMPORT_PRODUCT_SKU_IMPORT_LOCK_KEY.getExpireTime(), RedisKeyEnum.IMPORT_PRODUCT_SKU_IMPORT_LOCK_KEY.getExpireTimeUnit());
        if (!lock) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_IMPORT_EXIST);
        }
        try {
            String logId = Objects.toString(MDC.get("PFTID"), null);
            // 锁释放在导入逻辑,如果在这个try finally释放，异步锁会立刻释放
            CompletableFuture.runAsync(() -> importProductSkuExcel(logId, jdhFile.getFilePath(), cmd.getErp()), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductServiceItemApplicationImpl#importProductItem exception", e);
        }
        return Boolean.FALSE;
    }

    /**
     * 查询加项商品
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ProductApplicationImpl.queryAddProductDtoBySkuList")
    public Map<Long, AddProductDto> queryAddProductDtoBySkuList(JdhAddProductRequest request) {
        // 获取地址信息
        CompletableFuture<AddressDetailBO> addressDetailBoFuture = CompletableFuture.supplyAsync(() -> {
            AddressDetailBO addressDetailBo = null;
            try {
                UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(request.getUserPin(), request.getAddressId());
                if (Objects.nonNull(userAddressDetail)){
                    addressDetailBo = new AddressDetailBO();
                    BeanUtils.copyProperties(userAddressDetail, addressDetailBo);
                    return addressDetailBo;
                }
            } catch (Exception e) {
                log.error("ProductApplicationImpl queryAddProductDtoBySkuList getUserLastAddress error e", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 根据mainSkuIdSet查询运营端配置的加项商品Map
        CompletableFuture<Map<Long, List<JdhSkuRelDto>>> mainSkuAddProductMapFuture = CompletableFuture.supplyAsync(() -> {
            try {
                JdhSkuRelRequest relRequest = new JdhSkuRelRequest();
                relRequest.setParentSkuIdSet(request.getMainSkuSet());
                relRequest.setSkuRelType(SkuRelTypeEnum.ADD_ITEM.getType());
                return queryJdhSkuRelBatch(relRequest);
            } catch (Exception e) {
                log.error("ProductApplicationImpl queryAddProductDtoBySkuList queryJdhSkuRelBatch error e", e);
            }
            return null;
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        AddressDetailBO addressDetailBo = addressDetailBoFuture.join();
        Map<Long, List<JdhSkuRelDto>> mainSkuAddProductMap = mainSkuAddProductMapFuture.join();

        if (MapUtils.isEmpty(mainSkuAddProductMap)){
            log.info("ProductApplicationImpl queryAddProductDtoBySkuList mainSkuAddProductMap empty");
            return Collections.emptyMap();
        }
        // 商品关联sku
        List<JdhSkuRelDto> jdhSkuRelList = new ArrayList<>();
        new ArrayList<>(mainSkuAddProductMap.values()).forEach(jdhSkuRelList::addAll);
        log.info("ProductApplicationImpl queryAddProductDtoBySkuList jdhSkuRelList={}", JSON.toJSONString(jdhSkuRelList));

        // 查询商品实时价
        Set<Long> skuIdList = jdhSkuRelList.stream().map(JdhSkuRelDto::getSkuId).collect(Collectors.toSet());
        Set<String> skuIdSet = skuIdList.stream().map(String::valueOf).collect(Collectors.toSet());
        Map<String, PriceResultBO> skuPriceMap = new HashMap<>();
        PriceInfoResponseBO priceInfoResponseBO = skuInfoRpc.getSkuPriceAndPromotionByPin(skuIdSet, request.getUserPin(), addressDetailBo);
        if (Objects.nonNull(priceInfoResponseBO) && Objects.nonNull(priceInfoResponseBO.getPriceMap())) {
            skuPriceMap = priceInfoResponseBO.getPriceMap();
        }

        Map<Long, AddProductDto> addProductDtoMap = new HashMap<>();

        for (Map.Entry<Long, List<JdhSkuRelDto>> mainSkuEntry : mainSkuAddProductMap.entrySet()) {
            if(CollectionUtils.isEmpty(mainSkuEntry.getValue())){
                continue;
            }
            /**step2.以mainSkuId+addressInfo查询服务站，得到服务站：mainSkuStationList*/
            List<AddressDetail> addressDetailList = new ArrayList<>();
            AddressDetail addressDetail = new AddressDetail();
            addressDetail.setFullAddress(request.getFullAddress());
            addressDetail.setAddressId(String.valueOf(request.getAddressId()));
            addressDetailList.add(addressDetail);
            StationGeoQuery stationGeoQuery = new StationGeoQuery();
            stationGeoQuery.setAddressDetailList(addressDetailList);
            stationGeoQuery.setSkuNo(mainSkuEntry.getKey());
            List<JdhStationDto> jdhStationDtos = stationApplication.queryJdhStationGeo(stationGeoQuery);
            log.info("ProductApplicationImpl.queryAddProductDtoBySkuList.stationGeoQuery={},jdhStationDtos={}",JSON.toJSONString(stationGeoQuery),JSON.toJSONString(jdhStationDtos));
            //基准服务站str
            List<Long> stationPivotList = new ArrayList<>();
            Optional.ofNullable(jdhStationDtos).map(List::stream).orElseGet(Stream::empty).forEach(jdhStationDto -> {
                Optional.ofNullable(jdhStationDto.getStationDtoList()).map(List::stream).orElseGet(Stream::empty).forEach(stationDto -> {
                    stationPivotList.add(stationDto.getAngelStationId());
                });
            });
            log.info("ProductApplicationImpl.queryAddProductDtoBySkuList.request={},stationPivotStr={}",JSON.toJSONString(request),JSON.toJSONString(stationPivotList));
            /**step3.加项品sku + addressInfo，得到加项品的服务站Map<String,addSkuStationList>*/
            QuerySkuAndStationRefRequest refRequest = new QuerySkuAndStationRefRequest();
            List<Long> addProductList = Optional.ofNullable(mainSkuEntry.getValue()).map(List::stream).orElseGet(Stream::empty).map(JdhSkuRelDto::getSkuId).collect(Collectors.toList());
            refRequest.setSkuNos(addProductList);
            refRequest.setAddressDetail(addressDetail);
            List<SkuAndStationRefDto> skuAndStationRefDtos = stationApplication.querySkuAndStationRef(refRequest);
            log.info("ProductApplicationImpl.queryAddProductDtoBySkuList.refRequest={},skuAndStationRefDtos={}",JSON.toJSONString(refRequest),JSON.toJSONString(skuAndStationRefDtos));
            //step4.convert angelStationId,skuNos 2 skuNo angelStationIdStr
            Map<Long, List<Long>> skuStationListMap = new HashMap<>();
            for (SkuAndStationRefDto skuAndStationRefDto : skuAndStationRefDtos) {
                for (Long skuNo : skuAndStationRefDto.getSkuNos()) {
                    if (skuStationListMap.containsKey(skuNo)) {
                        List tmpList = skuStationListMap.get(skuNo);
                        tmpList.add(skuAndStationRefDto.getAngelStationId());
                        skuStationListMap.put(skuNo, tmpList);
                    } else {
                        List tmpList = new ArrayList();
                        tmpList.add(skuAndStationRefDto.getAngelStationId());
                        skuStationListMap.put(skuNo, tmpList);
                    }
                }
            }
            log.info("ProductApplicationImpl.queryAddProductDtoBySkuList.skuStationStrMap={}",JSON.toJSONString(skuStationListMap));
            if (MapUtils.isEmpty(skuStationListMap)) {
                return addProductDtoMap;
            }
            /**step5.以mainSkuStationList作为基准对加项品进行服务站完全匹配的逻辑过滤 */
            Set<Long> skuCanAddedSet = new HashSet<>();
            for (Map.Entry<Long, List<Long>> entry : skuStationListMap.entrySet()) {
                // 全部匹配调整为全部交集
                //if (isEqualList(stationPivotList, entry.getValue())) {
                if (entry.getValue().containsAll(stationPivotList)) {
                    skuCanAddedSet.add(entry.getKey());
                }
            }
            /**step5.拼装返回结果*/
            AddProductDto addProductDto = new AddProductDto();
            List<AddSkuItemDTO> addSkuItemDTOS = new ArrayList<>();
            List<JdhSkuRelDto> matchJdhSkuRelDto = new ArrayList<>();
            for (JdhSkuRelDto jdhSkuRelDto : mainSkuEntry.getValue()) {
                if (skuCanAddedSet.contains(jdhSkuRelDto.getSkuId())) {
                    matchJdhSkuRelDto.add(jdhSkuRelDto);
                }
            }
            log.info("ProductApplicationImpl.queryAddProductDtoBySkuList.matchJdhSkuRelDto={}",JSON.toJSONString(matchJdhSkuRelDto));
            for (JdhSkuRelDto s : matchJdhSkuRelDto) {
                AddSkuItemDTO addSkuItemDTO = JSON.parseObject(JSON.toJSONString(s), AddSkuItemDTO.class);
                addSkuItemDTO.setAddSkuName(s.getSkuShortName());
                addSkuItemDTO.setFinalPriceStr(new StringBuffer("¥").append(skuPriceMap.get(String.valueOf(addSkuItemDTO.getSkuId())).getJdPrice()).append("/份").toString());
                addSkuItemDTOS.add(addSkuItemDTO);
            }

            addProductDto.setAddSkuItemDTOS(addSkuItemDTOS);
            addProductDtoMap.put(mainSkuEntry.getKey(), addProductDto);
        }
        return addProductDtoMap;
    }

    private boolean isEqualList(List<Long> leftList, List<Long> rightList){
        if(CollectionUtils.isEmpty(leftList) && CollectionUtils.isEmpty(rightList)){
            return true;
        } else if(CollectionUtils.isEmpty(leftList) && CollectionUtils.isNotEmpty(rightList)){
            return false;
        } else if(CollectionUtils.isNotEmpty(leftList) && CollectionUtils.isEmpty(rightList)){
            return false;
        }
        if(leftList.size() != rightList.size()){
            return false;
        }
        return leftList.stream().filter(s -> !rightList.contains(s)).collect(Collectors.toList()).size() == 0;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryProductCouponList")
    public List<ProductCouponDTO> queryProductCouponList(ProductCouponRequest request) {
        log.info("ProductApplicationImpl queryProductCouponList request={}", JSON.toJSONString(request));
        if (!JSON.parseObject(duccConfig.getProductCouponConfig()).getBoolean("queryProductCouponListSwitch")){
            return Lists.newArrayList();
        }
        if (StringUtils.isAnyBlank(request.getUserPin(), request.getSkuNo())){
            return Lists.newArrayList();
        }
        FindCouponsRpcParam couponsRpcParam = new FindCouponsRpcParam();
        couponsRpcParam.setPin(request.getUserPin());
        couponsRpcParam.setCouponSkuAttributeList(buildCouponSkuAttributeList(request.getSkuNo(), request.getUserPin()));

        // 券购-可用券查询
        CompletableFuture<Map<String, List<CouponInfoBO>>> findCanUseCouponsFuture = CompletableFuture.supplyAsync(() -> {
            return couponServiceRpc.findCanUseCoupons(couponsRpcParam);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 券购-可领券查询
        CompletableFuture<Map<String, List<CouponInfoBO>>> findJoinActivesFuture = CompletableFuture.supplyAsync(() -> {
            return couponServiceRpc.findJoinActives(couponsRpcParam);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        PriceInfoResponseBO priceInfoResponseBO = null;
        if (BooleanUtil.isTrue(request.getQueryGiftCash())){
            // 查询sku实时价格
            CompletableFuture<PriceInfoResponseBO> priceInfoResponseBOFuture = CompletableFuture.supplyAsync(() -> {
                Set<String> skuIds = Sets.newHashSet(request.getSkuNo());
                return skuInfoRpc.getSkuPriceAndPromotionByPin(skuIds, request.getUserPin(), null);
            },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));
            priceInfoResponseBO = priceInfoResponseBOFuture.join();
        }

        Map<String, List<CouponInfoBO>> canUseCouponsMap = findCanUseCouponsFuture.join();
        Map<String, List<CouponInfoBO>> joinActivesMap = findJoinActivesFuture.join();
        log.info("ProductApplicationImpl queryProductCouponList canUseCouponsMap={}", JSON.toJSONString(canUseCouponsMap));
        log.info("ProductApplicationImpl queryProductCouponList joinActivesMap={}", JSON.toJSONString(joinActivesMap));

        // 返回结果
        List<ProductCouponDTO> productCouponList = new ArrayList<>();
        // 券购-可用券
        if (MapUtils.isNotEmpty(canUseCouponsMap)){
            List<ProductCouponDTO> canUseCouponsList = JSON.parseArray(JSON.toJSONString(canUseCouponsMap.get(request.getSkuNo())), ProductCouponDTO.class);
            productCouponList.addAll(canUseCouponsList);
        }
        // 券购-可领券
        if (MapUtils.isNotEmpty(joinActivesMap)){
            List<ProductCouponDTO> joinActivesList = JSON.parseArray(JSON.toJSONString(joinActivesMap.get(request.getSkuNo())), ProductCouponDTO.class);
            Iterator<ProductCouponDTO> iterator = joinActivesList.iterator();
            while (iterator.hasNext()) {
                ProductCouponDTO item = iterator.next();
                String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.PRODUCT_GET_COUPON_KEY, request.getUserPin() + ":" + item.getRoleId() + ":" + item.getEncryptedKey());
                if (StringUtils.isNotBlank(jimClient.get(cacheKey))) {
                    log.info("ProductApplicationImpl queryProductCouponList remove cacheKey={}, cacheResult={}, item={}", cacheKey, jimClient.get(cacheKey), JSON.toJSONString(item));
                    iterator.remove();
                }
            }
            productCouponList.addAll(joinActivesList);
        }

        productCouponList.forEach(c->{
            if (StringUtils.isNotBlank(c.getEncryptedKey())){
                c.setEncryptedKey(tdeClientUtil.encrypt(c.getEncryptedKey()));
            }
            if (StringUtils.isNotBlank(c.getRoleId())){
                c.setRoleId(tdeClientUtil.encrypt(c.getRoleId()));
            }
            c.setSortValue(Objects.isNull(c.getDiscount()) ?  new BigDecimal("0") : c.getDiscount());
            if (Objects.nonNull(c.getActivityBeginTime()) && Objects.nonNull(c.getActivityEndTime())){
                boolean within24Hours = TimeUtils.isWithin24Hours(c.getActivityBeginTime(), c.getActivityEndTime());
                c.setWithin24Hours(within24Hours);
                if (within24Hours){
                    c.setRemainingTime(c.getActivityEndTime().getTime() - c.getActivityBeginTime().getTime());
                }
            }
        });

        if (CollectionUtils.isNotEmpty(productCouponList)){
            // 按照优惠金额大小排序
            productCouponList.sort((a, b) -> b.getSortValue().compareTo(a.getSortValue()));
        }

        if (BooleanUtil.isTrue(request.getQueryGiftCash())){
            return cashGiftMoneyCouponMerge(request, priceInfoResponseBO, productCouponList);
        }
        return productCouponList;
    }

    private List<CouponComponentSkuRpcParam> buildCouponSkuAttributeList(String skuNo, String userPin) {
        List<CouponComponentSkuRpcParam> couponSkuAttributeList = new ArrayList<>();

        // 获取SKU信息
        CompletableFuture<Map<String, RpcSkuBO>> skuInfoMapFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.getSkuInfo(Sets.newHashSet(skuNo));
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        // 获取实时价格
        CompletableFuture<PriceInfoResponseBO> priceInfoFuture = CompletableFuture.supplyAsync(() -> {
            return skuInfoRpc.getSkuPrice(Sets.newHashSet(skuNo), userPin);
        },executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL));

        Map<String, RpcSkuBO> skuInfoMap = skuInfoMapFuture.join();
        PriceInfoResponseBO priceInfo = priceInfoFuture.join();
        log.info("buildCouponSkuAttributeList getSkuInfo skuNo={}, skuInfoMap={}, priceInfo={}", skuNo, JSON.toJSONString(skuInfoMap)
                , JSON.toJSONString(priceInfo));

        if (MapUtils.isEmpty(skuInfoMap) || Objects.isNull(skuInfoMap.get(skuNo))){
            return Lists.newArrayList();
        }
        RpcSkuBO rpcSkuBO = skuInfoMap.get(skuNo);

        CouponComponentSkuRpcParam couponComponentSkuRpcParam = new CouponComponentSkuRpcParam();
        couponComponentSkuRpcParam.setSkuNo(skuNo);
        couponComponentSkuRpcParam.setVenderId(String.valueOf(rpcSkuBO.getVenderId()));
        couponComponentSkuRpcParam.setIsGlobal(StringUtils.isBlank(rpcSkuBO.getIsGlobalPurchase()) ? "0" : rpcSkuBO.getIsGlobalPurchase());
        couponComponentSkuRpcParam.setIsCanUseDong(StringUtils.isBlank(rpcSkuBO.getIsCanUseDQ()) ? "1" : rpcSkuBO.getIsCanUseDQ());
        couponComponentSkuRpcParam.setIsCanUseJing(StringUtils.isBlank(rpcSkuBO.getIsCanUseJQ()) ? "1" : rpcSkuBO.getIsCanUseJQ());
        couponComponentSkuRpcParam.setCategoryId(String.valueOf(rpcSkuBO.getThirdCategoryId()));
        couponComponentSkuRpcParam.setSpuId(rpcSkuBO.getSpuId());
        couponComponentSkuRpcParam.setMsbybt(StringUtils.isBlank(rpcSkuBO.getMsbybt()) ? "0" : rpcSkuBO.getMsbybt());
        couponComponentSkuRpcParam.setVender_bizid(StringUtils.isBlank(rpcSkuBO.getVenderBizId()) ? "" : rpcSkuBO.getVenderBizId());
        if (priceInfo != null && priceInfo.getExtPropResponse() != null){
            Map<String, String> mutexPromosMap = priceInfo.getExtPropResponse().get(skuNo);
            if (MapUtils.isNotEmpty(mutexPromosMap) && StringUtils.isNotBlank(mutexPromosMap.get("mutexPromos"))){
                couponComponentSkuRpcParam.setMutexPromos(mutexPromosMap.get("mutexPromos"));
            }
        }
        couponSkuAttributeList.add(couponComponentSkuRpcParam);
        log.info("buildCouponSkuAttributeList couponSkuAttributeList={}", JSON.toJSONString(couponSkuAttributeList));
        return couponSkuAttributeList;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.getCoupon")
    public CouponGetResultDTO getCoupon(GetCouponCmd cmd) {
        log.info("ProductApplicationImpl getCoupon cmd={}", JSON.toJSONString(cmd));
        String userPin = cmd.getUserPin();
        String roleId = tdeClientUtil.decrypt(cmd.getRoleId());
        String encryptedKey = tdeClientUtil.decrypt(cmd.getEncryptedKey());
        GetCouponRpcParam getCouponRpcParam = new GetCouponRpcParam();
        BaseGetCouponRpcParam baseGetCouponParam = new BaseGetCouponRpcParam();
        baseGetCouponParam.setRuleId(Long.valueOf(roleId));
        baseGetCouponParam.setEncryptedKey(encryptedKey);
        baseGetCouponParam.setUserPin(userPin);
        getCouponRpcParam.setBaseGetCouponParam(baseGetCouponParam);

        GetCouponResultBO getCouponResult = couponServiceRpc.getCoupon(getCouponRpcParam);
        log.info("ProductApplicationImpl getCoupon getCouponResult={}", JSON.toJSONString(getCouponResult));
        if (GET_COUPON_SUCCESS_CODE.equals(getCouponResult.getResultCode())){
            RedisKeyEnum keyEnum = RedisKeyEnum.PRODUCT_GET_COUPON_KEY;
            String cacheKey = RedisKeyEnum.getRedisKey(keyEnum, userPin + ":" + roleId + ":" + encryptedKey);
            jimClient.setEx(cacheKey, JSON.toJSONString(cmd), keyEnum.getExpireTime(), keyEnum.getExpireTimeUnit());
            log.info("ProductApplicationImpl getCoupon redis cacheKey={}, cacheResult={}", cacheKey, jimClient.get(cacheKey));
        }
        return JSON.parseObject(JSON.toJSONString(getCouponResult), CouponGetResultDTO.class);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.batchGetCoupon")
    public CouponGetResultDTO batchGetCoupon(BatchGetCouponCmd cmd) {
        log.info("ProductApplicationImpl batchGetCoupon cmd={}", JSON.toJSONString(cmd));
        if (!JSON.parseObject(duccConfig.getProductCouponConfig()).getBoolean("batchGetCouponSwitch")){
            return null;
        }
        FindCouponsRpcParam couponsRpcParam = new FindCouponsRpcParam();
        couponsRpcParam.setPin(cmd.getUserPin());
        couponsRpcParam.setCouponSkuAttributeList(buildCouponSkuAttributeList(cmd.getSkuNo(), cmd.getUserPin()));

        // 券购-可领券查询
        Map<String, List<CouponInfoBO>> findJoinActiveMap = couponServiceRpc.findJoinActives(couponsRpcParam);
        log.info("ProductApplicationImpl batchGetCoupon findJoinActiveMap={}", JSON.toJSONString(findJoinActiveMap));
        if (MapUtils.isEmpty(findJoinActiveMap)){
            return null;
        }
        List<CouponInfoBO> couponInfoList = findJoinActiveMap.get(cmd.getSkuNo());

        BatchGetCouponRpcParam batchGetCouponRpcParam = new BatchGetCouponRpcParam();
        batchGetCouponRpcParam.setUserPin(cmd.getUserPin());

        List<BaseGetCouponRpcParam> batchBaseGetCouponParams = new ArrayList<>();
        couponInfoList.forEach(c->{
            BaseGetCouponRpcParam baseGetCouponRpcParam = new BaseGetCouponRpcParam();
            baseGetCouponRpcParam.setRuleId(Long.valueOf(c.getRoleId()));
            baseGetCouponRpcParam.setEncryptedKey(c.getEncryptedKey());
            batchBaseGetCouponParams.add(baseGetCouponRpcParam);
        });
        batchGetCouponRpcParam.setBatchBaseGetCouponParams(batchBaseGetCouponParams);

        GetCouponResultBO getCouponResult = couponServiceRpc.batchGetCoupon(batchGetCouponRpcParam);
        log.info("ProductApplicationImpl batchGetCoupon getCouponResult={}", JSON.toJSONString(getCouponResult));
        return JSON.parseObject(JSON.toJSONString(getCouponResult), CouponGetResultDTO.class);
    }

    @Override
    public  Set<String> listSkillCodes(List<Long> skuNos) {


        // 根据服务所需技能过滤可以服务的护士
        LambdaQueryWrapper<JdhSkuItemRelPo> serviceItemWrapper = Wrappers.lambdaQuery();
        serviceItemWrapper.in(JdhSkuItemRelPo::getSkuId, skuNos)
                .eq(JdhSkuItemRelPo::getSkuItemType, 1)
                .eq(JdhSkuItemRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSkuItemRelPo> relPos = jdhSkuItemRelPoMapper.selectList(serviceItemWrapper);
        if (CollectionUtils.isEmpty(relPos)) {
            return Collections.emptySet();
        }
        Set<Long> itemIds = relPos.stream().map(e -> Long.valueOf(e.getSkuItemId())).collect(toSet());
        List<ServiceItemAngelSkillRel> itemSkills = jdhServiceItemRepository.queryServiceItemAngelSkillRel(JdhItemAngelSkillRelQueryContext.builder().serviceItemIds(itemIds).build());
        if (CollectionUtils.isEmpty(itemSkills)) {
            return Collections.emptySet();
        }
        return itemSkills.stream().map(ServiceItemAngelSkillRel::getAngelSkillCode).collect(Collectors.toSet());
    }

    /**
     * 根据sku中serviceType替换文本
     * @param serviceType
     * @param scene
     * @param text
     * @return
     */
    @Override
    public String replaceWordsByServiceType(Integer serviceType, String scene, String text) {
        try {
            log.info("ProductApplicationImpl replaceWordsByServiceType serviceType={},scene={},text={}", serviceType, scene, text);
            if (Objects.isNull(serviceType) || StringUtils.isBlank(scene) || StringUtils.isBlank(text)){
                log.info("replaceWordsByServiceType param empty");
                return text;
            }
            // sku中serviceType替换文本配置
            JSONObject obj = JSON.parseObject(duccConfig.getServiceTypeReplaceWordsConfig());
            Map<String, String> sceneMap = JSON.parseObject(obj.getString("sceneMap"), new TypeReference<Map<String, String>>() {});
            if (Objects.isNull(sceneMap.get(scene))){
                log.info("replaceWordsByServiceType scene no match");
                return text;
            }
            // 替换文本
            String replaceWords = (String) sceneMap.get(scene);

            Map<String, String> serviceTypeMap = JSON.parseObject(obj.getString("serviceTypeMap"), new TypeReference<Map<String, String>>() {});
            if (Objects.isNull(serviceTypeMap.get(String.valueOf(serviceType)))){
                log.info("replaceWordsByServiceType serviceType no match");
                return text;
            }
            // 职业
            String career = (String) serviceTypeMap.get(String.valueOf(serviceType));
            String newText = text.replace(replaceWords, career);
            log.info("ProductApplicationImpl replaceWordsByServiceType text={},newText={}", text, newText);
            return newText;
        } catch (Exception e) {
            log.error("ProductApplicationImpl replaceWordsByServiceType error e", e);
        }
        return text;
    }

    /**
     * 查询商品限购
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryProductLimitBuy")
    public List<ProductLimitBuyDTO> queryProductLimitBuy(ProductLimitBuyRequest request) {
        Set<Long> skuIdList = new HashSet<>();
        List<SkuInfoRequestParam> skuInfoRequestParamList = new ArrayList<>();
        request.getSkuIdList().forEach(skuId->{
            SkuInfoRequestParam infoRequestParam = new SkuInfoRequestParam();
            infoRequestParam.setSkuId(skuId);
            skuInfoRequestParamList.add(infoRequestParam);
            skuIdList.add(Long.valueOf(skuId));
        });

        JdhSkuListRequest jdhSkuListRequest = new JdhSkuListRequest();
        jdhSkuListRequest.setSkuIdList(skuIdList);
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(jdhSkuListRequest);
        if (CollectionUtils.isEmpty(jdhSkus)){
            log.info("ProductApplicationImpl queryProductPurchaseLimit jdhSkus empty");
            return Lists.newArrayList();
        }

        List<ProductLimitBuyBO> productLimitStrategyList = productLimitbuyRpc.getProductLimitStrategy(skuInfoRequestParamList, request.getUserPin());
        log.info("ProductApplicationImpl queryProductPurchaseLimit productLimitStrategyList={}", JSON.toJSONString(productLimitStrategyList));
        if (CollectionUtils.isEmpty(productLimitStrategyList)){
            return Lists.newArrayList();
        }
        List<ProductLimitBuyDTO> productLimitBuyList = ProductApplicationConverter.instance.productLimitBuy2DTOList(productLimitStrategyList);
        log.info("ProductApplicationImpl queryProductPurchaseLimit productLimitBuyList={}", JSON.toJSONString(productLimitBuyList));

        // 查询健康商品主数据
        JdhSkuListRequest skuListRequest = new JdhSkuListRequest();
        skuListRequest.setSkuIdList(skuIdList);
        Map<Long, JdhSkuDto> skuMap = this.queryJdhSkuInfoList(skuListRequest);
        productLimitBuyList.forEach(productLimitBuy->{
            JdhSkuDto jdhSku = skuMap.get(Long.valueOf(productLimitBuy.getSkuId()));
            if (Objects.isNull(jdhSku)){
                return;
            }
            productLimitBuy.setMergeMaxBuyNum(Objects.isNull(productLimitBuy.getMergeMaxBuyNum()) ? 999 : productLimitBuy.getMergeMaxBuyNum());
            productLimitBuy.setLimitPreOrderNum(Objects.isNull(productLimitBuy.getLimitPreOrderNum()) ? 999 : productLimitBuy.getLimitPreOrderNum());
            productLimitBuy.setSkuType(jdhSku.getSkuType());
        });
        return productLimitBuyList;
    }

    @Override
    @LogAndAlarm(jKey = "ProductApplicationImpl.queryMultiSkuList")
    public List<JdhSkuDto> queryMultiSkuList(JdhSkuListRequest request) {
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(request);
        if (CollectionUtils.isEmpty(jdhSkus)){
            return Lists.newArrayList();
        }
        List<JdhSkuDto> jdhSkuDtoList = new ArrayList<>();
        jdhSkus.forEach(jdhSku->{
            jdhSkuDtoList.add(ProductApplicationConverter.instance.convertJdhSkuToJdhSkuDto(jdhSku));
        });
        return jdhSkuDtoList;
    }

    /**
     * 查询健康商品主数据
     *
     * @param skuId
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductApplicationImpl.queryJdhSkuInfoBySkuId")
    public JdhSkuDto queryJdhSkuInfoBySkuId(Long skuId) {
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(skuId);
        request.setQuerySkuCoreData(false);
        request.setQueryServiceItem(true);
        return queryJdhSkuInfo(request);
    }

    /**
     * 商详页按钮配置
     *
     * @param properties
     * @param detailFloorBO
     * @param buyLimitType
     * @return
     */
    private ProductDetailOrderConfigDTO getProductDetailOrderConfigDTO(Map<String, String> properties, ProductDetailFloorRequest detailFloorBO, Integer buyLimitType,String venderId, List<ProductCouponDTO> productCouponDTOS, JdhSku jdhSku) {
        log.info("ProductApplicationImpl -> queryProductDetailFloorBySku 配置 start");
        ProductDetailOrderConfigDTO productsAndOrderConfig = new ProductDetailOrderConfigDTO();
        //底部按钮
        ProductDetailCustomIconConfigDTO customIconConfig = JSON.parseObject(properties.getOrDefault("customIconConfig", "{\"isClick\":true,\"icon\":\"https://jkimg10.360buyimg.com/pop/jfs/t1/219909/18/33013/2470/6561a1bdF5e25832d/6c0c102fe952df2c.png\",\"text\":\"联系客服\",\"jumpLink\":\"http://\",\"class\":\"ProductDetailCustomIconConfigDTO\",\"open\":true}"), ProductDetailCustomIconConfigDTO.class);
        customIconConfig.setJumpLink(this.getCustomerServiceLink(detailFloorBO.getEnvType(), detailFloorBO.getSkuNo()));
        productsAndOrderConfig.setCustomIconConfig(customIconConfig);
        //下单按钮
        ProductDetailGoBuyBtnDTO goBuyBtn = com.jd.medicine.base.common.util.JsonUtil.parseObject(properties.getOrDefault("goBuyBtn", "{\"isClick\":true,\"jumpOpenAppLink\":\"\",\"isOpenApp\":false,\"text\":\"购买服务\",\"class\":\"ProductDetailGoBuyBtnDTO\",\"open\":true}"), ProductDetailGoBuyBtnDTO.class);
        goBuyBtn.setIsClick(Objects.isNull(buyLimitType));
        //判断此环境是否可以购买，无法购买需配置跳转app的链接引导用户在app中购买
        JSONObject envBuySwitch = com.jd.medicine.base.common.util.JsonUtil.parseObject(properties.getOrDefault("envBuySwitch", "{\"jdapp\":true,\"jdhapp\":true,\"jdheapp\":true,\"jdmeapp\":true,\"miniprogram\":true,\"wxwork\":true,\"wexin\":true,\"qq\":true,\"h5\":true}"));
        boolean canBuy = Objects.isNull(envBuySwitch) || envBuySwitch.getBoolean(detailFloorBO.getEnvType());
        if (!canBuy) {
            goBuyBtn.setIsClick(true);
            goBuyBtn.setIsOpenApp(true);
            String jumpUrl = properties.getOrDefault("envJumpUrl", "https://laputa-yf.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?skuId=%s");
            jumpUrl = String.format(jumpUrl, detailFloorBO.getSkuNo());
            if(StringUtils.isNotBlank(detailFloorBO.getChannelName())){
                jumpUrl = jumpUrl + "&channelName="+ detailFloorBO.getChannelName();
            }
            goBuyBtn.setJumpOpenAppLink(jumpUrl);
            JSONObject envJumpTextSwitch = com.jd.medicine.base.common.util.JsonUtil.parseObject(properties.getOrDefault("envJumpTextSwitch", "{\"jdapp\":\"请前往京东App购买\",\"jdhapp\":\"请前往京东健康App购买\",\"jdheapp\":\"请前往京东App购买\",\"jdmeapp\":\"请前往京东App购买\",\"miniprogram\":\"请前往京东App购买\",\"wxwork\":\"请前往京东App购买\",\"wexin\":\"请前往京东App购买\",\"qq\":\"请前往京东App购买\",\"h5\":\"请前往京东App购买\"}"));
            String envJumpText = envJumpTextSwitch.getString(StringUtil.isNotBlank(detailFloorBO.getShareAppChannel()) ? detailFloorBO.getShareAppChannel() : "default");
            goBuyBtn.setText(StringUtil.isNotBlank(envJumpText) ? envJumpText : "请前往京东App购买");
        }

        List<ProductCouponDTO> receiveCouponList = productCouponDTOS.stream().filter(p -> CouponUseStatusEnum.AVAILABLE.getCode().equals(p.getUseStatus())).collect(Collectors.toList());
        log.info("ProductApplicationImpl getProductDetailOrderConfigDTO receiveCouponList={}", JSON.toJSONString(receiveCouponList));
        if (CollectionUtils.isNotEmpty(receiveCouponList)){
            goBuyBtn.setText("领券购买");
        }
        productsAndOrderConfig.setGoBuyBtn(goBuyBtn);

        // 店铺按钮
        String storeConfig = properties.getOrDefault("storeConfig", "{\"open\":true,\"icon\":\"https://jkimg10.360buyimg.com/pop/jfs/t1/163750/39/27442/2106/66693e7dFb9fb2c74/f71a359b7fd6db64.png\",\"text\":\"\",\"jumpLink\":\"https://mall.jd.com/index-1000477238.html\",\"isClick\":true}");
        ProductDetailCustomIconConfigDTO productDetailCustomIconConfigDTO = JSON.parseObject(storeConfig, ProductDetailCustomIconConfigDTO.class);
        //动态拼接venderId
        String storeJumpUrl = String.format(productDetailCustomIconConfigDTO.getJumpLink(), venderId);

        // 频道页
        String channelPageConfig = properties.get("channelPageConfig");
        if (StringUtils.isNotBlank(channelPageConfig) && Objects.nonNull(jdhSku)){
            String channelPageUrl = JSON.parseObject(channelPageConfig).getString(String.valueOf(jdhSku.getServiceType()));
            if (StringUtils.isNotBlank(channelPageUrl)){
                storeJumpUrl = channelPageUrl;
            }
        }
        productDetailCustomIconConfigDTO.setJumpLink(storeJumpUrl);
        productsAndOrderConfig.setStoreConfig(productDetailCustomIconConfigDTO);
        log.info("ProductApplicationImpl -> queryProductDetailFloorBySku 配置 end, productsAndOrderConfig={}", com.jd.medicine.base.common.util.JsonUtil.toJSONString(productsAndOrderConfig));
        return productsAndOrderConfig;
    }

    /**
     * 根据app环境获取客服链接
     *
     * @param appType
     * @return
     */
    private String getCustomerServiceLink(String appType, String skuNo) {
        String customerServiceLink = duccConfig.getCustomerServiceLink();
        String defaultLink = "https://jdcs.m.jd.com/chat/index.action?entry=jd_sdk_kjzysxy&sku=%s";
        if (appType == null || StringUtils.isBlank(customerServiceLink)) {
            return String.format(defaultLink, skuNo);
        }
        JSONObject jsonObject = com.jd.medicine.base.common.util.JsonUtil.parseObject(customerServiceLink);
        if (Objects.isNull(jsonObject) || !jsonObject.containsKey(appType)) {
            return String.format(defaultLink, skuNo);
        }
        return String.format(jsonObject.getString(appType), skuNo);
    }

    /**
     * 查询主商品下的升级套餐信息
     *
     * @param parentSkuId 父商品id
     */
    private List<JdhSkuRel> getSubSkuList(Long parentSkuId, Integer skuRelType, Integer skuItemType) {
        log.info("ProductApplicationImpl.getSubSkuList, parentSkuId={} skuRelType={} skuItemType={}", parentSkuId, skuRelType, skuItemType);
        // 查询主商品下推荐套餐数据
        List<JdhSkuRel> subSkuList =
                jdhSkuRepository.queryJdhSkuRelInfo(JdhSkuRel.builder().parentSkuId(parentSkuId).skuRelType(skuRelType).build());
        if (CollUtil.isEmpty(subSkuList)) {
            log.info("ProductApplicationImpl.getSubSkuList, subSkuList={}", JSON.toJSONString(subSkuList));
            return subSkuList;
        }
        Map<Long, List<ServiceItem>> maps = getMultiSkuItemList(subSkuList.stream().map(JdhSkuRel::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet()), skuItemType == null ? 1 : skuItemType);
        for (JdhSkuRel jdhSkuRel : subSkuList) {
            if (!maps.containsKey(jdhSkuRel.getSkuId()) || CollUtil.isEmpty(maps.get(jdhSkuRel.getSkuId()))) {
                continue;
            }
            jdhSkuRel.setServiceItemList(maps.get(jdhSkuRel.getSkuId()));
        }
        log.info("ProductApplicationImpl.getSubSkuList, subSkuList={}", JSON.toJSONString(subSkuList));
        return subSkuList;
    }

    /**
     * 查询商品下项目列表信息
     *
     * @return 项目列表
     */
    private List<ServiceItem> getSkuItemList(Long skuId, Integer skuItemType) {
        log.info("ProductApplicationImpl.getSkuItemList, skuId={} skuItemType={}", skuId, skuItemType);
        List<JdhSkuItemRel> list = jdhSkuRepository.queryJdhSkuItemRelList(JdhSkuItemRel.builder().skuId(skuId).skuItemType(skuItemType).build());
        if (CollUtil.isEmpty(list)) {
            log.info("ProductApplicationImpl.getSkuItemList, list={}", JSON.toJSONString(list));
            return Collections.emptyList();
        }
        JdhItemListQueryContext itemListQuery = JdhItemListQueryContext.builder().itemIdList(list.stream().filter(Objects::nonNull).map(s -> Long.parseLong(s.getSkuItemId())).collect(Collectors.toSet())).build();
        List<ServiceItem> serviceItems = jdhServiceItemRepository.queryServiceItemList(itemListQuery);
        log.info("ProductApplicationImpl.getSkuItemList, serviceItems={}", JSON.toJSONString(serviceItems));
        return serviceItems;
    }

    /**
     * 查询多个商品下项目列表信息
     *
     * @return 项目列表
     */
    private Map<Long, List<ServiceItem>> getMultiSkuItemList(Set<Long> skuIds, Integer skuItemType) {
        log.info("ProductApplicationImpl.getMultiSkuItemList, skuIds={} skuItemType={}", JSON.toJSONString(skuIds), skuItemType);
        List<JdhSkuItemRel> jdhSkuItemRels = jdhSkuRepository.queryJdhSkuItemRelList(skuIds.stream().map(s -> JdhSkuItemRel.builder().skuId(s).build()).filter(Objects::nonNull).collect(Collectors.toList()), null, skuItemType == null ? 1 : skuItemType, false);
        Map<Long, List<ServiceItem>> multiMap = new HashMap<>(1);
        // 商品无项目信息返回
        if (CollUtil.isEmpty(jdhSkuItemRels)) {
            for (Long skuId : skuIds) {
                multiMap.put(skuId, Collections.emptyList());
            }
            log.info("ProductApplicationImpl.getMultiSkuItemList, jdhSkuItemRels={}", JSON.toJSONString(jdhSkuItemRels));
            return multiMap;
        }
        // 查询多商品下项目列表数据
        JdhItemListQueryContext itemListQuery = JdhItemListQueryContext.builder().itemIdList(jdhSkuItemRels.stream().filter(Objects::nonNull).map(s -> Long.parseLong(s.getSkuItemId())).collect(Collectors.toSet())).build();
        List<ServiceItem> serviceItems = jdhServiceItemRepository.queryServiceItemList(itemListQuery);
        // 查询多商品下项目列表数据为空返回
        if (CollUtil.isEmpty(serviceItems)) {
            for (Long skuId : skuIds) {
                multiMap.put(skuId, Collections.emptyList());
            }
            log.info("ProductApplicationImpl.getMultiSkuItemList, serviceItems={}", JSON.toJSONString(serviceItems));
            return multiMap;
        }
        // 商品项目关系list转map
        Map<Long, List<JdhSkuItemRel>> serviceItemRelMap =
                jdhSkuItemRels.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(JdhSkuItemRel::getSkuId));
        // 项目list转map
        Map<Long, ServiceItem> serviceItemMap = serviceItems.stream().filter(Objects::nonNull).collect(Collectors.toMap(ServiceItem::getItemId, a -> a, (k1, k2) -> k1));
        for (Long skuId : skuIds) {
            // 商品下无项目关联信息
            if (!serviceItemRelMap.containsKey(skuId) || CollUtil.isEmpty(serviceItemRelMap.get(skuId))) {
                multiMap.put(skuId, Collections.emptyList());
                continue;
            }
            List<ServiceItem> skuServiceItems = new ArrayList<>();
            for (Long itemId : serviceItemRelMap.get(skuId).stream().filter(Objects::nonNull).map(s -> Long.parseLong(s.getSkuItemId())).collect(Collectors.toList())) {
                // 项目信息不存在，跳过
                if (!serviceItemMap.containsKey(itemId) || serviceItemMap.get(itemId) == null) {
                    continue;
                }
                // 项目信息存在保存到sku列表
                skuServiceItems.add(serviceItemMap.get(itemId));
            }
            multiMap.put(skuId, skuServiceItems);
        }
        log.info("ProductApplicationImpl.getMultiSkuItemList, multiMap={}", JSON.toJSONString(multiMap));
        return multiMap;
    }

    /**
     * 构建商品关联数据
     *
     * @param createJdhSkuRelCmd cmd
     * @return list
     */
    private static List<JdhSkuRel> buildJdhSkuRels(CreateJdhSkuRelCmd createJdhSkuRelCmd) {
        log.info("ProductApplicationImpl.buildJdhSkuRels, createJdhSkuRelCmd={}", JSON.toJSONString(createJdhSkuRelCmd));
        List<JdhSkuRel> jdhSkuRelList = new ArrayList<>();
        for (CreateJdhSubSkuCmd subSkuCmd : createJdhSkuRelCmd.getSubSkuList()) {
            JdhSkuRel jdhSkuRel = new JdhSkuRel();
            jdhSkuRel.setParentSkuId(createJdhSkuRelCmd.getParentSkuId());
            jdhSkuRel.setParentShortName(createJdhSkuRelCmd.getParentShortName());
            jdhSkuRel.setSkuId(subSkuCmd.getSkuId());
            jdhSkuRel.setSkuShortName(subSkuCmd.getSkuShortName());
            jdhSkuRel.setSkuRelType(createJdhSkuRelCmd.getSkuRelType());
            jdhSkuRel.setCreateUser(createJdhSkuRelCmd.getCreateUser());
            jdhSkuRel.setUpdateUser(createJdhSkuRelCmd.getUpdateUser());
            jdhSkuRel.setExtend(buildJdhSkuRelsExtend(subSkuCmd));
            jdhSkuRelList.add(jdhSkuRel);
        }
        log.info("ProductApplicationImpl.buildJdhSkuRels, jdhSkuRelList={}", JSON.toJSONString(jdhSkuRelList));
        return jdhSkuRelList;
    }

    /**
     * 构建扩展字段数据
     * @param subSkuCmd
     * @return
     */
    private static String buildJdhSkuRelsExtend(CreateJdhSubSkuCmd subSkuCmd){
        if(Objects.isNull(subSkuCmd)){
            return null;
        }
        Map<String, String> resultMap = new HashMap<>();
        if(StringUtil.isNotEmpty(subSkuCmd.getSkuShortDesc())){
            resultMap.put("skuShortDesc", subSkuCmd.getSkuShortDesc());
        }
        if(StringUtil.isNotEmpty(subSkuCmd.getSkuFullDesc())){
            resultMap.put("skuFullDesc", subSkuCmd.getSkuFullDesc());
        }
        if(StringUtil.isNotEmpty(subSkuCmd.getSkuTagDesc())){
            resultMap.put("skuTagDesc", subSkuCmd.getSkuTagDesc());
        }
        if(resultMap.size() == 0){
            return null;
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 校验健康商品服务项是否在同一个门店下
     *
     * @param jdhSku jdhSku
     * @return bo
     */
    private Boolean checkSkuServiceItemAllInOneStore(JdhSku jdhSku, String stationId) {
        // 仅骑手上门校验
        if (jdhSku.getServiceType() != 1 || CollUtil.isEmpty(jdhSku.getServiceItemIdList())) {
            return true;
        }
        List<JdhStationServiceItemRel> jdhStationServiceItemRel = jdhSku.getServiceItemIdList().stream().map(s -> JdhStationServiceItemRel.builder().serviceItemId(s).build()).collect(Collectors.toList());
        List<JdhStationServiceItemRel> list = providerStoreRepository.queryStationListForServiceItemAllInOneStore(jdhStationServiceItemRel);
        log.info("ProductApplicationImpl#checkSkuServiceItemAllInOneStore list={}", JSON.toJSONString(list));
        // 不存在门店数据
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        if (StringUtils.isNotBlank(stationId)) {
            List<JdhStationServiceItemRel> filterList = list.stream().filter(s -> !s.getStationId().equalsIgnoreCase(stationId)).collect(Collectors.toList());
            log.info("ProductApplicationImpl#checkSkuServiceItemAllInOneStore stationId={}, filterList={}", stationId, JSON.toJSONString(filterList));
            // 不存在指定门店以外的数据
            return !CollUtil.isEmpty(filterList);
        }
        return true;
    }

    /**
     * 查询多个商品下加项信息
     *
     * @return 加项列表
     */
    private Map<Long, List<JdhSkuRel>> getMultiSkuRelList(Set<Long> skuIds, Integer skuRelType) {
        log.info("ProductApplicationImpl.getMultiSkuRelList, skuIds={} skuRelType={}", JSON.toJSONString(skuIds), skuRelType);
        Map<Long, List<JdhSkuRel>> multiMap = new HashMap<>(1);
        List<JdhSkuRel> subSkuList = jdhSkuRepository.queryMultiJdhSkuRelInfo(skuIds.stream().map(s -> JdhSkuRel.builder().parentSkuId(s).build()).filter(Objects::nonNull).collect(Collectors.toList()), skuRelType == null ? 2 : skuRelType);
        if (CollUtil.isEmpty(subSkuList)) {
            for (Long skuId : skuIds) {
                multiMap.put(skuId, Collections.emptyList());
            }
            log.info("ProductApplicationImpl.getMultiSkuRelList, multiMap={}", JSON.toJSONString(multiMap));
            return multiMap;
        }
        Map<Long, List<JdhSkuRel>> queryMap = subSkuList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(JdhSkuRel::getParentSkuId));
        for (Long skuId : skuIds) {
            if (!queryMap.containsKey(skuId) || CollUtil.isEmpty(queryMap.get(skuId))) {
                multiMap.put(skuId, Collections.emptyList());
                continue;
            }
            multiMap.put(skuId, queryMap.get(skuId));
        }
        log.info("ProductApplicationImpl.getMultiSkuRelList, multiMap={}", JSON.toJSONString(multiMap));
        return multiMap;
    }

    /**
     * 校验主商品数据有效性
     *
     * @param skuId id
     */
    private JdhSkuDto checkMainSkuInfo(Integer serviceType, Long skuId) {
        RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(skuId));
        JdhSkuDto jdhSkuDto = ProductApplicationConverter.instance.convertToJdhSkuDto(crsSku);
        if (jdhSkuDto == null || jdhSkuDto.getSkuId() == null || StringUtils.isBlank(jdhSkuDto.getSkuName())) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK);
        }
        if (ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(serviceType)) {
            // 快递检测必须打标
            if (!"1".equalsIgnoreCase(crsSku.getJdhdjfw())) {
                throw new BusinessException(ProductErrorCode.PRODUCT_SKU_TAG_CHECK);
            }
            JdhServiceTypeCategoryRelation categoryRelation = jdhServiceTypeCategoryRelationRepository.findCategoryRelation(crsSku.getThirdCategoryId(), NumConstant.NUM_3);
            if (categoryRelation == null) {
                throw new BusinessException(ProductErrorCode.PRODUCT_SKU_THIRD_CATEGORY_CHECK);
            }
        }
        JdhSku querySku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(skuId).build());
        if (querySku != null) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_EXIST_CHECK);
        }
        return jdhSkuDto;
    }

    /**
     * 校验商品配置的每日可约时间范围有效性
     * @param jdhSku
     */
    private void checkDayTimeFrame(JdhSku jdhSku) {
        if (StringUtils.isBlank(jdhSku.getDayTimeFrame())) {
            return;
        }
        List<String> dayTimes = JSON.parseArray(jdhSku.getDayTimeFrame(), String.class);
        if (CollectionUtils.isEmpty(dayTimes)) {
            return;
        }
        String startTime = dayTimes.get(0).split("-")[0];
        String endTime = dayTimes.get(0).split("-")[1];
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime open = LocalTime.parse(startTime, formatter);
        LocalTime close = LocalTime.parse(endTime, formatter);
        if(open.isAfter(close)){
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_BUSINESS_HOUR_ERROR);
        }
        /*if (open.getMinute() != 0 || (close.getMinute() != 0 && close.getMinute() != 59)) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_BUSINESS_HOUR_ERROR);
        }*/
    }

    /**
     * 构建消息商品及主站商品数据
     *
     * @param jdhSkuCustomDto 消医商品
     * @param jdSkuCoreData   主站商品
     * @return 融合后商品数据
     */
    private JdhSkuDto buildJdhSkuDtoWithCoreSku(JdhSkuDto jdhSkuCustomDto, JdhSkuDto jdSkuCoreData) {
        log.info("ProductApplicationImpl.queryAggregationJdhSkuInfo jdSkuCoreData={} jdhSkuCustomDto={}", JSON.toJSONString(jdSkuCoreData), JSON.toJSONString(jdhSkuCustomDto));
        JdhSkuDto ret = new JdhSkuDto();
        //  优先拷贝商品主数据属性
        if (jdSkuCoreData != null) {
            BeanUtil.copyProperties(jdSkuCoreData, ret, true);
        }
        // 消医异构数据存在，字段可覆盖主商品数据
        if (jdhSkuCustomDto != null) {
            BeanUtil.copyProperties(jdhSkuCustomDto, ret, true);
        }
        log.info("ProductApplicationImpl.queryAggregationJdhSkuInfo ret={}", JSON.toJSONString(ret));
        return ret;
    }

    /**
     * 构建服务流程图
     *
     * @param serviceProcessImg img
     * @return url
     */
    private String buildSkuServiceProcessImg(String serviceProcessImg) {
        if (StringUtils.isBlank(serviceProcessImg)) {
            return serviceProcessImg;
        }
        try {
            GenerateGetUrlCommand generateGetUrlCommand = new GenerateGetUrlCommand();
            generateGetUrlCommand.setDomainCode(DomainEnum.PRODUCT.getCode());
            generateGetUrlCommand.setFileIds(Stream.of(Long.parseLong(serviceProcessImg)).collect(Collectors.toSet()));
            generateGetUrlCommand.setIsPublic(true);
            List<FilePreSignedUrlDto> list = fileManageApplication.generateGetUrl(generateGetUrlCommand);
            if (CollUtil.isEmpty(list)) {
                return null;
            }
            return list.get(0).getUrl();
        } catch (Exception e) {
            log.error("buildSkuServiceProcessImg Exception", e);
            return null;
        }
    }

    /**
     * 校验子商品与项目信息
     */
    private void checkSkuSubRelInfo(CreateJdhSkuRelCmd createJdhSkuRelCmd) {
        if (createJdhSkuRelCmd.getSubSkuList().stream().map(CreateJdhSubSkuCmd::getSkuId).collect(Collectors.toSet()).contains(createJdhSkuRelCmd.getParentSkuId()) ||
                createJdhSkuRelCmd.getSubSkuList().size() != createJdhSkuRelCmd.getSubSkuList().stream().map(CreateJdhSubSkuCmd::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet()).size()) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_REPEAT);
        }
        // check子项商品总数限制
        checkSubSkuTotalLimitBySkuRelType(createJdhSkuRelCmd);

        // 主站商品数据查询
        Map<String, RpcSkuBO> skuMaps = skuInfoRpc.getSkuInfo(createJdhSkuRelCmd.getSubSkuList().stream().map(s -> String.valueOf(s.getSkuId())).collect(Collectors.toSet()));
        List<Long> subSkuNotExist = new ArrayList<>();
        for (Long subSkuId : createJdhSkuRelCmd.getSubSkuList().stream().map(CreateJdhSubSkuCmd::getSkuId).collect(Collectors.toSet())) {
            if (!skuMaps.containsKey(String.valueOf(subSkuId)) || skuMaps.get(String.valueOf(subSkuId)) == null || StringUtils.isBlank(skuMaps.get(String.valueOf(subSkuId)).getSkuName())) {
                subSkuNotExist.add(subSkuId);
            }
        }
        if (CollUtil.isNotEmpty(subSkuNotExist)) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK_FORMAT.formatDescription(Joiner.on("、").join(subSkuNotExist)));
        }

        List<JdhSku> jdhSkuList = createJdhSkuRelCmd.getSubSkuList().stream().map(s -> JdhSku.builder().skuId(s.getSkuId()).build()).collect(Collectors.toList());
        Map<Long, JdhSku> retMap = jdhSkuRepository.queryMultiSku(jdhSkuList).stream().collect(Collectors.toMap(JdhSku::getSkuId, a -> a, (k1, k2) -> k1));
        JdhSkuRequest mainSkuReq = new JdhSkuRequest();
        mainSkuReq.setSkuId(createJdhSkuRelCmd.getParentSkuId());
        mainSkuReq.setQueryServiceItem(true);
        JdhSkuDto mainSKu = queryAggregationJdhSkuInfo(mainSkuReq);

        // check skuType；加项场景下主sku的skuType为主品，子sku的skuType为加项品
        checkSkuType(createJdhSkuRelCmd, mainSKu, retMap);

        Integer parentServiceType = mainSKu.getServiceType();
        for (Long subSkuId : createJdhSkuRelCmd.getSubSkuList().stream().map(CreateJdhSubSkuCmd::getSkuId).collect(Collectors.toSet())) {
            if (!retMap.containsKey(subSkuId) || retMap.get(subSkuId) == null) {
                throw new BusinessException(ProductErrorCode.PRODUCT_SERVICE_ITEM_NOT_EXIST_FORMAT.formatDescription(subSkuId));
            }
            Integer subServiceType = retMap.get(subSkuId).getServiceType();
            //只有加项商品需要做此校验
           if (!parentServiceType.equals(subServiceType)&&SkuRelTypeEnum.ADD_ITEM.getType().equals(createJdhSkuRelCmd.getSkuRelType())) {
                throw new BusinessException(ProductErrorCode.PRODUCT_SKU_SUB_SERVICE_TYPE_NOT_MATCH_FORMAT.formatDescription(subSkuId));
            }
        }
        // 骑手上门-加项校验合管逻辑
        if (ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(mainSKu.getServiceType())) {
            List<ServiceItemDto> checkItemList = mainSKu.getServiceItemList();
            createJdhSkuRelCmd.getSubSkuList().stream().map(CreateJdhSubSkuCmd::getSkuId).collect(Collectors.toSet()).forEach(s -> {
                JdhSkuRequest jdhSkuRequest = new JdhSkuRequest();
                jdhSkuRequest.setSkuId(s);
                jdhSkuRequest.setQueryServiceItem(true);
                JdhSkuDto jdhSkuDto = queryAggregationJdhSkuInfo(jdhSkuRequest);
                checkItemList.addAll(jdhSkuDto.getServiceItemList());
            });
            // 白名单校验使用主品维度，类型传加项
            checkItemMergeConfig(mainSKu.getSkuId(), SkuRelTypeEnum.ADD_ITEM.getType(), CollUtil.isEmpty(checkItemList) ? null : checkItemList.stream().map(ServiceItemDto::getItemId).collect(Collectors.toList()), mainSKu);
        }
    }

    /**
     * check主子商品的skuType
     * @param createJdhSkuRelCmd
     * @param mainSku
     * @param subSkuMap
     * @throws BusinessException
     */
    private void checkSkuType(CreateJdhSkuRelCmd createJdhSkuRelCmd, JdhSkuDto mainSku, Map<Long, JdhSku> subSkuMap) throws BusinessException{
        if(Objects.nonNull(createJdhSkuRelCmd.getSkuRelType())){
            // 加项场景下主品的skuType为主品，加项品的skuType为加项品
             if(createJdhSkuRelCmd.getSkuRelType().intValue() == SkuRelTypeEnum.ADD_ITEM.getType().intValue()){
                if (Objects.nonNull(mainSku.getSkuType()) && mainSku.getSkuType().intValue() != 0) {
                    throw new BusinessException(ProductErrorCode.PRODUCT_SKU_TYPE_NOT_MAIN);
                }
                 subSkuMap.forEach((k, v) -> {
                     if (Objects.nonNull(v.getSkuType()) && v.getSkuType().intValue() != 1) {
                         throw new BusinessException(ProductErrorCode.PRODUCT_SKU_TYPE_NOT_ADDED.formatDescription(v.getSkuId()));
                     }
                 });
            }
        }
    }

    /**
     * check子项商品总数限制
     * @param createJdhSkuRelCmd
     * @throws BusinessException
     */
    private void checkSubSkuTotalLimitBySkuRelType(CreateJdhSkuRelCmd createJdhSkuRelCmd) throws BusinessException{
        if(Objects.nonNull(createJdhSkuRelCmd.getSkuRelType())){
            // 升级 限制10条
            if(createJdhSkuRelCmd.getSkuRelType().intValue() == SkuRelTypeEnum.UPGRADE_ITEM.getType().intValue()){
                if (createJdhSkuRelCmd.getSubSkuList().size() > CommonConstant.TEN) {
                    throw new BusinessException(ProductErrorCode.PRODUCT_SKU_SUB_NUM_LIMIT);
                }
                // 加项 限制5条 0828更新：产品更新为10条限制
            } else if(createJdhSkuRelCmd.getSkuRelType().intValue() == SkuRelTypeEnum.ADD_ITEM.getType().intValue()){
                if (createJdhSkuRelCmd.getSubSkuList().size() > CommonConstant.TEN) {
                    throw new BusinessException(ProductErrorCode.PRODUCT_SKU_SUB_NUM_LIMIT);
                }
            }
        }
    }

    /**
     * 读取导入文件
     *
     */
    private void importProductSkuExcel(String logId, String filePath, String erp){
        try {
            MDC.put("PFTID", logId);
            InputStream inputStream = fileManageService.get(filePath);
            ImportProductSkuListenerContext context = new ImportProductSkuListenerContext();
            context.setErp(erp);
            ImportProductSkuListener readListener = new ImportProductSkuListener(context);
            EasyExcelFactory.read(inputStream, ImportSku.class, readListener).sheet().doRead();
        } catch (Exception e) {
            log.error("ProductServiceItemApplicationImpl#importProductServiceItemExcel exception", e);
            throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
        } finally {
            String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.IMPORT_PRODUCT_SKU_IMPORT_LOCK_KEY, erp);
            redisUtil.unLock(lockRedisKey);
        }
    }

    /**
     * 校验是否配置合管逻辑
     * @param skuId
     */
    private void checkItemMergeConfig(Long skuId, Integer skuType, List<Long> serviceList, Object jdhSku) {
        log.info("ProductApplicationImpl.checkItemMergeConfig skuId={} skuType={} serviceList={}", skuId, skuType, JSON.toJSONString(serviceList));
        MergeMedicalPromiseCheckConfig mergeMedicalPromiseCheckConfig = duccConfig.getMergeMedicalPromiseCheckConfig();
        log.info("ProductApplicationImpl.checkItemMergeConfig mergeMedicalPromiseCheckConfig={}", JSON.toJSONString(mergeMedicalPromiseCheckConfig));
        if (mergeMedicalPromiseCheckConfig == null || !Boolean.TRUE.equals(mergeMedicalPromiseCheckConfig.getConfigSwitch())) {
            return;
        }
        // 白名单不校验
        if (CollUtil.isNotEmpty(mergeMedicalPromiseCheckConfig.getExcludeSkuIdList()) && mergeMedicalPromiseCheckConfig.getExcludeSkuIdList().contains(skuId)) {
            return;
        }
        // 项目为空不校验
        if (CollUtil.isEmpty(serviceList)) {
            return;
        }
        // 主品为一个项目不校验
        if (SkuRelTypeEnum.MAIN_ITEM.getType().equals(skuType) && serviceList.size() == 1) {
            return;
        }
        if (StringUtils.isBlank(mergeMedicalPromiseCheckConfig.getSkuExpress())) {
            return;
        }
        Map<String, Object> param = new HashMap<>(1);
        param.put("sku", jdhSku);
        if (!(Boolean) AviatorEvaluator.compile(mergeMedicalPromiseCheckConfig.getSkuExpress(), Boolean.TRUE).execute(param)){
            return;
        }
        Map<String, List<String>> mergeMedicalPromiseConfig = duccConfig.getMergeMedicalPromiseConfig();
        if (CollUtil.isEmpty(mergeMedicalPromiseConfig)) {
            throw new BusinessException(SystemErrorCode.CONFIG_ERROR.formatDescription("项目合管配置为空"));
        }
        Set<String> serviceItemSet = serviceList.stream().map(String::valueOf).collect(Collectors.toSet());

        for (List<String> itemList : mergeMedicalPromiseConfig.values()) {
            HashSet<String> resSet = new HashSet<>(serviceItemSet);
            resSet.retainAll(new HashSet<>(itemList));
            if (resSet.size() == itemList.size()) {
                return;
            }
        }
        // 主品，商品服务项目>1时，需要校验多个服务项目是否可以命中ducc配置的合管规则（被置换成1个项目）
        // 如不能则不允许创建，报错’该商品下的服务项目无法合管，请检查配置。
        if (SkuRelTypeEnum.MAIN_ITEM.getType().equals(skuType)) {
            throw new BusinessException(SystemErrorCode.CONFIG_ERROR.formatDescription("该商品下的服务项目无法合管，请检查配置。"));
        }
        // 结算页附加项设置：主sku+子sku需要命中ducc合管规则，如未命中则报错‘主子sku无法合管检测，请检查配置。
        if (SkuRelTypeEnum.ADD_ITEM.getType().equals(skuType)) {
            throw new BusinessException(SystemErrorCode.CONFIG_ERROR.formatDescription("主子sku无法合管检测，请检查配置。"));
        }
    }

    /**
     * 转换商品扩展信息
     */
    private void buildSkuExtend(JdhSkuRequest request, JdhSkuDto jdhSkuCustomDto) {
        try {
            if (Boolean.TRUE.equals(request.getQuerySkuExtendData())) {
                JdhSkuExtend jdhSkuExtend = new JdhSkuExtend();
                jdhSkuExtend.setSkuId(request.getSkuId());
                List<JdhSkuExtend> jdhSkuExtendList = jdhSkuRepository.queryJdhSkuExtendList(jdhSkuExtend);
                if (CollUtil.isNotEmpty(jdhSkuExtendList)) {
                    Map<String, String> maps = jdhSkuExtendList.stream().collect(Collectors.toMap(JdhSkuExtend::getAttribute, JdhSkuExtend::getValue, (existing, replacement) -> replacement));
                    JdhSkuExtendBo jdhSkuExtendDto = cn.hutool.core.bean.BeanUtil.mapToBean(maps, JdhSkuExtendBo.class, true, CopyOptions.create());
                    if (jdhSkuExtendDto == null) {
                        return;
                    }
                    if (StringUtils.isNotBlank(jdhSkuExtendDto.getHighQualityStoreId())) {
                        jdhSkuCustomDto.setHighQualityStoreId(JSON.parseArray(jdhSkuExtendDto.getHighQualityStoreId(), String.class));
                    }
                }

            }
        } catch (Exception exception) {
            log.error("ProductApplicationImpl.buildSkuExtend 查询商品扩展信息失败",exception);
        }
    }
}