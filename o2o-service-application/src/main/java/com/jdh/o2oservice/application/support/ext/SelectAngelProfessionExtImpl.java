package com.jdh.o2oservice.application.support.ext;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelProfessionRel;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectDataExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachDataParam;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.trade.enums.OrderAggregateCode;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.vo.JdSkuRelationInfoVo;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 触达领域扩展点，查询触达任务需要的实体，查询服务者职业
 * @author: yangxiyu
 * @date: 2024/4/21 7:54 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class SelectAngelProfessionExtImpl implements ReachServiceSelectDataExt {

    /**
     * 服务者职业
     */
    private static final String ANGEL_PROFESSION = "angelProfession";

    private static final String ANGEL_WORK = "angelWork";

    private static final String SKU_DATA = "skuData";

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private VoucherRepository voucherRepository;

    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private JdOrderRepository jdOrderRepository;

    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    /**
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Map<String, Object> selectData(SelectReachDataParam param) {
        Map<String, Object> res = Maps.newHashMap();
        if(StringUtils.isBlank(param.getAggregateId()) || StringUtils.isBlank(param.getDomainCode())){
            return res;
        }
        if(DomainEnum.PROMISE.getCode().equals(param.getDomainCode()) && PromiseAggregateEnum.VOUCHER.getCode().equals(param.getAggregateCode())){
            //查询服务单
            JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(Long.parseLong(param.getAggregateId())).build());

            //查询工单及服务者和服务者职业
            PromiseRepQuery query = new PromiseRepQuery();
            query.setVoucherIds(Lists.newArrayList(jdhVoucher.getVoucherId()));
            List<JdhPromise> list = promiseRepository.findList(query);
            if(CollectionUtils.isEmpty(list)) {
                return res;
            }
            JdhPromise jdhPromise = list.get(0);
            AngelWork lastAngelWork = angelWorkRepository.findLastAngelWork(jdhPromise.getPromiseId());
            if(Objects.isNull(lastAngelWork) || StringUtils.isBlank(lastAngelWork.getAngelId())){
                return res;
            }

            JdhAngelRepQuery angelQuery = new JdhAngelRepQuery();
            angelQuery.setAngelId(Long.valueOf(lastAngelWork.getAngelId()));
            JdhAngel jdhAngel = angelRepository.queryAngelDetail(angelQuery);
            if(Objects.isNull(jdhAngel) || CollectionUtils.isEmpty(jdhAngel.getJdhAngelProfessionRelList())){
                return res;
            }
            JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
            res.put(ANGEL_PROFESSION, jdhAngelProfessionRel);
            res.put(ANGEL_WORK, lastAngelWork);
        }else if(DomainEnum.PROMISE.getCode().equals(param.getDomainCode()) && PromiseAggregateEnum.PROMISE.getCode().equals(param.getAggregateCode())){
            AngelWork lastAngelWork = angelWorkRepository.findLastAngelWork(Long.parseLong(param.getAggregateId()));
            if(Objects.isNull(lastAngelWork) || StringUtils.isBlank(lastAngelWork.getAngelId())){
                return res;
            }

            JdhAngelRepQuery angelQuery = new JdhAngelRepQuery();
            angelQuery.setAngelId(Long.valueOf(lastAngelWork.getAngelId()));
            JdhAngel jdhAngel = angelRepository.queryAngelDetail(angelQuery);
            if(Objects.isNull(jdhAngel) || CollectionUtils.isEmpty(jdhAngel.getJdhAngelProfessionRelList())){
                return res;
            }
            JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
            res.put(ANGEL_PROFESSION, jdhAngelProfessionRel);
            res.put(ANGEL_WORK, lastAngelWork);
        }else if(DomainEnum.TRADE.getCode().equals(param.getDomainCode()) && OrderAggregateCode.ORDER.getCode().equals(param.getAggregateCode())) {
            Long orderId  = Long.valueOf(param.getAggregateId());
            JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
            List<JdOrderItem> items = jdOrderItemRepository.listByOrderId(jdOrder.getOrderId());

            jdOrder.setJdOrderItemList(items);
            res.put(OrderAggregateCode.ORDER.getCode(), jdOrder);

            //查询商品配置的服务模式,4号单没有父子单场景，此处不判断父子单
            Set<Long> skuIdSet = items.stream().map(JdOrderItem::getSkuId).collect(Collectors.toSet());
            JdhSkuListRequest request = new JdhSkuListRequest();
            request.setSkuIdList(skuIdSet);
            List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(request);
            if(CollectionUtils.isEmpty(jdhSkus)) {
                return res;
            }
            JdhSku jdhSku = jdhSkus.get(0);
            res.put(SKU_DATA, jdhSku);
            res.put(OrderAggregateCode.ORDER.getCode(), jdOrder);

            //查询工单及服务者和服务者职业,4号单没有父子单场景，此处不判断父子单
            AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
            angelWorkDBQuery.setJdOrderId(jdOrder.getOrderId());
            List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
            if(CollectionUtils.isEmpty(angelWorkList)){
                return res;
            }
            AngelWork angelWork = angelWorkList.get(0);
            res.put(ANGEL_WORK, angelWork);

            JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
            jdhAngelRepQuery.setAngelId(Long.valueOf(angelWork.getAngelId()));
            JdhAngel jdhAngel = angelRepository.queryAngelDetail(jdhAngelRepQuery);
            if(Objects.isNull(jdhAngel) || CollectionUtils.isEmpty(jdhAngel.getJdhAngelProfessionRelList())){
                return res;
            }
            JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
            res.put(ANGEL_PROFESSION, jdhAngelProfessionRel);
        }
        return res;
    }

    @Override
    public String functionId() {
        return "selectAngelProfession";
    }

    public static void main(String[] args) {
//        PromiseModifyEventBody modifyTime = JSON.parseObject("{\"afterStatus\":5,\"afterTime\":{\"appointmentEndTime\":\"2024-08-27T00:00:00\",\"appointmentStartTime\":\"2024-08-27T00:00:00\",\"dateType\":1},\"beforeStatus\":3,\"beforeTime\":{\"appointmentEndTime\":\"2024-08-28T00:00:00\",\"appointmentStartTime\":\"2024-08-28T00:00:00\",\"dateType\":1,\"isImmediately\":false}}\t", PromiseModifyEventBody.class);
//        String expressionStr = "lastModifyAppointmentTime.afterTime#formatDesc";
//        DynamicParse expression = new DynamicParse();
//        expression.setParseExpression(expressionStr);
//        expression.setParseType(2);
//
//        Map<String, Object> data = Maps.newHashMap();
//        data.put("lastModifyAppointmentTime", modifyTime);
//
//        Object value = expression.parse(data);
//        System.out.println(Objects.toString(value));


        JdhPromise promise = new JdhPromise();

        List<PromiseService> services = Lists.newArrayList();
        PromiseService service1 = new PromiseService();
        service1.setServiceName("项目1");
        services.add(service1);

        PromiseService service2 = new PromiseService();
        service2.setServiceName("项目2");
        services.add(service2);

        promise.setServices(services);

        Map<String, Object> map = Maps.newHashMap();
        map.put("promise", promise);
        System.out.println(BeanUtil.getProperty(map, "promise.services[0].serviceName").toString());
        System.out.println(BeanUtil.getProperty(map, "promise.services.size").toString());

    }

}
