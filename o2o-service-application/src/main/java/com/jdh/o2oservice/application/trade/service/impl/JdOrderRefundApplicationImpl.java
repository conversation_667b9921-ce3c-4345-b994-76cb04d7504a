package com.jdh.o2oservice.application.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.trade.convert.TradeOrderRefundConverter;
import com.jdh.o2oservice.application.trade.service.JdOrderRefundApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.AbilityCode;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.AbilityExecutor;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.HasAddedEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.trade.bo.RefundResult;
import com.jdh.o2oservice.core.domain.trade.context.OrderRefundContext;
import com.jdh.o2oservice.core.domain.trade.context.QueryOrderRefundAmountContext;
import com.jdh.o2oservice.core.domain.trade.enums.RefundStatusEnum;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderRefundEventBody;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderMoney;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundDetail;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundTask;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderMoneyRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRefundDetailRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRefundTaskRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import com.jdh.o2oservice.core.domain.trade.service.TradeDomainService;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmount;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmountExpand;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseSettleStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import com.jdh.o2oservice.export.promise.enums.VoucherOpEnum;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.trade.dto.OrderRefundAmountDto;
import com.jdh.o2oservice.export.trade.dto.OrderRefundTaskDto;
import com.jdh.o2oservice.export.trade.query.QueryRefundAmountParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JdOrderRefundApplication
 *
 * <AUTHOR>
 * @version 2024/4/23 23:15
 **/
@Slf4j
@Service
public class JdOrderRefundApplicationImpl implements JdOrderRefundApplication {

    /**
     * jdOrderRefundTaskRepository
     */
    @Resource
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;
    /**
     * jdOrderRefundDetailRepository
     */
    @Resource
    private JdOrderRefundDetailRepository jdOrderRefundDetailRepository;

    /**
     * tradeDomainService
     */
    @Resource
    private TradeDomainService tradeDomainService;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;
    /**
     * conditions
     */
    private final Map<String, DomainAbility> conditions = Maps.newConcurrentMap();

    /**
     * actions
     */
    private final Map<String, DomainAbility> actions = Maps.newConcurrentMap();

    /**
     * applicationContext
     */
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 服务者履约单
     */
    @Resource
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * jdOrderMoneyRepository
     */
    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;
    /**
     * promiseExtApplication
     */
    @Resource
    private PromiseExtApplication promiseExtApplication;
    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    @Resource
    private JdhSkuRepository jdhSkuRepository;
    /**
     * medicalPromiseApplication
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;


    /**
     * 保存退款任务
     * @param jdOrderRefundTask
     * @param jdOrderRefundDetailList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveJdOrderRefundTask(JdOrderRefundTask jdOrderRefundTask,List<JdOrderRefundDetail> jdOrderRefundDetailList) {
        jdOrderRefundTaskRepository.save(jdOrderRefundTask);
        jdOrderRefundDetailRepository.batchSaveRefundDetail(jdOrderRefundDetailList);
    }

    /**
     * 退款明细列表
     *
     * @param jdOrderRefundDetail
     * @return
     */
    @Override
    public List<JdOrderRefundDetail> findJdOrderRefundDetailList(JdOrderRefundDetail jdOrderRefundDetail) {
        return jdOrderRefundDetailRepository.findJdOrderRefundDetailList(jdOrderRefundDetail);
    }

    /**
     * 查询订单退款操作记录
     *
     * @param orderId
     * @return
     */
    @Override
    public List<OrderRefundTaskDto> queryOrderRefundRecord(Long orderId) {
        if(Objects.isNull(orderId)){
            return Arrays.asList();
        }
        //查询订单信息
        JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderId)).build());
        AssertUtils.nonNull(orderDetail,TradeErrorCode.ORDER_IS_NULL);

        boolean isParentOrder = orderDetail.getParentId().intValue() == 0;
        Long parentId = null;
        if (!isParentOrder){
            JdOrder parentOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetail.getParentId()).build());
            if (HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())){
                parentId = parentOrder.getOrderId();
                orderId = null;
            }
        }

        JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
        jdOrderRefundTask.setOrderId(orderId);
        jdOrderRefundTask.setParentId(parentId);
        List<JdOrderRefundTask> jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        List<OrderRefundTaskDto> orderRefundTaskDtoList = new ArrayList<>();
        if(CollUtil.isNotEmpty(jdOrderRefundTaskList)){
            jdOrderRefundTaskList.forEach( temp -> {
                OrderRefundTaskDto orderRefundTaskDto = TradeOrderRefundConverter.INSTANCE.convertOrderRefundTaskDto(temp);
                orderRefundTaskDto.setRefundTypeDesc(RefundTypeEnum.getRefundTypeDesc(orderRefundTaskDto.getRefundType()));
                orderRefundTaskDtoList.add(orderRefundTaskDto);
            });
        }
        return orderRefundTaskDtoList;
    }

    @Override
    public List<OrderRefundTaskDto> queryAddOrderRefundRecord(Long orderId) {
        if(Objects.isNull(orderId)){
            return Arrays.asList();
        }
        //查询加项订单退款记录
        JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
        jdOrderRefundTask.setOrderId(orderId);
        List<JdOrderRefundTask> jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        List<OrderRefundTaskDto> orderRefundTaskDtoList = new ArrayList<>();
        if(CollUtil.isNotEmpty(jdOrderRefundTaskList)){
            jdOrderRefundTaskList.forEach( temp -> {
                OrderRefundTaskDto orderRefundTaskDto = TradeOrderRefundConverter.INSTANCE.convertOrderRefundTaskDto(temp);
                orderRefundTaskDto.setRefundTypeDesc(RefundTypeEnum.getRefundTypeDesc(orderRefundTaskDto.getRefundType()));
                orderRefundTaskDtoList.add(orderRefundTaskDto);
            });
        }
        return orderRefundTaskDtoList;
    }

    /**
     * 查询订单退款操作记录
     *
     * @param jdOrderRefundTask
     * @return
     */
    @Override
    public List<OrderRefundTaskDto> queryOrderRefundTaskByPromiseId(JdOrderRefundTask jdOrderRefundTask) {
        if(Objects.isNull(jdOrderRefundTask.getOrderId()) || Objects.isNull(jdOrderRefundTask.getPromiseId())){
            return Arrays.asList();
        }
        JdOrderRefundTask jdOrderRefundQuery = JdOrderRefundTask.builder().build();
        jdOrderRefundQuery.setOrderId(jdOrderRefundTask.getOrderId());
        jdOrderRefundQuery.setPromiseId(jdOrderRefundTask.getPromiseId());
        List<JdOrderRefundTask> jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        List<OrderRefundTaskDto> orderRefundTaskDtoList = new ArrayList<>();
        if(CollUtil.isNotEmpty(jdOrderRefundTaskList)){
            jdOrderRefundTaskList.forEach( temp -> {
                OrderRefundTaskDto orderRefundTaskDto = TradeOrderRefundConverter.INSTANCE.convertOrderRefundTaskDto(temp);
                orderRefundTaskDto.setRefundTypeDesc(RefundTypeEnum.getRefundTypeDesc(orderRefundTaskDto.getRefundType()));
                orderRefundTaskDtoList.add(orderRefundTaskDto);
            });
        }
        return orderRefundTaskDtoList;
    }

    /**
     * 退款状态更新
     *
     * @param refundResultList
     * @return
     */
    @Override
    public int updateRefundStatusByTransactionNum(List<RefundResult> refundResultList) {
        if(CollUtil.isNotEmpty(refundResultList)){
            refundResultList.forEach(refundResult -> {
                jdOrderRefundDetailRepository.updateRefundStatusByTransactionNum(refundResult.getTransactionNumber(),refundResult.getRefundResult());
            });
        }
        return 1;
    }

    /**
     * 申请退款
     *
     * @param detail
     * @return
     */
    @Override
    public Boolean appointmentRefund(JdOrderRefundDetail detail) {
        return tradeDomainService.appointmentRefund(detail);
    }

    /**
     * 退款明细列表
     *
     * @param transactionNum
     * @return
     */
    @Override
    public JdOrderRefundDetail findJdOrderRefundDetailByTransactionNum(String transactionNum) {
        return jdOrderRefundDetailRepository.findJdOrderRefundDetailByTransactionNum(transactionNum);
    }

    /**
     * 退款成功，处理后续
     *
     * @param transactionNum
     * @param jdOrderRefundDetail
     * @return
     */
    @Override
    public int updateRefundSuccByTransactionNum(String transactionNum,JdOrderRefundDetail jdOrderRefundDetail) {
        int result = jdOrderRefundDetailRepository.updateRefundStatusByTransactionNum(transactionNum, RefundStatusEnum.REFUND_SUCCESS.getType());
        JdOrderRefundDetail orderRefundDetail = JdOrderRefundDetail.builder().taskId(jdOrderRefundDetail.getTaskId()).refundStatusList(Arrays.asList(RefundStatusEnum.NO_REFUND.getType(),
                RefundStatusEnum.REFUND_ING.getType(),RefundStatusEnum.REFUND_FAIL.getType())).build();

        List<JdOrderRefundDetail> jdOrderRefundDetailList = jdOrderRefundDetailRepository.findJdOrderRefundDetailList(orderRefundDetail);
        if(CollUtil.isEmpty(jdOrderRefundDetailList)){
            jdOrderRefundTaskRepository.updateRefundStatusByTaskId(jdOrderRefundDetail.getTaskId(),RefundStatusEnum.REFUND_SUCCESS.getType());
            JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
            jdOrderRefundTask.setTaskId(jdOrderRefundDetail.getTaskId());
            JdOrderRefundTask refundTask = jdOrderRefundTaskRepository.findJdOrderRefundTask(jdOrderRefundTask);
            jdOrderRefundDetail.setRefundReason(refundTask.getRefundReason());
            jdOrderRefundDetail.setRefundReasonCode(refundTask.getRefundReasonCode());
            eventCoordinator.publish((EventFactory.newDefaultEvent(jdOrderRefundDetail, TradeEventTypeEnum.SELF_ORDER_SUCC_REFUND,new OrderRefundEventBody(jdOrderRefundDetail))));
        }
        return result;
    }

    /**
     * 获取订单已退款金额
     *
     * @param orderId
     * @return
     */
    @Override
    public BigDecimal getOrderRefundAmount(Long orderId) {
        JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
        jdOrderRefundTask.setOrderId(orderId);
        jdOrderRefundTask.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.getType());
        List<JdOrderRefundTask> jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);

        BigDecimal orderRefundAmount = BigDecimal.ZERO;
        if(CollUtil.isNotEmpty(jdOrderRefundTaskList)){
            jdOrderRefundTaskList.forEach(temp -> {
                orderRefundAmount.add(temp.getRefundAmount());
            });
        }
        return orderRefundAmount;
    }

    /**
     * 查询订单退款金额
     *
     * @param queryRefundAmountParam
     * @return
     */
    @Override
    public OrderRefundAmountDto queryOrderRefundAmount(QueryRefundAmountParam queryRefundAmountParam) {
        log.info("JdOrderRefundApplicationImpl queryOrderRefundAmount queryRefundAmountParam={}", queryRefundAmountParam);
        Long orderId = queryRefundAmountParam.getOrderId();
        AssertUtils.nonNull(orderId, TradeErrorCode.ORDER_ID_NULL);

        //查询订单信息
        JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderId)).build());
        AssertUtils.nonNull(orderDetail,TradeErrorCode.ORDER_IS_NULL);

        Integer queryAmountType = queryRefundAmountParam.getQueryAmountType();
        AssertUtils.nonNull(queryAmountType, TradeErrorCode.QUERY_REFUND_AMOUNT_TYPE_NULL);
        AssertUtils.nonNull(queryRefundAmountParam.getPromiseId(), TradeErrorCode.QUERY_REFUND_AMOUNT_PROIMSE_NULL);

        boolean isParentOrder = orderDetail.getParentId().intValue() == 0;
        Boolean lastChildOrder = false;
        Boolean hasAdded = false;
        List<OrderRefundAmountDto> orderRefundAmountDtoList = new ArrayList<>();
        if (!isParentOrder){
            JdOrder parentOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetail.getParentId()).userPin(queryRefundAmountParam.getUserPin()).build());
            if (HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())){
                hasAdded = true;
            }
        }

        if(hasAdded){
            List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderDetail.getParentId()).build());
            if(CollUtil.isNotEmpty(jdOrderList)){
                for(JdOrder jdOrder : jdOrderList){
                    queryRefundAmountParam.setOrderId(jdOrder.getOrderId());
                    queryRefundAmountParam.setQueryAmountType(RefundTypeEnum.ORDER_REFUND.getType());
                    OrderRefundAmountDto temp = calcMultipleOrderRefundAmount(queryRefundAmountParam, jdOrder,true);
                    orderRefundAmountDtoList.add(temp);
                }
            }
        }else{
            if(RefundTypeEnum.AMOUNT_REFUND.getType().equals(queryRefundAmountParam.getQueryAmountType())){
                lastChildOrder = calcPromiseRefundLastPatientId(queryRefundAmountParam.getOrderId(),queryRefundAmountParam.getPromiseId()
                        ,queryRefundAmountParam.getPromisePatientIdList());
            } else if (RefundTypeEnum.ORDER_REFUND.getType().equals(queryRefundAmountParam.getQueryAmountType())) {
                lastChildOrder = true;
            }
            OrderRefundAmountDto temp = calcMultipleOrderRefundAmount(queryRefundAmountParam, orderDetail,lastChildOrder);
            orderRefundAmountDtoList.add(temp);
        }
        OrderRefundAmountDto orderRefundAmountDto = new OrderRefundAmountDto();
        BigDecimal suggestRefundAmount = BigDecimal.ZERO;
        BigDecimal mostRefundAmount = BigDecimal.ZERO;
        if(CollUtil.isNotEmpty(orderRefundAmountDtoList)){
            for(OrderRefundAmountDto orderRefundAmount : orderRefundAmountDtoList){
                mostRefundAmount = mostRefundAmount.add(orderRefundAmount.getMostRefundAmount());
                suggestRefundAmount = suggestRefundAmount.add(orderRefundAmount.getSuggestRefundAmount());
            }
        }
        orderRefundAmountDto.setMostRefundAmount(mostRefundAmount);
        orderRefundAmountDto.setSuggestRefundAmount(suggestRefundAmount);
        log.info("JdOrderRefundApplicationImpl queryOrderRefundAmount orderRefundAmountDto={}", orderRefundAmountDto);
        return orderRefundAmountDto;
    }

    @Override
    public List<JdOrderRefundTask> findJdOrderRefundTaskList(JdOrderRefundTask jdOrderRefundTask) {
        return jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);}

    /**
     *
     * @param queryRefundAmountParam
     * @param orderDetail
     * @param lastChildOrder
     * @return
     */
    private OrderRefundAmountDto calcMultipleOrderRefundAmount(QueryRefundAmountParam queryRefundAmountParam, JdOrder orderDetail,Boolean lastChildOrder) {
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(orderDetail.getOrderId());
        if(CollUtil.isEmpty(jdOrderMoneyList)){
            jdOrderMoneyList = createOrderMoneyInfo(orderDetail.getOrderId());
            jdOrderMoneyRepository.batchSave(jdOrderMoneyList);
        }

        QueryOrderRefundAmountContext context = TradeOrderRefundConverter.INSTANCE.convertToOrderRefundAmountContext(queryRefundAmountParam);
        context.setVerticalCode(orderDetail.getVerticalCode());
        context.setServiceType(orderDetail.getServiceType());
        context.setOrderDetail(orderDetail);
        context.setLastChildOrder(lastChildOrder);
        context.setWorkStatus(getPromiseWorkStauts(queryRefundAmountParam.getPromiseId()));
        /**增强服务类型用于退款*/
        enhanceServiceType4refund(orderDetail,context);
        context.init(TradeEventTypeEnum.QUERY_AMOUNT_REFUND);
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);
        OrderRefundAmountDto orderRefundAmountDto = new OrderRefundAmountDto();
        orderRefundAmountDto.setMostRefundAmount(context.getMostRefundAmount());
        orderRefundAmountDto.setSuggestRefundAmount(context.getSuggestRefundAmount());
        log.info("JdOrderRefundApplicationImpl calcMultipleOrderRefundAmount orderRefundAmountDto={}", orderRefundAmountDto);
        return orderRefundAmountDto;
    }
    /**
     *
     * @param orderId
     * @param promiseId
     * @param promisePatientIdList
     * @return
     */
    private Boolean calcPromiseRefundLastPatientId(Long orderId,Long promiseId,List<Long> promisePatientIdList){
        List<Long> promiseIdList = new ArrayList<>();
        List<PromiseDto> result = promiseExtApplication.getPromiseByOrderItemId(String.valueOf(orderId));
        if(CollUtil.isEmpty(result)){
            promiseIdList.add(promiseId);
        }else{
            promiseIdList = result.stream().map(PromiseDto::getPromiseId).collect(Collectors.toList());
        }

        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseIdList(promiseIdList);
        // 检测单列表
        List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
        if(medicalPromises.size() == CommonConstant.ONE){
            return Boolean.TRUE;
        }

        List<MedicalPromiseDTO> medicalPromisesTemp = medicalPromises.stream().filter(medical ->
                !promisePatientIdList.contains(medical.getPromisePatientId())).collect(Collectors.toList());

        // 退款中的，先不记收入
        if(CollUtil.isEmpty(medicalPromisesTemp)) {
            return Boolean.TRUE;
        }

        List<MedicalPromiseDTO> medicalPromisesInvalid = medicalPromisesTemp.stream().filter(medical -> !(medical.getFreeze().equals(CommonConstant.ONE) ||
                MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus()))).collect(Collectors.toList());
        if(CollUtil.isEmpty(medicalPromisesInvalid)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     *
     * @param promiseId
     * @return
     */
    private Integer getPromiseWorkStauts(Long promiseId){
        Integer	workStatus = AngelWorkStatusEnum.WAIT_RECEIVE.getType();
        AngelWorkDetailDto angelWorkDto = angelPromiseApplication.queryAngelWork(AngelWorkQuery.builder().promiseId(promiseId).build());
        if(Objects.nonNull(angelWorkDto) && Objects.nonNull(angelWorkDto.getStatus())){
            workStatus = angelWorkDto.getStatus();
        }
        return workStatus;
    }

        /**
         *
         * @param medicalPromiseListRequest
         * @return
         */
        private List<MedicalPromiseDTO> getMedicalPromises(MedicalPromiseListRequest medicalPromiseListRequest){
            List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
            return medicalPromises;
        }


    /**
     * 只想配置的condition和action
     * 这个execute和状态机调用的差异是，executor不由履约单状态控制
     * @param executor
     * @param context
     */
    private void execute(AbilityExecutor executor, BusinessContext context){
        // 未查询到当前状态+事件配置的执行器
        if (Objects.isNull(executor)){
            log.error("JdOrderRefundApplicationImpl submitDraft 未找到状态机执行器");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR);
        }
        List<String> validConditions = executor.getConditionCodes();
        for (String code : validConditions) {
            DomainAbility condition = conditions.get(code);
            condition.execute(context);
            log.info("JdOrderRefundApplicationImpl submitDraft conditionCode={}, context={}", code, JSON.toJSONString(context));
        }
        List<String> actionCodes = executor.getActionCodes();
        for (String code : actionCodes) {
            DomainAbility action = actions.get(code);
            action.execute(context);
            log.info("JdOrderRefundApplicationImpl submitDraft cationCode={}, context={}", code, JSON.toJSONString(context));
        }
    }

    /**
     * init
     */
    @PostConstruct
    public void init(){
        Map<String, DomainAbility> map = applicationContext.getBeansOfType(DomainAbility.class);
        for (DomainAbility ability : map.values()) {
            if (!Objects.equals(ability.getAbilityCode().domainType().getCode(), DomainEnum.TRADE.getCode())){
                log.info("JdOrderRefundApplicationImpl init continue code={}", ability.getAbilityCode().getCode());
                continue;
            }
            if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.ACTION)){
                actions.put(ability.getAbilityCode().getCode(), ability);
            }else if(Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.CONDITION)){
                conditions.put(ability.getAbilityCode().getCode(), ability);
            }
        }
    }

    /**
     * 生成订单金额信息列表
     *
     * @param orderId
     * @return
     */
    private static List<JdOrderMoney> createOrderMoneyInfo(Long orderId) {
        OrderCalculationQueryServiceRpc orderCalculationQueryServiceRpc = SpringUtil.getBean(OrderCalculationQueryServiceRpc.class);
        String amountAndExpand = orderCalculationQueryServiceRpc.queryOrderSplitAmountAndExpand(orderId);
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        if(StringUtils.isBlank(amountAndExpand)){
            return jdOrderMoneyList;
        }
        List<OrderAmount> orderAmountList = JSON.parseArray(amountAndExpand, OrderAmount.class);
        log.info("JdOrderRefundApplicationImpl -> createOrderMoneyInfo, orderId={}, orderAmountList={}", orderId, JSON.toJSONString(orderAmountList));

        if (CollectionUtils.isEmpty(orderAmountList)){
            log.info("JdOrderRefundApplicationImpl -> createOrderMoneyInfo 获取订单金额明细失败, orderId={}", orderId);
            return jdOrderMoneyList;
        }
        for (OrderAmount orderAmount : orderAmountList) {
            if (null == orderAmount.getSkuId()){
                log.info("JdOrderRefundApplicationImpl -> createOrderMoneyInfo, skuid is null,  orderAmount={}", JSON.toJSONString(orderAmount));
                continue;
            }
            Map<Integer,List<OrderAmountExpand>> orderAmountExpandMap = orderAmount.getAmountExpands().stream().collect(Collectors.groupingBy(b -> b.getType()));
            for(Map.Entry<Integer, List<OrderAmountExpand>> orderAmountExpand: orderAmountExpandMap.entrySet()){
                JdOrderMoney entity = new JdOrderMoney();
                List<OrderAmountExpand> orderAmountExpandList = orderAmountExpand.getValue();
                Integer amountType = orderAmountExpand.getKey();
                BigDecimal amount = new BigDecimal(0);
                for(OrderAmountExpand amountExpand : orderAmountExpandList){
                    amount = amount.add(amountExpand.getAmount());
                }
                if(amount.compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                entity.setOrderId(orderAmount.getOrderId());
                entity.setSkuId(orderAmount.getSkuId());
                entity.setMoneyType(amountType);
                entity.setAmount(amount);
                entity.setVersion(NumConstant.NUM_1);
                entity.setYn(YnStatusEnum.YES.getCode());
                entity.setCreateTime(new Date());
                entity.setUpdateTime(new Date());
                jdOrderMoneyList.add(entity);
            }
        }

        return jdOrderMoneyList;
    }
    /**
     * 增强服务类型用于退款
     * @param orderDetail
     * @param context
     */
    private void enhanceServiceType4refund(JdOrder orderDetail, QueryOrderRefundAmountContext context) {
        if (Objects.nonNull(orderDetail.getJdOrderItemList()) && CollectionUtils.isNotEmpty(orderDetail.getJdOrderItemList())) {
            Long skuId = orderDetail.getJdOrderItemList().get(0).getSkuId();
            JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(skuId).build());
            context.setServiceTypeNew(jdhSku.getServiceType());
        }
    }
}
