package com.jdh.o2oservice.application.angel.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.angel.service.AngelQueryApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.BeanUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.ServiceTypeNewEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelExtendKeyEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchFilterContext;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.service.ability.filter.DispatchFilterServiceAddress;
import com.jdh.o2oservice.core.domain.dispatch.service.ability.filter.DispatchFilterSkill;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelProfessionRelDto;
import com.jdh.o2oservice.export.angel.query.QueryIntendedAngelRequest;
import com.jdh.o2oservice.export.angel.query.QueryLastAngelRequest;
import com.jdh.o2oservice.export.angelpromise.dto.LastCompleteAngelDto;
import com.jdh.o2oservice.infrastructure.repository.db.dao.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/18 10:27
 */
@Component
@Slf4j
public class AngelQueryApplicationImpl implements AngelQueryApplication {

    /**
     * jdhPromisePoMapper
     */
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;
    @Resource
    private JdhAngelWorkPoMapper jdhAngelWorkPoMapper;
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private JdhMedicalPromisePoMapper jdhMedicalPromisePoMapper;
    @Resource
    private JdhAngelExtendPoMapper jdhAngelExtendPoMapper;
    @Resource
    private ProductApplication productApplication;
    @Resource
    private DispatchFilterServiceAddress dispatchFilterServiceAddress;
    @Resource
    private DispatchFilterSkill dispatchFilterSkill;
    @Resource
    private JdhAngelProfessionRelPoMapper jdhAngelProfessionRelPoMapper;

    /**
     * 查询用户最近（默认一年）服务过的历史护士。
     * （1）查询预约时间在startTime之后，履约完成的promise单，需要根据业务模式指定身份。
     * （2）根据promise查询work信息,需要按照服务时间排序；
     * （3）根据work查询护士信息；
     * （4）根据护士技能信息匹配当前服务，这个环境应该走的是一个模拟派单的环节
     *
     * @param request
     * @return
     */
    @Override
    public List<LastCompleteAngelDto> listLastAngel(QueryLastAngelRequest request) {
        log.info("AngelQueryApplicationImpl listLastAngel request={}", JSON.toJSONString(request));
        AssertUtils.hasText(request.getServiceType(), "serviceType is require");
        AssertUtils.hasText(request.getBusinessMode(), "businessMode is require");
        AssertUtils.hasText(request.getUserPin(), "userPin is require");
        List<JdhVerticalBusiness> businessList = verticalBusinessRepository.findByBusinessMode(request.getBusinessMode());
        List<String> verticalCodes = businessList.stream().map(JdhVerticalBusiness::getVerticalCode).collect(Collectors.toList());
        if (Objects.isNull(request.getEndTime())) {
            request.setEndTime(new Date());
        }
        if (Objects.isNull(request.getStartTime())) {
            Date start = DateUtils.addYears(request.getEndTime(), -1);
            request.setStartTime(start);
        }

        // 获取最近一年提供过服务，并且地区匹配当前用户地址的履约单信息，倒序排序
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getUserPin, request.getUserPin())
                .eq(JdhPromisePo::getPromiseStatus, JdhPromiseStatusEnum.COMPLETE.getStatus())
                .in(JdhPromisePo::getVerticalCode, verticalCodes)
                .gt(JdhPromisePo::getAppointmentStartTime, request.getStartTime())
                .le(JdhPromisePo::getAppointmentStartTime, request.getEndTime())
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhPromisePo::getAppointmentStartTime);

        List<JdhPromisePo> promisePos = jdhPromisePoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(promisePos)) {
            return Collections.emptyList();
        }

        // promiseIds的顺序是根据预约时间倒序排的
        List<Long> promiseIds = promisePos.stream().map(JdhPromisePo::getPromiseId).collect(Collectors.toList());
        LambdaQueryWrapper<JdhAngelWorkPo> angelWorkWrapper = Wrappers.lambdaQuery();
        angelWorkWrapper.in(JdhAngelWorkPo::getPromiseId, promiseIds)
                .eq(JdhAngelWorkPo::getWorkStatus, AngelWorkStatusEnum.COMPLETED.getType())
                .orderByDesc(JdhAngelWorkPo::getWorkEndTime)
                .eq(JdhAngelWorkPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhAngelWorkPo> workPos = jdhAngelWorkPoMapper.selectList(angelWorkWrapper);
        if (CollectionUtils.isEmpty(workPos)) {
            return Collections.emptyList();
        }

        // 按照顺序去重angelId
        List<Long> sortUniqAngelIds = workPos.stream().map(e -> Long.valueOf(e.getAngelId())).collect(Collectors.toList());
        Iterator<Long> iterator = sortUniqAngelIds.iterator();
        Set<Long> exist = Sets.newHashSet();
        while (iterator.hasNext()) {
            Long id = iterator.next();
            if (exist.contains(id)) {
                iterator.remove();
            } else {
                exist.add(id);
            }
        }
        log.info("AngelQueryApplicationImpl->listLastAngel sortUniqAngelIds={}", JSON.toJSONString(sortUniqAngelIds));
        DispatchFilterContext context = DispatchFilterContext.builder()
                .validAngelIds(sortUniqAngelIds).serviceAddress(request.getUserAddress()).build();
        // 根据服务地址过滤可以服务的护士
        dispatchFilterServiceAddress.execute(context);
        List<DispatchAngelBO> surviveAngel = context.getValidAngel();
        if (CollectionUtils.isEmpty(surviveAngel)) {
            return Collections.emptyList();
        }
        Set<String> skillCodes = productApplication.listSkillCodes(request.getServiceIds());
        context.setSkillCodes(skillCodes);
        dispatchFilterSkill.execute(context);
        surviveAngel = context.getValidAngel();
        if (CollectionUtils.isEmpty(surviveAngel)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(surviveAngel)) {
            return Collections.emptyList();
        }

        // 从满足条件的护士列表中，按照angelIds的顺序，获取前3个护士
        Map<Long, DispatchAngelBO> surviveMap = surviveAngel.stream().collect(Collectors.toMap(DispatchAngelBO::getAngelId, Function.identity(), (o, n) -> o));
        int count = 0;
        int size = Objects.isNull(request.getSize()) ? 3 : 0;
        List<DispatchAngelBO> finalAngel = Lists.newArrayList();
        for (int i = 0; i < sortUniqAngelIds.size() && count < size; i++) {
            if (surviveMap.containsKey(sortUniqAngelIds.get(i))) {
                finalAngel.add(surviveMap.get(sortUniqAngelIds.get(i)));
                count++;
            }
        }


        // 根据ID和护士信息组装返回的结果
        Map<String, JdhAngelWorkPo> finalWork = workPos.stream().collect(Collectors.toMap(JdhAngelWorkPo::getAngelId,
                Function.identity(), (o, n) -> o.getWorkEndTime().after(n.getWorkEndTime()) ? o : n));

        List<LastCompleteAngelDto> res = Lists.newArrayList();
        log.info("AngelQueryApplicationImpl listLastAngel finalAngel={}", JSON.toJSONString(finalAngel));
        finalAngel.forEach(e -> {
            LastCompleteAngelDto dto = new LastCompleteAngelDto();

            JdhAngelWorkPo workPo = finalWork.get(String.valueOf(e.getAngelId()));
            dto.setLastServiceTime(TimeUtils.dateTimeToStr(workPo.getWorkStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S));

            JdhAngelDto angel = new JdhAngelDto();
            BeanUtil.copyProperties(e, angel);
            if (!checkMatchProfessionCode(Integer.valueOf(request.getServiceType()), angel)){
                log.info("checkMatchProfessionCode professionCode no match angel={}", JSON.toJSONString(angel));
                return;
            }
            dto.setAngel(angel);


            LambdaQueryWrapper<JdhMedicalPromisePo> serviceQuery = Wrappers.lambdaQuery();
            serviceQuery.eq(JdhMedicalPromisePo::getPromiseId, workPo.getPromiseId());
            serviceQuery.eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
            List<JdhMedicalPromisePo> detailPos = jdhMedicalPromisePoMapper.selectList(serviceQuery);

            List<String> serviceNames = detailPos.stream().map(JdhMedicalPromisePo::getServiceItemName).collect(Collectors.toList());
            dto.setLastServiceName(serviceNames);
            res.add(dto);

        });
        return res;
    }

    private boolean checkMatchProfessionCode(Integer serviceType, JdhAngelDto angel) {
        try {
            // 查询服务者职业
            LambdaQueryWrapper<JdhAngelProfessionRelPo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(JdhAngelProfessionRelPo::getAngelId, angel.getAngelId()).eq(JdhAngelProfessionRelPo::getYn, YnStatusEnum.YES.getCode());
            List<JdhAngelProfessionRelPo> angelProfessionRelList = jdhAngelProfessionRelPoMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(angelProfessionRelList)){
                JdhAngelProfessionRelPo angelProfessionRelPo = angelProfessionRelList.get(0);
                if (ServiceTypeNewEnum.ANGEL_CARE.getType().equals(serviceType)){
                    if (!"24".equals(angelProfessionRelPo.getProfessionCode())){// 护士
                        return false;
                    }
                }
                if (ServiceTypeNewEnum.KFS_CARE.getType().equals(serviceType)){
                    if (!"26".equals(angelProfessionRelPo.getProfessionCode())){// 康复师
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            log.error("AngelQueryApplicationImpl checkMatchProfessionCode error e", e);
        }
        return true;
    }

    /**
     * 查询意向服务者
     * （1）根据邀请码查询护士；
     * （2）校验护士是否符合；
     *
     * @param request
     * @return
     */
    @Override
    public LastCompleteAngelDto getIntendedAngel(QueryIntendedAngelRequest request) {

        AssertUtils.hasText(request.getBusinessMode(), "businessMode is require");
        AssertUtils.hasText(request.getCpsInviteCode(), "cpsInviteCode is require");
        AssertUtils.hasText(request.getUserAddress(), "userAddress is require");
        AssertUtils.isNotEmpty(request.getServiceIds(), "serviceIds is empty");

        LambdaQueryWrapper<JdhAngelExtendPo> angelExtendWrapper = Wrappers.lambdaQuery();
        angelExtendWrapper.eq(JdhAngelExtendPo::getValue, request.getCpsInviteCode())
                .eq(JdhAngelExtendPo::getAttribute, AngelExtendKeyEnum.CPS_INVITE_CODE.getFiledKey())
                .eq(JdhAngelExtendPo::getYn, YnStatusEnum.YES.getCode()).last("limit 1");

        JdhAngelExtendPo extendPo = jdhAngelExtendPoMapper.selectOne(angelExtendWrapper);
        // 邀请码查不到护士
        if (Objects.isNull(extendPo)) {
            return null;
        }

        DispatchFilterContext context = DispatchFilterContext.builder()
                .validAngelIds(Lists.newArrayList(extendPo.getAngelId())).serviceAddress(request.getUserAddress()).build();
        // 根据服务地址过滤可以服务的护士
        dispatchFilterServiceAddress.execute(context);
        // 根据服务地址过滤
        List<DispatchAngelBO> surviveAngel = context.getValidAngel();
        log.info("AngelQueryApplicationImpl->getIntendedAngel surviveAngel={}", JSON.toJSONString(surviveAngel));
        if (CollectionUtils.isEmpty(surviveAngel)) {
            return null;
        }

        Set<String> skillCodes = productApplication.listSkillCodes(request.getServiceIds());
        log.info("AngelQueryApplicationImpl->getIntendedAngel skillCodes={}", JSON.toJSONString(skillCodes));
        context.setSkillCodes(skillCodes);
        dispatchFilterSkill.execute(context);
        if (CollectionUtils.isEmpty(context.getValidAngelIds())) {
            return null;
        }
        DispatchAngelBO angelBO = surviveAngel.get(0);
        JdhAngelDto angel = new JdhAngelDto();
        BeanUtil.copyProperties(angelBO, angel);

        LastCompleteAngelDto dto = new LastCompleteAngelDto();
        dto.setAngel(angel);
        // 获取
         fillLastInfo(request.getUserPin(), request.getBusinessMode(), angelBO.getAngelId(), dto);
         return dto;
    }

    /**
     * 填充最后一次服务的信息
     * @param userPin
     * @param businessMode
     * @param angelId
     * @param dto
     */
    private void fillLastInfo(String userPin, String businessMode, Long angelId, LastCompleteAngelDto dto) {
        // 获取业务身份
        List<JdhVerticalBusiness> businessList = verticalBusinessRepository.findByBusinessMode(businessMode);
        List<String> verticalCodes = businessList.stream().map(JdhVerticalBusiness::getVerticalCode).collect(Collectors.toList());
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();

        Date end = new Date();
        Date start = DateUtils.addYears(end, -1);

        queryWrapper.eq(JdhPromisePo::getUserPin, userPin)
                .eq(JdhPromisePo::getPromiseStatus, JdhPromiseStatusEnum.COMPLETE.getStatus())
                .in(JdhPromisePo::getVerticalCode, verticalCodes)
                .gt(JdhPromisePo::getAppointmentStartTime, start)
                .le(JdhPromisePo::getAppointmentStartTime, end)
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhPromisePo::getAppointmentStartTime);
        List<JdhPromisePo> promisePos = jdhPromisePoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(promisePos)) {
            return;
        }
        List<Long> promiseIds = promisePos.stream().map(JdhPromisePo::getPromiseId).collect(Collectors.toList());
        LambdaQueryWrapper<JdhAngelWorkPo> angelWorkWrapper = Wrappers.lambdaQuery();
        angelWorkWrapper.in(JdhAngelWorkPo::getPromiseId, promiseIds)
                .eq(JdhAngelWorkPo::getWorkStatus, AngelWorkStatusEnum.COMPLETED.getType())
                .orderByDesc(JdhAngelWorkPo::getWorkEndTime)
                .eq(JdhAngelWorkPo::getAngelId, String.valueOf(angelId))
                .eq(JdhAngelWorkPo::getYn, YnStatusEnum.YES.getCode())
                .last("limit 1");

        JdhAngelWorkPo workPo = jdhAngelWorkPoMapper.selectOne(angelWorkWrapper);
        if (Objects.isNull(workPo)){
            return;
        }
        dto.setLastServiceTime(TimeUtils.dateTimeToStr(workPo.getWorkStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S));

        LambdaQueryWrapper<JdhMedicalPromisePo> serviceQuery = Wrappers.lambdaQuery();
        serviceQuery.eq(JdhMedicalPromisePo::getPromiseId, workPo.getPromiseId());
        serviceQuery.eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        List<JdhMedicalPromisePo> detailPos = jdhMedicalPromisePoMapper.selectList(serviceQuery);

        List<String> serviceNames = detailPos.stream().map(JdhMedicalPromisePo::getServiceItemName).collect(Collectors.toList());
        dto.setLastServiceName(serviceNames);
    }
}
