package com.jdh.o2oservice.application.trade.event;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.trade.convert.TradeOrderConverter;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.common.enums.HasAddedEnum;
import com.jdh.o2oservice.common.enums.OrderStatusNetDiagEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationInventoryBusinessTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.VoucherEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.trade.bo.OrderRefundDetailBo;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderContext;
import com.jdh.o2oservice.core.domain.trade.enums.*;
import com.jdh.o2oservice.core.domain.trade.event.JdOrderItemEventBody;
import com.jdh.o2oservice.core.domain.trade.event.JdhOrderPromiseEventBody;
import com.jdh.o2oservice.core.domain.trade.event.OrderSplitEventBody;
import com.jdh.o2oservice.core.domain.trade.factory.JdOrderFactory;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRefundTaskRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderStatusRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.service.JdhOrderDomainService;
import com.jdh.o2oservice.core.domain.trade.service.TradeDomainService;
import com.jdh.o2oservice.export.angel.cmd.ReleaseInventoryCmd;
import com.jdh.o2oservice.export.promise.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderNetDiagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 交易域，事件订阅处理
 * @author: yangxiyu
 * @date: 2023/12/18 3:34 下午
 * @version: 1.0
 */
@Slf4j
@Component
public class TradeEventSubscriber {
    /**
     * 订单状态 mq
     */
    @Value("${topics.jdhReachStoreConsumer.o2oServiceOrderStatus}")
    private String o2oServiceOrderStatus;
    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * jdhOrderDomainService
     */
    @Resource
    private JdhOrderDomainService jdhOrderDomainService;

    /**
     * orderStatemachine
     */
    @Resource
    private StateMachine<TradeStatus, TradeEventTypeEnum, StateContext> orderStatemachine;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * jdOrderRefundTaskRepository
     */
    @Resource
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;
    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;
    /**
     * orderInfoRpc
     */
    @Resource
    private OrderInfoRpc orderInfoRpc;
    /**
     * tradeDomainService
     */
    @Resource
    private TradeDomainService tradeDomainService;
    /**
     * jdOrderStatusRepository
     */
    @Resource
    private JdOrderStatusRepository jdOrderStatusRepository;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;
    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Resource
    private Cluster jimClient;

    @Autowired
    private StationApplication stationApplication;

    @Resource
    private TradeApplication tradeApplication;

    /**
     * jdOrderRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {

        // 保存用户提交时间记录
        eventConsumerRegister.register(TradeEventTypeEnum.POP_ORDER_SPLIT, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "popOrderSplitAfterProcessor", this::popOrderSplitAfterProcessor, Boolean.TRUE, Boolean.FALSE));

        // pop订单完成
        eventConsumerRegister.register(TradeEventTypeEnum.POP_ORDER_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "popOrderComplete", this::popOrderComplete, Boolean.TRUE, Boolean.FALSE));

        // loc核销码状态变更通知
        eventConsumerRegister.register(TradeEventTypeEnum.LOC_CODE_STATUS_MODIFY, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "locCodeStatusModify", this::locCodeStatusModify, Boolean.FALSE, Boolean.FALSE));

        // 自营不拆单-支付完成
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_ORDER_NO_SPLIT, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "selfOrderNoSplitAfterProcessor", this::selfOrderNoSplitAfterProcessor, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.defaultInstance()));

        // 自营拆单-支付完成订单号是子单号
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_ORDER_SPLIT, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "selfOrderSplitAfterProcessor", this::selfOrderSplitAfterProcessor, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.defaultInstance()));

        // 自营取消订单-待支付取消
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_CANCEL_ORDER, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "selfCancelOrderProcessor", this::selfCancelOrderProcessor, Boolean.TRUE, Boolean.FALSE));

        // 自营订单退款任务成功
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_ORDER_SUCC_REFUND, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "selfRefundTaskSuccProcessor", this::selfRefundTaskSuccProcessor, Boolean.TRUE, Boolean.FALSE));

        // 自营订单提单，待支付
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_ORDER_CREATE, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "selfOrderCreateProcessor", this::selfOrderCreateProcessor, Boolean.TRUE, Boolean.FALSE));

       /* // VTP订单，已支付
        eventConsumerRegister.register(TradeEventTypeEnum.RECEIVE_ORDER, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "receiveOrderProcessor", this::receiveOrderProcessor, Boolean.TRUE, Boolean.FALSE));*/

        eventConsumerRegister.register(PromiseEventTypeEnum.VOUCHER_EXPIRE, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "voucherExpireOrderFinish", this::voucherExpireOrderFinish, Boolean.TRUE));

        // 物流签收订列状态待服务
        eventConsumerRegister.register(TradeEventTypeEnum.ORDER_DELIVERY_COMPLETED, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "orderChangeToUnUsed", this::orderChangeToUnUsed, Boolean.TRUE));

        // 提交样本已定订列状态服务中
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_CREATED, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "orderChangeToServicing", this::orderChangeToServicing, Boolean.TRUE));

        // 所有报告已出订单拉完成
        eventConsumerRegister.register(TradeEventTypeEnum.ORDER_ALL_REPORTED, WrapperEventConsumer.newInstance(DomainEnum.TRADE,
                "orderChangeToFinish", this::orderChangeToFinish, Boolean.TRUE));
    }




    /**
     * pop订单完成状态记录
     *
     * @param event
     */
    private void popOrderComplete(Event event) {
        JdOrderStatus jdOrderStatus = JdOrderFactory.createOrderStatus(Long.valueOf(event.getAggregateId()), OrderStatusEnum.ORDER_COMPLETE.getStatus());
        jdOrderStatusRepository.save(jdOrderStatus);
    }

    /**
     * 商家端同步loc核销码状态
     *
     * @param event
     */
    private void locCodeStatusModify(Event event) {
        log.info("[TradeEventSubscriber->locCodeStatusModify],event={}", JSON.toJSONString(event));
        JdhOrderPromiseEventBody jdhOrderPromiseEventBody = JSON.parseObject(event.getBody(), JdhOrderPromiseEventBody.class);
        JdOrder jdOrder = jdhOrderPromiseEventBody.getJdOrder();
        log.info("[TradeEventSubscriber->locCodeStatusModify],jdOrder={}", JSON.toJSONString(jdOrder));
        if(jdOrder.isAllRefund()){
            JdOrderContext jdOrderContext = TradeOrderConverter.INSTANCE.convertToJdOrderContext(jdOrder);
            jdOrderContext.setJdOrder(jdOrder);
            jdOrderContext.init(TradeEventTypeEnum.POP_ORDER_REFUND);
            orderStatemachine.fireEvent(ResolveOrderConditionEnum.ORDER_COMPLETE, TradeEventTypeEnum.POP_ORDER_REFUND, jdOrderContext);

            //修改订单状态
            jdOrder.setOrderStatus(OrderStatusEnum.ORDER_REFUND.getStatus());
            jdOrder.setJdOrderItemList(null);
            jdOrderRepository.updateOrderByOrderId(jdOrder);
        }
        //o2o中台在商家端（shop-merchant）中不生成预约单，不需要同步状态
//        Integer promiseStatusOfCodeStatus = LocCodeStatusEnum.getPromiseStatusOfCodeStatus(jdhOrderPromiseEventBody.getCodeStatus());
//        AppointmentRpcParam appointmentRpcParam = AppointmentRpcParam.builder()
//                .promiseStatus(promiseStatusOfCodeStatus)
//                .userPin(jdhOrderPromiseEventBody.getUserPin())
//                .writeOffStatus(promiseStatusOfCodeStatus == 4 ? 1 : 0)
//                .promiseId(jdhOrderPromiseEventBody.getPromiseId())
//                .writeDate(new Date())
//                .build();
//        log.info("[TradeEventSubscriber->locCodeStatusModify],appointmentRpcParam={}", JSON.toJSONString(appointmentRpcParam));
//        serviceDetailRpc.updateAppointmentWriteOffStatus(appointmentRpcParam);

        //记录订单状态
        JdOrderStatus jdOrderStatus = JdOrderFactory.createOrderStatus(jdOrder.getOrderId(), jdOrder.getOrderStatus());
        jdOrderStatusRepository.save(jdOrderStatus);
    }

    private void popOrderSplitAfterProcessor(Event event) {
        log.info("[TradeEventSubscriber->popOrderSplitAfterProcessor],eventBody={}", event.getBody());
        String body = event.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(jsonObject.getLong("orderId")).build());
        OrderSplitEventBody orderSplitEventBody = OrderSplitEventBody.builder()
                .orderItemEventBodyList(JSON.parseArray(jsonObject.getString("orderItemEventBodyList"), JdOrderItemEventBody.class))
                .orderId(jsonObject.getLong("orderId"))
                .orderStatus(jsonObject.getInteger("orderStatus"))
                .orderType(jsonObject.getInteger("orderType"))
                .extend(jsonObject.getString("extend"))
                .sendPay(jsonObject.getString("sendPay"))
                .build();
        JdOrderContext jdOrderContext = TradeOrderConverter.INSTANCE.convertToJdOrderContext(orderSplitEventBody);
        jdOrderContext.setJdOrder(orderEntity);
        log.info("[TradeEventSubscriber->popOrderSplitAfterProcessor],jdOrderContext={}", JSON.toJSONString(jdOrderContext));

        //获取业务类型
        String serviceType = jdhOrderDomainService.getServiceType(jdOrderContext);
        //获取业务垂直身份编码
        String verticalCode = jdhOrderDomainService.getVerticalCode(jdOrderContext, serviceType);

        //业务身份组装
        jdOrderContext.setVerticalCode(verticalCode);
        jdOrderContext.setServiceType(serviceType);
        for (JdOrderItem jdOrderItem : jdOrderContext.getJdOrderItemList()) {
            jdOrderItem.setVerticalCode(verticalCode);
            jdOrderItem.setServiceType(serviceType);
        }

        jdOrderContext.init(TradeEventTypeEnum.POP_ORDER_SPLIT);
        orderStatemachine.fireEvent(ResolveOrderConditionEnum.ORDER_PAID, TradeEventTypeEnum.POP_ORDER_SPLIT, jdOrderContext);

        JdOrder jdOrder = TradeOrderConverter.INSTANCE.convertToJdOrder(jdOrderContext);
        jdOrderRepository.updateOrderByOrderId(jdOrder);
    }

    /**
     *
     * @param event
     */
    private void selfOrderNoSplitAfterProcessor(Event event) {
        log.info("[TradeEventSubscriber->selfOrderNoSplitAfterProcessor],eventBody={}", event.getBody());
        String body = event.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        String sendPay = jsonObject.getString("sendPay");
        String sendPay294 = String.valueOf(sendPay.charAt(293));
        switch (sendPay294){
            case CommonConstant.NUMBER_SIX:
                //6 到家上门
                Long orderId = jsonObject.getLong("orderId");
                //给互医发送拆掉的支付事件
                JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(jsonObject.getLong("orderId")).build());
                if (orderEntity == null){
                    break;
                }
//                saveJdOrderExt(orderId);
                if(orderEntity.getPartnerSource() != null && (orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() ||
                        orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue())) {
                    log.info("给互医发送支付的消息，orderId:{},发送 mq 内容:{}", orderId, JSON.toJSONString(orderEntity));
                    sendJdOrderNetDiag(orderEntity, OrderStatusNetDiagEnum.PAY.getCode());
                }
                break;
            default:
                break;
        }

    }
    private void selfOrderSplitAfterProcessor(Event event) {
        log.info("[TradeEventSubscriber->selfOrderSplitAfterProcessor],eventBody={}", event.getBody());
        String body = event.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        String sendPay = jsonObject.getString("sendPay");
        String sendPay294 = String.valueOf(sendPay.charAt(293));
        switch (sendPay294){
            case CommonConstant.NUMBER_SIX:
                //6 到家上门
                Long orderId = jsonObject.getLong("orderId");
//                saveJdOrderExt(orderId);
                //给互医发送拆掉的支付事件
                JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(jsonObject.getLong("orderId")).build());
                if (orderEntity == null){
                    break;
                }
                if(orderEntity.getPartnerSource() != null && (orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() ||
                        orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue())) {
                    log.info("给互医发送支付的消息，orderId:{},发送 mq 内容:{}", orderEntity.getParentId(), JSON.toJSONString(orderEntity));
                    sendJdOrderNetDiag(orderEntity, OrderStatusNetDiagEnum.PAY.getCode());
                }
                break;
            default:
                break;
        }

        // 给互医发送支付的状态

    }
    private void selfCancelOrderProcessor(Event event) {
        log.info("[TradeEventSubscriber->selfCancelOrderProcessor],eventBody={}", event.getBody());
        String body = event.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(jsonObject.getLong("orderId")).build());
        if (orderEntity == null){
            return;
        }
        // 中台取消订单（事件发送处理？）
        tradeDomainService.cancelOrder(orderEntity.getOrderId(),orderEntity.getUserPin());
        String code = OrderStatusNetDiagEnum.CANCEL.getCode();
        //退单时释放库存
        releaseInventoryLogic(orderEntity);
        if(orderEntity.getPartnerSource() != null && (orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() ||
                orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue())) {
            sendJdOrderNetDiag(orderEntity, code);
        }
    }

    private void sendJdOrderNetDiag(JdOrder orderEntity, String code) {
        //给互医发送取消的事件
        JdOrderNetDiagDTO jdOrderMqDTO = new JdOrderNetDiagDTO();
        jdOrderMqDTO.setOrderId(orderEntity.getOrderId());
        jdOrderMqDTO.setOrderStatus(code);
        jdOrderMqDTO.setUserPin(orderEntity.getUserPin());
        if(Objects.nonNull(orderEntity.getPartnerSourceOrderId())) {
            jdOrderMqDTO.setSheetId(Long.valueOf(orderEntity.getPartnerSourceOrderId()));
        }
        Message message = new Message(o2oServiceOrderStatus, JSON.toJSONString(jdOrderMqDTO), String.valueOf(orderEntity.getOrderId()));
        try {
            log.info("给互医发送订单状态消息，orderId:{},发送 mq 内容:{}", orderEntity.getOrderId(), JSON.toJSONString(jdOrderMqDTO));
            reachStoreProducer.send(message);
        } catch (JMQException e) {
            log.error("给互医发送订单状态消息 error，orderId:{},发送 mq 内容:{}", orderEntity.getOrderId(), JSON.toJSONString(jdOrderMqDTO));
        }
    }

    /**
     * task 退款成功事件
     * @param event
     */
    private void selfRefundTaskSuccProcessor(Event event) {
        log.info("[TradeEventSubscriber->selfRefundTaskSuccProcessor],eventBody={}", event.getBody());
        String body = event.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        Long taskId = jsonObject.getLong("taskId");
        Long orderId = jsonObject.getLong("orderId");
        orderRefundStatus(orderId,taskId);
    }

    /**
     *
     * @param orderId
     * @param taskId
     */
    private void orderRefundStatus(Long orderId,Long taskId) {
        JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
        jdOrderRefundTask.setOrderId(orderId);
        List<JdOrderRefundTask> jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        log.info("TradeEventSubscriber orderRefundStatus orderId={},jdOrderRefundTaskList={}", orderId, JSON.toJSONString(jdOrderRefundTaskList));
        List<JdOrderRefundTask> refundTaskNoSuccList = new ArrayList<>();
        AtomicReference<Boolean> lastPromise = new AtomicReference<>(false);
        AtomicReference<JdOrderRefundTask> task = new AtomicReference<>(JdOrderRefundTask.builder().build());
        if(CollUtil.isNotEmpty(jdOrderRefundTaskList)){
            jdOrderRefundTaskList.forEach(taskTemp ->{
                Integer lastOrderTask = taskTemp.getLastOrderTask();
                if(lastOrderTask == CommonConstant.ONE){
                    lastPromise.set(true);
                    task.set(taskTemp);
                }else{
                    List<JdOrderItem> jdOrderItems = jdOrderItemRepository.itemListByOrderId(orderId);
                    if(CollUtil.isNotEmpty(jdOrderItems)){
                        Integer skuNum = jdOrderItems.get(0).getSkuNum();
                        if(skuNum == 1){
                            lastPromise.set(true);
                        }
                    }
                }
                log.info("[TradeEventSubscriber->orderRefundStatus],taskTemp={}",taskTemp);
                if(taskId.equals(taskTemp.getTaskId())){
                    invalidVoucher(taskTemp);
                }else{
                    if(!RefundStatusEnum.REFUND_SUCCESS.getType().equals(taskTemp.getRefundStatus())){
                        refundTaskNoSuccList.add(taskTemp);
                    }
                }
            });

            // 不区分部分退款、全部退款
            JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(orderId).build());
            log.info("[TradeEventSubscriber->orderRefundStatus],refundTaskNoSuccList={}，lastPromise={}",refundTaskNoSuccList,lastPromise.get());
            // 订单退款完成，订单状态更新
            if(lastPromise.get() && CollUtil.isEmpty(refundTaskNoSuccList)){
                updateOrderRefundSuccStatus(orderId);
            }else if(HasAddedEnum.HAS_ADDED.getValue().equals(orderEntity.getHasAdded())){
                updateOrderRefundSuccStatus(orderId);
            }else{
                if(Objects.nonNull(orderEntity.getParentId()) && orderEntity.getParentId() > 0){
                    JdOrder parentOrder = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(orderEntity.getParentId()).build());
                    if(HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())){
                        updateOrderRefundSuccStatus(orderId);
                    }
                }
            }

            if(orderEntity.getPartnerSource() != null && (orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() ||
                    orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue())){
                sendJdOrderNetDiag(orderEntity, OrderStatusNetDiagEnum.REFUND_SUCCESS.getCode());
                log.info("给互医发送订单退款消息，orderId:{},发送 mq 内容:{}", orderEntity.getOrderId(), JSON.toJSONString(orderEntity));
            }
        }
    }

    /**
     *
     * @param orderId
     */
    private void updateOrderRefundSuccStatus(Long orderId) {
        JdOrder jdOrder = JdOrder.builder().orderId(orderId).orderStatus(OrderStatusEnum.ORDER_REFUND.getStatus()).build();
        jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
        jdOrder = jdOrderRepository.find(jdOrder.getIdentifier());
        directReviseOrderFinishState(orderId);
        releaseInventoryLogic(jdOrder);
    }

    private void selfOrderCreateProcessor(Event event) {
        log.info("[TradeEventSubscriber->selfOrderCreateProcessor],eventBody={}", event.getBody());
        String body = event.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(jsonObject.getLong("orderId")).build());
        if (orderEntity == null){
            return;
        }
        if(orderEntity.getPartnerSource() != null && (orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_NETDIAG.getCode().intValue() ||
                orderEntity.getPartnerSource().intValue() == PartnerSourceEnum.JDH_HOMEDIAG.getCode().intValue())){
            sendJdOrderNetDiag(orderEntity, OrderStatusNetDiagEnum.SUBMIT.getCode());
            log.info("给互医发送提单的消息，orderId:{},发送 mq 内容:{}", orderEntity.getOrderId(), JSON.toJSONString(orderEntity));

            // 缓存检验单ID和订单的对应关系
//            String key = RedisKeyEnum.getRedisKey(RedisKeyEnum.SUBMIT_ORDER_PARTNER_SOURCE_KEY,orderEntity.getPartnerSource(), orderEntity.getPartnerSourceOrderId());
//            jimClient.set(key, orderEntity.getPartnerSourceOrderId());
//            log.info("缓存检验单与订单关系，orderId:{},检验单ID:{}", orderEntity.getOrderId(), orderEntity.getPartnerSourceOrderId());
        }

    }

    private void voucherExpireOrderFinish(Event event) {
        log.info("[TradeEventSubscriber->voucherExpireOrderFinish],eventId={},eventBody={}", event.getEventId(), event.getBody());
        VoucherEventBody eventBody = JSON.parseObject(event.getBody(), VoucherEventBody.class);
        if(StringUtils.isNumeric(eventBody.getSourceVoucherId())){
            Long orderId = Long.valueOf(eventBody.getSourceVoucherId());
            JdOrder jdOrder = JdOrder.builder().orderId(orderId).orderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus()).build();
            jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
            this.directReviseOrderFinishState(orderId);
        }
    }

    /**
     * 作废服务单
     * @param jdOrderRefundTask
     */
    private void invalidVoucher(JdOrderRefundTask jdOrderRefundTask){
        try{
            InvalidVoucherCmd cmd = InvalidVoucherCmd.builder().build();
            cmd.setVoucherId(jdOrderRefundTask.getVoucherId());
            String refundDetail = jdOrderRefundTask.getRefundDetail();
            List<Long> promisePatientIdList = new ArrayList<>();
            if(StringUtils.isNotBlank(refundDetail)){
                OrderRefundDetailBo orderRefundDetailBo = JsonUtil.parseObject(refundDetail,OrderRefundDetailBo.class);
                promisePatientIdList = orderRefundDetailBo.getPromisePatientIdList();
                if(!orderRefundDetailBo.getFreezeAndInvalid()){
                    jdOrderRefundTask.setRefundType(RefundTypeEnum.ORDER_REFUND.getType());
                    List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(jdOrderRefundTask.getOrderId());
                    if(CollUtil.isNotEmpty(jdOrderItems)){
                        jdOrderRefundTask.setServiceId(String.valueOf(jdOrderItems.get(0).getSkuId()));
                        jdOrderRefundTask.setOrderId(jdOrderRefundTask.getParentId());
                    }
                }
            }

            if(RefundTypeEnum.ORDER_ONE_REFUND.getType().equals(jdOrderRefundTask.getRefundType())
                    || RefundTypeEnum.AMOUNT_REFUND.getType().equals(jdOrderRefundTask.getRefundType())){
                cmd.setPromisePatient(bulidPromisePatientDtoList(promisePatientIdList,jdOrderRefundTask.getServiceId()));
                cmd.setInvalidType(CommonConstant.ONE);
            }else{
                VoucherPageRequest request = VoucherPageRequest.builder()
                        .sourceVoucherId(String.valueOf(jdOrderRefundTask.getOrderId())).build();
                List<VoucherDto> list = voucherApplication.queryVoucherList(request);
                cmd.setInvalidType(CommonConstant.TWO);
                cmd.setInvalidService(bulidPromiseServiceDetailDtoList(jdOrderRefundTask.getServiceId(),jdOrderRefundTask.getPromiseId()));
                cmd.setReason(jdOrderRefundTask.getRefundReason());
                if(CollUtil.isNotEmpty(list)){
                    for(VoucherDto voucherDto : list){
                        cmd.setVoucherId(voucherDto.getVoucherId());
                        voucherApplication.invalidVoucher(cmd);
                    }
                    return;
                }
            }
            cmd.setReason(jdOrderRefundTask.getRefundReason());
            voucherApplication.invalidVoucher(cmd);
        }catch (Exception e){
            log.error("[TradeEventSubscriber->invalidVoucher],taskId={}",jdOrderRefundTask.getTaskId());
        }
    }

    /**
     * 直接拉完成
     * @param orderId
     */
    private void directReviseOrderFinishState(Long orderId){
        // 直接拉完成，影响订单归堆逻辑
        tradeApplication.reviseOrderFinishState(orderId);
    }

    /**
     * bulidPromisePatientList
     * @param promisePatientIdList
     * @return
     */
    private List<PromisePatientDto> bulidPromisePatientDtoList(List<Long> promisePatientIdList,String serviceId){
        List<PromisePatientDto> freezeUser = new ArrayList<>();
        if(CollUtil.isNotEmpty(promisePatientIdList)){
            for(Long promisePatientId : promisePatientIdList){
                PromisePatientDto promisePatientDto = new PromisePatientDto();
                promisePatientDto.setPromisePatientId(promisePatientId);
                List<PromiseServiceDetailDto> serviceDetails = new ArrayList<>();
                PromiseServiceDetailDto promiseServiceDetailDto = new PromiseServiceDetailDto();
                promiseServiceDetailDto.setServiceId(Long.parseLong(serviceId));
                serviceDetails.add(promiseServiceDetailDto);
                promisePatientDto.setServiceDetails(serviceDetails);
                freezeUser.add(promisePatientDto);
            }
        }
        return freezeUser;
    }

    /**
     *
     * @param serviceId
     * @param promiseId
     * @return
     */
    private List<PromiseServiceDetailDto> bulidPromiseServiceDetailDtoList(String serviceId,Long promiseId){
        List<PromiseServiceDetailDto> freezeServiceList = new ArrayList<>();
        PromiseServiceDetailDto promiseServiceDetailDto = new PromiseServiceDetailDto();
        promiseServiceDetailDto.setServiceId(Long.parseLong(serviceId));
        promiseServiceDetailDto.setPromiseId(promiseId);
        freezeServiceList.add(promiseServiceDetailDto);
        return freezeServiceList;
    }
    /**
     * 退单时释放库存
     * @param jdOrder
     */
    private void releaseInventoryLogic(JdOrder jdOrder) {
        ReleaseInventoryCmd cmd = new ReleaseInventoryCmd();
        cmd.setBusinessId(String.valueOf(jdOrder.getOrderId()));
        cmd.setPin(jdOrder.getUserPin());
        cmd.setBusinessType(AngelStationInventoryBusinessTypeEnum.ORDER.getType());
        stationApplication.releaseInventory(cmd);
    }

    /**
     * 快递检测用户签收变待服务
     * @param event
     */
    private void orderChangeToUnUsed(Event event) {
        log.info("[TradeEventSubscriber->orderChangeToUnUsed],eventId={},eventBody={}", event.getEventId(), event.getBody());
        OrderSplitEventBody eventBody = JSON.parseObject(event.getBody(), OrderSplitEventBody.class);
        JdOrder order = jdOrderRepository.find(new JdOrderIdentifier(eventBody.getOrderId()));
        if (CollUtil.isNotEmpty(order.getSendPayMap()) && order.getSendPayMap().containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && order.getSendPayMap().get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equalsIgnoreCase(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())) {
            Boolean ret = orderInfoRpc.modifySendPayForOrderListStatus(order.getOrderId(), OrderListServiceStatusEnum.UNUSED);
            if (!Boolean.TRUE.equals(ret)) {
                throw new BusinessException(TradeErrorCode.ORDER_SEND_PAY_MODIFY_FAIL);
            }
        }
    }

    /**
     * 快递检测用户提交样本服务中
     * @param event
     */
    private void orderChangeToServicing(Event event) {
        log.info("[TradeEventSubscriber->orderChangeToServicing],eventId={},eventBody={}", event.getEventId(), event.getBody());
        Long promiseId = Long.valueOf(event.getAggregateId());
        JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
        // 仅非快检模式需要处理
        if (!StringUtils.equals(promise.getVerticalCode(), ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode())){
            return;
        }
        JdOrder order = jdOrderRepository.find(new JdOrderIdentifier(Long.parseLong(promise.getSourceVoucherId())));
        if (CollUtil.isNotEmpty(order.getSendPayMap()) && order.getSendPayMap().containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && order.getSendPayMap().get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equalsIgnoreCase(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())) {
            Boolean ret = orderInfoRpc.modifySendPayForOrderListStatus(order.getOrderId(), OrderListServiceStatusEnum.SERVICING);
            if (!Boolean.TRUE.equals(ret)) {
                throw new BusinessException(TradeErrorCode.ORDER_SEND_PAY_MODIFY_FAIL);
            }
        }
    }

    /**
     * 快递检测用户报告全部已出
     * @param event
     */
    private void orderChangeToFinish(Event event) {
        log.info("[TradeEventSubscriber->orderChangeToFinish],eventId={},eventBody={}", event.getEventId(), event.getBody());
        OrderSplitEventBody eventBody = JSON.parseObject(event.getBody(), OrderSplitEventBody.class);
        JdOrder order = jdOrderRepository.find(new JdOrderIdentifier(eventBody.getOrderId()));
        if (CollUtil.isNotEmpty(order.getSendPayMap()) && order.getSendPayMap().containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && order.getSendPayMap().get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equalsIgnoreCase(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())) {
            JdOrder jdOrder = new JdOrder();
            jdOrder.setOrderId(order.getOrderId());
            jdOrder.setOrderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus());
            jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
            Boolean ret = orderInfoRpc.modifySendPayForOrderListStatus(order.getOrderId(), OrderListServiceStatusEnum.FINISH);
            Boolean tabRet = orderInfoRpc.modifySendPayForOrderTabChange(order.getOrderId(), OrderListTabEnum.FINISH);
            if (!Boolean.TRUE.equals(ret) || !Boolean.TRUE.equals(tabRet)) {
                throw new BusinessException(TradeErrorCode.ORDER_SEND_PAY_MODIFY_FAIL);
            }
        }
    }

}
