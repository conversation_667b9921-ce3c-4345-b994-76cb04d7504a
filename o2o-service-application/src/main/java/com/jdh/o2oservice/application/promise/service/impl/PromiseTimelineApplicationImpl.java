package com.jdh.o2oservice.application.promise.service.impl;

import com.jdh.o2oservice.application.promise.service.PromiseTimelineApplication;
import com.jdh.o2oservice.export.product.dto.PromiseServiceStartTimeDescDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 履约单时间轴 应用服务
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Service
@Slf4j
public class PromiseTimelineApplicationImpl implements PromiseTimelineApplication {

    @Override
    public PromiseTimelineDto queryPromiseTimeline(QueryPromiseTimelineRequest request) {
        return null;
    }
}
