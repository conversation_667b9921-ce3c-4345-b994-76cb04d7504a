package com.jdh.o2oservice.application.promise.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseTimelineApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailForManRequest;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDetailDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.enums.PromiseTimelineActionEnum;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 履约单时间轴 应用服务
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Service
@Slf4j
public class PromiseTimelineApplicationImpl implements PromiseTimelineApplication {

    @Autowired
    private PromiseApplication promiseApplication;

    @Autowired
    private AngelWorkApplication angelWorkApplication;

    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;

    @Override
    public PromiseTimelineDto queryPromiseTimeline(QueryPromiseTimelineRequest request) {
        log.info("PromiseTimelineApplicationImpl -> queryPromiseTimeline request={}", JSON.toJSONString(request));

        Long promiseId = request.getPromiseId();

        // 查询履约单信息
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(promiseId).build());
        if (promiseDto == null) {
            log.warn("PromiseTimelineApplicationImpl -> queryPromiseTimeline 履约单不存在, promiseId={}", promiseId);
            throw new BusinessException(BusinessErrorCode.JDH_PROMISE_NOT_EXIST);
        }

        // 查询服务者工单详情
        AngelWorkDetailForManDto angelWorkDetailForManDto = findAngelWorkDetailForManDto(promiseId);

        // 查询检测单历史记录
        List<MedPromiseHistoryDTO> medPromiseHistoryList = findMedPromiseHistoryList(promiseId);

        // 构建时间轴
        List<PromiseTimelineDetailDto> timelineDetailList = buildPromiseTimeline(
                promiseDto, angelWorkDetailForManDto, medPromiseHistoryList);

        PromiseTimelineDto result = new PromiseTimelineDto();
        result.setPromiseTimelineDetailDtoList(timelineDetailList);

        log.info("PromiseTimelineApplicationImpl -> queryPromiseTimeline 成功, promiseId={}, timelineSize={}",
                promiseId, timelineDetailList != null ? timelineDetailList.size() : 0);

        return result;

    }

    /**
     * 查询服务者工单详情
     *
     * @param promiseId 履约单ID
     * @return AngelWorkDetailForManDto
     */
    private AngelWorkDetailForManDto findAngelWorkDetailForManDto(Long promiseId) {
        try {
            return angelWorkApplication.queryAngelWorkDetailForMan(
                    AngelWorkDetailForManRequest.builder().promiseId(promiseId).build());
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> findAngelWorkDetailForManDto error, promiseId={}", promiseId, e);
            return null;
        }
    }

    /**
     * 查询检测单历史记录
     *
     * @param promiseId 履约单ID
     * @return List<MedPromiseHistoryDTO>
     */
    private List<MedPromiseHistoryDTO> findMedPromiseHistoryList(Long promiseId) {
        try {
            return medPromiseHistoryApplication.queryMedPromiseHistoryList(
                    MedPromiseHistoryRequest.builder().promiseId(promiseId).build());
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> findMedPromiseHistoryList error, promiseId={}", promiseId, e);
            return new ArrayList<>();
        }
    }


    /**
     * 构建履约时间轴
     *
     * @param promiseDto               履约单信息
     * @param angelWorkDetailForManDto 服务者工单详情
     * @param medPromiseHistoryList    检测单历史记录
     * @return List<PromiseTimelineDetailDto>
     */
    private List<PromiseTimelineDetailDto> buildPromiseTimeline(
            PromiseDto promiseDto,
            AngelWorkDetailForManDto angelWorkDetailForManDto,
            List<MedPromiseHistoryDTO> medPromiseHistoryList) {

        try {
            List<PromiseTimelineDetailDto> timelineList = new ArrayList<>();

            // 1. 添加履约单创建事件
            if (promiseDto != null && promiseDto.getCreateTime() != null) {
                PromiseTimelineDetailDto createEvent = new PromiseTimelineDetailDto();
                createEvent.setActionEnum(PromiseTimelineActionEnum.CREATE_PROMISE);
                createEvent.setCreateTime(promiseDto.getCreateTime());
                createEvent.setExtend("{}");
                timelineList.add(createEvent);
            }

            // 2. 添加服务者工单相关事件
            if (angelWorkDetailForManDto != null && CollUtil.isNotEmpty(angelWorkDetailForManDto.getWorkHistoryDtoList())) {
                angelWorkDetailForManDto.getWorkHistoryDtoList().forEach(history -> {
                    PromiseTimelineDetailDto workEvent = convertAngelWorkHistoryToTimeline(history, angelWorkDetailForManDto);
                    if (workEvent != null) {
                        timelineList.add(workEvent);
                    }
                });
            }

            // 3. 添加检测单历史事件
            if (CollUtil.isNotEmpty(medPromiseHistoryList)) {
                medPromiseHistoryList.forEach(history -> {
                    PromiseTimelineDetailDto medEvent = convertMedPromiseHistoryToTimeline(history);
                    if (medEvent != null) {
                        timelineList.add(medEvent);
                    }
                });
            }

            // 4. 按时间倒序排序
            timelineList.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));

            log.info("PromiseTimelineApplicationImpl -> buildPromiseTimeline 构建时间轴完成, timelineSize={}", timelineList.size());
            return timelineList;

        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> buildPromiseTimeline error", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换服务者工单历史为时间轴事件
     *
     * @param history         工单历史
     * @param angelWorkDetail 工单详情
     * @return PromiseTimelineDetailDto
     */
    private PromiseTimelineDetailDto convertAngelWorkHistoryToTimeline(Object history, AngelWorkDetailForManDto angelWorkDetail) {
        try {
            // 这里需要根据实际的AngelWorkHistory结构来实现转换逻辑
            // 由于没有具体的AngelWorkHistory类定义，这里提供一个基本框架

            PromiseTimelineDetailDto timelineEvent = new PromiseTimelineDetailDto();

            // 根据工单状态变化映射到对应的时间轴事件
            // 例如：接单、出发、到达、开始服务、完成服务等

            // 设置服务者姓名等扩展信息
            Map<String, Object> extendMap = new HashMap<>();
            if (angelWorkDetail != null && angelWorkDetail.getAngelDto() != null) {
                extendMap.put("SERVICER_NAME", angelWorkDetail.getAngelDto().getName());
            }
            timelineEvent.setExtend(JSON.toJSONString(extendMap));

            return timelineEvent;

        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> convertAngelWorkHistoryToTimeline error", e);
            return null;
        }
    }

    /**
     * 转换检测单历史为时间轴事件
     *
     * @param history 检测单历史
     * @return PromiseTimelineDetailDto
     */
    private PromiseTimelineDetailDto convertMedPromiseHistoryToTimeline(MedPromiseHistoryDTO history) {
        try {
            // 根据检测单状态变化判断是否需要添加到时间轴
            if (history.getAfterStatus() == null || history.getCreateTime() == null) {
                return null;
            }

            // 过滤掉不需要显示的状态变化
            // 参考ManViaApplicationImpl中的逻辑，过滤掉WAIT_COLLECTED状态和结算完成事件
            if ("WAIT_COLLECTED".equals(history.getAfterStatus()) ||
                    "medPromiseSettlementComplete".equals(history.getEventCode())) {
                return null;
            }

            PromiseTimelineDetailDto timelineEvent = new PromiseTimelineDetailDto();

            // 根据检测单状态变化映射到对应的时间轴事件
            // 这里可以根据具体的业务需求来映射不同的状态到时间轴事件
            timelineEvent.setActionEnum(PromiseTimelineActionEnum.COMPLETE_PROMISE); // 默认使用完成事件
            timelineEvent.setCreateTime(history.getCreateTime());
            timelineEvent.setExtend("{}");

            return timelineEvent;

        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> convertMedPromiseHistoryToTimeline error", e);
            return null;
        }
    }


}
