package com.jdh.o2oservice.application.via.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelEcologyApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.application.support.service.UserFeedbackApplication;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ReportShowTypeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateCodeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angel.query.JdhAngelEcologyQueryRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTrackDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelTrackQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;
import com.jdh.o2oservice.export.user.query.UserFeedbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 到家护理，预约详情页
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/2 10:14
 */
@Component
@Slf4j
public class HomePromiseDetailHandler extends AbstractViaDataFillHandler implements MapAutowiredKey {

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private VoucherRepository voucherRepository;

    /**
     * promiseHistoryRepository
     */
    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * angelApplication
     */
    @Autowired
    private AngelApplication angelApplication;

    /**
     * angelPromiseApplication
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;


    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * angelWorkApplication
     */
    @Autowired
    private AngelWorkApplication angelWorkApplication;
    @Resource
    private UserFeedbackApplication userFeedbackApplication;
    @Resource
    private SkuInfoRpc skuInfoRpc;
    @Resource
    private ViaConfigRepository viaConfigRepository;
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private CallRecordApplication callRecordApplication;
    @Resource
    private AngelEcologyApplication angelEcologyApplication;

    @Resource
    private ViaComponentDomainService viaComponentDomainService;

    @Resource
    private MedicalReportApplication medicalReportApplication;

    private static List<Integer> MEDICAL_WAIT_TOLAB_STATUS_LIST = Arrays.asList(JdhPromiseStatusEnum.COMPLETE.getStatus(),JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus());


    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx) {
        //场景
        AssertUtils.hasText(ctx.getScene(), SystemErrorCode.PARAM_NULL_ERROR.formatDescription("scene is null"));
        AssertUtils.hasText(ctx.getPromiseId(), SystemErrorCode.PARAM_NULL_ERROR.formatDescription("promiseId is null"));
    }


    /**
     * 命中状态映射
     * {
     * "statusExpression": "!include(seq.list(1,7,8,9),orderStatus) && include(seq.list(1,2),promiseStatus)",
     * "mainTitle": "待护士接单",
     * "title":"正在通知护士接单，预计 <span>{angelWorkReceiveTime}</span> 前接单",
     * "titleDynamicField":["angelWorkReceiveTime"]
     * "dynamicCursorMinutes": 30,
     * "mainIcon": "https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "promiseTitle":"上门信息",
     * "promiseTitleDynamicField":[]
     * "stepGuideFinishCodeList":[],
     * "stepGuideFinishIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "stepGuideProcessCodeList":["angelReceiveWork"],
     * "stepGuideProcessIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "stepGuideWaitCodeList":["service","submitTest","testing","reported"],
     * "stepGuideWaitIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "footerButtonCodeList": ["refundBtn","contactCustomerBtn","rePurchaseBtn"],
     * "hiddenFloorCode":["promiseAngelInfo","promideCodeInfo"],
     * "hiddenPatientFieldList":[],
     * "hiddenAngelInfoList":[],
     * "hiddenAngelBtnList":[]
     * }
     *
     * @param statusMapping      订单状态映射
     * @param jdhPromise         jdhPromise
     * @param medicalPromiseList medicalPromiseList
     * @return {@link ViaStatusMapping}
     */
    private ViaStatusMapping hitStatusMapping(JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList, List<ViaStatusMapping> statusMapping) {
        Map<String, Object> param = new HashMap<>();
        param.put("promiseStatus", Objects.isNull(jdhPromise) ? null : jdhPromise.getPromiseStatus());
        param.put("medPromiseStatusList", CollUtil.isEmpty(medicalPromiseList) ? null : medicalPromiseList.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toList()));

        for (ViaStatusMapping viaStatusMapping : statusMapping) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }


    /**
     * 处理楼层列表
     *
     * @param ctx           上下文
     * @param statusMapping 状态映射
     */
    private void dealFloorList(FillViaConfigDataContext ctx, ViaStatusMapping statusMapping, JdhPromise promise,
                               List<MedicalPromiseDTO> medicalPromiseList, Map<Long, JdhSkuDto> skuDtoMap,
                               JdhVoucher voucher) {
        ViaConfig viaConfig = ctx.getViaConfig();
        // 填充模版数据
        Map<String, Object> sourceData = Maps.newHashMap();
        sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), promise);
        sourceData.put("ctx", ctx);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : viaConfig.getFloorList()) {
            //概要 summaryInfo
            if (ViaFloorEnum.PROMISE_SUMMARY_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleSummaryInfo(viaFloorInfo, statusMapping, promise, medicalPromiseList, skuDtoMap, ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handleSummaryInfo exception", exception);
                    return null;
                }));
            }

            //步骤条 stepGuideInfo
            if (ViaFloorEnum.PROMISE_STEP_GUIDE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleStepGuideInfo(viaFloorInfo, statusMapping), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handleStepGuideInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 标题 promiseTitleInfo
            if (ViaFloorEnum.PROMISE_TITLE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseTitleInfo(viaFloorInfo, statusMapping, promise, medicalPromiseList, skuDtoMap, ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handlePromiseTitleInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 服务者 promiseAngelInfo
            if (ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseAngelInfo(viaFloorInfo, statusMapping, promise, skuDtoMap, ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handlePromiseAngelInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 消费码 promiseCodeInfo
            if (ViaFloorEnum.PROMISE_CODE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseCodeInfo(viaFloorInfo, promise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handlePromiseCodeInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 被服务者 promisePatientInfo
            if (ViaFloorEnum.PROMISE_PATIENT_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelPromisePatientInfo(viaFloorInfo, statusMapping, promise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handelPromisePatientInfo exception", exception);
                    return null;
                }));
            }

            //样本信息楼层 materialInfo
            if (ViaFloorEnum.PROMISE_MATERIAL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelMaterialInfo(viaConfig, viaFloorInfo, promise, medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handelMaterialInfo exception", exception);
                    return null;
                }));
            }

            //样本信息楼层 materialInfo
            if (ViaFloorEnum.PROMISE_MATERIAL_INFO2.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelMaterialInfo2(viaFloorInfo,viaConfig, medicalPromiseList, promise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handelMaterialInfo exception", exception);
                    return null;
                }));
            }


            //底部按钮 footerButtons
            if (ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterButtons(ctx, viaFloorInfo, statusMapping, promise, sourceData, voucher),
                        executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }


            /**
             * 检测项目条码信息（自采样需要）
             */
            if (ViaFloorEnum.PROMISE_SPECIMEN_CODE.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterMedicalPromise(viaFloorInfo, sourceData, medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }

            /**
             * 采样教程楼层（自采样需要）
             */
            if (ViaFloorEnum.PROMISE_SAMPLE_COURSE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterSampleCourse(viaFloorInfo, sourceData, promise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }

            //用户反馈
            if (ViaFloorEnum.PROMISE_USER_FEEDBACK_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleUserFeedbackInfo(viaFloorInfo, promise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomePromiseDetailHandler dealFloorList handleUserFeedbackInfo exception", exception);
                    return null;
                }));
            }
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    private void handlePromiseAngelInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise jdhPromise, Map<Long, JdhSkuDto> skuDtoMap, FillViaConfigDataContext ctx) {
        // 查询护士信息
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();

        AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
        angelWorkQuery.setPromiseId(jdhPromise.getPromiseId());
        AngelWorkDetailDto angelWork = angelPromiseApplication.queryAngelWork(angelWorkQuery);
        queryAngelDto(jdhPromise.getPromiseId());
        log.info("HomePromiseDetailHandler handlePromiseAngelInfo angelWork:{}", JSON.toJSONString(angelWork));
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            String fileKey = viaFloorConfig.getFieldKey();
            if (CollectionUtils.isNotEmpty(viaFloorConfig.getBtnList())){
                handlerFloorConfigBtns(viaFloorConfig.getBtnList(), statusMapping, jdhPromise);
                continue;
            }
            if (!statusMapping.supportFiled(viaFloorInfo.getFloorCode(), fileKey)) {
                iterator.remove();
                continue;
            }
            if (ViaAngelInfoFieldEnum.HOME_DATE.getField().equals(fileKey)) {
                //不合适
                if (JdhPromiseStatusEnum.SERVICING.getStatus().equals(jdhPromise.getPromiseStatus())) {
                    JdhPromiseHistory last = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), JdhPromiseStatusEnum.SERVICING.getStatus());
                    Date servicingTime = jdhPromise.getUpdateTime();
                    if (Objects.nonNull(last)) {
                        servicingTime = last.getCreateTime();
                    }
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(servicingTime, TimeFormat.LONG_PATTERN_LINE) + " 已上门");
                } else {
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    if(Objects.nonNull(jdhVerticalBusiness) && BusinessModeEnum.checkTestAndCare(jdhVerticalBusiness.getBusinessModeCode())) {
                        viaFloorConfig.setFieldValue("预约 " + getFullAppointmentDateDesc(jdhPromise.getAppointmentTime()) + " 上门");
                    }
                }
                continue;
            }
            if (ViaAngelInfoFieldEnum.ANGEL_NAME.getField().equals(fileKey)) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    String angelName = angelWork.getAngelName();
                    String angelType = AngelWorkTypeEnum.getEnumByCode(angelWork.getWorkType()).getAngelType();
                    if (MapUtil.isNotEmpty(skuDtoMap)){
                        JdhSkuDto jdhSku = new ArrayList<>(skuDtoMap.values()).get(0);
                        angelType = productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), angelType);
                    }
                    viaFloorConfig.setFieldValue(new UserName(angelName).mask() + "-" + angelType);
                }
                continue;
            }
            if (ViaAngelInfoFieldEnum.ANGEL_PHONE.getField().equals(fileKey)) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(angelWork.getAngelPhone());
                }
                continue;
            }

            if (ViaAngelInfoFieldEnum.HEAD_IMG.getField().equals(fileKey)) {
                if (Objects.nonNull(angelWork)) {
                    viaFloorConfig.setFieldValue(angelWork.getAngelHeadImg());
                } else {
                    iterator.remove();
                }
                continue;
            }
            if(ViaAngelInfoFieldEnum.CONTACT_ANGEL.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    if (Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType())
                            .contains(angelWork.getStatus())){
                        ViaActionInfo action = viaFloorConfig.getAction();
                        if (action != null){
                            action.getParams().put("promiseId",angelWork.getPromiseId());
                        }
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }
        }

        log.info("HomePromiseDetailHandler handlePromiseAngelInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理楼层嵌套的按钮
     */
    /**
     *
     * @param btnList 当前楼层配置的按钮
     * @param statusMapping 当前状态配置
     */
    private void handlerFloorConfigBtns(List<ViaBtnInfo> btnList, ViaStatusMapping statusMapping, JdhPromise jdhPromise){

        // 当前楼层拥有的按钮
        List<String> showBtns = statusMapping.getFooterButtonCodeList();
        if (CollUtil.isNotEmpty(btnList) && CollectionUtils.isNotEmpty(showBtns)) {
            Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
            while (btnInfoIterator.hasNext()) {
                ViaBtnInfo btnInfo = btnInfoIterator.next();
                if (!showBtns.contains(btnInfo.getCode())) {
                    btnInfoIterator.remove();
                    continue;
                }
                //查看位置
                if (ViaAngelInfoBtnEnum.VIEW_POSITION.getBtn().equals(btnInfo.getCode())) {
                    AngelTrackQuery angelTrackQuery = new AngelTrackQuery();
                    angelTrackQuery.setPromiseId(jdhPromise.getPromiseId());
//                    angelTrackQuery.setScene(BizSceneEnum.C_ANGEL_DETAIL.getScene());
                    AngelTrackDto track = angelWorkApplication.getTransferTrack(angelTrackQuery);
                    log.info("HomePromiseDetailHandler handlerFloorConfigBtns track={}", JSON.toJSONString(track));
                    if (Objects.nonNull(track) && StringUtils.isNotBlank(track.getTrackUrl())) {
                        try {
                            ViaActionInfo action = btnInfo.getAction();
                            String trackUrl = track.getTrackUrl();
                            log.info("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo trackUrl={}", trackUrl);
                            String phone = callRecordApplication.getAppointmentPhone(jdhPromise);
                            PhoneNumber phoneNumber = new PhoneNumber();
                            String encodedPhone = URLEncoder.encode(phoneNumber.encrypt(phone), "UTF-8");
                            trackUrl = trackUrl+"&userType=appointmentUser&encryptPhone="+encodedPhone;
                            action.setUrl(trackUrl);
                        } catch (Exception e) {
                            log.error("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo trackUrl error e", e);
                        }
                    } else {
                        btnInfoIterator.remove();
                    }
                }
            }
        }
    }
    private void handlePromiseTitleInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList, Map<Long, JdhSkuDto> skuDtoMap, FillViaConfigDataContext ctx) {
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setMainTitle(StrUtil.format(statusMapping.getPromiseTitle(), getDynamicFieldValue(promise, medicalPromiseList, statusMapping.getPromiseTitleDynamicField(), statusMapping.getDynamicCursorMinutes(), skuDtoMap)));
        if (MapUtil.isNotEmpty(skuDtoMap)){
            JdhSkuDto jdhSkuDto = new ArrayList<>(skuDtoMap.values()).get(0);
            viaFloorConfig.setMainTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), viaFloorConfig.getMainTitle()));
        }
        floorConfigList.add(viaFloorConfig);
        log.info("HomePromiseDetailHandler handlePromiseTitleInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 根据promise获取业务身份和serviceType,映射到ViaConfig的场景值，根据场景值查询当前状态对应的statusMapping,解析mapping。
     * todo 后续优化方案，需要吧流程节点展示的状态和文案单独抽取出来，根据业务模式、服务类型、服务者类型进行解析，提供一套通用的能力，提供给预约详情页、订单列表页、订单详情页使用
     *
     * @param promise
     * @return
     */
    public String calcPromiseTime(JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList, Map<Long, JdhSkuDto> skuDtoMap) {

        String verticalCode = promise.getVerticalCode();
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(verticalCode);
        String scene = "";
        if (StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())) {
            scene = "homeAngelTestPromiseDetail";
        } else if (StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())) {
            scene = "homeAngelCarePromiseDetail";
        } else if (StringUtils.equals(jdhVerticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())) {
            scene = "homeAngelTestNoLaboratoryPromiseDetail";
        }

        ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(scene).build());

        try {
            //查商品配置
            if (CollectionUtils.isNotEmpty(promise.getServices())) {
                Set<Long> serviceIds = promise.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toSet());
                skuDtoMap = querySkuInfo(serviceIds);
            }
            ViaStatusMapping statusMapping = hitStatusMapping(promise, medicalPromiseList, viaConfig.getStatusMapping());
            Map<String, String> value = getDynamicFieldValue(promise, medicalPromiseList, statusMapping.getTitleDynamicField(), statusMapping.getDynamicCursorMinutes(), skuDtoMap);
            return StrUtil.format(statusMapping.getTitle(), value);
        } catch (Exception e) {
            log.error("HomePromiseDetailHandler handle error", e);
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 获取冬天属性值
     *
     * @param jdhPromise
     * @param medicalPromiseList
     * @param dynamicFieldList
     * @param dynamicCursorMinutes
     * @param skuDtoMap
     * @return
     */
    private Map<String, String> getDynamicFieldValue(JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList,
                                                     List<String> dynamicFieldList, Integer dynamicCursorMinutes, Map<Long, JdhSkuDto> skuDtoMap) {
        if (CollUtil.isEmpty(dynamicFieldList)) {
            return null;
        }
        log.info("[HomePromiseDetailHandler -> getDynamicFieldValue],dynamicFieldList={}", JSON.toJSONString(dynamicFieldList));
        Map<String, String> result = new HashMap<>(dynamicFieldList.size());
        Date now = new Date();
        Date tomorrow = DateUtil.tomorrow().toJdkDate();

        for (String field : dynamicFieldList) {
            // 预估服务者接单时间  提交派单时间 + 30分钟
            if (ViaDynamicFieldEnum.ANGEL_WORK_RECEIVE_TIME.getField().equals(field)) {
                JdhPromiseHistory last = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus());
                String waitPickUpTime = getWaitPickUpTime(Lists.newArrayList(last), jdhPromise, dynamicCursorMinutes);
                result.put(ViaDynamicFieldEnum.ANGEL_WORK_RECEIVE_TIME.getField(), waitPickUpTime);
            } else if (ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField().equals(field)) {
                //预估服务者上门日期，如果是今天展示"今天"，否则yyyy-MM-dd 日期
                LocalDateTime appointmentStartTime = jdhPromise.getAppointmentTime().getAppointmentStartTime();
                if (DateUtil.isSameDay(now, TimeUtils.localDateTimeToDate(appointmentStartTime))) {
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField(), "今天");
                } else if (DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentStartTime))) {
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField(), "明天");
                } else {
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField(), TimeUtils.localDateTimeToStr(appointmentStartTime, TimeFormat.SHORT_PATTERN_LINE));
                }
                //预估服务者上门开始时间
                //立即预约 1 - 开始时间  预约开始时间 + 1h
                //非立即预约 1 - 开始时间  预约开始时间
            } else if (ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField().equals(field)) {
                PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
//                Boolean isImmediately = appointmentTime.getIsImmediately();
//                if (isImmediately) {
//                    if(ServiceTypeEnum.TEST.getServiceType().equals(jdhPromise.getServiceType())) {
//                        Date startTime = TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime());
//                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(), TimeUtils.dateTimeToStr(startTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
//                    }else {
//                        DateTime startTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()), DateField.MINUTE, dynamicCursorMinutes);
//                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(), TimeUtils.dateTimeToStr(startTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
//                    }
//                } else {
//                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(), TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentStartTime(), TimeFormat.DATE_PATTERN_HM_SIMPLE));
//                }
                result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(), TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentStartTime(), TimeFormat.DATE_PATTERN_HM_SIMPLE));
                //预估服务者上门结束时间
                //立即预约   结束时间  预约结束时间 + 1h
                //非理解预约 结束时间  预约结束时间
            } else if (ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField().equals(field)) {
                PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
                Boolean isImmediately = appointmentTime.getIsImmediately();
                if (isImmediately) {
                    if(ServiceTypeEnum.TEST.getServiceType().equals(jdhPromise.getServiceType())) {
                        DateTime endTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()), DateField.MINUTE, dynamicCursorMinutes);
                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(), TimeUtils.dateTimeToStr(endTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
                    }else {
//                        DateTime endTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentEndTime()), DateField.MINUTE, dynamicCursorMinutes);
//                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(), TimeUtils.dateTimeToStr(endTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(), TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentEndTime(), TimeFormat.DATE_PATTERN_HM_SIMPLE));
                    }
                } else {
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(), TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentEndTime(), TimeFormat.DATE_PATTERN_HM_SIMPLE));
                }
                //预估服务者服务完成日期 如果是今天，展示"今天"，否则yyyy-MM-dd 日期
            } else if (ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField().equals(field)) {

                JdhPromiseHistory last = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), JdhPromiseStatusEnum.SERVICING.getStatus());
                Date servicingTime = jdhPromise.getUpdateTime();
                if (Objects.nonNull(last)) {
                    servicingTime = last.getCreateTime();
                }

                if (DateUtil.isSameDay(now, servicingTime)) {
                    result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField(), "今天");
                } else if (DateUtil.isSameDay(tomorrow, servicingTime)) {
                    result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField(), "明天");
                } else {
                    result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField(), TimeUtils.dateTimeToStr(servicingTime, TimeFormat.SHORT_PATTERN_LINE));
                }
                //预估服务者服务完成时间  变更服务中时间 + （所有sku服务时长总和）× 服务人数

            } else if (ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_TIME.getField().equals(field)) {
                JdhPromiseHistory last = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), JdhPromiseStatusEnum.SERVICING.getStatus());
                Date servicingTime = jdhPromise.getUpdateTime();
                if (Objects.nonNull(last)) {
                    servicingTime = last.getCreateTime();
                }

                Collection<JdhSkuDto> skuDtos = skuDtoMap.values();
                int totalServiceTime = 0;
                for (JdhSkuDto skuDto : skuDtos) {
                    totalServiceTime += skuDto.getServiceDuration();
                }
                long promisePatientNums = jdhPromise.getPatients().size();

                DateTime completeTime = DateUtil.offset(servicingTime, DateField.MINUTE, (int) (totalServiceTime * promisePatientNums));

                result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_TIME.getField(), TimeUtils.dateTimeToStr(completeTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
                //预估送检时间  服务变更完成时间 + 1小时

            } else if (ViaDynamicFieldEnum.SUBMIT_TEST_TIME.getField().equals(field)) {
                JdhPromiseHistory last = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus());
                Date servicingCompleteTime = jdhPromise.getUpdateTime();
                if (Objects.nonNull(last)) {
                    servicingCompleteTime = last.getCreateTime();
                }

                DateTime submitTestTime = DateUtil.offset(servicingCompleteTime, DateField.MINUTE, dynamicCursorMinutes);
                result.put(ViaDynamicFieldEnum.SUBMIT_TEST_TIME.getField(), TimeUtils.dateTimeToStr(submitTestTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
                //预估出报告时间
                // 取实验室检测单变更检测中时间 + 实验室检测项检测时长总和（多个检测单，取最长）
            } else if (ViaDynamicFieldEnum.REPORT_TIME_TIME.getField().equals(field)) {
                Date reportedTime = null;
                for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseList) {
                    Date checkTime = medicalPromiseDTO.getCheckTime();
                    Integer testDuration = medicalPromiseDTO.getTestDuration();
                    if (Objects.nonNull(checkTime) && Objects.nonNull(testDuration)) {
                        DateTime offset = DateUtil.offset(checkTime, DateField.MINUTE, testDuration);
                        if (Objects.isNull(reportedTime)) {
                            reportedTime = offset;
                        } else {
                            reportedTime = reportedTime.getTime() > offset.getTime() ? reportedTime : offset;
                        }
                    }
                }
                result.put(ViaDynamicFieldEnum.REPORT_TIME_TIME.getField(), TimeUtils.dateTimeToStr(reportedTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));

            }

        }
        log.info("HomePromiseDetailHandler -> getDynamicFieldValue result:{}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 处理样本信息楼层
     * {
     * "floorCode": "materialInfo",
     * "floorName": "样本信息",
     * "floorConfigList": [
     * {
     * "title": "*杰伦 | JD24281231298",
     * "viaStatus":1,
     * "statusDesc":"送检中",
     * "targetUrl":""
     * }
     * ]
     * }
     *
     * @param jdhPromise jdhPromise
     */
    private void handelMaterialInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        if (CollUtil.isNotEmpty(medicalPromiseList)) {
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();
                //是否有已出报告,且没有退款
                List<MedicalPromiseDTO> reportedList = medPromiseList.stream().filter(ele -> (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) && !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(reportedList)) {
                    ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                    MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                    JdhPromisePatient jdhPromisePatient = promisePatientMap.get(reportedMedPromise.getPromisePatientId());
                    viaFloorConfig.setTitle(Objects.isNull(jdhPromisePatient.getUserName()) ? "" : jdhPromisePatient.getUserName().mask() + "的检测报告");
                    fillMedPromiseFloorConfig(viaFloorConfig, reportedMedPromise, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                    viaFloorConfigList.add(viaFloorConfig);
                } else {
                    for (MedicalPromiseDTO medicalPromiseDTO : medPromiseList) {
                        // 冻结或者作废不展示
                        if (JdhFreezeEnum.FREEZE.getStatus().equals(medicalPromiseDTO.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDTO.getStatus())) {
                            continue;
                        }
                        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                        JdhPromisePatient jdhPromisePatient = promisePatientMap.get(medicalPromiseDTO.getPromisePatientId());
                        viaFloorConfig.setTitle(jdhPromisePatient.getUserName().mask() + " | " + medicalPromiseDTO.getSpecimenCode());
                        fillMedPromiseFloorConfig(viaFloorConfig, medicalPromiseDTO, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                        viaFloorConfigList.add(viaFloorConfig);
                    }
                }
            }
            viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        }
        log.info("HomePromiseDetailHandler handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * @param viaFloorInfo       楼层配置
     * @param medicalPromiseList 检测单
     * @param jdhPromise         履约单数据
     */
    private void handelMaterialInfo2(ViaFloorInfo viaFloorInfo, ViaConfig viaConfig, List<MedicalPromiseDTO> medicalPromiseList, JdhPromise jdhPromise) {
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseViewReportInfo2.viaPromiseFloorInfoDto={},jdhPromise={}", JSON.toJSONString(viaFloorInfo), JSON.toJSONString(jdhPromise));
        if(CollUtil.isNotEmpty(medicalPromiseList)){
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            MedicalPromiseDTO completeMed = medicalPromiseList.stream().filter(p -> Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(), p.getStatus())).findFirst().orElse(null);
            Boolean completeExist = Objects.nonNull(completeMed) ? Boolean.TRUE : Boolean.FALSE;
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();

                Map<Integer, List<MedicalPromiseDTO>> showTypeToList = medPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getReportShowType));
                ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                List<ViaBtnInfo> newBtnList = new ArrayList<>();
                viaFloorConfig.setBtnList(newBtnList);
                AtomicReference<Boolean> report = new AtomicReference<>(Boolean.FALSE);
                viaFloorConfigList.add(viaFloorConfig);
                MedicalPromiseDTO first = medPromiseList.get(0);
                JdhPromisePatient patient = promisePatientMap.get(first.getPromisePatientId());
                //如果已出报告，则有title
                if (completeExist){
                    viaFloorConfig.setTitle(Objects.isNull(patient.getUserName()) ? "" : patient.getUserName().mask() + "的检测报告");
                }
                showTypeToList.forEach((showType,list)->{

                    //如果是结构化页面
                    if (Objects.equals(ReportShowTypeEnum.STRUCT.getType(),showType)){
                        //判断是否有出报告的检测单
                        //是否有已出报告,且没有退款
                        List<MedicalPromiseDTO> reportedList = list.stream().filter(ele ->
                                (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) &&
                                        !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                                                || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))
                        ).collect(Collectors.toList());
                        String tip = list.stream().map(MedicalPromiseDTO::getServiceItemName).collect(Collectors.joining(","));
                        log.info("handelMaterialInfo->name={},reportedList={},tip={}",patient.getUserName(),reportedList,tip);
                        //如果出报告
                        if(CollUtil.isNotEmpty(reportedList)){
                            report.set(Boolean.TRUE);

                            MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                            JdhPromisePatient jdhPromisePatient = promisePatientMap.get(reportedMedPromise.getPromisePatientId());

                            for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                log.info("handelMaterialInfo->,statusList={},stats={}",JSON.toJSONString(viaStatusMapping.getStatusList()),reportedMedPromise.getStatus());
                                if (viaStatusMapping.getStatusList().contains(reportedMedPromise.getStatus())) {
                                    viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                    viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                    List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                                    if(CollUtil.isNotEmpty(btnList)){
                                        for (ViaBtnInfo viaBtnInfo : btnList) {
                                            if(ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())){
                                                if(Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())){
                                                    ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo),ViaBtnInfo.class);
                                                    newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(),jdhPromise.getSourceVoucherId(),jdhPromisePatient.getPatientId().toString(),jdhPromise.getPromiseId().toString()));
                                                    newBtnInfo.setBtnTip(tip);
                                                    newBtnInfo.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                                    newBtnList.add(newBtnInfo);
                                                }
                                            }
                                        }
                                    }
                                    break;
                                }
                            }

                        }else {
                            //如果未出报告
                            for (MedicalPromiseDTO medicalPromiseDTO : list) {
                                ViaBtnInfo newBtnInfo = new ViaBtnInfo();
                                newBtnList.add(newBtnInfo);
                                newBtnInfo.setStyle("text");
                                String btnTip = completeExist ? medicalPromiseDTO.getServiceItemName() : patient.getUserName().mask() + " | " + medicalPromiseDTO.getSpecimenCode();
                                newBtnInfo.setBtnTip(btnTip);
                                for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                    log.info("handelMaterialInfo2->,statusList={},stats={}",JSON.toJSONString(viaStatusMapping.getStatusList()),medicalPromiseDTO.getStatus());
                                    if (viaStatusMapping.getStatusList().contains(medicalPromiseDTO.getStatus())) {
                                        newBtnInfo.setName(viaStatusMapping.getStatusDesc());
                                        newBtnInfo.setLevel(viaStatusMapping.getViaStatus());
                                        if (!Boolean.TRUE.equals(report.get())){
                                            viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                            viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                        }
                                        break;
                                    }
                                }
                            }

                        }

                    }else {
                        //如果是PDF页面
                        //遍历
                        //判断是否出报告
                        //如果出报告
                        //如果未出报告
                        for (MedicalPromiseDTO medPromise : list) {
                            ViaBtnInfo newBtnInfo = new ViaBtnInfo();
                            newBtnList.add(newBtnInfo);
                            String btnTip = completeExist ? medPromise.getServiceItemName() : patient.getUserName().mask() + " | " + medPromise.getSpecimenCode();
                            newBtnInfo.setBtnTip(btnTip);
                            //如果出报告了
                            if (Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(),medPromise.getStatus())){
                                report.set(Boolean.TRUE);
                                newBtnInfo.setName("去查看");
                                newBtnInfo.setStyle("primaryBordered");
                                ViaActionInfo ai = new ViaActionInfo();
                                newBtnInfo.setAction(ai);
                                Map<String,Object> params = new HashMap<>();
                                params.put("medicalPromiseId",medPromise.getMedicalPromiseId());
                                ai.setParams(params);
                                ai.setType("request");
                                ai.setFunctionId("jdh_o2oservice_queryUrlForCenter");
                                newBtnInfo.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                viaFloorConfig.setViaStatus(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                viaFloorConfig.setStatusDesc("已出报告");
                            }else {
                                //如果未出报告
                                newBtnInfo.setStyle("text");
                                for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                    if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                                        newBtnInfo.setName(viaStatusMapping.getStatusDesc());
                                        newBtnInfo.setLevel(viaStatusMapping.getViaStatus());
                                        if (!Boolean.TRUE.equals(report.get())){
                                            viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                            viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                    }
                });

                if (CollectionUtils.isNotEmpty(newBtnList)){
                    newBtnList.sort(Comparator.comparing(ViaBtnInfo::getLevel).reversed());
                }
            }
            viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        }
        log.info("XfylHomeTestOrderDetailHandler handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * fillMedPromiseFloorConfig
     *
     * @param medPromise           medPromise
     * @param medicalStatusMapping medicalStatusMapping
     * @param jdhPromisePatient    jdhPromisePatient
     * @param jdhPromise           jdhPromise
     */
    private void fillMedPromiseFloorConfig(ViaFloorConfig viaFloorConfig, MedicalPromiseDTO medPromise,
                                           List<ViaStatusMapping> medicalStatusMapping,
                                           JdhPromisePatient jdhPromisePatient,
                                           JdhPromise jdhPromise) {

        Map<String, Object> sourceData = Maps.newHashMap();
        sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
        sourceData.put(MedPromiseAggregateEnum.MED_PROMISE.getCode(), medPromise);
        for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
            if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                if (CollUtil.isNotEmpty(btnList)) {
                    List<ViaBtnInfo> newBtnList = new ArrayList<>();
                    for (ViaBtnInfo viaBtnInfo : btnList) {
                        if (ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())) {
                            if (Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())) {
                                ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo), ViaBtnInfo.class);
                                newBtnInfo.init(sourceData);
                                newBtnList.add(newBtnInfo);
                            }
                        }
                    }
                    viaFloorConfig.setBtnList(newBtnList);
                }
                break;
            }
        }
    }

    /**
     * 处理底部按钮
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     */
    private void handleFooterButtons(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo,
                                     ViaStatusMapping statusMapping, JdhPromise promise, Map<String, Object> sourceData,
                                     JdhVoucher voucher) {
        //申请退款 refundBtn
        //再次购买 rePurchaseBtn
        //联系客服 contactCustomerBtn
        //取消订单 cancelOrderBtn
        //立即支付 payNowBtn
        ViaConfig viaConfig = ctx.getViaConfig();
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();

        while (btnInfoIterator.hasNext()) {
            ViaBtnInfo btnInfo = btnInfoIterator.next();

            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode", viaConfig.getVerticalCode());
            actionCommonParams.put("serviceType", viaConfig.getServiceType());
            actionCommonParams.put("envType", ctx.getEnvType());
            actionCommonParams.put("promiseId",Objects.nonNull(promise) ? promise.getPromiseId() : null);
            // 移除不需要展示的按钮
            if (!statusMapping.getFooterButtonCodeList().contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }

            ViaActionInfo action = btnInfo.getAction();
            //再次购买
            if (ViaBtnCodeEnum.RE_PURCHASE_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    String jumpUrl = MessageFormat.format(btnInfo.getJumpUrlRule(), promise.findBasicService().getServiceId().toString());
                    action.setUrl(jumpUrl);
                } catch (Exception e) {
                    log.error("HomePromiseDetailHandler handleFooterButtons RE_PURCHASE_BTN fail", e);
                    btnInfoIterator.remove();
                }

                //联系客服
            } else if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    String orderId = promise.findExtend(PromiseExtendKeyEnum.ORDER_ID.getFiledKey()).getValue();
                    PromiseService service = promise.findBasicService();
                    RpcSkuBO skuBO = skuInfoRpc.getCrsSkuBoBySkuId(service.getServiceId().toString());
                    String jumpUrl = JSON.parseObject(btnInfo.getJumpUrlRule(), new TypeReference<Map<String, String>>() {
                    }).get(EnvTypeEnum.get(ctx.getEnvType()).getCode());
                    if (EnvTypeEnum.JD_APP.getCode().equals(ctx.getEnvType())) {
                        action.setUrl(jumpUrl.replace("{0}", String.valueOf(skuBO.getVenderId())));
                    } else {
                        //https://jdcs.m.jd.com/chat/index.action?entry=jd_sdk_kjzydxy&orderId={0}
                        action.setUrl(MessageFormat.format(jumpUrl, String.valueOf(skuBO.getVenderId()), orderId));
                    }
                } catch (Exception e) {
                    log.error("HomePromiseDetailHandler handleFooterButtons CONTACT_CUSTOMER_BTN fail", e);
                    btnInfoIterator.remove();
                }
                /**
                 * 重新预约按钮，如果voucher已过期则不展示
                 */
            } else if(ViaBtnCodeEnum.RE_APPOINT_BTN.getCode().equals(btnInfo.getCode())){
                if (Objects.equals(voucher.getStatus(), JdhVoucherStatusEnum.EXPIRED.getStatus())){
                    btnInfoIterator.remove();
                }else{
                    btnInfo.init(sourceData);

                }
            } else if (ViaBtnCodeEnum.EVALUATE_ANGEL_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    Map<Long, String> urlMap = angelEcologyApplication.queryAngelEcologyPageUrl(
                            JdhAngelEcologyQueryRequest.builder().envType(ctx.getEnvType()).orderId(Long.valueOf(promise.getSourceVoucherId())).build());
                    String jumpUrl = urlMap.get(promise.getPromiseId());
                    //有链接设置url
                    if (StringUtils.isNotBlank(jumpUrl)) {
                        action.setUrl(jumpUrl);
                    } else {//无连接不返回按钮
                        btnInfoIterator.remove();
                    }
                } catch (Exception e) {
                    log.error("HomePromiseDetailHandler handleFooterButtons EVALUATE_ANGEL_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }else {
                btnInfo.init(sourceData);
            }
        }

        log.info("HomePromiseDetailHandler handleFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 履约信息 - 被服务者
     * {
     * "floorCode": "promisePatientInfo",
     * "floorName": "履约被服务者信息",
     * "floorConfigList": [
     * {
     * "fieldKey":"appointmentTime",
     * "fieldValue": "2024-01-01 00:00 - 01:00"
     * },
     * {
     * "fieldKey":"patientList",
     * "fieldValue":[
     * {
     * "userName":"*先生"
     * },
     * {
     * "userName":"*女士"
     * }
     * ]
     * },
     * {
     * "fieldKey":"patientAddress",
     * "fieldValue": "北京大兴区旧宫地区旧宫新苑南区14号楼二单元302"
     * },
     * {
     * "fieldKey":"remark",
     * "fieldValue": "上门前请提前联系"
     * }
     * ]
     * }
     *
     * @param statusMapping 状态映射
     * @param jdhPromise    jdhPromise
     */
    private void handelPromisePatientInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise jdhPromise) {
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            String filed = viaFloorConfig.getFieldKey();
            if (!statusMapping.supportFiled(viaFloorInfo.getFloorCode(), filed)) {
                iterator.remove();
                continue;
            }
            if (ViaPatientInfoFieldEnum.APPOINTMENT_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                viaFloorConfig.setFieldValue(getFullAppointmentDateDesc(jdhPromise.getAppointmentTime()));
            }
            if (ViaPatientInfoFieldEnum.PATIENT_ADDRESS.getField().equals(viaFloorConfig.getFieldKey())) {
                viaFloorConfig.setFieldValue(jdhPromise.getStore().getStoreAddr());
            }
            if (ViaPatientInfoFieldEnum.PATIENT_LIST.getField().equals(viaFloorConfig.getFieldKey())) {
                List<JdhPromisePatient> patients = jdhPromise.getPatients();
                List<Map<String, String>> value = new ArrayList<>();
                for (JdhPromisePatient patient : patients) {
                    value.add(MapUtil.builder("userName", patient.getUserName().mask()).build());
                }
                viaFloorConfig.setFieldValue(JSON.toJSONString(value));
            }
            if (ViaPatientInfoFieldEnum.REMARK.getField().equals(viaFloorConfig.getFieldKey())) {
                List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
                for (JdhPromiseExtend promiseExtend : promiseExtends) {
                    if (promiseExtend.getAttribute().equals(PromiseExtendKeyEnum.ORDER_REMARK.getFiledKey())) {
                        viaFloorConfig.setFieldValue(promiseExtend.getOrderRemark());
                    }
                }
            }
        }
        log.info("HomePromiseDetailHandler handelPromisePatientInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取完整预约日期描述
     *
     * @param appointmentTime 预约时间
     * @return {@link String}
     */
    private String getFullAppointmentDateDesc(DomainAppointmentTime appointmentTime) {
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        String dateDesc = " ";
        if (DateUtil.isSameDay(new Date(), TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))) {
            dateDesc = "[今天]";
        }
        if (DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))) {
            dateDesc = "[明天]";
        }
        return appointmentTime.formatAppointDate() + dateDesc + appointmentTime.formatAppointTimeDesc();
    }

    /**
     *
     */
    private void handlePromiseCodeInfo(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        viaFloorConfig.setPromiseCode(jdhPromise.getCode());
        log.info("HomePromiseDetailHandler handlePromiseCodeInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 隐藏楼层
     *
     * @param statusMapping 状态映射
     * @param floorList     楼层列表
     */
    private void clearHiddenFloor(ViaStatusMapping statusMapping, List<ViaFloorInfo> floorList) {
        Iterator<ViaFloorInfo> iterator = floorList.iterator();
        while (iterator.hasNext()) {
            ViaFloorInfo viaFloorInfo = iterator.next();
            if (!statusMapping.getShowFloorCode().contains(viaFloorInfo.getFloorCode())) {
                iterator.remove();
            }
        }
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    @SuppressWarnings("all")
    public void handle(FillViaConfigDataContext ctx) {
        log.info("HomePromiseDetailHandler handle ctx:{}", JSON.toJSONString(ctx));
        // ==>>>> 入参校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();


        JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(Long.valueOf(ctx.getPromiseId())));
        if (Objects.isNull(promise)) {
            throw new SystemException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
        }
        JdhVoucher voucher = voucherRepository.find(new JdhVoucherIdentifier(promise.getVoucherId()));
        List<CompletableFuture> futures = new ArrayList<>();
        //查检测单
        CompletableFuture<List<MedicalPromiseDTO>> medPromiseListCf = null;
        if (Objects.nonNull(promise)) {
            medPromiseListCf = CompletableFuture.supplyAsync(() -> queryMedicalPromiseList(promise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(medPromiseListCf);
        }


        //查商品配置
        CompletableFuture<Map<Long, JdhSkuDto>> skuDtoCf = null;
        PromiseService promiseService = promise.findBasicService();
        if (CollectionUtils.isNotEmpty(promise.getServices())) {
            Set<Long> serviceIds = promise.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toSet());
            skuDtoCf = CompletableFuture.supplyAsync(() -> querySkuInfo(serviceIds), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(skuDtoCf);
        }
        futures.add(medPromiseListCf);

        try {
            // ==>>>> 过滤statusMapping
            ViaStatusMapping statusMapping = hitStatusMapping(promise, Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(), viaConfig.getStatusMapping());
            log.info("HomePromiseDetailHandler handle statusMapping:{}", JSON.toJSONString(statusMapping));

            // ==>>>> 移除当前状态下隐藏的楼层
            List<ViaFloorInfo> floorList = viaConfig.getFloorList();
            clearHiddenFloor(statusMapping, floorList);
            log.info("HomePromiseDetailHandler handle floorList:{}", JSON.toJSONString(floorList));

            // ==>>>> 楼层处理
            dealFloorList(ctx, statusMapping, promise, Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(),
                    Objects.isNull(skuDtoCf) ? null : skuDtoCf.get(), voucher);
            log.info("HomePromiseDetailHandler handle viaConfig:{}", JSON.toJSONString(viaConfig));
        } catch (Exception e) {
            log.error("HomePromiseDetailHandler handle error", e);
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_HAND_ERROR);
        }


        //异步编译 Aviator
        futures.add(CompletableFuture.runAsync(() -> compileAviator(viaConfig), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)));

        if (CollUtil.isNotEmpty(futures)) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }


    }


    /**
     * compileAviator
     *
     * @param viaConfig VIA配置
     */
    private void compileAviator(ViaConfig viaConfig) {
        try {
            for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
                if (StrUtil.isNotBlank(viaStatusMapping.getStatusExpression())) {
                    AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.info("HomePromiseDetailHandler handle compileAviator exception", e);
        }
    }

    /**
     * 查询SKU信息
     *
     * @return {@link JdhSkuDto}
     */
    private Map<Long, JdhSkuDto> querySkuInfo(Set<Long> skuIds) {
        try {
            return productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().querySkuCoreData(Boolean.TRUE).queryServiceItem(Boolean.TRUE).skuIdList(skuIds).build());
        } catch (Exception e) {
            log.info("HomePromiseDetailHandler handle querySkuInfo exception", e);
            return null;
        }
    }


    /**
     * queryMedicalPromiseList
     *
     * @param jdhPromise jdhPromise
     * @return {@link List}<{@link MedicalPromiseDTO}>
     */
    private List<MedicalPromiseDTO> queryMedicalPromiseList(JdhPromise jdhPromise) {
        try {
            MedicalPromiseListRequest medPromiseRequest = new MedicalPromiseListRequest();
            medPromiseRequest.setPromiseId(jdhPromise.getPromiseId());
            medPromiseRequest.setItemDetail(Boolean.TRUE);
            medPromiseRequest.setPatientDetail(Boolean.TRUE);
            List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(medPromiseRequest);
            log.info("HomePromiseDetailHandler handle queryMedicalPromiseList medicalPromiseList:{}", JSON.toJSONString(medicalPromiseList));
            return medicalPromiseList;
        } catch (Exception e) {
            log.info("HomePromiseDetailHandler handle queryMedicalPromiseList exception", e);
            return null;
        }
    }

    /**
     * queryMedicalPromiseList
     *
     * @param voucherId voucherId
     * @return {@link List}<{@link MedicalPromiseDTO}>
     */
    private JdhVoucher queryVoucher(Long voucherId) {
        try {
            return voucherRepository.find(new JdhVoucherIdentifier(voucherId));
        } catch (Exception e) {
            log.info("HomePromiseDetailHandler handle queryMedicalPromiseList exception", e);
            return null;
        }
    }


    /**
     * 处理检测单项目信息，订单详情的检测单信息是根据用户归堆展示的，每个用户是一个groupInfoList。
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleFooterMedicalPromise(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData, List<MedicalPromiseDTO> medicalPromiseList) {

        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        // 过滤已经冻结和作废的检测单
        medicalPromiseList = medicalPromiseList.stream().filter(e -> !Objects.equals(e.getFreeze(), YnStatusEnum.YES.getCode())).filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus())).collect(Collectors.toList());

        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getName));

        List<ViaGroupInfo> groups = Lists.newArrayList();
        medicalPromiseMap.forEach((name, list) -> {
            ViaGroupInfo groupInfo = new ViaGroupInfo();
            groupInfo.setTitle(name);
            List<ViaFormItem> items = Lists.newArrayListWithExpectedSize(list.size());
            for (MedicalPromiseDTO medicalPromiseDTO : list) {
                ViaFormItem item = new ViaFormItem();
                item.setFormName(medicalPromiseDTO.getServiceItemName());
                item.setFormType(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                item.setParamField(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                item.setValue(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                // 这个表单项除了value之外需要额外的属性，这个属性可能是前端需要，也可能是action需要
                Map<String, Object> extMap = Maps.newHashMap();
                extMap.put("medicalPromiseId", medicalPromiseDTO.getMedicalPromiseId());
                extMap.put("promiseId", medicalPromiseDTO.getPromiseId());
                extMap.put("specimenCode", medicalPromiseDTO.getSpecimenCode());
                item.setExtMap(extMap);
                item.setPlaceholder("请扫码/输入采集管上的条码");
                item.setRequired(Boolean.TRUE);
                items.add(item);
            }
            groupInfo.setItems(items);
            groups.add(groupInfo);
        });

        viaFloorConfig.setGroupInfoList(groups);
        // 按钮初始化（提交样本信息按钮）
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        if (CollectionUtils.isNotEmpty(btnList)) {
            Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
            while (btnInfoIterator.hasNext()) {
                ViaBtnInfo btnInfo = btnInfoIterator.next();
                btnInfo.init(sourceData);
            }
        }
        log.info("HomePromiseDetailHandler handleFooterMedicalPromise viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理采样教程楼层，当前只有自采样的业务需要采样教程。
     * 互医的场景可能有多个SKU，但是只取了一个SKU的采样教程，业务上保证互医渠道的SKU配置一个通用的采样教程。
     * 理想情况下采样教程应该取检测项目维度的。
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleFooterSampleCourse(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData, JdhPromise jdhPromise) {
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);

        Long serviceId = jdhPromise.getServices().get(0).getServiceId();
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(serviceId);
        JdhSkuDto skuDto = productApplication.queryAggregationJdhSkuInfo(request);
        skuDto.setTutorialUrl(skuDto.getTutorialUrl());
        sourceData.put(ProductAggregateCodeEnum.JDH_PRODUCT_SERVICE.getCode(), skuDto);
        ViaActionInfo action = viaFloorConfig.getAction();
        action.init(sourceData);
        log.info("HomePromiseDetailHandler handleFooterSampleCourse viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * @param viaFloorInfo 通用楼层信息
     * @param jdhPromise      JD履约单
     */
    private void handleUserFeedbackInfo(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();
        UserFeedbackAggregationDTO userFeedbackAggregationDTO = userFeedbackApplication.queryUserFeedback(UserFeedbackRequest.builder().userPin(jdhPromise.getUserPin()).promiseId(jdhPromise.getPromiseId()).businessScene("serviceStandardFeedback").build());
        if (Objects.nonNull(userFeedbackAggregationDTO)) {
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(userFeedbackAggregationDTO)).build());
        }
        viaFloorInfo.setFloorConfigList(floorConfigList);
        log.info("HomePromiseDetailHandler handleUserFeedbackInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * queryAngelDto
     *
     * @param promiseId promiseId
     * @return {@link JdhAngelDto}
     */
    private JdhAngelDto queryAngelDto(Long promiseId) {
        try {
            AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
            angelWorkQuery.setPromiseId(promiseId);
            AngelWorkDetailDto workDetailDto = angelPromiseApplication.queryWorkListOrRecently(angelWorkQuery);
            log.info("HomePromiseDetailHandler queryAngelDto,workDetailDto:{}", JSON.toJSONString(workDetailDto));
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(Long.parseLong(workDetailDto.getAngelId()));
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            log.info("HomePromiseDetailHandler queryAngelDto,jdhAngelDto:{}", JSON.toJSONString(jdhAngelDto));
            return jdhAngelDto;
        } catch (Exception e) {
            log.error("HomePromiseDetailHandler queryAngelDto error", e);
            return null;
        }
    }

    /**
     * 处理摘要信息
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdhPromise    promise
     *                      <p>
     *                      * {
     *                      *     "floorCode": "promiseSummaryInfo",
     *                      *     "floorName": "履约信息概要",
     *                      *     "floorConfigList": [
     *                      *         {
     *                      *             "mainTitle": "待护士接单",
     *                      *             "mainIcon":"https://img11.360buyimg.com/imagetools/jfs/t1/138462/3/30857/44483/64589a51F1d9b5d61/bb3348ce18928273.png",
     *                      *             "title":""
     *                      *        }
     *                      *     ]
     *                      * }
     */
    private void handleSummaryInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList, Map<Long, JdhSkuDto> skuDtoMap, FillViaConfigDataContext ctx) {
        List<ViaFloorConfig> floorConfig = new ArrayList<>();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setMainTitle(StrUtil.format(statusMapping.getMainTitle(), getDynamicFieldValue(jdhPromise, medicalPromiseList, statusMapping.getMainTitleDynamicField(), statusMapping.getDynamicCursorMinutes(), skuDtoMap)));
        viaFloorConfig.setMainIcon(statusMapping.getMainIcon());
        viaFloorConfig.setTitle(StrUtil.format(statusMapping.getTitle(), getDynamicFieldValue(jdhPromise, medicalPromiseList, statusMapping.getTitleDynamicField(), statusMapping.getDynamicCursorMinutes(), skuDtoMap)));
        if (MapUtil.isNotEmpty(skuDtoMap)){
            JdhSkuDto jdhSkuDto = new ArrayList<>(skuDtoMap.values()).get(0);
            viaFloorConfig.setMainTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), viaFloorConfig.getMainTitle()));
            viaFloorConfig.setTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), viaFloorConfig.getTitle()));
        }
        floorConfig.add(viaFloorConfig);
        viaFloorInfo.setFloorConfigList(floorConfig);
        log.info("HomePromiseDetailHandler handleSummaryInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理步骤指南信息
     * {
     * "floorCode": "promiseStepGuideInfo",
     * "floorName": "履约信息步骤条信息",
     * "floorConfigList": [
     * {
     * "title": "预约",
     * "description":"04/01 08:29",
     * "status":"process",
     * "icon":"https://img11.360buyimg.com/imagetools/jfs/t1/138462/3/30857/44483/64589a51F1d9b5d61/bb3348ce18928273.png",
     * "code":"appointment"
     * },
     * ]
     * }
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     */
    private void handleStepGuideInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping) {
        for (ViaFloorConfig viaFloorConfig : viaFloorInfo.getFloorConfigList()) {
            String stepCode = viaFloorConfig.getStepCode();
            //完成
            if (statusMapping.getStepGuideFinishCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.FINISH.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideFinishIcon());
            }
            //进行中
            if (statusMapping.getStepGuideProcessCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.PROCESS.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideProcessIcon());
            }
            //等待
            if (statusMapping.getStepGuideWaitCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.WAIT.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideWaitIcon());
            }
        }
        log.info("HomePromiseDetailHandler handleStepGuideInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.HOME_PROMISE_DETAIL.getScene() + "_default";
    }
}
