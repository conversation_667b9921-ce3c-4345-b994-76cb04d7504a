package com.jdh.o2oservice.application.angelpromise.event;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.enums.ServiceRecordStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelServiceRecordEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelServiceRecordEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordFlowRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordQuestionGroupDto;
import com.jdh.o2oservice.export.promise.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.export.trade.dto.ManRefundTipsInfoDto;
import com.jdh.o2oservice.export.trade.query.ManRefundTipsOrderParam;
import com.jdh.o2oservice.export.trade.query.ManRefundTipsParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderSku;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AngelServiceRecordEventConsumer {

    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private VoucherApplication voucherApplication;

    @Resource
    private TradeApplication tradeApplication;

    @Resource
    private AngelServiceRecordRepository angelServiceRecordRepository;

    @Resource
    private AngelServiceRecordFlowRepository angelServiceRecordFlowRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @PostConstruct
    public void registerEventConsumer() {

        // 评估高风险
        eventConsumerRegister.register(AngelServiceRecordEventTypeEnum.SERVICE_RECORD_EVALUATE_HIGH_RISK,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "evaluateHighRiskProcessor", this::evaluateHighRiskProcessor, Boolean.TRUE, Boolean.FALSE
                        , EventConsumerRetryTemplate.exponentialRetryInstance(3, 100, 2.0, 1000))
        );

        // 护士已接单-创建护理单
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE,
                "workReceivedCreateServiceRecord", this::workReceivedCreateServiceRecord, Boolean.TRUE, Boolean.TRUE
                , EventConsumerRetryTemplate.exponentialRetryInstance(3, 100, 2.0, 1000)));

        // 工单已取消-取消护理单
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_CANCEL_SERVED, WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE,
                "cancelServedWithServiceRecord", this::cancelServedWithServiceRecord, Boolean.TRUE, Boolean.TRUE
                , EventConsumerRetryTemplate.exponentialRetryInstance(3, 100, 2.0, 1000)));



    }

    /**
     * 工单已取消-取消护理单
     * @param event
     */
    private void cancelServedWithServiceRecord(Event event){
        log.info("AngelServiceRecordEventConsumer cancelServedWithServiceRecord event={}", JSON.toJSONString(event));
        // 查询服务者工单
        AngelWorkDBQuery query = AngelWorkDBQuery.builder()
                .workIds(Collections.singletonList(Long.valueOf(event.getAggregateId())))
                .build();
        List<AngelWork> angelWorkList = angelWorkRepository.findList(query);
        if (CollectionUtils.isEmpty(angelWorkList)){
            log.info("cancelServedWithServiceRecord angelWorkList empty");
            return;
        }
        AngelWork angelWork = angelWorkList.get(0);
        log.info("cancelServedWithServiceRecord angelWork={}", JSON.toJSONString(angelWork));

        // 查询护理单
        AngelServiceRecordDBQuery angelServiceRecordDBQuery = AngelServiceRecordDBQuery.builder().promiseId(angelWork.getPromiseId()).build();
        List<AngelServiceRecord> angelServiceRecordList = angelServiceRecordRepository.findList(angelServiceRecordDBQuery);
        log.info("cancelServedWithServiceRecord angelServiceRecordList={}", JSON.toJSONString(angelServiceRecordList));
        if (CollectionUtils.isEmpty(angelServiceRecordList)){
            return;
        }

        // 取消护理单
        angelServiceRecordList.forEach(s->{
            AngelServiceRecord updateAngelServiceRecord = AngelServiceRecord.builder()
                    .id(s.getId())
                    .recordId(s.getRecordId())
                    .status(ServiceRecordStatusEnum.CANCEL.getStatus())
                    .build();
            angelServiceRecordRepository.save(updateAngelServiceRecord);
        });
    }

    /**
     * 护士已接单-创建护理单
     * @param event
     */
    private void workReceivedCreateServiceRecord(Event event){
        log.info("AngelServiceRecordEventConsumer workReceivedCreateServiceRecord event={}", JSON.toJSONString(event));
        JSONObject obj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
        if (!obj.getBoolean("serviceRecordSwitch")){
            log.info("workReceivedCreateServiceRecord serviceRecordSwitch false");
            return ;
        }

        // 查询服务者工单
        AngelWorkDBQuery query = AngelWorkDBQuery.builder()
                .workIds(Collections.singletonList(Long.valueOf(event.getAggregateId())))
                .build();
        List<AngelWork> angelWorkList = angelWorkRepository.findList(query);
        if (CollectionUtils.isEmpty(angelWorkList)){
            log.info("workReceivedCreateServiceRecord angelWorkList empty");
            return;
        }
        AngelWork angelWork = angelWorkList.get(0);
        log.info("workReceivedCreateServiceRecord angelWork={}", JSON.toJSONString(angelWork));

        // 查询业务模式
        JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(angelWork.getVerticalCode());
        List<String> businessModeCodes = JSON.parseArray(obj.getString("businessModeCodes"), String.class);
        if (!businessModeCodes.contains(verticalBusiness.getBusinessModeCode())){
            log.info("workReceivedCreateServiceRecord businessModeCode no match");
            return;
        }

        // 查询服务者任务单
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelWork.getWorkId());
        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        log.info("workReceivedCreateServiceRecord angelTaskList={}", JSON.toJSONString(angelTaskList));
        if (CollectionUtils.isEmpty(angelTaskList)){
            return;
        }

        // 查询检测单
        List<String> promisePatientIdList = angelTaskList.stream().map(AngelTask::getPatientId).distinct().collect(Collectors.toList());
        List<Long> promisePatientIds = new ArrayList<>();
        for (String promisePatientId : promisePatientIdList) {
            promisePatientIds.add(Long.valueOf(promisePatientId));
        }
        MedicalPromiseListQuery medicalPromiseListQuery = MedicalPromiseListQuery.builder().promisePatientIdList(promisePatientIds).build();
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        log.info("workReceivedCreateServiceRecord medicalPromiseListQuery={}, medicalPromiseList={}", JSON.toJSONString(medicalPromiseListQuery), JSON.toJSONString(medicalPromiseList));
        Map<Long, List<MedicalPromise>> promisePatientMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));

        // 查询护理单
        AngelServiceRecordDBQuery angelServiceRecordDBQuery = AngelServiceRecordDBQuery.builder().promiseId(angelWork.getPromiseId()).build();
        List<AngelServiceRecord> angelServiceRecordList = angelServiceRecordRepository.findList(angelServiceRecordDBQuery);
        log.info("workReceivedCreateServiceRecord angelServiceRecordList={}", JSON.toJSONString(angelServiceRecordList));
        if (CollectionUtils.isNotEmpty(angelServiceRecordList)){
            // 取消护理单
            angelServiceRecordList.forEach(s->{
                AngelServiceRecord updateAngelServiceRecord = AngelServiceRecord.builder()
                        .id(s.getId())
                        .recordId(s.getRecordId())
                        .status(ServiceRecordStatusEnum.CANCEL.getStatus())
                        .build();
                angelServiceRecordRepository.save(updateAngelServiceRecord);
            });
        }

        // 创建新护理单
        for (AngelTask angelTask : angelTaskList) {
            List<MedicalPromise> medicalPromises = promisePatientMap.get(Long.parseLong(angelTask.getPatientId()));
            List<Long> serviceItemIdList = medicalPromises.stream().map(m->Long.parseLong(m.getServiceItemId())).distinct().collect(Collectors.toList());
            // 查询节点配置数据
            List<AngelServiceRecordQuestionGroupDto> questionGroupConfigList = angelServiceRecordApplication.queryQuestionGroupConfigList(serviceItemIdList
                    , verticalBusiness.getBusinessModeCode(), angelWork.getServiceType());
            if (CollectionUtils.isEmpty(questionGroupConfigList)){
                log.info("workReceivedCreateServiceRecord questionGroupConfigList empty serviceItemIdList={}", JSON.toJSONString(serviceItemIdList));
                continue;
            }
            AngelServiceRecord angelServiceRecordDB = AngelServiceRecord.builder()
                    .recordId(generateIdFactory.getId())
                    .verticalCode(angelWork.getVerticalCode())
                    .serviceType(angelWork.getServiceType())
                    .taskId(angelTask.getTaskId())
                    .workId(angelTask.getWorkId())
                    .promiseId(angelWork.getPromiseId())
                    .angelId(angelWork.getAngelId())
                    .serviceItemIdList(JSON.toJSONString(serviceItemIdList))
                    .status(ServiceRecordStatusEnum.INIT.getStatus())
                    .build();
            angelServiceRecordRepository.save(angelServiceRecordDB);
        }
    }


    /**
     * 护理单评估结果高风险
     * @param event
     */
    private void evaluateHighRiskProcessor(Event event){
        log.info("AngelServiceRecordEventConsumer evaluateHighRiskProcessor event={}", JSON.toJSONString(event));
        AngelServiceRecordEventBody recordEventBody = JSON.parseObject(event.getBody(), AngelServiceRecordEventBody.class);
        // 查询护理单
        AngelServiceRecord angelServiceRecord = angelServiceRecordRepository.find(AngelServiceRecordIdentifier.builder().recordId(recordEventBody.getRecordId()).build());
        log.info("AngelServiceRecordEventConsumer evaluateHighRiskProcessor angelServiceRecord={}", JSON.toJSONString(angelServiceRecord));
        if (!ServiceRecordStatusEnum.HIGH_RISK.getStatus().equals(angelServiceRecord.getStatus())){
            log.info("evaluateHighRiskProcessor no highRisk");
            return;
        }
        
        // 查询护理单节点
        AngelServiceRecordFlow angelServiceRecordFlow = angelServiceRecordFlowRepository.find(AngelServiceRecordFlowIdentifier.builder().recordFlowId(recordEventBody.getRecordFlowId()).build());
        log.info("AngelServiceRecordEventConsumer evaluateHighRiskProcessor angelServiceRecordFlow={}", JSON.toJSONString(angelServiceRecordFlow));

        // 查询服务者任务单
        AngelTask angelTask = angelTaskRepository.find(AngelTaskIdentifier.builder().taskId(angelServiceRecord.getTaskId()).build());

        // 查询服务者工单
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelTask.getWorkId()).build());

        // 查询履约单
        JdhPromise promise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(angelWork.getPromiseId()).build());

        JSONObject obj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
        List<String> onlyInvalidVoucherVerticals = JSON.parseArray(obj.getString("onlyInvalidVoucherVertical"), String.class);
        log.info("evaluateHighRiskProcessor onlyInvalidVoucherVerticals={}, verticalCode={}", JSON.toJSONString(onlyInvalidVoucherVerticals), promise.getVerticalCode());

        if (onlyInvalidVoucherVerticals.contains(promise.getVerticalCode())){
            InvalidVoucherCmd invalidVoucherCmd = InvalidVoucherCmd.builder()
                    .voucherId(promise.getVoucherId())
                    .invalidType(3)
                    .reason(recordEventBody.getRefundReason())
                    .build();
            // 结束服务者履约单
            voucherApplication.invalidVoucher(invalidVoucherCmd);
        }else {
            // 触发给C端客户退款（精准营养和B端门户的，护士评估高危不走退款，只作废工单即可）
            this.orderRefund(angelWork, angelTask, promise, recordEventBody.getRefundReason());
        }
    }

    /**
     * 触发给C端客户退款
     * @param angelWork
     * @param angelTask
     * @param promise
     * @param refundReason
     */
    private void orderRefund(AngelWork angelWork, AngelTask angelTask, JdhPromise promise, String refundReason) {
        log.info("AngelServiceRecordEventConsumer orderRefund angelWork={}, angelTask={}, promise={}, refundReason={}", JSON.toJSONString(angelWork)
                , JSON.toJSONString(angelTask), JSON.toJSONString(promise),refundReason );
        ManRefundTipsParam manRefundTipsParam = new ManRefundTipsParam();
        manRefundTipsParam.setPromiseId(angelWork.getPromiseId());
        List<ManRefundTipsOrderParam> orderParamList = new ArrayList<>();
        ManRefundTipsOrderParam manRefundTipsOrderParam = ManRefundTipsOrderParam.builder()
                .orderId(angelWork.getJdOrderId())
                .promisePatientId(Long.valueOf(angelTask.getPatientId()))
                .build();
        orderParamList.add(manRefundTipsOrderParam);
        manRefundTipsParam.setOrderList(orderParamList);
        // 查询运营端退款提示信息
        ManRefundTipsInfoDto manRefundTipsInfo = tradeApplication.queryManRefundTipsInfo(manRefundTipsParam);
        log.info("AngelServiceRecordEventConsumer orderRefund manRefundTipsParam={}, manRefundTipsInfo={}", JSON.toJSONString(manRefundTipsParam), JSON.toJSONString(manRefundTipsInfo));

        RefundOrderParam refundOrderParam = new RefundOrderParam();
        refundOrderParam.setOperator("护士");
        refundOrderParam.setOrderId(manRefundTipsInfo.getOrderId());
        refundOrderParam.setPromiseId(angelWork.getPromiseId());
        refundOrderParam.setRefundAmount(manRefundTipsInfo.getSugRefundAmount());

        List<RefundOrderSku> refundOrderSkuList = new ArrayList<>();
        promise.getServices().forEach(service->{
            RefundOrderSku refundOrderSku = new RefundOrderSku();
            refundOrderSku.setServiceId(String.valueOf(service.getServiceId()));
            refundOrderSku.setPromisePatientId(Long.valueOf(angelTask.getPatientId()));
            refundOrderSkuList.add(refundOrderSku);
        });
        refundOrderParam.setRefundOrderSkuList(refundOrderSkuList);
        refundOrderParam.setRefundReason(refundReason);
        refundOrderParam.setRefundReasonCode("99999904");// 护士评估未通过退款
        refundOrderParam.setRefundSource("2");
        refundOrderParam.setRefundType(3);
        refundOrderParam.setVoucherId(promise.getVoucherId());
        // 申请退款
        Boolean orderRefundResult = tradeApplication.xfylOrderRefund(refundOrderParam);
        log.info("AngelServiceRecordEventConsumer orderRefund refundOrderParam={}, orderRefundResult={}", JSON.toJSONString(refundOrderParam), JSON.toJSONString(orderRefundResult));
    }

}
