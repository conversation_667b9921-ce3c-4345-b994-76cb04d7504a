package com.jdh.o2oservice.application.angelpromise.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.UavConfig;
import com.jdh.o2oservice.base.enums.ShipRepeatReasonEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.support.rpc.AngelRealTrackRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelLocationBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipCallTransferFailBody;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipCreateFailBody;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskStatusCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelWorkStatusDbCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelWorkDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ShipTask;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseDeliveryStep;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelLocationRealParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.ship.enums.JdLogisticsEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.ship.enums.ShunFengEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.ship.enums.UavEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.ship.event.JdLogisticsAngelShipSupportEventBody;
import com.jdh.o2oservice.core.domain.support.ship.event.ShunFengAngelShipSupportEventBody;
import com.jdh.o2oservice.core.domain.support.ship.event.UavAngelShipSupportEventBody;
import com.jdh.o2oservice.core.domain.support.ship.model.JdLogisticsAngelShipSupport;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.export.angelpromise.cmd.AngelCheckBarCodeCmd;
import com.jdh.o2oservice.export.angelpromise.query.AngelCheckTaskBarCodeQuery;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchAngelDetail;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchCallbackCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseSubmitCmd;
import com.jdh.o2oservice.export.trade.query.RefundOrderKnightParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:AngelTaskEventConsumer
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/19 20:40
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelShipEventConsumer {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelWorkDomainService angelWorkDomainService;

    @Resource
    private AngelTaskDomainService angelTaskDomainService;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 修改派单状态application
     */
    @Resource
    private DispatchApplication dispatchApplication;

    @Resource
    private TradeApplication tradeApplication;

    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private AngelWorkApplication angelWorkApplication;

    /**
     * angelWorkStateMachine
     */
    @Resource
    private StateMachine<AngelPromiseStatus, AngelWorkEventTypeEnum, StateContext> angelWorkStateMachine;

    /**
     * angelWorkStateMachine
     */
    @Resource
    private StateMachine<AngelPromiseStatus, AngelTaskEventTypeEnum, StateContext> angelTaskStatemachine;

    @Resource
    private ReachApplication reachApplication;

    @Resource
    private AngelShipDomainService angelShipDomainService;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private AngelRealTrackRpc angelRealTrackRpc;

    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    @PostConstruct
    public void registerEventConsumer(){

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_WAIT_RECEIVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "waitReceived", this::handleShipWaitReceived, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_RECEIVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "shipReceived", this::handleShipReceived, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_STORE,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "reachStore", this::handleShipReachStore, Boolean.TRUE, Boolean.FALSE,
                        EventConsumerRetryTemplate.exponentialRetryInstance(10, 1000, 2.0, 60000)));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "sending", this::handleShipSending, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "shipFinish", this::handleShipFinish, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "shipCancel", this::handleShipCancel, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_ABNORMAL_DELIVERY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "abnormalDelivery", this::handleShipAbnormalDelivery, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CREATE_FAIL,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "createShipFail", this::createShipFail, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(ShunFengEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL,
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "shunFengShipCancel", this::shunFengCancelShip));

        eventConsumerRegister.register(ShunFengEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CREATE,
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "shunFengShipCreate", this::shunFengCreateShip));

        eventConsumerRegister.register(JdLogisticsEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CALL,
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "callJdLogistics", this::callJdLogistics));

        eventConsumerRegister.register(JdLogisticsEventTypeEnum.ANGEL_WORK_SHIP_EVENT_DELIVERY,
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "deliveryJdLogistics", this::deliveryJdLogistics));

        eventConsumerRegister.register(Lists.newArrayList(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY, AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH),
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "angelSettleByShip", this::angelSettleByShip));

        eventConsumerRegister.register(Lists.newArrayList(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_STEP_FINISH),
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "createNextShip", this::createNextShip));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_WAIT_RECEIVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "sendSms", reachApplication::submitTask, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(UavEventTypeEnum.ANGEL_SHIP_RECEIVE,
                WrapperEventConsumer.newDelayInstance(DomainEnum.ANGEL_PROMISE, "uavShipReceive", this::uavCreateShip));


        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_CREATE_FAIL,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "sendDongDong", this::sendDongDong, Boolean.TRUE, Boolean.FALSE));
    }

    /**
     * 通过运单状态给护士结算
     *
     * @param event
     */
    private void angelSettleByShip(Event event) {
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        angelWorkApplication.angelSettleByShip(angelWork);
    }

    /**
     * 创建运单失败订单退款
     *
     * @param event
     */
    private void createShipFail(Event event) {
        log.info("[AngelWorkEventConsumer.createShipFail],工单创建运单失败,退款订单开始,event={}", JSON.toJSONString(event));
        AngelShipCreateFailBody angelShipCreateFailBody = JSON.parseObject(event.getBody(), AngelShipCreateFailBody.class);

        //1、先同dispatch派单失败
        DispatchCallbackCmd cmd = new DispatchCallbackCmd();
        cmd.setDispatchId(angelShipCreateFailBody.getSourceId());
        cmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus());

        List<DispatchAngelDetail> angelDetailList = Lists.newArrayList();
        DispatchAngelDetail angelDetail = new DispatchAngelDetail();
        angelDetail.setDispatchDetailType(DispatchDetailTypeEnum.ASSIGN.getType());
        angelDetailList.add(angelDetail);

        cmd.setAngelDetailList(angelDetailList);
        cmd.setPromiseId(angelShipCreateFailBody.getPromiseId());
        dispatchApplication.callBack(cmd);

        //2、逆向交易订单
        RefundOrderParam param = AngelPromiseApplicationConverter.instance.convertToRefundOrderParam(angelShipCreateFailBody);
        try{
            if (StringUtils.isBlank(param.getRefundReasonCode())){
                param.setRefundReasonCode("99999905");// 创建运单失败订单退款
            }
            Boolean orderRefundBool = tradeApplication.xfylOrderRefund(param);
            if(!orderRefundBool) {
                throw new BusinessException(AngelPromiseBizErrorCode.TRADE_REFUND_ERROR);
            }
        }catch (Exception ex){
            log.error("[AngelWorkEventConsumer.createShipFail],运单退款失败!", ex);
            UmpUtil.showWarnMsg(UmpKeyEnum.ANGEL_WORK_TRADE_REFUND_FAIL_ERROR);
        }
    }

    /**
     * 处理运单接单
     * 1、调用派单已派单
     *
     * @param event
     */
    private void handleShipWaitReceived(Event event) {
        log.info("[AngelShipEventConsumer.handleShipWaitReceived],start! event={}", JSON.toJSONString(event));
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelShip.getWorkId()));
        if (Objects.isNull(angelWork)) {
            log.info("[AngelWorkEventConsumer.handleWorkReceive],非骑手工单不需要同步变更工单状态!workId={}", angelWork.getWorkId());
            return;
        }
        if(!Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            log.error("[AngelShipEventConsumer.handleShipWaitReceived],非骑手模式下不需要回调!");
            return;
        }

        DispatchCallbackCmd cmd = new DispatchCallbackCmd();
        cmd.setDispatchId(angelWork.getSourceId());
        cmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus());

        List<DispatchAngelDetail> angelDetailList = Lists.newArrayList();
        DispatchAngelDetail angelDetail = new DispatchAngelDetail();
        angelDetail.setAngelId(StringUtils.isNotBlank(angelWork.getAngelId()) ? Long.valueOf(angelWork.getAngelId()) : null);
        angelDetail.setAngelName(angelWork.getAngelName());
        angelDetail.setDispatchDetailType(DispatchDetailTypeEnum.ASSIGN.getType());
        angelDetailList.add(angelDetail);

        cmd.setAngelDetailList(angelDetailList);
        cmd.setPromiseId(angelWork.getPromiseId());
        dispatchApplication.callBack(cmd);
        log.info("[AngelShipEventConsumer.handleShipWaitReceived],end");
    }

    /**
     * 处理运单妥投异常
     *  1、骑手模式退款+时间触达
     *  2、护士模式短信触达
     *
     * @param event
     */
    private void handleShipAbnormalDelivery(Event event) {
        log.info("[AngelShipEventConsumer.handleShipSending],妥投异常!event={}", JSON.toJSONString(event));
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());

        //骑手模式调用交易域退款
        if(Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            log.info("[AngelShipEventConsumer.handleShipSending],妥投异常开始退款,开始退款!");
        }
    }

    /**
     * 处理运单配送中
     *  1、骑手模式下先推送服务完成事件，在推送配送中状态
     *  2、护士模式下推送配送中
     *
     * @param event
     */
    public void handleShipSending(Event event) {
        log.info("[AngelShipEventConsumer.handleShipSending],运单配送中!event={}", JSON.toJSONString(event));
        CallerInfo callerInfo = Profiler.registerInfo(UmpKeyEnum.SHIP_SENDING_STORE_UMP_REPORT.getUmpKey());
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        Set<Long> executeTaskList = getExecuteTaskList(event, angelWork);
        if(CollectionUtils.isEmpty(executeTaskList)){
            log.info("[AngelShipEventConsumer.handleShipSending],没有任务完成了配送!event={}", JSON.toJSONString(event));
            return;
        }
        if(Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            //检查当前状态是否可以继续执行
            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setTaskIds(Lists.newArrayList(executeTaskList));
            angelTaskDBQuery.setWorkId(angelWork.getWorkId());
            List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
            if(CollectionUtils.isEmpty(angelTaskList)) {
                log.error("[AngelShipEventConsumer.handleShipSending],服务任务单不存在!angelTaskDBQuery={}", JSON.toJSONString(angelTaskDBQuery));
                return;
            }
            for (AngelTask angelTask : angelTaskList) {
                AngelTaskStatusEnum enumByCode = AngelTaskStatusEnum.getEnumByCode(angelTask.getTaskStatus());
                if(!angelTaskStatemachine.verify(enumByCode, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DONE_SERVED)){
                    log.info("[AngelShipEventConsumer.handleShipSending],配送中状态不能执行!event={}", JSON.toJSONString(event));
                    return;
                }
            }
            //转单更新工单信息
            if(ShipRepeatReasonEnum.matchReasonType(angelShip.getRepeatType(), ShipRepeatReasonEnum.RE_TRANSFER)) {
                log.info("AngelShipEventConsumer -> handleShipSending,发生转单更新骑手信息!event={}", JSON.toJSONString(event));
                //将骑手的名称的信息同步到工单上
                angelWork.setAngelName(angelShip.getTransferName());
                angelWork.setAngelId(angelShip.getTransferId());
                angelWork.setAngelPhone(angelShip.getTransferPhone());
                angelWork.setAngelPhoneIndex(angelShip.getTransferPhone());
                angelWorkRepository.save(angelWork);
            }
            List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
            executeTaskList.stream().forEach(item -> {
                AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                        .taskId(item)
                        .taskStatus(AngelTaskStatusEnum.SERVICED.getType())
                        .build();
                angelTaskStateBoList.add(angelTaskStateBo);
            });
            AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder().build();
            angelTaskStatusContext.setWorkId(angelShip.getWorkId());
            angelTaskStatusContext.setAngelTaskStateBoList(angelTaskStateBoList);
            angelTaskStatusContext.setAngelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DONE_SERVED);
            angelTaskDomainService.executeTask(angelTaskStatusContext);
        }else if(Objects.equals(AngelWorkTypeEnum.NURSE.getType(), angelWork.getWorkType())) {
            //检查检测单有没有同步实验室，如果没有同步实验室同步到实验室
            checkAndSubmitSample(angelShip, angelWork);
        }
        Profiler.registerInfoEnd(callerInfo);
    }

    /**
     * 处理运单到店
     *
     * @param event
     */
    public void handleShipReachStore(Event event) {
        log.info("[AngelShipEventConsumer.handleShipReachStore],已到店!event={}", JSON.toJSONString(event));
        CallerInfo callerInfo = Profiler.registerInfo(UmpKeyEnum.SHIP_REACH_STORE_UMP_REPORT.getUmpKey());
        UmpUtil.showWarnMsg(UmpKeyEnum.SHIP_CANCEL_UMP_REPORT);
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        if(!Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            log.error("[AngelShipEventConsumer.handleShipReachStore],非骑手模式下不需要触达工单!");
            return;
        }
        Set<Long> executeTaskList = getExecuteTaskList(event, angelWork);
        if(CollectionUtils.isEmpty(executeTaskList)){
            log.info("[AngelShipEventConsumer.handleShipReachStore],没有任务完成了配送!event={}", JSON.toJSONString(event));
            return;
        }

        //检查当前状态是否可以继续执行
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setTaskIds(Lists.newArrayList(executeTaskList));
        angelTaskDBQuery.setWorkId(angelWork.getWorkId());
        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        if(CollectionUtils.isEmpty(angelTaskList)) {
            log.error("[AngelShipEventConsumer.handleShipReachStore],服务任务单不存在!angelTaskDBQuery={}", JSON.toJSONString(angelTaskDBQuery));
            return;
        }
        for (AngelTask angelTask : angelTaskList) {
            AngelTaskStatusEnum enumByCode = AngelTaskStatusEnum.getEnumByCode(angelTask.getTaskStatus());
            if(!angelTaskStatemachine.verify(enumByCode, AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_IN_SERVED)){
                log.info("[AngelShipEventConsumer.handleShipReachStore],已到店状态不能执行!event={}", JSON.toJSONString(event));
                return;
            }
        }

        List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
        executeTaskList.stream().forEach(item -> {
            AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                    .taskId(item)
                    .taskStatus(AngelTaskStatusEnum.SERVICING.getType())
                    .build();
            angelTaskStateBoList.add(angelTaskStateBo);
        });
        AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder().build();
        angelTaskStatusContext.setWorkId(angelShip.getWorkId());
        angelTaskStatusContext.setAngelTaskStateBoList(angelTaskStateBoList);
        angelTaskStatusContext.setAngelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_IN_SERVED);
        angelTaskDomainService.executeTask(angelTaskStatusContext);

        if(ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equals(angelWork.getVerticalCode())
                && ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getServiceType().equals(angelWork.getServiceType())){
            //模拟京东物流发送 配送中 事件
            JdLogisticsAngelShipSupport jdLogisticsAngelShipSupport = JdLogisticsAngelShipSupport.builder().outShipId(angelShip.getOutShipId()).build();

            JdLogisticsAngelShipSupportEventBody jdLogisticsAngelShipSupportEventBody = new JdLogisticsAngelShipSupportEventBody();
            jdLogisticsAngelShipSupportEventBody.setOrderStatus(-190);
            jdLogisticsAngelShipSupportEventBody.setWaybillCode(angelShip.getOutShipId());

            Event publishEvent = EventFactory.newDelayEvent(jdLogisticsAngelShipSupport, JdLogisticsEventTypeEnum.ANGEL_WORK_SHIP_EVENT_DELIVERY, jdLogisticsAngelShipSupportEventBody,10L);
            eventCoordinator.publish(publishEvent);
        }
        Profiler.registerInfoEnd(callerInfo);
    }

    /**
     * 处理运单取消
     * 1、骑手订单退款逆向
     * @param event
     */
    public void handleShipCancel(Event event) {
        log.info("[AngelShipEventConsumer.handleShipCancel],运单取消!event={}", JSON.toJSONString(event));
        CallerInfo callerInfo = Profiler.registerInfo(UmpKeyEnum.SHIP_CANCEL_UMP_REPORT.getUmpKey());
        AngelShipEventBody angelShipEventBody = JSON.parseObject(event.getBody(), AngelShipEventBody.class);

        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());

        //骑手模式调用交易域退款
        if(!Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            if(DeliveryTypeEnum.SHANSONG_DELIVERY.getType().equals(angelShip.getType())){
                log.info("[AngelShipEventConsumer.handleShipCancel],闪送不需要处理库存!angelShip={}", JSON.toJSONString(angelShip));
                if(Objects.nonNull(angelShipEventBody.getCancelFrom()) && CommonConstant.TEN == angelShipEventBody.getCancelFrom()) {
                    log.info("[AngelShipEventConsumer.handleShipCancel],推送超时取消事件!angelShipEventBody={}", JSON.toJSONString(angelShipEventBody));
                    eventCoordinator.publish(EventFactory.newDefaultEvent(angelShip, AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_TIMEOUT_CANCEL, null));
                }
                return;
            }
            return;
        }
        //处理工单和任务单取消冻结中
        Set<Long> executeTaskList = getExecuteTaskList(event, angelWork);
        if(CollectionUtils.isNotEmpty(executeTaskList)){
            List<AngelTaskStatusCmd> taskStatusCmdList = Lists.newArrayList();
            log.info("[AngelShipEventConsumer.handleShipFinish],executeTaskList={}", JSON.toJSONString(executeTaskList));
            executeTaskList.forEach(taskId -> {
                AngelTaskStatusCmd statusCmd = new AngelTaskStatusCmd();
                statusCmd.setTaskId(taskId);
                statusCmd.setStopStatus(AngelTaskStopStatusEnum.CANCEL_STOP.getStatus());
                statusCmd.setOperator(angelShipEventBody.getAngelName());
                taskStatusCmdList.add(statusCmd);
            });
            angelTaskRepository.updateStatus(taskStatusCmdList);

            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setWorkId(angelWork.getWorkId());
            List<AngelTask> angelTaskList1 = angelTaskRepository.findList(angelTaskDBQuery);
            List<AngelTask> angelTasks = angelTaskList1.stream()
                    .filter(task -> !executeTaskList.contains(task.getTaskId()))
                    .filter(task -> Objects.equals(AngelWorkStopStatusEnum.INIT.getStatus(), task.getStopStatus()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(angelTasks)){
                AngelWorkStatusDbCmd angelWorkStatusDbCmd = AngelWorkStatusDbCmd.builder()
                        .workStopStatus(AngelWorkStopStatusEnum.CANCEL_STOP.getStatus())
                        .workId(angelWork.getWorkId())
                        .build();
                angelWorkRepository.updateAngelWorkStatus(angelWorkStatusDbCmd);
            }
        }

        //调用交易域订单逆向
        if(AngelShipCancelCodeStatusEnum.findCancelStatusEnum(angelShip.getStandCancelCode()).getIsRefund()){
            RefundOrderKnightParam param = new RefundOrderKnightParam();
            param.setOrderId(angelWork.getJdOrderId());
            param.setPromiseId(angelWork.getPromiseId());
            param.setOperator(angelShipEventBody.getAngelName());
            param.setRefundType(RefundTypeEnum.ORDER_REFUND.getType());
            param.setRefundReason(angelShipEventBody.getReason());
            param.setRefundSource("3");
            try{
                if (StringUtils.isBlank(param.getRefundReasonCode())){
                    param.setRefundReasonCode("99999906");// 骑手订单退款逆向
                }
                Boolean orderRefundBool = tradeApplication.xfylOrderRefund2Knight(param);
                if(!orderRefundBool) {
                    throw new BusinessException(AngelPromiseBizErrorCode.TRADE_REFUND_ERROR);
                }
            }catch (Exception ex){
                log.error("[AngelWorkEventConsumer.createShipFail],运单退款失败!", ex);
                UmpUtil.showWarnMsg(UmpKeyEnum.ANGEL_WORK_SHIP_TRADE_REFUND_FAIL_ERROR);
            }
        }
        Profiler.registerInfoEnd(callerInfo);
    }

    /**
     * 处理运单完成
     * 1、运单完成推动工单完成
     *
     * @param event
     */
    public void handleShipFinish(Event event) {
        log.info("[AngelShipEventConsumer.handleShipFinish],运单配送完成!");
        CallerInfo callerInfo = Profiler.registerInfo(UmpKeyEnum.SHIP_FINISH_UMP_REPORT.getUmpKey());
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        Set<Long> executeTaskList = getExecuteTaskList(event, angelWork);
        if(CollectionUtils.isEmpty(executeTaskList)){
            log.info("[AngelShipEventConsumer.handleShipFinish],没有任务完成了配送!event={}", JSON.toJSONString(event));
            return;
        }
        if(Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
            executeTaskList.stream().forEach(item -> {
                AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                        .taskId(item)
                        .taskStatus(AngelTaskStatusEnum.COMPLETED.getType())
                        .build();
                angelTaskStateBoList.add(angelTaskStateBo);
            });
            AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder().build();
            angelTaskStatusContext.setWorkId(angelShip.getWorkId());
            angelTaskStatusContext.setAngelTaskStateBoList(angelTaskStateBoList);
            angelTaskStatusContext.setAngelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_FINISH_SERVED);
            angelTaskDomainService.executeTask(angelTaskStatusContext);
        } else {
            log.error("[AngelShipEventConsumer.handleShipFinish],运单配送中，业务模式不存在!angelWork={}", JSON.toJSONString(angelWork));
        }
        Profiler.registerInfoEnd(callerInfo);
    }

    /**
     * 处理运单接单
     * 1、更新任务状态到已服务
     *
     * @param event
     */
    public void handleShipReceived(Event event) {
        log.info("[AngelShipEventConsumer.handleShipReceived],运单已接单!event={}", JSON.toJSONString(event));
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        if(Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            if(executeAngelWork(event, AngelWorkStatusEnum.RECEIVED, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED)) {
                log.error("[AngelShipEventConsumer.handleShipReceived],运单接单处理成功!event={}", JSON.toJSONString(event));
            }else {
                log.error("[AngelShipEventConsumer.handleShipReceived],运单接单处理失败!event={}", JSON.toJSONString(event));
            }
            //将骑手的名称的信息同步到工单上
            angelWork.setAngelName(angelShip.getTransferName());
            angelWork.setAngelId(angelShip.getTransferId());
            angelWork.setAngelPhone(angelShip.getTransferPhone());
            angelWork.setAngelPhoneIndex(angelShip.getTransferPhone());
            angelWorkRepository.save(angelWork);
        }
    }

    /**
     * 获取运单
     *
     * @param event
     * @return
     */
    public AngelShip getAngelShip(Event event){
        String shipId = event.getAggregateId();
        if(StringUtils.isBlank(shipId)){
            log.error("[AngelShipEventConsumer.handleShipFinish],运单完成状态无聚合根id信息!");
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }
        AngelShip angelShip = angelShipRepository.find(AngelShipIdentifier.builder().shipId(Long.valueOf(shipId)).build());
        if(Objects.isNull(angelShip)){
            log.error("[AngelShipEventConsumer.handleShipFinish],运单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }

        return angelShip;
    }

    /**
     * 执行工单
     *
     * @param event
     * @param angelWorkStatusEnum
     * @param angelWorkEventTypeEnum
     * @return
     */
    private boolean executeAngelWork(Event event, AngelWorkStatusEnum angelWorkStatusEnum, AngelWorkEventTypeEnum angelWorkEventTypeEnum){
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        if(!Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())){
            log.error("[AngelShipEventConsumer.executeAngelWork],非骑手模式下不需要触达工单!");
            return true;
        }
        AngelWorkStatusEnum currStatusEnum = AngelWorkStatusEnum.getEnumByCode(angelWork.getWorkStatus());
        if(!angelWorkStateMachine.verify(currStatusEnum, angelWorkEventTypeEnum)){
            log.error("[AngelShipEventConsumer.executeAngelWork],该状态无法执行对应事件!angelWork={}", JSON.toJSONString(angelWork));
            return false;
        }

        //查询服务者位置信息
        AngelLocationRealParam angelLocationRealParam = new AngelLocationRealParam();
        angelLocationRealParam.setAngelId(angelWork.getAngelId());
        angelLocationRealParam.setWorkId(angelWork.getWorkId());
        angelLocationRealParam.setPromiseId(angelWork.getPromiseId());
        AngelLocationBo angelLocationBo = angelRealTrackRpc.queryAngelLocationRealTrack(angelLocationRealParam);

        AngelWorkStatusContext angelWorkStatusContext = AngelWorkStatusContext.builder()
                .angelLocationBo(angelLocationBo)
                .workId(angelShip.getWorkId())
                .eventCode(angelWorkEventTypeEnum.getCode())
                .workStatus(angelWorkStatusEnum.getType())
                .build();
        return angelWorkDomainService.executeWork(angelWorkStatusContext);
    }


    /**
     * 获取要执行的任务id
     *
     * @param event
     * @return
     */
    private Set<Long> getExecuteTaskList(Event event, AngelWork angelWork){
        log.info("[AngelTaskExtEventConsumer.handleTaskInServed],任务扩展状态执行事件开始!event={}", JSON.toJSONString(event));
        Set<Long> resultAngelTask = Sets.newHashSet();
        String aggregateId = event.getAggregateId();
        if(StringUtils.isBlank(aggregateId)){
            log.error("[AngelShipEventConsumer.executeTask],任务单扩展状态聚合根id为空!event={}", JSON.toJSONString(event));
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }
        AngelShip angelShip = angelShipRepository.find(new AngelShipIdentifier(Long.valueOf(aggregateId)));
        if(Objects.isNull(angelShip)){
            log.error("[AngelShipEventConsumer.executeTask],未查询到运单!event={}", JSON.toJSONString(event));
            return resultAngelTask;
        }

        Set<Long> taskIdSet = angelShip.getJdhAngelShipExtVo().getShipTaskList().stream().map(ShipTask::getTaskId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(taskIdSet)) {
            log.error("[AngelShipEventConsumer.executeTask],运单任务为空!event={}", JSON.toJSONString(event));
            return resultAngelTask;
        }
        return checkTaskExtStatus(taskIdSet, angelShip, angelWork);
    }

    /**
     * 检查任务扩展状态
     *
     * @param executeTaskList
     * @param angelShip
     * @param angelWork
     * @return
     */
    private Set<Long> checkTaskExtStatus(Set<Long> executeTaskList, AngelShip angelShip, AngelWork angelWork) {
        Set<Long> executeList = Sets.newHashSet();
        //运单的状态排序
        List<Integer> statusList = Lists.newArrayList(0 ,1, 2, 100, 3, 4, 5, 9, 10, 1000);
        for (Long taskId : executeTaskList) {
            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setTaskIds(Lists.newArrayList(taskId));
            angelTaskDBQuery.setWorkId(angelShip.getWorkId());
            angelTaskDBQuery.setTaskStatusSet(Sets.newHashSet(AngelTaskStatusEnum.getValidStatus()));
            List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
            if(CollectionUtils.isEmpty(angelTaskList)) {
                log.error("[AngelShipEventConsumer -> checkTaskExtStatus],没有查询任务单!angelShip={}", JSON.toJSONString(angelShip));
                continue;
            }

            List<Long> patientIds = angelTaskList.stream().map(task -> Long.valueOf(task.getPatientId())).collect(Collectors.toList());
            MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
            medicalPromiseListQuery.setPromiseId(angelWork.getPromiseId());
            medicalPromiseListQuery.setPromisePatientIdList(Lists.newArrayList(patientIds));
            medicalPromiseListQuery.setInvalid(false);
            medicalPromiseListQuery.setFreezeQuery(false);
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
            if(CollectionUtils.isEmpty(medicalPromises)) {
                log.error("[AngelShipEventConsumer -> checkTaskExtStatus],没有查询到检测单!angelShip={}", JSON.toJSONString(angelShip));
                continue;
            }

            Set<String> stationIdSet = medicalPromises.stream().map(MedicalPromise::getStationId).collect(Collectors.toSet());
            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
            angelShipDBQuery.setStatus(Sets.newHashSet(AngelShipStatusEnum.getValidStatus()));
            angelShipDBQuery.setWorkId(angelWork.getWorkId());
            angelShipDBQuery.setReceiverIds(stationIdSet);
            List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
            if(CollectionUtils.isEmpty(angelShipList) || angelShipList.size() < stationIdSet.size()) {
                log.error("[AngelShipEventConsumer -> checkTaskExtStatus],没有查询到运单!angelShip={}", JSON.toJSONString(angelShip));
                continue;
            }

            int currIndex = statusList.indexOf(angelShip.getShipStatus());
            boolean statusBool = angelShipList.stream().allMatch(item -> {
                int compareIndex = statusList.indexOf(item.getShipStatus());
                return currIndex <= compareIndex;
            });
            if(statusBool) {
                executeList.add(taskId);
            }
        }
        return executeList;
    }

    /**
     * 检查并提交样本
     *
     * @param angelShip
     * @param angelWork
     */
    private void checkAndSubmitSample(AngelShip angelShip, AngelWork angelWork) {
        log.info("AngelShipEventConsumer -> checkAndSubmitSample, angelShip={}, angelWork={}", JSON.toJSONString(angelShip), JSON.toJSONString(angelWork));
        //如果工单已经服务完成结束一下逻辑
        if(!AngelWorkStatusEnum.SERVICING.getType().equals(angelWork.getWorkStatus())){
            log.info("AngelShipEventConsumer -> checkAndSubmitSample,工单状态不能执行提交检测码,终止执行");
            return;
        }
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(angelWork.getWorkId());
        angelShipDBQuery.setStatus(Sets.newHashSet(AngelShipStatusEnum.getValidStatus()));
        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
        List<AngelShip> deliveryShipList = list.stream().filter(item -> AngelShipStatusEnum.getSendingStatus().contains(item.getShipStatus())).collect(Collectors.toList());
        if(list.size() != deliveryShipList.size()) {
            submitMedicalPromise(angelShip, angelWork);
        }else {
            MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
            medicalPromiseListQuery.setPromiseId(angelWork.getPromiseId());
            medicalPromiseListQuery.setFreezeQuery(Boolean.FALSE);
            medicalPromiseListQuery.setInvalid(Boolean.FALSE);
            medicalPromiseListQuery.setAllQuery(Boolean.FALSE);
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
            Map<String, List<MedicalPromise>> stationMap = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getStationId));
            if(stationMap.size() == deliveryShipList.size()) {
                //模拟护士提交检测单
                List<AngelCheckTaskBarCodeQuery> taskBar = Lists.newArrayList();
                List<ShipTask> shipTaskList = angelShip.getJdhAngelShipExtVo().getShipTaskList();
                shipTaskList.forEach(item -> {
                    AngelCheckTaskBarCodeQuery codeQuery = new AngelCheckTaskBarCodeQuery();
                    codeQuery.setTaskId(String.valueOf(item.getTaskId()));
                    taskBar.add(codeQuery);
                });
                AngelCheckBarCodeCmd angelCheckBarCodeCmd = new AngelCheckBarCodeCmd();
                angelCheckBarCodeCmd.setWorkId(angelWork.getWorkId());
                angelCheckBarCodeCmd.setTaskBar(taskBar);
                angelCheckBarCodeCmd.setUserPin(angelWork.getAngelPin());
                angelWorkApplication.submitBindBarCode(angelCheckBarCodeCmd);
            }else {
                submitMedicalPromise(angelShip, angelWork);
            }
        }
    }

    private void submitMedicalPromise(AngelShip angelShip, AngelWork angelWork) {
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(angelWork.getPromiseId());
        medicalPromiseListQuery.setFreezeQuery(Boolean.FALSE);
        medicalPromiseListQuery.setInvalid(Boolean.FALSE);
        medicalPromiseListQuery.setAllQuery(Boolean.FALSE);
        medicalPromiseListQuery.setStationId(angelShip.getReceiverId());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        if(CollectionUtils.isEmpty(medicalPromises)) {
            log.error("AngelShipEventConsumer -> checkAndSubmitSample,没有查询到检测单信息,medicalPromiseListQuery={}", JSON.toJSONString(medicalPromiseListQuery));
            return;
        }
        List<MedicalPromise> medicalPromiseList = medicalPromises.stream().filter(item -> MedicalPromiseStatusEnum.needChangeBindStatus(item.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(medicalPromiseList)) {
            log.error("AngelShipEventConsumer -> checkAndSubmitSample,没有需要提交的检测单!", JSON.toJSONString(medicalPromiseListQuery));
            return;
        }
        medicalPromiseList.stream()
                .filter(item -> item.getStatus() < MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus())
                .forEach(med -> {
                    MedicalPromiseSubmitCmd medicalPromiseSubmitCmd = new MedicalPromiseSubmitCmd();
                    medicalPromiseSubmitCmd.setMedicalPromiseId(med.getMedicalPromiseId());
                    medicalPromiseApplication.submitMedicalPromiseToStation(medicalPromiseSubmitCmd);
                });
    }

    /**
     * 顺丰取消运单事件
     */
    public void shunFengCancelShip(Event event){
        log.info("[AngelShipEventConsumer.shunFengCancelShip],运单取消!event={}", JSON.toJSONString(event));
        CallerInfo callerInfo = Profiler.registerInfo(UmpKeyEnum.SHUN_FENG_SHIP_CANCEL_UMP_REPORT.getUmpKey());
        ShunFengAngelShipSupportEventBody shunFengAngelShipSupportEventBody = JSON.parseObject(event.getBody(), ShunFengAngelShipSupportEventBody.class);

        AngelShipCallBackContext angelShipCallBackContext = toAngelShipCallBackContext(shunFengAngelShipSupportEventBody);
        angelWorkApplication.shipStatusCallback(angelShipCallBackContext);

        Profiler.registerInfoEnd(callerInfo);
    }

    /**
     * 顺丰创建运单事件
     */
    public void shunFengCreateShip(Event event){
        log.info("[AngelShipEventConsumer.shunFengCreateShip],运单取消!event={}", JSON.toJSONString(event));
        CallerInfo callerInfo = Profiler.registerInfo(UmpKeyEnum.SHUN_FENG_SHIP_CREATE_UMP_REPORT.getUmpKey());
        ShunFengAngelShipSupportEventBody shunFengAngelShipSupportEventBody = JSON.parseObject(event.getBody(), ShunFengAngelShipSupportEventBody.class);

        AngelShipCallBackContext angelShipCallBackContext = toAngelShipCreateCallBackContext(shunFengAngelShipSupportEventBody);
        angelWorkApplication.shipStatusCallback(angelShipCallBackContext);

        Profiler.registerInfoEnd(callerInfo);
    }

    @LogAndAlarm
    public void uavCreateShip(Event event){
        log.info("[AngelShipEventConsumer.uavCreateShip],模拟无人机接单回调!event={}", JSON.toJSONString(event));
        UavAngelShipSupportEventBody uavAngelShipSupportEventBody = JSON.parseObject(event.getBody(), UavAngelShipSupportEventBody.class);
        AngelShipCallBackContext angelShipCallBackContext = toAngelShipCreateCallBackContext(uavAngelShipSupportEventBody);
        angelShipCallBackContext.setOrderId(Long.parseLong(event.getAggregateId()));
        angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
    }

    /**
     * 模拟回调-京东物流-配送
     * @param event
     */
    public void deliveryJdLogistics(Event event){
        log.info("[AngelShipEventConsumer.deliveryJdLogistics],配送中!event={}", JSON.toJSONString(event));
        JdLogisticsAngelShipSupportEventBody jdLogisticsAngelShipSupportEventBody = JSON.parseObject(event.getBody(), JdLogisticsAngelShipSupportEventBody.class);

        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();

        angelShipCallBackContext.setOrderStatus(3);
        angelShipCallBackContext.setClientId(jdLogisticsAngelShipSupportEventBody.getWaybillCode());
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date()));
        angelShipCallBackContext.setDeliveryType(DeliveryTypeEnum.JD_LOGISTICS_DELIVERY.getType());
        angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
    }


    /**
     * 模拟回调-京东物流-配送中
     * @param event
     */
    public void callJdLogistics(Event event){
        log.info("[AngelShipEventConsumer.callJdLogistics],呼叫运力!event={}", JSON.toJSONString(event));
        JdLogisticsAngelShipSupportEventBody jdLogisticsAngelShipSupportEventBody = JSON.parseObject(event.getBody(), JdLogisticsAngelShipSupportEventBody.class);

        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();

        angelShipCallBackContext.setOrderStatus(1);
        angelShipCallBackContext.setClientId(jdLogisticsAngelShipSupportEventBody.getWaybillCode());
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date()));
        angelShipCallBackContext.setDeliveryType(DeliveryTypeEnum.JD_LOGISTICS_DELIVERY.getType());
        angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
    }

    /**
     * 顺丰入参对象转成内部对象
     * @return
     */
    private AngelShipCallBackContext toAngelShipCallBackContext(ShunFengAngelShipSupportEventBody shunFengAngelShipSupportEventBody){
        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();

        angelShipCallBackContext.setOrderStatus(5);
        angelShipCallBackContext.setOrderId(Long.parseLong(shunFengAngelShipSupportEventBody.getOrderId()));
        angelShipCallBackContext.setDmId("0");
        angelShipCallBackContext.setDmName("系统");
        angelShipCallBackContext.setDmMobile("18510654114");
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date()));
        return angelShipCallBackContext;
    }

    /**
     * 顺丰入参对象转成内部对象
     * @return
     */
    private AngelShipCallBackContext toAngelShipCreateCallBackContext(ShunFengAngelShipSupportEventBody shunFengAngelShipSupportEventBody){
        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();

        angelShipCallBackContext.setOrderStatus(1);
        angelShipCallBackContext.setOrderId(Long.parseLong(shunFengAngelShipSupportEventBody.getOrderId()));
        angelShipCallBackContext.setDmId("0");
        angelShipCallBackContext.setDmName("系统");
        angelShipCallBackContext.setDmMobile("18510654114");
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date()));
        return angelShipCallBackContext;
    }

    private AngelShipCallBackContext toAngelShipCreateCallBackContext(UavAngelShipSupportEventBody uavAngelShipSupportEventBody){
        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();
        angelShipCallBackContext.setOrderStatus(2);
        angelShipCallBackContext.setDmId("0");
        angelShipCallBackContext.setDmName(uavAngelShipSupportEventBody.getConnectName());
        angelShipCallBackContext.setDmMobile(uavAngelShipSupportEventBody.getConnectPhone());
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date()));
        return angelShipCallBackContext;
    }

    /**
     * 判断是否创建下一个运单
     * @param event
     */
    private void createNextShip(Event event){
        log.info("[AngelShipEventConsumer.createNextShip],判断是否自动呼叫下一段运力!event={}", JSON.toJSONString(event));
        Long eventId = Long.parseLong(event.getAggregateId());
        AngelShip angelShip = angelShipRepository.find(AngelShipIdentifier.builder().shipId(eventId).build());
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());

        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(angelWork.getPromiseId()).stationId(angelShip.getReceiverId()).build());
        List<MedPromiseDeliveryStep> medPromiseDeliverySteps = JSON.parseArray(medicalPromises.get(0).getDeliveryStepFlow(),MedPromiseDeliveryStep.class);
        log.info("createNextShip medPromiseDeliverySteps={}",JSON.toJSONString(medPromiseDeliverySteps));
        boolean createUavShip = false;
        if(CollectionUtils.isNotEmpty(medPromiseDeliverySteps)&&medPromiseDeliverySteps.size()>1){
            if(DeliveryTypeEnum.RIDER_DELIVERY.getType().equals(angelShip.getType())){
                //创建无人机运单
                createUavShip = true;
            }
        }
        if(createUavShip){
            //创建无人机运单
            AngelWorkShipCreateContext angelWorkShipCreateContext = this.toAngelWorkShipCreateContext(angelWork,angelShip,medPromiseDeliverySteps);
            angelShipDomainService.createAngelShip(angelWorkShipCreateContext);
        }else{
            //兼容之前老逻辑
            AngelShipEventBody angelShipEventBody = JSON.parseObject(event.getBody(),AngelShipEventBody.class);
            //发送运单全部完成事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(angelShip, AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH, angelShipEventBody));
        }
    }

    /**
     * 生成AngelWorkShipCreateContext
     */
    AngelWorkShipCreateContext toAngelWorkShipCreateContext(AngelWork angelWork,AngelShip angelShip,List<MedPromiseDeliveryStep> medPromiseDeliveryStep){

        AngelWorkShipCreateContext shipCreateContext = new AngelWorkShipCreateContext();
        shipCreateContext.setVerticalCode(angelWork.getVerticalCode());
        shipCreateContext.setServiceType(angelWork.getServiceType());
        shipCreateContext.setWorkId(angelWork.getWorkId());
        shipCreateContext.setShopNo(angelShip.getReceiverId());
        shipCreateContext.setProviderShopNo(angelShip.getReceiverId());
        shipCreateContext.setIsPrepay(CommonConstant.ZERO);
        shipCreateContext.setReceiverAddress(medPromiseDeliveryStep.get(1).getEndAddress());
        UavConfig.UavFlightInfo uavFlightInfo = duccConfig.getUavConfig().getUavFlightInfoByFlightId(medPromiseDeliveryStep.get(1).getThirdStationId(),medPromiseDeliveryStep.get(1).getThirdStationTargetId());
        shipCreateContext.setReceiverName(uavFlightInfo.getReceiverName());
        shipCreateContext.setReceiverPhone(uavFlightInfo.getReceiverPhone());


        shipCreateContext.setSupplierAddress(medPromiseDeliveryStep.get(1).getStartAddress());
        shipCreateContext.setSupplierPhone(angelShip.getTransferPhone());
        shipCreateContext.setSupplierName(angelShip.getTransferName());

        shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.getTypeByDelivery(DeliveryTypeEnum.UAV.getType()));
        shipCreateContext.setAngelType(AngelTypeEnum.DELIVERY.getType());

        shipCreateContext.setStartAirportId(medPromiseDeliveryStep.get(1).getThirdStationId());
        shipCreateContext.setEndAirportId(medPromiseDeliveryStep.get(1).getThirdStationTargetId());

        shipCreateContext.setParentShipId(angelShip.getShipId());
        shipCreateContext.setShipTaskList(angelShip.getJdhAngelShipExtVo().getShipTaskList());
        shipCreateContext.setDeliveryType(DeliveryTypeEnum.UAV.getType());

        shipCreateContext.setAngelWork(angelWork);
        shipCreateContext.setAngelStationId(angelShip.getJdhAngelShipExtVo().getAngelStationId());
        shipCreateContext.setMedicalPromiseIds(angelShip.getJdhAngelShipExtVo().getMedicalPromiseIds());

        return shipCreateContext;
    }

    /**
     * 发送咚咚
     * @param event
     */
    public void sendDongDong(Event event){
        log.info("[AngelShipEventConsumer.sendDongDong],!event={}", JSON.toJSONString(event));
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("运单创建失败");

        AngelShipCallTransferFailBody eventBody = JSON.parseObject(event.getBody(), AngelShipCallTransferFailBody.class);
        dongDongRobotRpc.sendDongDongRobotMessage(String.format("运单创建失败，运单ID：%s,异常信息: %s ,traceId: %s,请关注",
                        event.getAggregateId(),eventBody.getErrorData(),eventBody.getTraceId()),
                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
    }

}
