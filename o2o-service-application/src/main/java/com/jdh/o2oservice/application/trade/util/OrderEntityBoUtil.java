package com.jdh.o2oservice.application.trade.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.jd.jmq.common.message.Message;
import com.jd.purchase.domain.old.bean.*;
import com.jd.purchase.order.msg.domain.PurchaseOrderMsg;
import com.jd.purchase.utils.serializer.dict.SerializeType;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.base.enums.CouponAmountTypeEnum;
import com.jdh.o2oservice.base.enums.InvoiceContentEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.enums.SkuTagsEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.trade.bo.DiscountAmountDetailBO;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import com.jdh.o2oservice.core.domain.trade.bo.SkuInfoBO;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.VenderShopRpc;
import com.jdh.o2oservice.core.domain.trade.vo.RpcVenderShopVO;
import com.jdh.o2oservice.core.domain.trade.vo.SkuFeaturesVo;
import com.jdh.o2oservice.infrastructure.rpc.convert.OrderInfoConverter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;

/**
 * OFC下发pop订单MQ解析
 *
 * <AUTHOR>
 * @date 2022-11-16 14:07
 */
public class OrderEntityBoUtil {

    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(OrderEntityBoUtil.class);

    /**
     * BigDecimal=100
     */
    private static final BigDecimal BIGDECIMAL_100 = new BigDecimal(100);

    public static final String BBC_OUTER_CHANNEL_ID = "bbcShopId";

    public static final String BBC_OUTER_USER_ID = "bbcThirdUserId";

    /**
     * 默认的商家ID
     */
    private static final String DEFAULT_SHOP_ID = "0";

    private static final String DEFAULT_SHOP_NAME = "京东";

    /**
     * 商品扩展属性shopId
     */
    private static final String SHOP_ID_KEY = "shopId";

    private static final String SHOP_NAME_KEY = "shop_name";

    // 解密数据信息
    public static String getDecryptEncInfo(String enc) {
        return decryptEncStr(enc);
    }



    /**
     * 获取订单基础信息BO
     * @param order
     * @return
     */
    private static OrderEntityBO getBaseOrderInfoBo(Order order){
        // 订单基础信息
        OrderEntityBO orderEntityBO = getOrderBaseInfo(order);
        // 订单客户加密信息
        getDecryptEncInfo(order,orderEntityBO);
        // 订单发票信息
        getOrderInvoiceType(order,orderEntityBO);
        return orderEntityBO;
    }

    /**
     *
     * @param order
     * @return
     */
    private static OrderEntityBO getOrderBaseInfo(Order order){
        OrderEntityBO orderEntityBO = new OrderEntityBO();
        orderEntityBO.setUserPin(order.getPin());
        orderEntityBO.setSendPay(order.getSendPay());
        orderEntityBO.setSplitType(order.getSplitType());
        orderEntityBO.setSendPayMap(order.getSendPayMap());
        orderEntityBO.setParentId(order.getParentId());
        orderEntityBO.setOrderId(order.getOrderId());
        orderEntityBO.setOrderType(order.getOrderType());
        orderEntityBO.setPayType(order.getIdPaymentType());
        orderEntityBO.setPaymentWay(String.valueOf(order.getPaymentWay()));
        // 订单总金额
        orderEntityBO.setOrderTotalAmount(order.getPrice() == null ? BigDecimal.ZERO : (order.getPrice().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : order.getPrice()));
        // 订单优惠金额
        BigDecimal orderDiscount = new BigDecimal("0.00");
        if (order.getDiscount() != null && order.getDiscount().compareTo(BigDecimal.ZERO) >= 0) {
            orderDiscount = order.getDiscount();
        }
        // 店铺新人礼金
        if (order.getTotalGiftCashDiscount() != null && order.getTotalGiftCashDiscount().compareTo(BigDecimal.ZERO) >= 0) {
            orderDiscount = orderDiscount.add(order.getTotalGiftCashDiscount());
        }
        // plus95折
        if (order.getTotalPlusRebate() != null && order.getTotalPlusRebate().compareTo(BigDecimal.ZERO) >= 0) {
            orderDiscount = orderDiscount.add(order.getTotalPlusRebate());
        }
        orderEntityBO.setOrderDiscount(orderDiscount);
        // 订单优惠券金额
        BigDecimal orderCoupon = null;
        if (order.getCouponDiscount() != null && order.getCouponDiscount().compareTo(BigDecimal.ZERO) >= 0) {
            orderCoupon = order.getCouponDiscount();
        }
        orderEntityBO.setOrderCoupon(orderCoupon);
        log.info("OrderEntityBoUtil -> getOrderBaseInfo,orderId={},orderDiscount={},orderCoupon={}", order.getOrderId(), orderDiscount, orderCoupon);
        // 优惠列表
        List<DiscountAmountDetailBO> discountDetailList = getDiscountDetailList(order);
        orderEntityBO.setDiscountDetailList(discountDetailList);

        // 预售标识
        String presale = String.valueOf(order.getSendPay().charAt(SendpayValueEnum.SEND_PAY_44_1.getSendPayIndex()));
        orderEntityBO.setPresale(presale);

        // 订单实付金额
        orderEntityBO.setOrderAmount(order.getPayMoney());
        try {
            orderEntityBO.setOrderCreateTime(TimeUtils.timeStrToDate(order.getCreateDate().replace("T", " "), TimeFormat.LONG_PATTERN_LINE));
        } catch (Exception e) {
            log.error("getOrderBaseInfo.setExpireTime 格式转换异常,添加默认时间为当前时间", e);
            orderEntityBO.setExpireTime(new Date());
        }
        orderEntityBO.setExpireTime(orderEntityBO.getOrderCreateTime());

        //popInfo扩展信息
        if(Objects.nonNull(order.getPop())){
            orderEntityBO.getOrderExtMap().put(OrderExtTypeEnum.POP_INFO.getType(), JSON.toJSONString(order.getPop()));
        }

        // 优惠券
        if(Objects.nonNull(order.getCouponList())){
            orderEntityBO.getOrderExtMap().put(OrderExtTypeEnum.COUPON_INFO.getType(), JSON.toJSONString(convertAssetDetail(order.getCouponList())));
        }

        Map<String, String> sendPayMap = JSON.parseObject(orderEntityBO.getSendPayMap(), new TypeReference< Map<String, String>>(){});
        if(MapUtils.isNotEmpty(sendPayMap)){
            //非快检模式
            if(sendPayMap.containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && sendPayMap.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equalsIgnoreCase(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())){
                OrderInfoRpc orderInfoRpc = SpringUtil.getBean(OrderInfoRpc.class);

                OrderInfoQueryContext context = new OrderInfoQueryContext();
                context.setOrderId(String.valueOf(order.getOrderId()));
                context.setUserPin(order.getPin());
                JdOrder jdOrder = orderInfoRpc.queryOrderInfo(context);
                if (jdOrder!=null&& CollectionUtils.isNotEmpty(jdOrder.getJdOrderExtList())){
                    JSONObject jsonObject = JSON.parseObject(jdOrder.getJdOrderExtList().get(0).getExtContext());
                    orderEntityBO.getOrderExtMap().put(OrderExtTypeEnum.ORDER_ADDRESS.getType(),jsonObject.getString("addressInfo"));
                }
            }
        }
        return orderEntityBO;
    }

    /**
     * 将券转为资产对象存储
     * @param couponList
     * @return
     */
    private static List<Map<String, String>> convertAssetDetail(List<Coupon> couponList){
        List<Map<String, String>> result = Lists.newArrayList();
        couponList.forEach(s -> {
            Map<String, String> assetDetail = new HashMap<>();
            //assetDetail.put("assetId", s.getId());
            assetDetail.put("couponType", String.valueOf(s.getCouponType()));
            assetDetail.put("couponStyle", String.valueOf(s.getCouponStyle()));
            assetDetail.put("couponQuota", String.valueOf(s.getQuota()));
            assetDetail.put("assetDiscount", String.valueOf(s.getDiscount()));
            assetDetail.put("couponDiscountCurrentUsed", String.valueOf(s.getDiscountCurrentUsed()));
            //assetDetail.put("couponReBate", String.valueOf(s.getDiscountAllocation()));
            assetDetail.put("couponTopDiscount", String.valueOf(s.getDiscountAllocation()));
            assetDetail.put("detailAssetType", String.valueOf(OrderInfoConverter.COUPON_ASSET_TYPE));
            result.add(assetDetail);
        });
        return result;
    }


    /**
     * 客户加密信息
     * @param order
     * @param orderEntityBO
     * @return
     */
    private static void getDecryptEncInfo(Order order,OrderEntityBO orderEntityBO){

        String nameEnc = order.getNameENC();
        if(StringUtils.isNotBlank(nameEnc)){
            String name = decryptEncStr(nameEnc);
            orderEntityBO.setName(name);
        }else {
            log.info("OrderEntityBoUtil -> getDecryptEncInfo nameENC is null, orderId={}", order.getOrderId());
            orderEntityBO.setName(order.getName());
        }

        String mobileENC = order.getMobileENC();
        if(StringUtils.isNotBlank(mobileENC)){
            String mobile = decryptEncStr(mobileENC);
            orderEntityBO.setUserPhone(mobile);
        }else{
            log.info("OrderEntityBoUtil -> getDecryptEncInfo mobileENC is null, orderId={}", order.getOrderId());
            if(StringUtils.isNotBlank(order.getMobile())){
                orderEntityBO.setUserPhone(order.getMobile());
            }
        }
    }

    /**
     * 发票信息
     * @param order
     * @param orderEntityBO
     */
    private static void getOrderInvoiceType(Order order,OrderEntityBO orderEntityBO){
        // 发票类型 1.普通发票 2是增值税发票，3是电子发票
        orderEntityBO.setInvoiceType(order.getIdInvoiceType());

        //发票内容类型 1.明细 2 办公用品 3 电脑配件 。。。。
        orderEntityBO.setInvoiceContent(InvoiceContentEnum.fromType(order.getIdInvoiceContentsType()) == null ? "" :
                InvoiceContentEnum.fromType(order.getIdInvoiceContentsType()).getDesc());
        // 发票抬头
        String invoiceTitleEnc = order.getInvoiceTitleENC();
        if(StringUtils.isNotBlank(invoiceTitleEnc)){
            String invoiceTitle = decryptEncStr(invoiceTitleEnc);
            orderEntityBO.setInvoiceTitle(invoiceTitle);
        }else{
            log.info("OrderEntityBoUtil -> getOrderInvoiceType invoiceTitle is null, orderId={}", order.getOrderId());
            if(StringUtils.isNotBlank(order.getInvoiceTitle())){
                orderEntityBO.setInvoiceTitle(order.getInvoiceTitle());
            }
        }
        // 纳税人识别号
        String vatNoEnc = order.getVatNoENC();
        if(StringUtils.isNotBlank(vatNoEnc)){
            String vatNo = decryptEncStr(vatNoEnc);
            orderEntityBO.setVatNo(vatNo);
        }else{
            log.info("OrderEntityBoUtil -> getOrderInvoiceType invoiceTitle is null, orderId={}", order.getOrderId());
            if(StringUtils.isNotBlank(order.getVatNo())){
                orderEntityBO.setVatNo(order.getVatNo());
            }
        }
        orderEntityBO.setInvoiceHeaderType(order.getIdInvoiceHeaderType());
        orderEntityBO.setInvoiceState(order.getIdInvoicePutType());
        // 订单发票信息：机构号
        orderEntityBO.setOrgId(order.getIdCompanyBranch());
    }

    /**
     * 解析订单OFC的MQ,统一封装对象OrderEntityBO
     *
     * @param order
     * @return
     */
    public static OrderEntityBO getOrderEntity(Order order) {
        log.info("OrderEntityBoUtil -> getOrderEntity start, orderId={}", order.getOrderId());
        OrderEntityBO orderEntityBO = getBaseOrderInfoBo(order);
        //补全订单商品明细
        fillOrderSkuItemInfo(orderEntityBO, order);
        //不全订单外部信息
        fillOrderOutInfo(orderEntityBO, order);
        log.info("OrderEntityBoUtil -> getOrderEntity end, orderEntityBO={}", JSON.toJSONString(orderEntityBO));
        return orderEntityBO;
    }


    /**
     * 构建订单信息，基于中台
     *
     * @param order
     * @return
     */
    public static OrderEntityBO getOrderEntityByCartSku(Order order) {
        log.info("OrderEntityBoUtil -> getOrderEntity start, orderId={}", order.getOrderId());
        OrderEntityBO orderEntityBO = getBaseOrderInfoBo(order);
        //补全订单商品明细
        Cart cart = order.getCart();
        // 商品信息
        if (orderEntityBO.getOrderAmount().compareTo(BigDecimal.ZERO) == 0) {
            orderEntityBO.setOrderAmount(order.getInitFactPrice() == null ? BigDecimal.ZERO : (order.getInitFactPrice().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : order.getInitFactPrice()));
        }
        List<SkuInfoBO> skuList = listSkus(orderEntityBO, cart);
        orderEntityBO.setSkuList(skuList);

        if(CollectionUtil.isNotEmpty(skuList)){
            SkuInfoBO skuInfoBO = skuList.get(0);
            orderEntityBO.setSkuAmount(skuInfoBO.getSkuAmount());
            orderEntityBO.setSkuNo(skuInfoBO.getSkuNo());
            orderEntityBO.setSkuName(skuInfoBO.getSkuName());
            orderEntityBO.setSkuImage(skuInfoBO.getSkuImage());
            orderEntityBO.setSkuNum(skuInfoBO.getSkuNum());
            orderEntityBO.setMainSku(skuInfoBO.getMainSku());
            orderEntityBO.setTheVirtualSku(skuInfoBO.getTheVirtualSku());
        }

        log.info("OrderEntityBoUtil -> getOrderEntity end, orderEntityBO={}", JSON.toJSONString(orderEntityBO));
        return orderEntityBO;
    }
    /**
     * 补全订单商品明细
     *
     * @param orderEntityBO
     * @param order
     * @return
     */
    private static void fillOrderSkuItemInfo(OrderEntityBO orderEntityBO, Order order) {
        Cart cart = order.getCart();
        // 商品信息
        if (orderEntityBO.getOrderAmount().compareTo(BigDecimal.ZERO) == 0) {
            orderEntityBO.setOrderAmount(order.getInitFactPrice() == null ? BigDecimal.ZERO : (order.getInitFactPrice().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : order.getInitFactPrice()));
        }
        List<SkuInfoBO> skuList = getSkus(orderEntityBO, cart);
        orderEntityBO.setSkuList(skuList);

        if(CollectionUtil.isNotEmpty(skuList)){
            SkuInfoBO skuInfoBO = skuList.get(0);
            orderEntityBO.setSkuAmount(skuInfoBO.getSkuAmount());
            orderEntityBO.setSkuNo(skuInfoBO.getSkuNo());
            orderEntityBO.setSkuName(skuInfoBO.getSkuName());
            orderEntityBO.setSkuImage(skuInfoBO.getSkuImage());
            orderEntityBO.setSkuNum(skuInfoBO.getSkuNum());
        }
    }

    /**
     * 不全订单外部信息
     *
     * @param orderEntityBO
     * @param order
     */
    private static void fillOrderOutInfo(OrderEntityBO orderEntityBO, Order order) {
        try {
            for (ExtTagPair theExtTag : order.getTheExtTags()) {
                if (Objects.equals(BBC_OUTER_CHANNEL_ID, theExtTag.getKey())) {
                    orderEntityBO.setOuterChannelId(theExtTag.getVal());
                }
                if (Objects.equals(BBC_OUTER_USER_ID, theExtTag.getKey())) {
                    orderEntityBO.setOuterUserId(theExtTag.getVal());
                }
            }
        } catch (Exception e) {
            log.error("OrderEntityBoUtil -> getOrderEntity TheExtTags error, order={}", JSON.toJSONString(order));
            //报警
        }
    }

    /**
     * 解析订单优惠金额列表
     * @param order
     * @return
     */
    private static List<DiscountAmountDetailBO> getDiscountDetailList(Order order) {
        List<DiscountAmountDetailBO> discountDetailList = new ArrayList<>();
        //总返现
        if (order.getRePrice() != null && order.getRePrice().compareTo(BigDecimal.ZERO) > 0) {
            DiscountAmountDetailBO rePrice = new DiscountAmountDetailBO();
            rePrice.setAmount(order.getRePrice().toString());
            rePrice.setAmountType(CouponAmountTypeEnum.BACK_AMOUNT_COUPON.getType());
            discountDetailList.add(rePrice);
        }
        //优惠券优惠金额  优惠券优惠金额不做单独展示，合在商品优惠中
        if (order.getCouponDiscount() != null && order.getCouponDiscount().compareTo(BigDecimal.ZERO) > 0) {
            DiscountAmountDetailBO couponDiscount = new DiscountAmountDetailBO();
            couponDiscount.setAmount(order.getCouponDiscount().toString());
            couponDiscount.setAmountType(CouponAmountTypeEnum.AMOUNT_COUPON.getType());
            discountDetailList.add(couponDiscount);
        }
        //礼品卡优惠金额
        if (order.getDiscountLipinka() != null && order.getDiscountLipinka().compareTo(BigDecimal.ZERO) > 0) {
            DiscountAmountDetailBO discountLipinka = new DiscountAmountDetailBO();
            discountLipinka.setAmount(order.getDiscountLipinka().toString());
            discountLipinka.setAmountType(CouponAmountTypeEnum.GIFT_CARD_COUPON.getType());
            discountDetailList.add(discountLipinka);
        }
        //手机红包优惠
        if (order.getAssetPaymentInfo() != null && order.getAssetPaymentInfo().getAmount() != null && order.getAssetPaymentInfo().getAmount().compareTo(BigDecimal.ZERO) > 0) {
            DiscountAmountDetailBO discountMobile = new DiscountAmountDetailBO();
            discountMobile.setAmount(order.getAssetPaymentInfo().getAmount().toString());
            discountMobile.setAmountType(CouponAmountTypeEnum.PHONE_CUPON.getType());
            discountDetailList.add(discountMobile);
        }
        //京豆优惠
        if (order.getJingDouCount() != null && order.getJingDouCount() != 0) {
            DiscountAmountDetailBO jingDouCount = new DiscountAmountDetailBO();
            jingDouCount.setAmount(new BigDecimal(order.getJingDouCount()).divide(BIGDECIMAL_100).toString());
            jingDouCount.setAmountType(CouponAmountTypeEnum.JING_BEAN_COUPON.getType());
            discountDetailList.add(jingDouCount);
        }
        //Plus专享95折
        if (order.getTotalPlusRebate() != null && order.getTotalPlusRebate().compareTo(BigDecimal.ZERO) > 0) {
            DiscountAmountDetailBO jingDouCount = new DiscountAmountDetailBO();
            jingDouCount.setAmount(order.getTotalPlusRebate().toString());
            jingDouCount.setAmountType(CouponAmountTypeEnum.PlUS_COUPON.getType());
            discountDetailList.add(jingDouCount);
        }
        return discountDetailList;
    }

    /**
     * 解析订单sku--待支付时 MQ解析
     * @param cart
     * @return
     */
    private static List<SkuInfoBO> getSkus(OrderEntityBO orderEntityBO,Cart cart) {
        List<SkuInfoBO> skuList = new ArrayList<>();
        //单品数组
        SKU[] theSkus = cart.getTheSkus();
        if (theSkus != null) {
            for (SKU sku1 : theSkus) {
                if (sku1 != null) {
                    convertToSkuInfoParam(sku1, skuList, orderEntityBO, cart);
                }
            }
        }
        //赠品数组
        Suit[] theGifts = cart.getTheGifts();
        if (theGifts != null) {
            for (Suit suit1 : theGifts) {
                SKU sku2 = suit1.getMainSKU();
                if (null != sku2) {
                    convertToSkuInfoParam(sku2, skuList, orderEntityBO, cart);
                }
                List<SKU> skus1 = suit1.getSkus();
                if (CollectionUtil.isNotEmpty(skus1)) {
                    for (SKU sku3 : skus1) {
                        if (null != sku3) {
                            convertToSkuInfoParam(sku3, skuList, orderEntityBO, cart);
                        }

                    }
                }
            }
        }
        //套装数组
        Suit[] thePacks = cart.getThePacks();
        if (thePacks != null) {
            for (Suit suit2 : thePacks) {
                SKU sku4 = suit2.getMainSKU();
                if (null != sku4) {
                    convertToSkuInfoParam(sku4, skuList, orderEntityBO, cart);
                }

                List<SKU> skus2 = suit2.getSkus();
                if (CollectionUtil.isNotEmpty(skus2)) {
                    for (SKU sku5 : skus2) {
                        if (null != sku5) {
                            convertToSkuInfoParam(sku5, skuList, orderEntityBO, cart);
                        }
                    }
                }

                List<SKU> skus3 = suit2.getGifts();
                if (CollectionUtil.isNotEmpty(skus3)) {
                    for (SKU sku6 : skus3) {
                        if (null != sku6) {
                            convertToSkuInfoParam(sku6, skuList, orderEntityBO, cart);
                        }
                    }
                }
            }
        }

        if (skuList.size() == 0) {
            return null;
        }
        return skuList;
    }
    /**
     * 获取订单sku信息,添加list中
     *
     * @param sku
     * @param skuList
     * @param orderEntityBO
     */
    private static void convertToSkuInfoParam(SKU sku, List<SkuInfoBO> skuList, OrderEntityBO orderEntityBO, Cart cart) {
        log.info("OrderEntityBoUtil.convertToSkuInfoParam sku={}, cart={}", JSON.toJSONString(sku), JSON.toJSONString(cart));
        Map<String, String> skuMap = sku.getExtTags();
        if(null != skuMap){
            String xfyl = skuMap.get(SkuTagsEnum.XFYL.getTagName());
            orderEntityBO.setXfyl(xfyl);
        }
        log.info("[OrderEntityBoUtil.convertToSkuInfoParam], skuMap={}", JSON.toJSONString(skuMap));
        if(StringUtils.isBlank(orderEntityBO.getStoreId())){
            orderEntityBO.setStoreId(sku.getExtTags().get("locShopId"));
        }
        if(StringUtils.isBlank(orderEntityBO.getMedicalLocId())){
            orderEntityBO.setMedicalLocId(sku.getExtTags().get("medicalLocId"));
        }
        if(Objects.isNull(orderEntityBO.getCid2())){
            Integer cid2 = sku.getCid2();
            orderEntityBO.setCid2(cid2);
        }
        if (CollectionUtil.isNotEmpty(skuMap) && skuMap.containsKey(SHOP_ID_KEY)) {
            String shopIdStr = skuMap.get(SHOP_ID_KEY);
            orderEntityBO.setShopId(shopIdStr);
            String shopName = skuMap.get(SHOP_NAME_KEY);
            if(StringUtils.isNotBlank(shopName)){
                orderEntityBO.setShopName(shopName);
            }else if(!StringUtils.equals(DEFAULT_SHOP_ID, shopIdStr)) {
                VenderShopRpc shopRpc = SpringUtil.getBean(VenderShopRpc.class);
                RpcVenderShopVO rpcVenderShopVO = shopRpc.queryInfo(Long.valueOf(shopIdStr));
                orderEntityBO.setShopName(rpcVenderShopVO.getShopName());
            }
        }
        if(StringUtils.isBlank(orderEntityBO.getShopId())) {
            orderEntityBO.setShopId(DEFAULT_SHOP_ID);
            orderEntityBO.setShopName(DEFAULT_SHOP_NAME);
        }

        SkuInfoBO skuInfoBo = new SkuInfoBO();
        skuInfoBo.setSkuAmount(sku.getPrice());
        skuInfoBo.setMainSku(String.valueOf(sku.getId()));
        // 主sku与影分身sku映射关系
        Map<String, String> mainVirtualSkuMap = transMainVirtualSkuMap(cart);
        String theVirtualSku = MapUtils.isEmpty(mainVirtualSkuMap) ? null : mainVirtualSkuMap.get(String.valueOf(sku.getId()));
        log.info("OrderEntityBoUtil convertToSkuInfoParam theVirtualSku={}", theVirtualSku);
        if (StringUtils.isNotBlank(theVirtualSku)){
            skuInfoBo.setSkuNo(theVirtualSku);
            skuInfoBo.setTheVirtualSku(theVirtualSku);
        }else {
            Map<String, String> mainVirtualSkuMapV2 = transMainVirtualSkuMapV2(sku);
            String theVirtualSkuV2 = MapUtils.isEmpty(mainVirtualSkuMapV2) ? null : mainVirtualSkuMapV2.get(String.valueOf(sku.getId()));
            log.info("OrderEntityBoUtil convertToSkuInfoParam theVirtualSkuV2={}", theVirtualSkuV2);
            if (StringUtils.isNotBlank(theVirtualSkuV2)){
                skuInfoBo.setSkuNo(theVirtualSkuV2);
                skuInfoBo.setTheVirtualSku(theVirtualSkuV2);
            }else {
                skuInfoBo.setSkuNo(String.valueOf(sku.getId()));
            }
        }
        skuInfoBo.setSkuName(sku.getName());
        skuInfoBo.setSkuImage(sku.getImgUrl());
        skuInfoBo.setSkuNum(sku.getNum());
        skuInfoBo.setCid1(sku.getCidFirst());
        skuInfoBo.setCid2(sku.getCid2());
        skuInfoBo.setCid3(sku.getCid());
        SkuFeaturesVo skuFeaturesVo = SkuFeaturesVo.builder()
//                .skuTag(sku.getExtTags())
                .cid1(sku.getCidFirst())
                .cid2(sku.getCid2())
                .cid3(sku.getCid()).build();
        skuInfoBo.setSkuFeatures(JSON.toJSONString(skuFeaturesVo));
        skuList.add(skuInfoBo);
        log.info("OrderEntityBoUtil convertToSkuInfoParam skuList={}", JSON.toJSONString(skuList));
    }

    /**
     * 主sku与影分身sku映射关系
     * @param cart
     * @return
     */
    private static Map<String, String> transMainVirtualSkuMap(Cart cart){
        Map<String, String> mainVirtualSkuMap = new HashMap<>();
        try {
            if (Objects.isNull(cart)){
                return mainVirtualSkuMap;
            }
            Suit[] theGifts = cart.getTheGifts();
            if (Objects.isNull(theGifts)){
                return mainVirtualSkuMap;
            }
            for (Suit theGift : theGifts) {
                SKU mainSKU = theGift.getMainSKU();
                if (Objects.isNull(mainSKU)){
                    continue;
                }
                Map<String, String> extTags = mainSKU.getExtTags();
                if (Objects.isNull(extTags)){
                    continue;
                }
                String theVirtualSku = extTags.get("TheVirtualSku");
                if (StringUtils.isBlank(theVirtualSku)){
                    continue;
                }
                mainVirtualSkuMap.put(String.valueOf(mainSKU.getId()), theVirtualSku);
            }
        } catch (Exception e) {
            log.error("OrderEntityBoUtil transMainVirtualSkuMap error e", e);
        }
        log.info("OrderEntityBoUtil transMainVirtualSkuMap mainVirtualSkuMap={}", JSON.toJSONString(mainVirtualSkuMap));
        return mainVirtualSkuMap;
    }

    /**
     * 主sku与影分身sku映射关系
     * @param sku
     * @return
     */
    private static Map<String, String> transMainVirtualSkuMapV2(SKU sku){
        Map<String, String> mainVirtualSkuMap = new HashMap<>();
        try {
            if (Objects.isNull(sku)){
                return mainVirtualSkuMap;
            }
            Map<String, String> extTags = sku.getExtTags();
            if (MapUtils.isEmpty(extTags)){
                return mainVirtualSkuMap;
            }
            String theVirtualSku = extTags.get("TheVirtualSku");
            if (StringUtils.isBlank(theVirtualSku)){
                return mainVirtualSkuMap;
            }
            mainVirtualSkuMap.put(String.valueOf(sku.getId()), theVirtualSku);
        } catch (Exception e) {
            log.error("OrderEntityBoUtil transMainVirtualSkuMapV2 error e", e);
        }
        log.info("OrderEntityBoUtil transMainVirtualSkuMapV2 mainVirtualSkuMap={}", JSON.toJSONString(mainVirtualSkuMap));
        return mainVirtualSkuMap;
    }

    /**
     * 调用字符串解密API
     * @param encStr
     * @return
     */
    private static String decryptEncStr(String encStr){
        String decryptString =null;
        try {
            TdeClientUtil tdeClientUtil = SpringUtil.getBean(TdeClientUtil.class);
            if (tdeClientUtil.isDecryptable(encStr)) {
                // #4-调用字符串解密API
                decryptString = tdeClientUtil.decrypt(encStr);
                log.info("OrderEntityBoUtil -> decryptEncStr decryptString is suceess, decryptString={}", decryptString);
                return decryptString;
            }else{
                log.error("OrderEntityBoUtil -> decryptEncStr decryptString is fail, encStr={}", encStr);
            }
        } catch (Exception e) {
            log.error("OrderEntityBoUtil -> decryptEncStr decryptString is fail, encStr={},errorMes=", encStr,e);
        }
        return decryptString;
    }

    private static List<SkuInfoBO> listSkus(OrderEntityBO orderEntityBO,Cart cart) {
        List<SkuInfoBO> skuList = new ArrayList<>();
        List<SKU> skus = cart.findAllSkusAndNum();
        if (CollectionUtil.isNotEmpty(skus)) {
            for (SKU sku : skus) {
                if (sku != null) {
                    convertToSkuInfoParam(sku, skuList, orderEntityBO, cart);
                }
            }
        }
        return skuList;
    }

}
