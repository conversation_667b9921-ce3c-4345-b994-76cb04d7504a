package com.jdh.o2oservice.application.provider.event;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.support.service.PromisegoApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.enums.LabAggStatusEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.common.enums.WsEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.es.JdMedicalPromiseEsRepository;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.LabPromisegoBo;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.export.support.query.SendLaboratorySocketMsgRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 实验室端通知消息事件订阅，当检测单数据有变化时，订阅相关事件
 *
 * （1）状态变更的事件：骑手接单、骑手取样、骑手送样、收样、报告生成、退款；参考com.jdh.o2oservice.application.provider.service.impl.ProviderQueryApplicationImpl.CompositeStatus
 * （2）绑码事件：
 * （3）重新预估ETA事件
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@Service
public class StationNotifyEventSubscriber {

    private static  final  Set<String> REFRESH_TODO_LIDT_EVENT = Sets.newHashSet(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH.getCode(),
            MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE.getCode(),
            MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT.getCode(),
            MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE.getCode(),
            MedPromiseEventTypeEnum.MED_PROMISE_TESTING_TIMEOUT.getCode()
    );
    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private ReachApplication reachApplication;
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;
    @Resource
    private AngelShipRepository angelShipRepository;
    @Resource
    private AngelWorkRepository angelWorkRepository;
    @Resource
    private PromisegoApplication promisegoApplication;
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * jdMedicalPromiseEsRepository
     */
    @Autowired
    private JdMedicalPromiseEsRepository jdMedicalPromiseEsRepository;
    /**
     * todo 年后找海哥确认事件是否和需求所需环节匹配
     */
    @PostConstruct
    public void registerEventConsumer() {
        // 骑手接单 -> 状态变成待取货
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));

        // 送检中 -> 状态变成配送中
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));
        // 已送达 -> 状态变成待收样
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));

        // 已收样 -> 状态变成检测中
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));

        // 报告已出
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));
        // 作废成功
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_INVALID, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));

        // 绑码事件
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));

        // ETA变更事件
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_ETA_UPDATE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));

        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_TESTING_TIMEOUT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "refreshRow", this::refreshRow, Boolean.TRUE, Boolean.TRUE));


        /********************* 实验室端通知消息事件订阅 *********************/
        /**
         * 实验室收样 push
         */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "labReceiveReach", this::labReach, Boolean.TRUE, Boolean.TRUE));

        /**
         * 实验室收样超时push
         */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_TIMEOUT,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "labWaitingTestTimeoutReach", this::labReach, Boolean.TRUE, Boolean.TRUE));

        /**
         * 实验室检测超时push
         */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_TESTING_TIMEOUT,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "labTestingTimeoutReach", this::labReach, Boolean.TRUE, Boolean.TRUE));
        /**
         * 实验室检测完成-待审核 push
         */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_LAB_TEST_FINISH,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "labTestFinishReach", this::labReach, Boolean.TRUE, Boolean.TRUE));
        /**
         * 用户已同意让步检测 push
         */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_USER_AGREE_CONCESSION_TEST,
                WrapperEventConsumer.newInstance(DomainEnum.BASE, "labUserAgreeConcessionTestReach", this::labReach, Boolean.TRUE, Boolean.TRUE));

    }

    /**
     * 预约处理
     * @param event
     */
    public void refreshRow(Event event){
        ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.LOW_PRIORITY_POOL);
        CompletableFuture.runAsync(()->{
            try {
                log.error("ReachApplicationImpl refreshRow start event={}", JSON.toJSONString(event));
                List<MedicalPromise> list = Lists.newArrayList();
                // 检测单事件
                if (StringUtils.equals(event.getDomainCode(), DomainEnum.MED_PROMISE.getCode())
                        && StringUtils.equals(event.getAggregateCode(), MedPromiseAggregateEnum.MED_PROMISE.getCode())) {
                    String aggregateId = event.getAggregateId();
                    MedicalPromise medicalPromise = medicalPromiseRepository.findIgnoreYn(new MedicalPromiseIdentifier(Long.valueOf(aggregateId)));
                    list.add(medicalPromise);
                    // ship事件
                } else if (StringUtils.equals(event.getDomainCode(), DomainEnum.ANGEL_PROMISE.getCode())
                        && StringUtils.equals(event.getAggregateCode(), AngelWorkAggregateEnum.SHIP.getCode())) {
                    String aggregateId = event.getAggregateId();

                    AngelShip angelShip = angelShipRepository.find(new AngelShipIdentifier(Long.valueOf(aggregateId)));
                    Long workId = angelShip.getWorkId();
                    AngelWork work = angelWorkRepository.find(new AngelWorkIdentifier(workId));
                    MedicalPromiseListQuery query = new MedicalPromiseListQuery();
                    query.setPromiseId(work.getPromiseId());
                    List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
                    list.addAll(medicalPromises);

                }

                if (CollectionUtils.isNotEmpty(list)) {
                    List<Long> medicalIds = Lists.newArrayList();
                    Set<String> stationIds = list.stream()
                            .peek(e-> medicalIds.add(e.getMedicalPromiseId()))
                            .map(MedicalPromise::getStationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                    log.info("ReachApplicationImpl refreshRow medicalIds={}", JSON.toJSONString(medicalIds));
                    for (String stationId : stationIds) {
                        /**
                         * 通知待办事项更新
                         * 1、待收样本数量：已送达和已收样事件需要刷新；
                         * 2、待上传报告数量：已收样，已出报告需要刷新；
                         * 3、上传报告超时：报告超时，报告已出需要刷新
                         */
                        if (REFRESH_TODO_LIDT_EVENT.contains(event.getEventCode())){
                            SendLaboratorySocketMsgRequest doNotify = SendLaboratorySocketMsgRequest
                                    .builder()
                                    .stationId(stationId)
                                    .wsEventType("refreshTodoList")
                                    .build();
                            reachApplication.sendLaboratorySocketMsg(doNotify);

                        }
                    }
                }else {
                    log.error("ReachApplicationImpl refreshRow list is empty");
                }
            }catch (Exception e){
                log.error("ReachApplicationImpl refreshRow error", e);
            }
        }, executorService);
    }


    /**
     * 实验室端数据超时通知（待收样、检测）
     *
     * @param event 事件
     */
    public void labReach(Event event) {
        log.info("ReachApplicationImpl labTimeoutReach event={}", JSON.toJSONString(event));
        String eventCode = event.getEventCode();
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(Long.parseLong(event.getAggregateId())).build());

        SendLaboratorySocketMsgRequest socketMsgRequest = SendLaboratorySocketMsgRequest
                .builder()
                .stationId(medicalPromise.getStationId())
                .build();

        Map<String, Object> data = new HashMap<>();

        //实验室收样 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_STATION_WAIT_RECEIVE.getCode().equals(eventCode)){
            // 质控单数据不需要通知
            if (ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
                return;
            }
            socketMsgRequest.setWsEventType(WsEventTypeEnum.LAB_RECEIVE.getType());
            // 您有一个样本已送达，请在15分钟内完成收样”；（15分钟是eta配置的收样时间）
            LabPromisegoBo labPromisegoBo = promisegoApplication.queryLabPromisego(medicalPromise.getMedicalPromiseId(), LabAggStatusEnum.WAITING_TEST);
            if(Objects.nonNull(labPromisegoBo) && Objects.nonNull(labPromisegoBo.getCurrScript())){
                data.put("msg","您有一个样本已送达，请在" + (int)Math.ceil((double)labPromisegoBo.getCurrScript().getDuration()/60) + "分钟内完成收样");
            }
        }

        //实验室检测超时 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_TESTING_TIMEOUT.getCode().equals(eventCode)){
            // 质控单数据不需要通知
            if (ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
                return;
            }

            socketMsgRequest.setWsEventType(WsEventTypeEnum.LAB_TESTING_TIMEOUT.getType());
            //您有*个样本检测超时
            data.put("msg","您有一个样本检测超时，请及时处理");
        }

        //实验室收样超时 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_TIMEOUT.getCode().equals(eventCode)){
            // 质控单数据不需要通知
            if (ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
                return;
            }
            socketMsgRequest.setWsEventType(WsEventTypeEnum.LAB_WAITING_TEST_TIMEOUT.getType());
            //您有一个样本收样超时，请及时收样
            data.put("msg","您有一个样本收样超时，请及时收样");
        }

        if (MedPromiseEventTypeEnum.MED_PROMISE_LAB_TEST_FINISH.getCode().equals(eventCode)) {
            Date appointEndTime = TimeUtils.getEndTime(new Date());
            Date appointStartTime = TimeUtils.getStartTime(TimeUtils.addDays(appointEndTime, -6));

            MedicalPromiseEsQuery todayWaitAuditTotalBO = new MedicalPromiseEsQuery();
            todayWaitAuditTotalBO.setLaboratoryStationId(medicalPromise.getStationId());
            todayWaitAuditTotalBO.setProviderId(medicalPromise.getProviderId());
            todayWaitAuditTotalBO.setOrderStatusNotInList(Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()));
            todayWaitAuditTotalBO.setInSubStatusList(Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK.getSubStatus()));
            todayWaitAuditTotalBO.setAppointmentStartTime(appointStartTime);
            todayWaitAuditTotalBO.setAppointmentEndTime(appointEndTime);
            Long total = jdMedicalPromiseEsRepository.queryQuickCheckCount(todayWaitAuditTotalBO);
            if (total == null) {
                total = 0L;
            }

            MedicalPromiseEsQuery todayQcWaitAuditTotalBO = new MedicalPromiseEsQuery();
            todayQcWaitAuditTotalBO.setLaboratoryStationId(medicalPromise.getStationId());
            todayQcWaitAuditTotalBO.setProviderId(medicalPromise.getProviderId());
            todayQcWaitAuditTotalBO.setOrderStatusNotInList(Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()));
            todayQcWaitAuditTotalBO.setInSubStatusList(Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK.getSubStatus()));
            todayQcWaitAuditTotalBO.setAppointmentStartTime(appointStartTime);
            todayQcWaitAuditTotalBO.setAppointmentEndTime(appointEndTime);
            todayQcWaitAuditTotalBO.setVerticalCode(ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode());
            Long qcTotal = jdMedicalPromiseEsRepository.queryQuickCheckCount(todayQcWaitAuditTotalBO);
            if (qcTotal == null) {
                qcTotal = 0L;
            }
            // 质控单数据
            if (ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
                if (qcTotal > 0) {
                    socketMsgRequest.setWsEventType(WsEventTypeEnum.QC_CHECK_RESULT_OUT.getType());
                    data.put("msg",String.format("您有%s个质控已出报告，请点击去审核", qcTotal));
                }
            } else {
                // 用户检测单，总检测单排除质控单
                int userTotal = (int)(total - qcTotal);
                if (userTotal > 0) {
                    socketMsgRequest.setWsEventType(WsEventTypeEnum.USER_CHECK_RESULT_OUT.getType());
                    data.put("msg",String.format("您有%s个样本已出报告，请点击去审核", userTotal));
                }
            }
        }

        //让步检测 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_USER_AGREE_CONCESSION_TEST.getCode().equals(eventCode)){
            // 质控单数据不需要通知
            if (ServiceHomeTypeEnum.XFYL_HOME_MERCHANT_QUALITY_TEST.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
                return;
            }
            socketMsgRequest.setWsEventType(WsEventTypeEnum.USER_AGREE_CONCESSION_TEST.getType());
            //您有一个样本收样超时，请及时收样
            data.put("msg",String.format("样本%s，用户同意让步检测，请继续实验", medicalPromise.getSpecimenCode()));
        }

        socketMsgRequest.setData(data);
        reachApplication.sendLaboratorySocketMsg(socketMsgRequest);
    }

}
