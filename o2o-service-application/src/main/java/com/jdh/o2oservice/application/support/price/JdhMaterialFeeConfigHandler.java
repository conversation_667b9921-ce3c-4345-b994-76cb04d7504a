package com.jdh.o2oservice.application.support.price;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.application.product.service.ProductMaterialPackageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceFactObjectEnum;
import com.jdh.o2oservice.core.domain.support.price.context.PricingServiceCalculateContext;
import com.jdh.o2oservice.core.domain.support.price.handler.PricingServiceFactObjectHandler;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.cmd.ItemMaterialPackageRelCmd;
import com.jdh.o2oservice.export.product.dto.JdhMaterialPackageDto;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhMaterialPackageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName JdhMaterialPriceConfigHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/5/3 18:26
 **/
@Component
@Slf4j
public class JdhMaterialFeeConfigHandler extends AbstractFactObjectHandler {

    /**
     * 耗材价格配置
     */
    @Resource
    private ProductMaterialPackageApplication productMaterialPackageApplication;

    /**
     * sku配置数据
     */
    @Resource
    private JdhSkuFeeConfigHandler jdhSkuFeeConfigHandler;

    /**
     * 检测项目事实对象处理器
     */
    @Resource
    private JdhItemFeeConfigHandler jdhItemFeeConfigHandler;

    /**
     * 护士数据
     */
    @Resource
    private JdhAngelFactObjectHandler jdhAngelFactObjectHandler;

    /**
     * 检测单数据
     */
    @Resource
    private JdhMedicalPromiseFactObjectHandler medicalPromiseFactObjectHandler;

    /**
     * 获取事实对象
     * @param context
     * @return
     */
    @Override
    public Object getData(PricingServiceCalculateContext context) {
        boolean containsKey = context.getFactObjectMap().containsKey(getMapKey());
        if (containsKey) {
            return context.getFactObjectMap().get(getMapKey());
        }

        Object o = jdhSkuFeeConfigHandler.getFactObject(context);
        //如果sku不存在，则直接返回
        if (Objects.isNull(o)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        Map<Long, JdhSkuDto> skuMap =  Convert.convert(new TypeReference<Map<Long, JdhSkuDto>>(){}, o);
        //Map<Long, JdhSkuDto> skuMap = jdhSkuDtos.stream().collect(Collectors.toMap(JdhSkuDto::getSkuId, jdhSkuDto -> jdhSkuDto, (t, t2) -> t2));

        //2. 提取检测单数据
        Object factObject = medicalPromiseFactObjectHandler.getFactObject(context);
        if (Objects.isNull(factObject)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = Convert.convert(new TypeReference<List<MedicalPromiseDTO>>() {}, factObject);


        // 3.提取项目数据
        //前置依赖项目数据，先从上下文中获取，没有则调用接口获取，并放入上下文
        Object itemFeeConfigHandlerFactObject = jdhItemFeeConfigHandler.getFactObject(context);
        //没有项目数据，默认没有耗材，返回null
        if (Objects.isNull(itemFeeConfigHandlerFactObject)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        Map<Long, ServiceItemDto> id2ItemMap = Convert.convert(new TypeReference<Map<Long, ServiceItemDto>>(){}, itemFeeConfigHandlerFactObject );

        // 4.提取护士数据
        Object angel = jdhAngelFactObjectHandler.getFactObject(context);
        //没有护士数据，返回null
        if (Objects.isNull(angel)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        JdhAngelDto jdhAngelDto = Convert.convert(JdhAngelDto.class, angel);
        //没有护士全兼职标签数据，返回null
        if (Objects.isNull(jdhAngelDto.getJobNature())) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        //获取耗材ID列表
        List<Long> materialIdList = id2ItemMap.values().stream().flatMap(serviceItemDto -> Optional.ofNullable(serviceItemDto.getMaterialList()).orElse(new ArrayList<>()).stream()).map(ItemMaterialPackageRelCmd::getMaterialPackageId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialIdList)) {
            context.getFactObjectMap().put(getMapKey(), null);
            return null;
        }
        List<JdhMaterialPackageRequest> requests = materialIdList.stream().map(materialId -> {
            JdhMaterialPackageRequest req = new JdhMaterialPackageRequest();
            req.setMaterialPackageId(materialId);
            return req;
        }).collect(Collectors.toList());
        List<JdhMaterialPackageDto> jdhMaterialPackageDtos = productMaterialPackageApplication.queryList(requests);
        Map<Long, JdhMaterialPackageDto> id2MaterialMap = jdhMaterialPackageDtos.stream().collect(Collectors.toMap(JdhMaterialPackageDto::getMaterialPackageId, jdhMaterialPackageDto -> {
            JdhMaterialPackageDto dto = new JdhMaterialPackageDto();
            dto.setSelfAngelSettlementPrice(jdhMaterialPackageDto.getSelfAngelSettlementPrice());
            dto.setPartAngelSettlementPrice(jdhMaterialPackageDto.getPartAngelSettlementPrice());
            return dto;
        }, (o1, o2) -> o2));


        //获取sku和item的关联关系
        Map<Long, Set<Long>> sku2ItemListMap = new HashMap<>();
        for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseDTOList) {
            if (Objects.isNull(medicalPromiseDTO.getServiceId()) || StringUtils.isBlank(medicalPromiseDTO.getServiceItemId())) {
                continue;
            }
            Set<Long> itemSet = sku2ItemListMap.getOrDefault(medicalPromiseDTO.getServiceId(), new HashSet<>());
            itemSet.add(Long.valueOf(medicalPromiseDTO.getServiceItemId()));
            sku2ItemListMap.put(medicalPromiseDTO.getServiceId(), itemSet);
        }

        //返回值
        Map<Long, BigDecimal> result = new HashMap<>();

        for (Map.Entry<Long, Set<Long>> entry : sku2ItemListMap.entrySet()) {
            //sku编号
            Long skuId = entry.getKey();
            JdhSkuDto jdhSkuDto = skuMap.get(skuId);
            if (Objects.isNull(jdhSkuDto)) {
                continue;
            }
            //护士结算价
            BigDecimal angelSettlementPrice = BigDecimal.ZERO;

            Set<Long> itemSet = entry.getValue();
            if (CollectionUtils.isNotEmpty(itemSet)) {
                for (Long itemId : itemSet) {
                    ServiceItemDto itemDto = id2ItemMap.getOrDefault(itemId, ServiceItemDto.builder().build());
                    Set<Long> materialIdNeedList = itemDto.getMaterialIdNeedList();
                    //如果项目有必需耗材，进行sum计算
                    if (CollectionUtils.isNotEmpty(materialIdNeedList)) {
                        for (Long materialId : materialIdNeedList) {
                            JdhMaterialPackageDto jdhMaterialPackageDto = id2MaterialMap.getOrDefault(materialId, new JdhMaterialPackageDto());
                            BigDecimal price = Objects.equals(JobNatureEnum.FULL_TIME.getValue(),jdhAngelDto.getJobNature()) ? jdhMaterialPackageDto.getSelfAngelSettlementPrice() : jdhMaterialPackageDto.getPartAngelSettlementPrice();
                            angelSettlementPrice = angelSettlementPrice.add(Objects.nonNull(price) ? price : BigDecimal.ZERO);
                        }
                    }
                }
            }
            result.put(skuId, angelSettlementPrice);
        }

        //将耗材数据放入项目中
        for (ServiceItemDto serviceItemDto : id2ItemMap.values()) {
            if (CollectionUtils.isEmpty(serviceItemDto.getMaterialList())) {
                continue;
            }
            List<JdhMaterialPackageDto> collect = serviceItemDto.getMaterialList().stream().map(cmd -> id2MaterialMap.get(cmd.getMaterialPackageId())).filter(Objects::nonNull).collect(Collectors.toList());
            serviceItemDto.setMaterialPackageDtos(collect);
        }
        //将耗材数据放入上下文
        context.getFactObjectMap().put(getMapKey(), result);
        return result;
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        return PricingServiceFactObjectEnum.MATERIAL_FEE_CONFIG.getCode();
    }

    /*public static void main(String[] args) {
        // 你的数据结构
        Map<String, Object> data = JSON.parseObject("{       \n" +
                "        \"promiseDto\": {\n" +
                "            \"appointmentPhone\": \"17600747539\",\n" +
                "            \"appointmentTime\": {\n" +
                "                \"appointmentEndTime\": 1745496000000,\n" +
                "                \"appointmentStartTime\": 1745492400000,\n" +
                "                \"dateType\": 2,\n" +
                "                \"isImmediately\": false\n" +
                "            },\n" +
                "            \"branch\": \"yfb\",\n" +
                "            \"code\": \"1250\",\n" +
                "            \"codeId\": \"170624500105297\",\n" +
                "            \"codePwd\": \"5634\",\n" +
                "            \"createTime\": 1745482007000,\n" +
                "            \"createUser\": \"jd_5b89d0cb1f009\",\n" +
                "            \"expireDate\": 1777046399000,\n" +
                "            \"freeze\": 1,\n" +
                "            \"id\": 36667,\n" +
                "            \"patients\": [\n" +
                "                {\n" +
                "                    \"birthday\": {\n" +
                "                        \"age\": 31,\n" +
                "                        \"birth\": \"1993-09-09\"\n" +
                "                    },\n" +
                "                    \"credentialNum\": {\n" +
                "                        \"credentialNo\": \"130637199309090039\",\n" +
                "                        \"credentialType\": 1\n" +
                "                    },\n" +
                "                    \"gender\": 1,\n" +
                "                    \"marriage\": 2,\n" +
                "                    \"patientId\": 12814171042799,\n" +
                "                    \"phoneNumber\": {\n" +
                "                        \"phone\": \"17600747539\"\n" +
                "                    },\n" +
                "                    \"promiseId\": 170624500105265,\n" +
                "                    \"promisePatientId\": 170624500105281,\n" +
                "                    \"relativesType\": 1,\n" +
                "                    \"userName\": {\n" +
                "                        \"name\": \"测试不要说话\"\n" +
                "                    },\n" +
                "                    \"userPin\": \"jd_5b89d0cb1f009\",\n" +
                "                    \"version\": 11\n" +
                "                }\n" +
                "                {\n" +
                "                    \"birthday\": {\n" +
                "                        \"age\": 31,\n" +
                "                        \"birth\": \"1993-09-09\"\n" +
                "                    },\n" +
                "                    \"credentialNum\": {\n" +
                "                        \"credentialNo\": \"130637199309090039\",\n" +
                "                        \"credentialType\": 1\n" +
                "                    },\n" +
                "                    \"gender\": 1,\n" +
                "                    \"marriage\": 2,\n" +
                "                    \"patientId\": 12814171042799,\n" +
                "                    \"phoneNumber\": {\n" +
                "                        \"phone\": \"17600747539\"\n" +
                "                    },\n" +
                "                    \"promiseId\": 170624500105265,\n" +
                "                    \"promisePatientId\": 170624500105281,\n" +
                "                    \"relativesType\": 1,\n" +
                "                    \"userName\": {\n" +
                "                        \"name\": \"测试不要说话\"\n" +
                "                    },\n" +
                "                    \"userPin\": \"jd_5b89d0cb1f009\",\n" +
                "                    \"version\": 11\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"jdhAngelDto\": {\n" +
                "            \"angelId\": 164274347966823,\n" +
                "            \"angelName\": \"测试王鹏飞\",\n" +
                "            \"angelPin\": \"jd_48de9e9fbd832\",\n" +
                "            \"auditProcessStatus\": 1,\n" +
                "            \"cityName\": \"蚌埠市\",\n" +
                "            \"countyName\": \"蚌山区\",\n" +
                "            \"createTime\": 1733370054000,\n" +
                "            \"fullAddress\": \"安徽-蚌埠市-蚌山区\",\n" +
                "            \"fullDepartmentName\": \"白内障科\",\n" +
                "            \"gender\": 1,\n" +
                "            \"headImg\": \"jfs/t1/228512/34/5324/24330/65682a85F1acd24e0/be9495b716e612f3.png\",\n" +
                "            \"id\": 584,\n" +
                "            \"idCard\": \"131121199810163214\",\n" +
                "            \"idCardImgFrontUrl\": \"jfs/t1/224284/18/22286/48373/675ad74aF60fefe50/c0991b91d3639b37.jpg\",\n" +
                "            \"idCardImgOppositeUrl\": \"jfs/t1/227502/12/32159/37178/675ad74aF72b33b3d/d1371d6dfee407c4.jpg\",\n" +
                "            \"idCardIndex\": \"kjIsFQkNQvbAkjIsFQkjIsFQkf0FPwkjIsFQkjIsFQmrgcWwmrgcWwm0ZpDAkjIsFQk6vjPAkjIsFQlTuVhwkNQvbAkf0FPwkjIsFQl/58+g\",\n" +
                "            \"idCardType\": 1,\n" +
                "            \"jobNature\": 1,\n" +
                "            \"nethpDocId\": *************,\n" +
                "            \"phone\": \"15810163320\",\n" +
                "            \"phoneIndex\": \"kjIsFQlq3qhQm0ZpDAkjIsFQk6vjPAkjIsFQlTuVhwkNQvbAkNQvbAkf0FPwk6vjPA\",\n" +
                "            \"professionTitleCodeList\": [\n" +
                "                \"36\"\n" +
                "            ],\n" +
                "            \"provinceName\": \"安徽\",\n" +
                "            \"stationId\": 168451487825921,\n" +
                "            \"stationMaster\": \"\",\n" +
                "            \"takeOrderStatus\": 1,\n" +
                "            \"twoDepartmentName\": \"白内障科\",\n" +
                "            \"updateTime\": 1744103388000,\n" +
                "            \"workIdentity\": 1,\n" +
                "            \"yn\": 1\n" +
                "        },\n" +
                "        \"materialFeeConfig\": [\n" +
                "            {\n" +
                "                \"createTime\": 1714992535000,\n" +
                "                \"createUser\": \"chenhao263\",\n" +
                "                \"materialPackageDetail\": \"针管\",\n" +
                "                \"materialPackageId\": 154639235285063,\n" +
                "                \"materialPackageName\": \"测试耗材0506\",\n" +
                "                \"partAngelSettlementPriceStr\": \"\",\n" +
                "                \"selfAngelSettlementPriceStr\": \"\",\n" +
                "                \"skuId\": 100092081929,\n" +
                "                \"updateTime\": 1715591400000,\n" +
                "                \"updateUser\": \"rongzhe1\",\n" +
                "                \"selfAngelSettlementPrice\":10.5,\n" +
                "                \"partAngelSettlementPrice\":20\n" +
                "            }\n" +
                "        ],\n" +
                "        \"itemFeeConfig\": [\n" +
                "            {\n" +
                "                \"ageRange\": [\n" +
                "                    \"0\",\n" +
                "                    \"100\"\n" +
                "                ],\n" +
                "                \"angelBasicSettlementPrice\": 55.1,\n" +
                "                \"angelBasicSettlementPriceStr\": \"55.10\",\n" +
                "                \"createTime\": 1718387175000,\n" +
                "                \"id\": 12921,\n" +
                "                \"indicatorString\": \"蓝贻贝、虾、蟹、蛋黄、鸡蛋白、蒲公英、普通豚草、杨树、柳树、梧桐、榆树、栎树、刺柏、桦、小糠草、肯塔基蓝草、梯牧草、黑麦草、草地羊茅、果园草、黄花茅、交链孢霉、烟曲霉、分枝孢霉、点青霉、德国小蠊、蜜蜂毒、热带无爪螨、总IgE、蚕丝、葎草、苋、艾蒿、桑树、狗毛皮屑、猫毛皮屑、屋尘、粉尘螨、屋尘螨、胡萝卜、旱芹、鳕鱼、牛奶、芒果、菠萝、西红柿、花生、腰果、芝麻、大豆、小麦、羊肉、猪肉、鸡肉、牛肉\",\n" +
                "                \"itemId\": 156402454626615,\n" +
                "                \"itemName\": \"过敏原IgE综合组55项\",\n" +
                "                \"itemNameEn\": \"Allergen IgE comprehensive group of 55 items\",\n" +
                "                \"itemSource\": 1,\n" +
                "                \"itemType\": 2,\n" +
                "                \"reportShowType\": 1,\n" +
                "                \"sampleType\": 12,\n" +
                "                \"serviceDuration\": 30\n" +
                "            },\n" +
                "            {\n" +
                "                \"ageRange\": [\n" +
                "                    \"0\",\n" +
                "                    \"100\"\n" +
                "                ],\n" +
                "                \"angelBasicSettlementPrice\": 102.9,\n" +
                "                \"angelBasicSettlementPriceStr\": \"102.90\",\n" +
                "                \"angelSkillCodeList\": [\n" +
                "                    \"155097740869705\"\n" +
                "                ],\n" +
                "                \"createTime\": 1718387170000,\n" +
                "                \"id\": 12849,\n" +
                "                \"indicatorString\": \"测试浓度公式计算、流感嗜血杆菌、肺炎链球菌、肺炎衣原体、人冠状病毒、人副流感病毒、合胞病毒、肺炎支原体、新型冠状病毒、人鼻病毒、人腺病毒、甲流\",\n" +
                "                \"itemId\": 156076212225319,\n" +
                "                \"itemName\": \"呼吸道病毒细菌12联检\",\n" +
                "                \"itemNameEn\": \"12 joint tests for respiratory viruses and bacteria\",\n" +
                "                \"itemSource\": 1,\n" +
                "                \"itemType\": 2,\n" +
                "                \"materialIdNeedList\": [\n" +
                "                    154639235285063\n" +
                "                ],\n" +
                "                \"materialList\": [\n" +
                "                    {\n" +
                "                        \"materialPackageDetail\": \"针管\",\n" +
                "                        \"materialPackageId\": 154639235285063,\n" +
                "                        \"materialPackageName\": \"测试耗材0506\",\n" +
                "                        \"requiredFlag\": 1\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"materialPackageDtos\": [\n" +
                "                    {\n" +
                "                        \"$ref\": \"$.factObjectMap.materialFeeConfig[0]\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }", new TypeReference<Map<String, Object>>() {});
        // 初始化data...

        // 定义Aviator表达式
        String expression = "let jobNature = jdhAngelDto.jobNature; " +
                "let materialFeeMap = seq.map(); " +
                "for materialFee in materialFeeConfig {   materialFeeMap[materialFee.materialPackageId] = materialFee; }" +
                "let itemFeeMap = seq.map(); " +
                "for item in itemFeeConfig { itemFeeMap[item.itemId] = item ;}" +
                "let totalAmount = 0.0; " +
                "for medicalPromise in medicalPromiseDtoList {   " +
                "    let serviceItemId = medicalPromise.serviceItemId;" +
                "    if (serviceItemId == nil) {" +
                "        continue;" +
                "    }" +
                "    let item = itemFeeMap[long(serviceItemId)];" +
                "    if (item == nil) {" +
                "        continue;" +
                "    }   " +
                "    let materialIds = item.materialIdNeedList; " +
                "    if (materialIds != nil) {     " +
                "        for materialId in materialIds {  " +
                "            let material = materialFeeMap[materialId]; " +
                "            if (material == nil) {" +
                "                continue;" +
                "            }     " +
                "            let price = (jobNature == 1) ? material.selfAngelSettlementPrice : material.partAngelSettlementPrice;" +
                "            totalAmount = totalAmount + (price != nil ? price : 0.0);  " +
                "        }  " +
                "    } " +
                "} " +
                "return totalAmount; ";

        // 编译表达式
        Expression compiledExp = AviatorEvaluator.compile(expression, true);

        // 计算结果
        System.out.println("Total Amount: " + compiledExp.execute(data));
    }*/
}