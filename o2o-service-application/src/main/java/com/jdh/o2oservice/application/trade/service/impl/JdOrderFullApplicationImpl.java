package com.jdh.o2oservice.application.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.jd.common.web.LoginContext;
import com.jd.i18n.order.dict.FieldKeyEnum;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.domain.old.bean.SKU;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderEquipmentApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.support.util.OrderTypeDicUtil;
import com.jdh.o2oservice.application.trade.JdOrderFullExtApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeNewEnum;
import com.jdh.o2oservice.common.enums.VerticalEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipHistoryDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderFullPageContext;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderIdContext;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderFull;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.repository.es.JdOrderFullEsRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseStationDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.ProviderEquipmentDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;
import com.jdh.o2oservice.export.trade.cmd.SyncAngelShipEsCmd;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.export.trade.query.JdOrderFullPageParam;
import com.jdh.o2oservice.export.trade.query.JdOrderIdParam;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 17:39
 * @Desc :
 */
@Slf4j
@Service
public class JdOrderFullApplicationImpl implements JdOrderFullApplication, JdOrderFullExtApplication {

    /**
     * es
     */
    @Autowired
    private JdOrderFullEsRepository jdOrderFullEsRepository;

    /**
     * promiseApplication
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * medicalPromiseRepository
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * angelPromiseApplication
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * angelTaskRepository
     */
    @Autowired
    private AngelTaskRepository angelTaskRepository;

    /**
     * angelWorkRepository
     */
    @Autowired
    private AngelWorkRepository angelWorkRepository;
    /**
     * jdOrderRepository
     */
    @Autowired
    private JdOrderRepository jdOrderRepository;
    /**
     * jdOrderRepository
     */
    @Autowired
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * TradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * medPromiseHistoryApplication
     */
    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;

    /**
     * redisLockUtil
     */
    @Autowired
    private RedisLockUtil redisLockUtil;
    /**
     * 运单
     */
    @Autowired
    private AngelShipRepository angelShipRepository;
    /**
     * 运单历史状态表
     */
    @Autowired
    private AngelShipHistoryRepository angelShipHistoryRepository;

    /**
     * 供应商门店
     */
    @Autowired
    private ProviderStoreApplication providerStoreApplication;

    /**
     * fileManageApplication
     */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     * voucherRepository
     */
    @Autowired
    private VoucherRepository voucherRepository;
    /**
     * orderInfoRpc
     */
    @Resource
    private OrderInfoRpc orderInfoRpc;

    /**
     * 订单类型枚举
     */
    @Resource
    OrderTypeDicUtil orderTypeDicUtil;

    @Autowired
    private ProviderEquipmentApplication providerEquipmentApplication;


    /**
     * 查询大宽表数据
     * @param param
     * @return
     */
    @Override
    public PageDto<JdOrderFullDTO> queryPage(JdOrderFullPageParam param){
        JdOrderFullPageContext jdOrderFullPageContext = convert2JdOrderFullPageContext(param);
        PageDto<JdOrderFull> jdOrderFullPage = jdOrderFullEsRepository.queryPage(jdOrderFullPageContext);
        if (jdOrderFullPage == null || jdOrderFullPage.getTotalCount() == 0) {
            jdOrderFullPage = queryOrderNotExistES(param.getOrderId());
            if (jdOrderFullPage == null) {
                jdOrderFullPage = new PageDto<>();
                jdOrderFullPage.setPageNum(1);
                jdOrderFullPage.setPageSize(20);
                jdOrderFullPage.setTotalPage(0);
                jdOrderFullPage.setTotalCount(0);
            }
        }
        if (jdOrderFullPage != null && CollUtil.isNotEmpty(jdOrderFullPage.getList())) {
            fillStatusDeac(jdOrderFullPage.getList());
        }
        return convert2JdOrderFullDTOPage(jdOrderFullPage);
    }

    /**
     * 给前端-补全状态描述
     * @param dataList
     */
    private void fillStatusDeac(List<JdOrderFull> dataList) {
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        for(JdOrderFull jdOrderFull: dataList){
            if(StringUtils.isNotBlank(jdOrderFull.getPartnerSource())){
                //0 C端交易  1 互医检验单
                if(PartnerSourceEnum.JDH_NETDIAG.getCode().equals(new Integer(jdOrderFull.getPartnerSource()))){
                    jdOrderFull.setPartnerSourceStr("互医检验单");
                }else if(PartnerSourceEnum.JDH_HOMEDIAG.getCode().equals(new Integer(jdOrderFull.getPartnerSource()))){
                    jdOrderFull.setPartnerSourceStr("家医检验单");
                }else if(PartnerSourceEnum.JDH_XFYL.getCode().equals(new Integer(jdOrderFull.getPartnerSource()))){
                    jdOrderFull.setPartnerSourceStr("C端交易");
                }else if(PartnerSourceEnum.OUT_HOSPITAL_PAID_GUIDANCE_A.getCode().equals(new Integer(jdOrderFull.getPartnerSource()))){
                    jdOrderFull.setPartnerSourceStr("自费导诊");
                }
            }
            if(StringUtils.isNotBlank(jdOrderFull.getServiceType()) && StringUtils.isNotBlank(jdOrderFull.getVerticalCode())){
                //1 骑手上门检测 2 护士上门检测  3 护士上门护理服务
                if(ServiceHomeTypeEnum.NH_HOME_CARE2.getServiceType().equals(jdOrderFull.getServiceType())){
                    jdOrderFull.setServiceTypeStr("护士上门护理服务");
                }else if(ServiceHomeTypeEnum.NH_HOME_TEST_PHASE1.getServiceType().equals(jdOrderFull.getServiceType())){
                    if(ServiceHomeTypeEnum.XFYL_HOME_TEST_PHASE1.getVerticalCode().equals(jdOrderFull.getVerticalCode()) ||
                    ServiceHomeTypeEnum.XFYL_HOME_TEST_PHASE2.getVerticalCode().equals(jdOrderFull.getVerticalCode()) ||
                            ServiceHomeTypeEnum.NH_HOME_TEST_PHASE1.getVerticalCode().equals(jdOrderFull.getVerticalCode()) ||
                            ServiceHomeTypeEnum.VTP_HOME_TEST_PHASE.getVerticalCode().equals(jdOrderFull.getVerticalCode())){
                        jdOrderFull.setServiceTypeStr("骑手上门检测服务");
                    } else if (ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equals(jdOrderFull.getVerticalCode())) {
                        jdOrderFull.setServiceTypeStr(ServiceTypeNewEnum.TRANSPORT_TEST.getDesc());
                    } else {
                        jdOrderFull.setServiceTypeStr("护士上门检测服务");
                    }
                }
            }

            if (StringUtils.isNotBlank(jdOrderFull.getVoucherId())) {
                //看是不是已有采样盒场景，状态的覆盖逻辑不一样
                JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(Long.parseLong(jdOrderFull.getVoucherId())).build());
                boolean preSampleFlag = Objects.nonNull(jdhVoucher) && Objects.nonNull(jdhVoucher.getExtend()) && Boolean.TRUE.equals(jdhVoucher.getExtend().getPreSampleFlag());
                if (Boolean.TRUE.equals(preSampleFlag)) {
                    dealPreSampleStatus(jdOrderFull);
                    continue;
                }
            }
            /**
             *
             7退款中 jdOrderFull.getOrderStatus() == 8 || medPromiseStatu
             8已退款  jdOrderFull.getOrderStatus() == 9
             9送检中 jdOrderFull.getMedicalPromiseStatus() ==3
             10已送达 jdOrderFull.getMedicalPromiseStatus() ==4
             11检测中 jdOrderFull.getMedicalPromiseStatus() ==4
             12已出报告 jdOrderFull.getMedicalPromiseStatus() ==5
             1 待预约 jdOrderFull.getPromiseStatus()==1;   jdOrderFull.getOrderStatus() != 7\8\9
             2待接单  jdOrderFull.getPromiseStatus()==2;
             13已接单 jdOrderFull.getPromiseStatus()==13;
             3取消中  没有取消中 @张鑫
             4开始上门 jdOrderFull.getPromiseStatus()==15;
             5服务中 jdOrderFull.getPromiseStatus()==16;
             6采样完成/服务完成 jdOrderFull.getPromiseStatus()==17
             */
            if(!MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus()) && JdhFreezeEnum.FREEZE.getStatus().toString().equals(jdOrderFull.getMedPromiseFreeze())){
                jdOrderFull.setCommonStatusStr("退款中");
            } else if(OrderStatusEnum.ORDER_REFUND.getStatus().toString().equals(jdOrderFull.getOrderStatus()) || MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
                jdOrderFull.setCommonStatusStr("已退款");
            } else if(JdhPromiseStatusEnum.CANCEL_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("取消预约中");
            } else if(JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("取消预约成功");
            } else if(JdhPromiseStatusEnum.CANCEL_FAIL.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("取消预约失败");
            } else if(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
                jdOrderFull.setCommonStatusStr("送检中");
            } else if(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
                if (jdOrderFull.getDetectingNodesNum() != null && jdOrderFull.getDetectingNodesNum() == 5) {
                    if (com.google.common.collect.Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus(), MedicalPromiseSubStatusEnum.SAMPLE_DEAL_FINISH.getSubStatus()).contains(jdOrderFull.getSubStatus())) {
                        jdOrderFull.setCommonStatusStr("样本处理中");
                    } else if (com.google.common.collect.Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus(), MedicalPromiseSubStatusEnum.SAMPLE_TEST_FINISH.getSubStatus()).contains(jdOrderFull.getSubStatus())) {
                        jdOrderFull.setCommonStatusStr("上机检测中");
                    } else if (jdOrderFull.getSubStatus() != null && jdOrderFull.getSubStatus() >= MedicalPromiseSubStatusEnum.SAMPLE_TEST_ERROR_RE_TEST.getSubStatus()) {
                        MedicalPromiseSubStatusEnum subStatusEnum = MedicalPromiseSubStatusEnum.getEnumBySubStatus(jdOrderFull.getSubStatus());
                        String desc = (subStatusEnum == null ? "" : subStatusEnum.getDesc());
                        if (subStatusEnum == null || StringUtils.isBlank(desc)) {
                            jdOrderFull.setCommonStatusStr("上机检测样本异常处理中");
                        } else if (StringUtils.isNotBlank(desc)) {
                            jdOrderFull.setCommonStatusStr(desc);
                        } else {
                            jdOrderFull.setCommonStatusStr("检测中");
                        }
                    } else {
                        jdOrderFull.setCommonStatusStr("检测中");
                    }
                } else {
                    jdOrderFull.setCommonStatusStr("检测中");
                }
            } else if( MedicalPromiseStatusEnum.COLLECTED.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
                jdOrderFull.setCommonStatusStr("采样完成");
            } else if( MedicalPromiseStatusEnum.COMPLETED.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
                if(ServiceTypeEnum.TEST.getServiceType().equals(jdOrderFull.getServiceType())){
                    if (jdOrderFull.getDetectingNodesNum() != null && jdOrderFull.getDetectingNodesNum() == 5) {
                        if (com.google.common.collect.Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK.getSubStatus()).contains(jdOrderFull.getSubStatus())) {
                            jdOrderFull.setCommonStatusStr("报告审核中");
                        } else if (jdOrderFull.getSubStatus() != null && jdOrderFull.getSubStatus() >= MedicalPromiseSubStatusEnum.REPORT_CHECK_ERROR.getSubStatus()) {
                            MedicalPromiseSubStatusEnum subStatusEnum = MedicalPromiseSubStatusEnum.getEnumBySubStatus(jdOrderFull.getSubStatus());
                            String desc = (subStatusEnum == null ? "" : subStatusEnum.getDesc());
                            if (subStatusEnum == null || StringUtils.isBlank(desc)) {
                                jdOrderFull.setCommonStatusStr("报告数据异常处理中");
                            } else if (StringUtils.isNotBlank(desc)) {
                                jdOrderFull.setCommonStatusStr(desc);
                            } else {
                                jdOrderFull.setCommonStatusStr("已出报告");
                            }
                        } else {
                            jdOrderFull.setCommonStatusStr("已出报告");
                        }
                    } else {
                        jdOrderFull.setCommonStatusStr("已出报告");
                    }
                }else if(ServiceTypeEnum.CARE.getServiceType().equals(jdOrderFull.getServiceType())){
                    jdOrderFull.setCommonStatusStr("服务完成");
                }
            } else if(AngelWorkStatusEnum.COMPLETED.getType().toString().equals(jdOrderFull.getWorkStatus())){
                jdOrderFull.setCommonStatusStr("已送达");
            } else if(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("待预约");
            } else if(JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus()) || JdhPromiseStatusEnum.MODIFY_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("待接单");
            } else if(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus()) || JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("已接单");
            } else if(JdhPromiseStatusEnum.SERVICE_READY.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("开始上门");
            } else if(JdhPromiseStatusEnum.SERVICING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
                jdOrderFull.setCommonStatusStr("服务中");
            }


        }
    }

    /**
     * dealPreSampleStatus
     *
     * @param jdOrderFull jdOrderFull
     */
    private void dealPreSampleStatus(JdOrderFull jdOrderFull){
        /**
         *
         7退款中 jdOrderFull.getOrderStatus() == 8 || medPromiseStatu
         8已退款  jdOrderFull.getOrderStatus() == 9
         9送检中 jdOrderFull.getMedicalPromiseStatus() ==3
         10已送达 jdOrderFull.getMedicalPromiseStatus() ==4
         11检测中 jdOrderFull.getMedicalPromiseStatus() ==4
         12已出报告 jdOrderFull.getMedicalPromiseStatus() ==5
         1 待预约 jdOrderFull.getPromiseStatus()==1;   jdOrderFull.getOrderStatus() != 7\8\9
         2待接单  jdOrderFull.getPromiseStatus()==2;
         13已接单 jdOrderFull.getPromiseStatus()==13;
         3取消中  没有取消中 @张鑫
         4开始上门 jdOrderFull.getPromiseStatus()==15;
         5服务中 jdOrderFull.getPromiseStatus()==16;
         6采样完成/服务完成 jdOrderFull.getPromiseStatus()==17
         */
        if(!MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus()) && JdhFreezeEnum.FREEZE.getStatus().toString().equals(jdOrderFull.getMedPromiseFreeze())){
            jdOrderFull.setCommonStatusStr("退款中");
        } else if(OrderStatusEnum.ORDER_REFUND.getStatus().toString().equals(jdOrderFull.getOrderStatus()) || MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("已退款");
        } else if(JdhPromiseStatusEnum.CANCEL_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("取消预约中");
        } else if(JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("取消预约成功");
        } else if(JdhPromiseStatusEnum.CANCEL_FAIL.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("取消预约失败");
        } else if(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("待预约");
        } else if(JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus()) || JdhPromiseStatusEnum.MODIFY_ING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("待接单");
        } else if(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus()) || JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("已接单");
        } else if(JdhPromiseStatusEnum.SERVICE_READY.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("开始上门");
        } else if(JdhPromiseStatusEnum.SERVICING.getStatus().toString().equals(jdOrderFull.getPromiseStatus())){
            jdOrderFull.setCommonStatusStr("服务中");
        }else if(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("送检中");
        } else if(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("检测中");
        } else if( MedicalPromiseStatusEnum.COLLECTED.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            jdOrderFull.setCommonStatusStr("采样完成");
        } else if( MedicalPromiseStatusEnum.COMPLETED.getStatus().toString().equals(jdOrderFull.getMedicalPromiseStatus())){
            if(ServiceTypeEnum.TEST.getServiceType().equals(jdOrderFull.getServiceType())){
                jdOrderFull.setCommonStatusStr("已出报告");
            }else if(ServiceTypeEnum.CARE.getServiceType().equals(jdOrderFull.getServiceType())){
                jdOrderFull.setCommonStatusStr("服务完成");
            }
        } else if(AngelWorkStatusEnum.COMPLETED.getType().toString().equals(jdOrderFull.getWorkStatus())){
            jdOrderFull.setCommonStatusStr("已送达");
        }
    }

    @Override
    public Boolean save(JdOrderFullSaveCmd cmd){
        log.info("JdOrderFullApplicationImpl -> save cmd:{}",JSON.toJSONString(cmd));
        JdOrderFull jdOrderFull = convert2JdOrderFull(cmd);
        log.info("JdOrderFullApplicationImpl -> save jdOrderFull:{}",JSON.toJSONString(jdOrderFull));
        return jdOrderFullEsRepository.save(jdOrderFull);
    }

    @Override
    public List<JdOrderFullDTO> queryByOrderId(JdOrderIdParam param) {
        JdOrderIdContext jdOrderIdContext = convert2JdOrderIdContext(param);
        List<JdOrderFull> jdOrderFullList = jdOrderFullEsRepository.queryByOrderId(jdOrderIdContext);
        return convert2JdOrderFullDTOList(jdOrderFullList);
    }

    @Override
    public List<JdOrderFullDTO> queryByPromiseId(String promiseId) {
        List<JdOrderFull> jdOrderFullList = jdOrderFullEsRepository.queryByPromiseId(promiseId);
        return convert2JdOrderFullDTOList(jdOrderFullList);
    }

    @Override
    public JdOrderFullDTO getById(String id) {
        JdOrderFull jdOrderFull = jdOrderFullEsRepository.getById(id);
        return convert2JdOrderFullDTO(jdOrderFull);
    }

    @Override
    public void deleteById(List<String> orderEsIdList) {
        jdOrderFullEsRepository.deleteById(orderEsIdList);
    }

    /**
     * 重新加载完整订单信息
     *
     * @param promiseId 承诺ID
     */
    @Override
    public void reloadFullOrderInfo(Long promiseId) {
        if(Objects.nonNull(promiseId)){
            String lockKey = CacheConstant.RELOAD_FULL_ORDER_LOCK_KEY_PREFIX + promiseId;
            String exceptValue = UUID.randomUUID().toString();
            log.info("JdOrderFullApplicationImpl reloadFullOrderInfo lockKey:{} , exceptValue:{}", lockKey,exceptValue);
            try {
                boolean lock = redisLockUtil.tryLock(lockKey, exceptValue, NumConstant.NUM_5);
                if(lock){
                    PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
                    promiseIdRequest.setPromiseId(promiseId);
                    PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
                    log.info("JdOrderFullApplicationImpl.reloadFullOrderInfo promiseDto{}", JSON.toJSONString(promiseDto));
                    if(promiseDto == null){
                        log.info("JdOrderFullApplicationImpl.reloadFullOrderInfo promiseDto == null");
                        return;
                    }
                    MedicalPromiseListQuery query = new MedicalPromiseListQuery();
                    query.setPromiseId(promiseDto.getPromiseId());
                    List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(query);
                    log.info("JdOrderFullApplicationImpl.reloadFullOrderInfo medicalPromiseList{}", JSON.toJSONString(medicalPromiseList));
                    if(CollUtil.isEmpty(medicalPromiseList)){
                        log.info("JdOrderFullApplicationImpl.reloadFullOrderInfo medicalPromiseList为空");
                        return;
                    }
                    for(MedicalPromise medicalPromise: medicalPromiseList){

                        JdOrderFull byId = jdOrderFullEsRepository.getById(medicalPromise.getMedicalPromiseId().toString());
                        JdOrderFullSaveCmd cmd = new JdOrderFullSaveCmd();
                        //id
                        cmd.setId(medicalPromise.getMedicalPromiseId().toString());
                        cmd.setFullType(NumConstant.NUM_2.toString());

                        //业务身份
                        cmd.setVerticalCode(medicalPromise.getVerticalCode());
                        cmd.setServiceType(medicalPromise.getServiceType());
                        cmd.setUserPin(medicalPromise.getUserPin());

                        //=======>>>>检测单
                        cmd.setMedicalPromiseId(medicalPromise.getMedicalPromiseId().toString());
                        cmd.setSpecimenCode(medicalPromise.getSpecimenCode());
                        cmd.setMedicalPromiseStatus(medicalPromise.getStatus().toString());
                        cmd.setAngelStationId(medicalPromise.getAngelStationId());
                        cmd.setAngelStationName(medicalPromise.getAngelStationName());
                        //采样时间
                        fillCollectTime(cmd);
                        cmd.setMedPromiseFreeze(medicalPromise.getFreeze().toString());
                        cmd.setServiceId(medicalPromise.getServiceId());
                        cmd.setPromiseId(medicalPromise.getPromiseId().toString());
                        cmd.setPromisePatientId(medicalPromise.getPromisePatientId().toString());
                        cmd.setServiceItemId(medicalPromise.getServiceItemId());
                        cmd.setServiceItemName(medicalPromise.getServiceItemName());
                        cmd.setReportStatus(medicalPromise.getReportStatus());
                        cmd.setCheckStatus(Objects.nonNull(medicalPromise.getCheckTime()) ? CommonConstant.ONE : CommonConstant.ZERO);
                        cmd.setCheckTime(medicalPromise.getCheckTime());
                        cmd.setCheckStatus(medicalPromise.getCheckStatus());
                        cmd.setReportTime(medicalPromise.getReportTime());
                        cmd.setSerialNum(medicalPromise.getSerialNum());
                        cmd.setOrderCreateTime(medicalPromise.getCreateTime());
                        cmd.setWaitingTestTimeOutDate(medicalPromise.getWaitingTestTimeOutDate());
                        cmd.setWaitingTestTimeOutStatus(medicalPromise.getWaitingTestTimeOutStatus());
                        cmd.setTestingTimeOutDate(medicalPromise.getTestingTimeOutDate());
                        cmd.setTestingTimeOutStatus(medicalPromise.getTestingTimeOutStatus());
                        cmd.setFlowCode(medicalPromise.getFlowCode());
                        cmd.setSubStatus(medicalPromise.getSubStatus());
                        cmd.setExceptionRecord(medicalPromise.getExceptionRecord());
                        cmd.setTestTime(medicalPromise.getTestTime());
                        cmd.setTestFinishTime(medicalPromise.getTestFinishTime());
                        cmd.setTestStatus(medicalPromise.getTestStatus());
                        //=======>>>>promise
                        fillPromise(cmd);
                        //补全患者信息
                        fillPatientInfo(cmd);

                        //=======>>>>订单
                        fillOrderDTO(promiseDto.getSourceVoucherId(), cmd);

                        //=======>>>>实验室信息
                        cmd.setProviderId(Objects.isNull(medicalPromise.getProviderId()) ? "" : medicalPromise.getProviderId().toString());
                        cmd.setLaboratoryStationId(medicalPromise.getStationId());
                        cmd.setLaboratoryStationDetail(medicalPromise.getStationAddress());
                        cmd.setLaboratoryStationName(medicalPromise.getStationName());
                        cmd.setLaboratoryStationPhone(medicalPromise.getStationPhone());

                        //work & angel & task
                        fillWorkInfo(cmd);
                        //如果有工单，组装运单
                        if (StringUtils.isNotBlank(cmd.getWorkId())){
                            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
                            angelShipDBQuery.setWorkId(Long.valueOf(cmd.getWorkId()));
                            List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
                            if (CollectionUtils.isNotEmpty(angelShipList)){
                                //根据配送地址匹配运单
                                AngelShip angelShip = angelShipList.stream().filter(p -> StringUtils.equals(p.getReceiverId(), medicalPromise.getStationId())).findFirst().orElse(null);
                                if (Objects.nonNull(angelShip)){
                                    cmd.setShipStatus(angelShip.getShipStatus());
                                    cmd.setShipId(angelShip.getShipId());
                                    cmd.setOutShipId(angelShip.getOutShipId());
                                    AngelShipHistoryDBQuery build = AngelShipHistoryDBQuery.builder().workId(Long.valueOf(cmd.getWorkId())).shipIds(Sets.newHashSet(Collections.singleton(angelShip.getShipId()))).build();
                                    List<AngelShipHistory> shipHistories = angelShipHistoryRepository.findList(build);
                                    if (CollectionUtils.isNotEmpty(shipHistories)){
                                        AngelShipHistory angelShipHistory = shipHistories.stream().filter(p -> Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(), p.getAfterStatus())).findFirst().orElse(null);
                                        if (Objects.nonNull(angelShipHistory)){
                                            cmd.setDeliveryStoreTime(angelShipHistory.getCreateTime());
                                        }
                                    }
                                    cmd.setSendCode(angelShip.getSendCode());
                                    cmd.setFinishCode(angelShip.getFinishCode());
                                }
                            }
                        }
                        //如果原来的为空，或是运单状态为空
                        if (Objects.isNull(byId) || Objects.isNull(byId.getShipStatus())){
                            //如果现在没有运单状态
                            if (Objects.isNull(cmd.getShipStatus())){
                                //如果现在是待收养，则运单状态是配送中
                                if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),Integer.valueOf(cmd.getMedicalPromiseStatus()))){
                                    cmd.setShipStatus(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus());
                                }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),Integer.valueOf(cmd.getMedicalPromiseStatus()))){
                                    //如果现在是检测中，则运单状态是已送达
                                    cmd.setShipStatus(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());
                                    cmd.setDeliveryStoreTime(new Date());
                                }
                            }
                        }

                        //如果原来的不为空，并且原来的运单状态不为空
                        if (Objects.nonNull(byId) && Objects.nonNull(byId.getShipStatus())){
                            //如果现在是检测中，但运单状态不是已送达，则改为已送达
                            if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),Integer.valueOf(cmd.getMedicalPromiseStatus())) &&
                                    !Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),byId.getShipStatus())){
                                cmd.setShipStatus(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());
                                cmd.setDeliveryStoreTime(new Date());
                            }
                        }

                        //检测时效(报告出具-接收时间)
                        if (Objects.nonNull(cmd.getReportTime()) && Objects.nonNull(cmd.getCheckTime())){
                            long reportIntervalTime = DateUtil.between(cmd.getCheckTime(), cmd.getReportTime(), DateUnit.SECOND);
                            cmd.setReportIntervalTime(reportIntervalTime);
                        }
                        //报告出具时效(报告出具-送达时间)
                        if (Objects.nonNull(cmd.getDeliveryStoreTime()) && Objects.nonNull(cmd.getReportTime())){
                            long betweenDeliveryToReportTime = DateUtil.between(cmd.getDeliveryStoreTime(), cmd.getReportTime(), DateUnit.SECOND);
                            cmd.setBetweenDeliveryToReportTime(betweenDeliveryToReportTime);
                        }
                        log.info("JdOrderFullApplicationImpl -> byId :{}",JSON.toJSONString(byId));
                        log.info("JdOrderFullApplicationImpl -> cmd :{}",JSON.toJSONString(cmd));




                        //如果存在项目ID和对应实验室，查询实验室对应项目检测时间
                        if (Objects.nonNull(cmd.getServiceItemId()) && StringUtils.isNotBlank(cmd.getProviderId()) && StringUtils.isNotBlank(cmd.getLaboratoryStationId())){
                            List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(JdhStationServiceItemRelRequest.builder().serviceItemId(Long.valueOf(cmd.getServiceItemId())).build());
                            if (CollectionUtils.isNotEmpty(jdhStationServiceItemRelDtos)) {
                                JdhStationServiceItemRelDto jdhStationServiceItemRelDto = jdhStationServiceItemRelDtos.stream().filter(p -> StringUtils.equals(p.getStationId(), cmd.getLaboratoryStationId())).findFirst().orElse(null);
                                if (Objects.nonNull(jdhStationServiceItemRelDto)) {
                                    //分钟
                                    Integer testDuration = jdhStationServiceItemRelDto.getTestDuration();
                                    if (Objects.nonNull(testDuration)){
                                        cmd.setInspectDuration((long) (testDuration * 60 ));
                                    }else {
                                        //默认240分钟
                                        cmd.setInspectDuration((long) (240*60));
                                    }
                                }
                            }

                            if (StringUtils.isNotBlank(cmd.getLaboratoryStationId()) && (byId==null||Objects.isNull(byId.getDetectingNodesNum())) ){
                                ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(cmd.getLaboratoryStationId(), Long.valueOf(cmd.getServiceItemId()));
                                if (Objects.nonNull(providerEquipmentDto)){
                                    cmd.setDetectingNodesNum(providerEquipmentDto.getDetectingNodesNum());
                                }
                            }

                        }

                        if (Objects.nonNull(cmd.getDeliveryStoreTime()) && Objects.nonNull(cmd.getInspectDuration())){
                            log.info("setExpiredCheckTime,date={},offset={}",cmd.getDeliveryStoreTime(),cmd.getInspectDuration());
                            cmd.setExpiredCheckTime(DateUtil.offsetSecond(cmd.getDeliveryStoreTime(), Integer.parseInt(cmd.getInspectDuration().toString())).toJdkDate());
                            log.info("setExpiredCheckTime,expiredCheckTime={}",cmd.getExpiredCheckTime());

                        }
                        if (Objects.isNull(cmd.getExpiredCheckTime())) {
                            cmd.setExpiredCheckTime(DateUtil.parseDate("2099-01-01"));
                        }
                        if (Objects.isNull(cmd.getInspectDuration())){
                            cmd.setInspectDuration((long) (240*60));
                        }

                        this.save(cmd);
                    }
                }else{
                    log.error("JdOrderFullApplicationImpl reloadFullOrderInfo 获取分布式锁失败，重试");
                    throw new RuntimeException("数据同步es 获取分布式锁失败，重试");
                }
            }catch (Exception e){
                log.error("JdOrderFullApplicationImpl reloadFullOrderInfo exception",e);
                throw e;
            }finally {
                redisLockUtil.unLock(lockKey,exceptValue);
            }
        }
    }


    /**
     * 补全C端履约单信息，并返回履约单
     *
     * @param cmd cmd
     * @return {@link PromiseDto}
     */
    @Override
    public PromiseDto fillPromise(JdOrderFullSaveCmd cmd) {
        if (StrUtil.isBlank(cmd.getPromiseId())) {
            return null;
        }
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(new Long(cmd.getPromiseId()));
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        log.info("JdOrderFullApplicationImpl fillPromise promiseIdRequest={} promiseDto={}", JSON.toJSONString(promiseIdRequest), JSON.toJSONString(promiseDto));
        if (promiseDto == null) {
            return null;
        }
        cmd.setVoucherId(promiseDto.getVoucherId().toString());
        if (promiseDto.getVoucherId() != null) {
            cmd.setVoucherId(promiseDto.getVoucherId().toString());
        }
        if (promiseDto.getPromiseStatus() != null) {
            cmd.setPromiseStatus(promiseDto.getPromiseStatus().toString());
        }
        if (promiseDto.getAppointmentTime() != null) {
            if (promiseDto.getAppointmentTime().getAppointmentStartTime() != null) {
                cmd.setAppointmentStartTime(promiseDto.getAppointmentTime().getAppointmentStartTime());
            }
            if (promiseDto.getAppointmentTime().getAppointmentEndTime() != null) {
                cmd.setAppointmentEndTime(promiseDto.getAppointmentTime().getAppointmentEndTime());
            }
            if (promiseDto.getAppointmentTime().getDateType() != null) {
                cmd.setDateType(promiseDto.getAppointmentTime().getDateType().toString());
            }
            if (promiseDto.getAppointmentTime().getIsImmediately() != null) {
                cmd.setIsImmediately(promiseDto.getAppointmentTime().getIsImmediately().toString());
            }
        }

        if (promiseDto.getStore() != null){
            cmd.setProvinceCode(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getProvinceCode));
            cmd.setProvinceName(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getProvinceName));
            cmd.setCityCode(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getCityCode));
            cmd.setCityName(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getCityName));
            cmd.setDistrictCode(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getDistrictCode));
            cmd.setDistrictName(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getDistrictName));
            cmd.setTownCode(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getTownCode));
            cmd.setTownName(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getTownName));
            cmd.setStoreAddr(EntityUtil.getFiledDefaultNull(promiseDto.getStore(), PromiseStationDto::getStoreAddr));
        }
        return promiseDto;
    }


    /**
     * 补全订单数据
     *
     * @param orderId 订单 ID
     * @param cmd     cmd
     */
    @Override
    public void fillOrderDTO(String orderId, JdOrderFullSaveCmd cmd) {
        if (StrUtil.isBlank(orderId)) {
            return;
        }
        OrderDetailParam orderDetailParam = new OrderDetailParam();
        orderDetailParam.setOrderId(orderId);
        JdOrderDTO orderDetail = tradeApplication.getOrderDetail(orderDetailParam);
        if(Objects.isNull(orderDetail) && VerticalEnum.isNonOrderVertical(cmd.getVerticalCode())){
            cmd.setOrderId(orderId);
            cmd.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode().toString());
            return;
        }
        log.info("JdOrderFullApplicationImpl fillOrderDTO param={}  orderDetail={}", JSON.toJSONString(orderDetailParam), JSON.toJSONString(orderDetail));
        //如果是拆单，需要查询对应的子订单
        if (OrderStatusEnum.ORDER_SPLIT.getStatus().equals(orderDetail.getOrderStatus())) {
            JdOrder jdOrder = new JdOrder();
            jdOrder.setParentId(new Long(orderId));
            //查询子订单
            List<JdOrder> subJdOrderList = jdOrderRepository.findOrderListByParentId(jdOrder);
            log.info("JdOrderFullApplicationImpl fillOrderDTO subJdOrderList={}", JSON.toJSONString(subJdOrderList));
            if (CollUtil.isNotEmpty(subJdOrderList)) {
                Map<Long, JdOrder> subOrderMap = subJdOrderList.stream().collect(Collectors.toMap(JdOrder::getOrderId, ele -> ele));
                //根据orderId 查orderItem
                List<Long> orderIdList = subJdOrderList.stream().map(JdOrder::getOrderId).collect(Collectors.toList());
                List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.listByOrderIdList(orderIdList);
                for (JdOrderItem jdOrderItem : jdOrderItemList) {
                    if (jdOrderItem.getSkuId() == null) {
                        continue;
                    }
                    if (jdOrderItem.getSkuId().toString().equals(cmd.getServiceId())) {
                        cmd.setOrderId(jdOrderItem.getOrderId().toString());
                        cmd.setOrderStatus(subOrderMap.get(jdOrderItem.getOrderId()).getOrderStatus().toString());
                    }
                }
            }
            cmd.setParentOrderId(orderId);
        } else {
            cmd.setOrderId(orderDetail.getOrderId().toString());
            cmd.setParentOrderId(orderDetail.getParentId().toString());
            cmd.setOrderStatus(orderDetail.getOrderStatus().toString());
        }
        cmd.setUserPin(orderDetail.getUserPin());
        cmd.setOrderCreateTime(orderDetail.getCreateTime());
        if (orderDetail.getPartnerSource() != null) {
            cmd.setPartnerSource(orderDetail.getPartnerSource().toString());
        }
        cmd.setServiceType(orderDetail.getServiceType());
        cmd.setVerticalCode(orderDetail.getVerticalCode());

        //orderType
        cmd.setOrderType(Objects.isNull(orderDetail.getOrderType()) ? null : orderDetail.getOrderType().toString());


        //wareType
        String extend = orderDetail.getExtend();
        if(StrUtil.isNotBlank(extend)){
            JdOrderExtendVo orderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
            if(Objects.nonNull(orderExtendVo)){
                cmd.setWareType(orderExtendVo.getWareType());
            }
        }

        //是否包含加项
        cmd.setIncludeAddSku(orderDetail.getHasAdded());

    }


    /**
     * 补全患者信息
     *
     * @param cmd null
     */
    @Override
    public void fillPatientInfo(JdOrderFullSaveCmd cmd) {
        log.info("JdOrderFullApplicationImpl fillPatientInfo cmd ={}", JSON.toJSONString(cmd));
        if (StrUtil.isBlank(cmd.getPromisePatientId())) {
            log.info("JdOrderFullApplicationImpl fillPatientInfo promisePatientId is null cmd ={}", JSON.toJSONString(cmd));
            return;
        }
        Long promisePatientId = new Long(cmd.getPromisePatientId());
        Set<Long> promisePatientIdSet = Sets.newHashSet();
        promisePatientIdSet.add(promisePatientId);
        List<PromisePatientDto> proList = promiseApplication.listPromisePatient(promisePatientIdSet);
        log.info("JdOrderFullApplicationImpl fillPatientInfo promisePatientIdSet ={} proList={}", JSON.toJSONString(promisePatientIdSet), JSON.toJSONString(proList));
        if (CollUtil.isEmpty(proList)) {
            log.info("JdOrderFullApplicationImpl fillPatientInfo proList isEmpty cmd ={}", JSON.toJSONString(cmd));
            return;
        }
        PromisePatientDto dto = proList.get(0);
        if(dto.getPatientId() != null){
            cmd.setPatientId(dto.getPatientId().toString());
        }
        if(dto.getMarriage() != null){
            cmd.setMarriage(dto.getMarriage().toString());
        }
        if(dto.getGender() != null){
            cmd.setUserGender(dto.getGender().toString());
        }
        if(dto.getBirthday() != null){
            cmd.setBirthday(dto.getBirthday().getBirth());
        }
        if(dto.getRelativesType() != null){
            cmd.setRelativesType(dto.getRelativesType().toString());
        }
        if(dto.getUserName() != null){
            cmd.setUserName(dto.getUserName().getName());
            cmd.setUserNameIndex(dto.getUserName().getName());
        }
        if(dto.getPhoneNumber() != null){
            cmd.setUserPhone(dto.getPhoneNumber().getPhone());
            cmd.setUserPhoneIndex(dto.getPhoneNumber().getPhone());
        }
        if(dto.getCredentialNum() != null){
            if(dto.getCredentialNum().getCredentialType() != null){
                cmd.setUserCredentialType(dto.getCredentialNum().getCredentialType().toString());
            }
            cmd.setUserCredentialNo(dto.getCredentialNum().getCredentialNo());
            cmd.setUserCredentialNoIndex(dto.getCredentialNum().getCredentialNo());
        }
    }

    /**
     * 补全检测时间
     *
     * @param cmd null
     */
    @Override
    public void fillCollectTime(JdOrderFullSaveCmd cmd) {
        MedPromiseHistoryRequest request = new MedPromiseHistoryRequest();
        request.setMedicalPromiseId(new Long(cmd.getMedicalPromiseId()));
        List<MedPromiseHistoryDTO> medPromiseHistoryDTOList = medPromiseHistoryApplication.queryMedPromiseHistoryList(request);
        log.info("JdOrderFullApplicationImpl fillCollectTime -> request={} medPromiseHistoryDTOList={}", JSON.toJSONString(request), JSON.toJSONString(medPromiseHistoryDTOList));
        if (CollUtil.isNotEmpty(medPromiseHistoryDTOList)) {
            //取检测时间
            for (MedPromiseHistoryDTO ele : medPromiseHistoryDTOList) {
                if(MedicalPromiseStatusEnum.COLLECTED.getStatus().equals(ele.getAfterStatus())){
                    cmd.setCollectionTime(ele.getCreateTime());
                    break;
                }
            }

            //取服务完成时间
            for (MedPromiseHistoryDTO ele : medPromiseHistoryDTOList) {
                if(MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getAfterStatus())){
                    cmd.setCompleteTime(ele.getCreateTime());
                    break;
                }
            }
        }

    }


    /**
     * fillWorkInfo
     *
     * @param cmd null
     */
    @Override
    public void fillWorkInfo(JdOrderFullSaveCmd cmd){
        AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
        angelWorkQuery.setPromiseId(new Long(cmd.getPromiseId()));
        angelWorkQuery.setWorkStatus(AngelWorkStatusEnum.getWorkStatusAll());
        log.info("JdOrderFullApplicationImpl fillWorkInfo queryWorkListOrRecently angelWorkQuery={}", JSON.toJSONString(angelWorkQuery));
        //查询promise对应的有效work
        AngelWorkDetailDto angelWorkDetailDto = angelPromiseApplication.queryWorkListOrRecently(angelWorkQuery);
        log.info("JdOrderFullApplicationImpl fillWorkInfo  , queryWorkListOrRecently angelWorkDetailDto={}", JSON.toJSONString(angelWorkDetailDto));
        if(Objects.isNull(angelWorkDetailDto)){
            log.info("JdOrderFullApplicationImpl fillWorkInfo  , angelWorkDetailDto isNull  promiseId={}", cmd.getPromiseId());
            return;
        }
        cmd.setWorkId(angelWorkDetailDto.getWorkId().toString());
        if(angelWorkDetailDto.getStatus() != null){
            cmd.setWorkStatus(angelWorkDetailDto.getStatus().toString());
        }
        if(angelWorkDetailDto.getWorkType() != null){
            cmd.setWorkType(angelWorkDetailDto.getWorkType().toString());
        }
        cmd.setAngelId(angelWorkDetailDto.getAngelId());
        cmd.setAngelPin(angelWorkDetailDto.getAngelPin());
        cmd.setAngelName(angelWorkDetailDto.getAngelName());
        cmd.setAngelPhone(angelWorkDetailDto.getAngelPhone());

        //补全是否上传状态
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setWorkIds(Collections.singletonList(angelWorkDetailDto.getWorkId()));
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        log.info("JdOrderFullApplicationImpl fillWorkInfo  angelWorkDBQuery={} angelWork={}", JSON.toJSONString(angelWorkDBQuery), JSON.toJSONString(angelWork));
        if(angelWork != null && angelWork.getJdhAngelWorkExtVo() != null && angelWork.getJdhAngelWorkExtVo().getComplete() != null) {
            cmd.setUploadServiceRecord(angelWork.getJdhAngelWorkExtVo().getComplete().toString());
        }
        //根据workId查询task
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelWorkDetailDto.getWorkId());
        List<AngelTask> taskList = angelTaskRepository.findList(angelTaskDBQuery);
        log.info("JdOrderFullApplicationImpl fillWorkInfo  , taskList{}", JSON.toJSONString(taskList));
        if(CollUtil.isEmpty(taskList)){
            log.info("JdOrderFullApplicationImpl fillWorkInfo  , taskList isEmpty  promiseId={} WorkId={}", cmd.getPromiseId(), angelWorkDetailDto.getWorkId());
            return;
        }
        AngelTask angelTask = taskList.stream().filter(ele -> ele.getPatientId().equals(cmd.getPromisePatientId())).findFirst().orElse(null);
        if(Objects.nonNull(angelTask)){
            cmd.setTaskId(angelTask.getTaskId().toString());
            if(angelTask.getTaskStatus() != null){
                cmd.setTaskStatus(angelTask.getTaskStatus().toString());
            }
            if(angelTask.getBizExtStatus() != null){
                cmd.setTaskBizExtStatus(angelTask.getBizExtStatus().toString());
            }
        }
    }

    /**
     * 预约中心同步运单信息到es
     *
     * @param syncAngelShipEsCmd
     * @return
     */
    @Override
    public Boolean syncShipStatusToEs(SyncAngelShipEsCmd syncAngelShipEsCmd) {
        JdOrderIdParam param = new JdOrderIdParam();
        param.setOrderId(String.valueOf(syncAngelShipEsCmd.getWorkId()));
        List<JdOrderFullDTO> jdOrderFullDTOS = queryByOrderId(param);
        log.info("JdOrderFullApplicationImpl syncShipStatusToEs syncAngelShipEsCmd={}  jdOrderFullDTOS={} ", JSON.toJSONString(syncAngelShipEsCmd), JSON.toJSONString(jdOrderFullDTOS));
        if (CollectionUtils.isEmpty(jdOrderFullDTOS)){
            return Boolean.FALSE;
        }

        for (JdOrderFullDTO jdOrderFullDTO : jdOrderFullDTOS){
            JdOrderFullSaveCmd jdOrderFullSaveCmd = new JdOrderFullSaveCmd();
            jdOrderFullSaveCmd.setId(jdOrderFullDTO.getId());
            jdOrderFullSaveCmd.setShipStatus(syncAngelShipEsCmd.getShipStatus());
            //TODO 状态机
            if (Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),syncAngelShipEsCmd.getShipStatus())){
                if (Objects.nonNull(syncAngelShipEsCmd.getUpdateTime())){
                    jdOrderFullSaveCmd.setDeliveryStoreTime(syncAngelShipEsCmd.getUpdateTime());
                }else {
                    jdOrderFullSaveCmd.setDeliveryStoreTime(new Date());
                }
            }
            save(jdOrderFullSaveCmd);
        }
        return Boolean.TRUE;

    }

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @Override
    public List<JdOrderFullDTO> scrollQuery(JdOrderFullPageParam param) {
        JdOrderFullPageContext jdOrderFullPageContext = convert2JdOrderFullPageContext(param);
        log.info("JdOrderFullApplicationImpl->scrollQuery,jdOrderFullPageContext={}", JSON.toJSONString(jdOrderFullPageContext));
        List<JdOrderFull> jdOrderFullList = jdOrderFullEsRepository.scrollQuery(jdOrderFullPageContext);
        fillStatusDeac(jdOrderFullList);
        return convert2JdOrderFullDTOList(jdOrderFullList);
    }

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @Override
    public Boolean export(JdOrderFullPageParam param) {
        //1、构建上下文
        Map<String, Object> ctx = cn.hutool.core.bean.BeanUtil.beanToMap(param);
        ctx.put("scene", FileExportTypeEnum.MEDICAL_PROMISE_EXPORT.getType());
        ctx.put("userPin", LoginContext.getLoginContext().getPin());
        ctx.put("queryUserPin", param.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.MEDICAL_PROMISE_EXPORT.getType());
        return fileManageApplication.export(ctx);
    }

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @Override
    public Long queryCount(JdOrderFullPageParam param) {
        JdOrderFullPageContext jdOrderFullPageContext = convert2JdOrderFullPageContext(param);
        return jdOrderFullEsRepository.queryCount(jdOrderFullPageContext);
    }

    private PageDto<JdOrderFullDTO> convert2JdOrderFullDTOPage(PageDto<JdOrderFull> jdOrderFullPage) {
        PageDto<JdOrderFullDTO> resultPage = new PageDto<>();
        resultPage.setPageNum(jdOrderFullPage.getPageNum());
        resultPage.setPageSize(jdOrderFullPage.getPageSize());
        resultPage.setTotalPage(jdOrderFullPage.getTotalPage());
        resultPage.setTotalCount(jdOrderFullPage.getTotalCount());
        List<JdOrderFullDTO> dtoList = convert2JdOrderFullDTOList(jdOrderFullPage.getList());
        resultPage.setList(dtoList);
        return resultPage;
    }
    private JdOrderFullPageContext convert2JdOrderFullPageContext(JdOrderFullPageParam param) {
        if (param == null) {
            return null;
        }
        JdOrderFullPageContext jdOrderFullPageContext = new JdOrderFullPageContext();
        jdOrderFullPageContext.setPartnerSource(param.getPartnerSource());
        jdOrderFullPageContext.setVerticalCode(param.getVerticalCode());
        jdOrderFullPageContext.setServiceType(param.getServiceType());
        jdOrderFullPageContext.setOrderId(param.getOrderId());
        jdOrderFullPageContext.setMedicalPromiseId(param.getMedicalPromiseId());
        jdOrderFullPageContext.setSpecimenCode(param.getSpecimenCode());
        jdOrderFullPageContext.setTaskBizExtStatus(param.getTaskBizExtStatus());
        jdOrderFullPageContext.setUserPin(param.getUserPin());
        jdOrderFullPageContext.setCommonStatus(param.getCommonStatus());
        jdOrderFullPageContext.setAngelName(param.getAngelName());
        jdOrderFullPageContext.setUserPhone(param.getUserPhone());
        jdOrderFullPageContext.setLaboratoryStationName(param.getLaboratoryStationName());
        jdOrderFullPageContext.setPromiseId(param.getPromiseId());
        if(param.getOrderCreateTimeStart() != null){
            jdOrderFullPageContext.setOrderCreateTimeStart(new Date(param.getOrderCreateTimeStart()));
        }
        if(param.getOrderCreateTimeEnd() != null) {
            jdOrderFullPageContext.setOrderCreateTimeEnd(new Date(param.getOrderCreateTimeEnd()));
        }
        if(param.getAppointmentStartTime() != null) {
            jdOrderFullPageContext.setAppointmentStartTime(new Date(param.getAppointmentStartTime()));
        }
        if(param.getAppointmentEndTime() != null) {
            jdOrderFullPageContext.setAppointmentEndTime(new Date(param.getAppointmentEndTime()));
        }
        if(param.getCheckStartTime() != null) {
            jdOrderFullPageContext.setCheckStartTime(new Date(param.getCheckStartTime()));
        }
        if(param.getCheckEndTime() != null) {
            jdOrderFullPageContext.setCheckEndTime(new Date(param.getCheckEndTime()));
        }
        jdOrderFullPageContext.setPageNum(param.getPageNum());
        jdOrderFullPageContext.setPageSize(param.getPageSize());
        jdOrderFullPageContext.setElementCode(param.getElementCode());
        jdOrderFullPageContext.setAppName(param.getAppName());
        jdOrderFullPageContext.setClientIp(param.getClientIp());
        jdOrderFullPageContext.setAccessKey(param.getAccessKey());
        jdOrderFullPageContext.setVerticalCode(param.getVerticalCode());
        jdOrderFullPageContext.setServiceId(param.getServiceId());
        jdOrderFullPageContext.setServiceType(param.getServiceType());
        jdOrderFullPageContext.setEnvType(param.getEnvType());
        jdOrderFullPageContext.setUserPin(param.getUserPin());
        jdOrderFullPageContext.setUnpl(param.getUnpl());
        jdOrderFullPageContext.setUa(param.getUa());
        jdOrderFullPageContext.setOpenId(param.getOpenId());
        jdOrderFullPageContext.setOrderType(param.getOrderType());
        jdOrderFullPageContext.setWareType(param.getWareType());
        jdOrderFullPageContext.setIncludeAddSku(param.getIncludeAddSku());
        jdOrderFullPageContext.setStationId(param.getStationId());
        jdOrderFullPageContext.setMedicalPromiseStatus(param.getMedicalPromiseStatus());
        jdOrderFullPageContext.setOutShipId(param.getOutShipId());
        jdOrderFullPageContext.setServiceItemName(param.getServiceItemName());
        return jdOrderFullPageContext;
    }

    private JdOrderFullDTO convert2JdOrderFullDTO(JdOrderFull jdOrderFull) {
        if (jdOrderFull == null) {
            return null;
        }
        JdOrderFullDTO jdOrderFullDTO = new JdOrderFullDTO();
        jdOrderFullDTO.setId(jdOrderFull.getId());
        jdOrderFullDTO.setFullType(jdOrderFull.getFullType());
        jdOrderFullDTO.setOrderId(jdOrderFull.getOrderId());
        jdOrderFullDTO.setParentOrderId(jdOrderFull.getParentOrderId());
        jdOrderFullDTO.setOrderStatus(jdOrderFull.getOrderStatus());
        jdOrderFullDTO.setOrderCreateTime(jdOrderFull.getOrderCreateTime());
        jdOrderFullDTO.setPartnerSource(jdOrderFull.getPartnerSource());
        jdOrderFullDTO.setPartnerSourceOrderId(jdOrderFull.getPartnerSourceOrderId());
        jdOrderFullDTO.setVerticalCode(jdOrderFull.getVerticalCode());
        jdOrderFullDTO.setServiceType(jdOrderFull.getServiceType());
        jdOrderFullDTO.setUserPin(jdOrderFull.getUserPin());
        jdOrderFullDTO.setPromiseId(jdOrderFull.getPromiseId());
        jdOrderFullDTO.setVoucherId(jdOrderFull.getVoucherId());
        jdOrderFullDTO.setPromiseStatus(jdOrderFull.getPromiseStatus());
        jdOrderFullDTO.setAppointmentStartTime(jdOrderFull.getAppointmentStartTime());
        jdOrderFullDTO.setAppointmentEndTime(jdOrderFull.getAppointmentEndTime());
        jdOrderFullDTO.setDateType(jdOrderFull.getDateType());
        jdOrderFullDTO.setIsImmediately(jdOrderFull.getIsImmediately());
        jdOrderFullDTO.setMedicalPromiseId(jdOrderFull.getMedicalPromiseId());
        jdOrderFullDTO.setSpecimenCode(jdOrderFull.getSpecimenCode());
        jdOrderFullDTO.setMedicalPromiseStatus(jdOrderFull.getMedicalPromiseStatus());
        jdOrderFullDTO.setServiceItemId(jdOrderFull.getServiceItemId());
        jdOrderFullDTO.setServiceItemName(jdOrderFull.getServiceItemName());
        jdOrderFullDTO.setMaterialPackageId(jdOrderFull.getMaterialPackageId());
        jdOrderFullDTO.setMaterialPackageName(jdOrderFull.getMaterialPackageName());
        jdOrderFullDTO.setProviderId(jdOrderFull.getProviderId());
        jdOrderFullDTO.setLaboratoryStationId(jdOrderFull.getLaboratoryStationId());
        jdOrderFullDTO.setLaboratoryStationDetail(jdOrderFull.getLaboratoryStationDetail());
        jdOrderFullDTO.setLaboratoryStationName(jdOrderFull.getLaboratoryStationName());
        jdOrderFullDTO.setLaboratoryStationPhone(jdOrderFull.getLaboratoryStationPhone());
        jdOrderFullDTO.setWorkId(jdOrderFull.getWorkId());
        jdOrderFullDTO.setWorkStatus(jdOrderFull.getWorkStatus());
        jdOrderFullDTO.setWorkType(jdOrderFull.getWorkType());
        jdOrderFullDTO.setAngelId(jdOrderFull.getAngelId());
        jdOrderFullDTO.setAngelPin(jdOrderFull.getAngelPin());
        jdOrderFullDTO.setAngelName(jdOrderFull.getAngelName());
        jdOrderFullDTO.setAngelPhone(jdOrderFull.getAngelPhone());
        jdOrderFullDTO.setTaskId(jdOrderFull.getTaskId());
        jdOrderFullDTO.setTaskStatus(jdOrderFull.getTaskStatus());
        jdOrderFullDTO.setTaskBizExtStatus(jdOrderFull.getTaskBizExtStatus());
        jdOrderFullDTO.setPatientId(jdOrderFull.getPatientId());
        jdOrderFullDTO.setMarriage(jdOrderFull.getMarriage());
        jdOrderFullDTO.setUserGender(jdOrderFull.getUserGender());
        jdOrderFullDTO.setBirthday(jdOrderFull.getBirthday());
        jdOrderFullDTO.setRelativesType(jdOrderFull.getRelativesType());
        jdOrderFullDTO.setUserName(jdOrderFull.getUserName());
        jdOrderFullDTO.setUserNameIndex(jdOrderFull.getUserNameIndex());
        jdOrderFullDTO.setUserPhone(jdOrderFull.getUserPhone());
        jdOrderFullDTO.setUserPhoneIndex(jdOrderFull.getUserPhoneIndex());
        jdOrderFullDTO.setUserCredentialType(jdOrderFull.getUserCredentialType());
        jdOrderFullDTO.setUserCredentialNo(jdOrderFull.getUserCredentialNo());
        jdOrderFullDTO.setUserCredentialNoIndex(jdOrderFull.getUserCredentialNoIndex());
        jdOrderFullDTO.setCollectionTime(jdOrderFull.getCollectionTime());
        jdOrderFullDTO.setCompleteTime(jdOrderFull.getCompleteTime());
        jdOrderFullDTO.setPartnerSourceStr(jdOrderFull.getPartnerSourceStr());
        jdOrderFullDTO.setCommonStatusStr(jdOrderFull.getCommonStatusStr());
        jdOrderFullDTO.setServiceTypeStr(jdOrderFull.getServiceTypeStr());
        jdOrderFullDTO.setPromisePatientId(jdOrderFull.getPromisePatientId());
        jdOrderFullDTO.setOrderType(jdOrderFull.getOrderType());
        jdOrderFullDTO.setIncludeAddSku(jdOrderFull.getIncludeAddSku());
        jdOrderFullDTO.setWareType(jdOrderFull.getWareType());
        //实验室三期添加
        jdOrderFullDTO.setSendCode(jdOrderFull.getSendCode());
        jdOrderFullDTO.setFinishCode(jdOrderFull.getFinishCode());
        jdOrderFullDTO.setWaitingTestTimeOutDate(jdOrderFull.getWaitingTestTimeOutDate());
        jdOrderFullDTO.setWaitingTestTimeOutStatus(jdOrderFull.getWaitingTestTimeOutStatus());
        jdOrderFullDTO.setTestingTimeOutDate(jdOrderFull.getTestingTimeOutDate());
        jdOrderFullDTO.setTestingTimeOutStatus(jdOrderFull.getTestingTimeOutStatus());
        return jdOrderFullDTO;
    }

    private JdOrderFull convert2JdOrderFull(JdOrderFullSaveCmd cmd) {
        if (cmd == null) {
            return null;
        }
        JdOrderFull jdOrderFull = new JdOrderFull();
        jdOrderFull.setId(cmd.getId());
        jdOrderFull.setOrderId(cmd.getOrderId());
        jdOrderFull.setFullType(cmd.getFullType());
        jdOrderFull.setParentOrderId(cmd.getParentOrderId());
        jdOrderFull.setOrderStatus(cmd.getOrderStatus());
        jdOrderFull.setOrderCreateTime(cmd.getOrderCreateTime());
        jdOrderFull.setPartnerSource(cmd.getPartnerSource());
        jdOrderFull.setPartnerSourceOrderId(cmd.getPartnerSourceOrderId());
        jdOrderFull.setVerticalCode(cmd.getVerticalCode());
        jdOrderFull.setServiceType(cmd.getServiceType());
        jdOrderFull.setUserPin(cmd.getUserPin());
        jdOrderFull.setPromiseId(cmd.getPromiseId());
        jdOrderFull.setVoucherId(cmd.getVoucherId());
        jdOrderFull.setPromiseStatus(cmd.getPromiseStatus());
        jdOrderFull.setAppointmentStartTime(cmd.getAppointmentStartTime());
        jdOrderFull.setAppointmentEndTime(cmd.getAppointmentEndTime());
        jdOrderFull.setDateType(cmd.getDateType());
        jdOrderFull.setIsImmediately(cmd.getIsImmediately());
        jdOrderFull.setMedicalPromiseId(cmd.getMedicalPromiseId());
        jdOrderFull.setSpecimenCode(cmd.getSpecimenCode());
        jdOrderFull.setMedicalPromiseStatus(cmd.getMedicalPromiseStatus());
        jdOrderFull.setServiceItemId(cmd.getServiceItemId());
        jdOrderFull.setServiceItemName(cmd.getServiceItemName());
        jdOrderFull.setMaterialPackageId(cmd.getMaterialPackageId());
        jdOrderFull.setMaterialPackageName(cmd.getMaterialPackageName());
        jdOrderFull.setProviderId(cmd.getProviderId());
        jdOrderFull.setLaboratoryStationId(cmd.getLaboratoryStationId());
        jdOrderFull.setLaboratoryStationDetail(cmd.getLaboratoryStationDetail());
        jdOrderFull.setLaboratoryStationName(cmd.getLaboratoryStationName());
        jdOrderFull.setLaboratoryStationPhone(cmd.getLaboratoryStationPhone());
        jdOrderFull.setWorkId(cmd.getWorkId());
        jdOrderFull.setWorkStatus(cmd.getWorkStatus());
        jdOrderFull.setWorkType(cmd.getWorkType());
        jdOrderFull.setAngelId(cmd.getAngelId());
        jdOrderFull.setAngelPin(cmd.getAngelPin());
        jdOrderFull.setAngelName(cmd.getAngelName());
        jdOrderFull.setAngelPhone(cmd.getAngelPhone());
        jdOrderFull.setTaskId(cmd.getTaskId());
        jdOrderFull.setTaskStatus(cmd.getTaskStatus());
        jdOrderFull.setTaskBizExtStatus(cmd.getTaskBizExtStatus());
        jdOrderFull.setPatientId(cmd.getPatientId());
        jdOrderFull.setMarriage(cmd.getMarriage());
        jdOrderFull.setUserGender(cmd.getUserGender());
        jdOrderFull.setBirthday(cmd.getBirthday());
        jdOrderFull.setRelativesType(cmd.getRelativesType());
        jdOrderFull.setUserName(cmd.getUserName());
        jdOrderFull.setUserNameIndex(cmd.getUserNameIndex());
        jdOrderFull.setUserPhone(cmd.getUserPhone());
        jdOrderFull.setUserPhoneIndex(cmd.getUserPhoneIndex());
        jdOrderFull.setUserCredentialType(cmd.getUserCredentialType());
        jdOrderFull.setUserCredentialNo(cmd.getUserCredentialNo());
        jdOrderFull.setUserCredentialNoIndex(cmd.getUserCredentialNoIndex());
        jdOrderFull.setCollectionTime(cmd.getCollectionTime());
        jdOrderFull.setCompleteTime(cmd.getCompleteTime());
        jdOrderFull.setPromisePatientId(cmd.getPromisePatientId());
        jdOrderFull.setMedPromiseFreeze(cmd.getMedPromiseFreeze());
        jdOrderFull.setUploadServiceRecord(cmd.getUploadServiceRecord());
        jdOrderFull.setReportTime(cmd.getReportTime());
        jdOrderFull.setCheckStatus(cmd.getCheckStatus());
        jdOrderFull.setCheckTime(cmd.getCheckTime());
        jdOrderFull.setBetweenDeliveryToReportTime(cmd.getBetweenDeliveryToReportTime());
        jdOrderFull.setDeliveryStoreTime(cmd.getDeliveryStoreTime());
        jdOrderFull.setShipStatus(cmd.getShipStatus());
        jdOrderFull.setShipId(cmd.getShipId());
        jdOrderFull.setOutShipId(cmd.getOutShipId());
        jdOrderFull.setReportIntervalTime(cmd.getReportIntervalTime());
        jdOrderFull.setReportStatus(cmd.getReportStatus());
        jdOrderFull.setSerialNum(cmd.getSerialNum());
        jdOrderFull.setInspectDuration(cmd.getInspectDuration());
        jdOrderFull.setExpiredCheckTime(cmd.getExpiredCheckTime());
        jdOrderFull.setOrderType(cmd.getOrderType());
        jdOrderFull.setIncludeAddSku(cmd.getIncludeAddSku());
        jdOrderFull.setWareType(cmd.getWareType());
        jdOrderFull.setWaitingTestTimeOutDate(cmd.getWaitingTestTimeOutDate());
        jdOrderFull.setWaitingTestTimeOutStatus(cmd.getWaitingTestTimeOutStatus());
        jdOrderFull.setTestingTimeOutDate(cmd.getTestingTimeOutDate());
        jdOrderFull.setTestingTimeOutStatus(cmd.getTestingTimeOutStatus());
        jdOrderFull.setSendCode(cmd.getSendCode());
        jdOrderFull.setFinishCode(cmd.getFinishCode());

        jdOrderFull.setProvinceCode(cmd.getProvinceCode());
        jdOrderFull.setProvinceName(cmd.getProvinceName());
        jdOrderFull.setCityCode(cmd.getCityCode());
        jdOrderFull.setCityName(cmd.getCityName());
        jdOrderFull.setDistrictCode(cmd.getDistrictCode());
        jdOrderFull.setDistrictName(cmd.getDistrictName());
        jdOrderFull.setTownCode(cmd.getTownCode());
        jdOrderFull.setTownName(cmd.getTownName());
        jdOrderFull.setStoreAddr(cmd.getStoreAddr());
        jdOrderFull.setFlowCode(cmd.getFlowCode());
        jdOrderFull.setSubStatus(cmd.getSubStatus());
        jdOrderFull.setExceptionRecord(cmd.getExceptionRecord());
        jdOrderFull.setTestTime(cmd.getTestTime());
        jdOrderFull.setTestFinishTime(cmd.getTestFinishTime());
        jdOrderFull.setTestStatus(cmd.getTestStatus());
        jdOrderFull.setDetectingNodesNum(cmd.getDetectingNodesNum());

        return jdOrderFull;
    }

    private List<JdOrderFullDTO> convert2JdOrderFullDTOList(List<JdOrderFull> jdOrderFullList) {
        List<JdOrderFullDTO> resultList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(jdOrderFullList)){
            return resultList;
        }
        for(JdOrderFull jdOrderFull: jdOrderFullList){
            JdOrderFullDTO dto = convert2JdOrderFullDTO(jdOrderFull);
            resultList.add(dto);
        }
        return resultList;
    }

    private JdOrderIdContext convert2JdOrderIdContext(JdOrderIdParam param) {
        if (param == null) {
            return null;
        }
        JdOrderIdContext jdOrderIdContext = new JdOrderIdContext();
        jdOrderIdContext.setOrderId(param.getOrderId());
        jdOrderIdContext.setIncludes(param.getIncludes());
        return jdOrderIdContext;
    }

    /**
     * 由于ES docid为检验单id，会存在订单已落库但是未同步到ES情况，导致查询订单查不到
     * @param orderId
     * @return
     */
    private PageDto<JdOrderFull> queryOrderNotExistES(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        orderId = orderId.trim();
        JdOrderFullPageContext jdOrderFullPageContext = new JdOrderFullPageContext();
        jdOrderFullPageContext.setOrderId(orderId);
        PageDto<JdOrderFull> jdOrderFullPage = jdOrderFullEsRepository.queryPage(jdOrderFullPageContext);
        // 如果兜底查询ES已存在不再查下数据库或rpc
        if (jdOrderFullPage != null && jdOrderFullPage.getTotalCount() > 0) {
            return null;
        }
        JdOrder jdOrder = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(Long.parseLong(orderId)).build());
        if (jdOrder != null) {
            JdOrderFull jdOrderFull = new JdOrderFull();
            jdOrderFull.setVerticalCode(jdOrder.getVerticalCode());
            jdOrderFull.setServiceType(jdOrder.getServiceType());
            jdOrderFull.setOrderId(jdOrder.getOrderId().toString());
            String orderTypeDesc = orderTypeDicUtil.getOrderTypeDesc(jdOrder.getOrderType());
            jdOrderFull.setPartnerSourceStr("订单类型:" + jdOrder.getOrderType() + ",订单类型描述:" + (orderTypeDesc == null ? "未知" : orderTypeDesc));
            jdOrderFull.setUserPin(jdOrder.getUserPin());
            jdOrderFull.setId("0");
            jdOrderFull.setMedicalPromiseId("0");
            jdOrderFull.setCommonStatusStr("订单落库(未生成服务单)");
            jdOrderFull.setOrderCreateTime(jdOrder.getCreateTime());
            List<String> skuIdName = new ArrayList<>();
            List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.listByOrderId(Long.parseLong(orderId));
            if (CollUtil.isNotEmpty(jdOrderItemList)) {
                for(JdOrderItem sku : jdOrderItemList) {
                    skuIdName.add("[商品ID:" + sku.getSkuId() + ",商品名称:" + sku.getSkuName() + "]");
                }
            }
            jdOrderFull.setServiceItemName(Joiner.on(",").join(skuIdName));
            PageDto<JdOrderFull> jdOrderFullPageDto = new PageDto<>();
            jdOrderFullPageDto.setPageNum(1);
            jdOrderFullPageDto.setTotalPage(1);
            jdOrderFullPageDto.setPageSize(1);
            jdOrderFullPageDto.setTotalCount(1);
            jdOrderFullPageDto.setList(com.google.common.collect.Lists.newArrayList(jdOrderFull));
            return jdOrderFullPageDto;
        } else {
            // 1.查到订单大数据
            Map<String,Object> dataMap = orderInfoRpc.getOrderData(Long.parseLong(orderId));
            if (CollUtil.isEmpty(dataMap)) {
                log.info("TradeApplicationImpl handleGiftOrder 通过父订单查到订单大数据为空,不执行");
                return null;
            }
            String orderxml = Objects.toString(dataMap.get(FieldKeyEnum.V_ORDERXML.getFieldName()));
            String cartxml = Objects.toString(dataMap.get(FieldKeyEnum.V_CARTXML.getFieldName()));
            // 2.解压缩
            orderxml = com.jd.orderver.component.utils.ZipUtils.gunzip(orderxml);
            cartxml = com.jd.orderver.component.utils.ZipUtils.gunzip(cartxml);
            // 3.按照返回 serializationType 进行反序列化成对象
            String serializationType = Objects.toString(dataMap.get(FieldKeyEnum.V_CBDFLAG.getFieldName()));
            Order serializeOrder = SerializersHelper.ofString(orderxml, Order.class, serializationType);
            Cart serializeCart = SerializersHelper.ofString(cartxml, Cart.class, serializationType);
            if (serializeOrder == null) {
                return null;
            }
            JdOrderFull jdOrderFull = new JdOrderFull();
            jdOrderFull.setOrderId(String.valueOf(serializeOrder.getOrderId()));
            String orderTypeDesc = orderTypeDicUtil.getOrderTypeDesc(serializeOrder.getOrderType());
            jdOrderFull.setPartnerSourceStr("订单类型:" + serializeOrder.getOrderType() + ",订单类型描述:" + (orderTypeDesc == null ? "未知" : orderTypeDesc));
            jdOrderFull.setUserPin(serializeOrder.getPin());
            jdOrderFull.setId("0");
            jdOrderFull.setMedicalPromiseId("0");
            jdOrderFull.setCommonStatusStr("订单未落库(非到家服务订单或订单异常)");
            if (serializeOrder.getOrderType() == 0 && StringUtils.isNotBlank(serializeOrder.getSendPayMap())) {
                Map<String, String> sendPayDict = JSON.parseObject(serializeOrder.getSendPayMap(), new TypeReference< Map<String, String> >(){});
                if (CollUtil.isNotEmpty(sendPayDict) && sendPayDict.containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && sendPayDict.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equals(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())) {
                    jdOrderFull.setServiceTypeStr(ServiceTypeNewEnum.TRANSPORT_TEST.getDesc());
                }
            }
            jdOrderFull.setOrderCreateTime(serializeOrder.findCreateDate());
            List<String> skuIdName = new ArrayList<>();

            List<SKU> skuList = serializeCart.findAllSkus();
            if (CollUtil.isNotEmpty(skuList)) {
                for(SKU sku : skuList) {
                    skuIdName.add("[商品ID:" + sku.getId() + ",商品名称:" + sku.getName() + "]");
                }
            }
            jdOrderFull.setServiceItemName(Joiner.on(",").join(skuIdName));
            PageDto<JdOrderFull> jdOrderFullPageDto = new PageDto<>();
            jdOrderFullPageDto.setPageNum(1);
            jdOrderFullPageDto.setTotalPage(1);
            jdOrderFullPageDto.setPageSize(1);
            jdOrderFullPageDto.setTotalCount(1);
            jdOrderFullPageDto.setList(com.google.common.collect.Lists.newArrayList(jdOrderFull));
            return jdOrderFullPageDto;
        }
    }


    public static void main(String[] args) {
        String ids = "2703209646818079746, 2706240149974602754, 2701465555088626690, 2697021603967274498, 2696680119707194370, 2704777747968372226, 2702268825641946626, 2701410381938603522, 2701035938099858946, 2699245203025571330, 2710264663177827330, 2706341674409356802, 2706230838483348994, 2704314132018148866, 2701917600396391426, 2700376411511813122, 2697333882449352194, 2696019064110749186, 2685913615948615170, 2685213089602737154, 2706167925802284546, 2705813255993112066, 2700696309265904642, 2695529111421562370, 2684056162852434946, 2700444237635401218, 2696272355512028674, 2695900686222183938, 2623571890769236994, 2707309742857975298, 2701308350695493122, 2695969611857458690, 2695123297141558786, 2694386985128193538, 2691702235431237122, 2702562086008992770, 2689207091360453634, 2673889751924805122, 2707329044441060354, 2706686551693217282, 2701948060304455170, 2701401431226758146, 2693058672002799106, 2710220536683779586, 2710281868816788994, 2707725779160208386, 2694374701521851906, 2694344318923068930, 2693991349920738818, 2690797964426769922, 2706987190814019074, 2701885697379405314, 2697699882382152706, 2689254524979260930, 2698281085946582018, 2710725341370002946, 2701282357553435650, 2701148680991370754, 2700624385743590914, 2697092067200355842, 2695962559520994818, 2694835955239554562, 2684839779635311106, 2684293485565236226, 2684141057175891458, 2684082430872245250, 2683989556499493890, 2708134745945976322, 2708035566561242626, 2705095790296086018, 2704161884017448450, 2703616990106392066, 2703606269867994626, 2702155953901540866, 2701412787120387586, 2701291188006171650, 2692828152517989378, 2691839708744414722, 2691090322850592258, 2687303819552886274, 2710752227865179650, 2709590851528553986, 2707069559696790530, 2706477395375846402, 2705930018973816322, 2705605413935560194, 2704205185877579266, 2703545496080821762, 2702744141082694658, 2701373694327969282, 2699257332013026818, 2697255576605324290, 2697044204084843522, 2696932105440703490, 2696775880297969154, 2685733888747474434, 2684703972769419778, 2684226466895410178, 2683961845370328066, 2683636905324737026, 2707113127845155842, 2706491156451347970, 2705932338256244226, 2705691605339387394, 2704959974840208898, 2704768324809616898, 2703282798701221378, 2702946090444918274, 2701334154859262978, 2701283268086568450, 2699768295682354690, 2696285274773753346, 2693904711840536066, 2693408093361933826, 2693122563936097794, 2684142715033146370, 2708742853185605122, 2706551028295235074, 2706339810393470466, 2701286935988827650, 2699643063026103810, 2697582552465700354, 2689371348089721858, 2689277176636814338, 2689240566335689730, 2689226289864347138, 2688855170330160642, 2688830096311064066, 2685838488380648962, 2684142311306318338, 2683567885200380930, 2682540984289546754, 2682066605151638018, 2704819297481602562, 2701291626092844546, 2696183741746836994, 2696182676594912770, 2692343431098816002, 2691755329816899074, 2690261248133606914, 2690126411930373634, 2689986112528741890, 2707816325660765698, 2705672862102713858, 2705616314562515970, 2705451353458611714, 2705411556291704322, 2704314630234478082, 2691377733472179202, 2691098680856950274, 2691086130962568706, 2690012603886956034, 2689565618050446338, 2689497250761078274, 2685770559177954306, 2685611628208092162, 2709654021907592194, 2701282529352103426, 2696624147693458946, 2696595749369558530, 2694466742670896642, 2691392027123314178, 2687358142299337730, 2684151562665735170, 2681888106310897154, 2707346670987056642, 2706485435554633218, 2697439014658618370, 2693966739758206466, 2693218496325710850, 2691786640128480770, 2686299252472165890, 2683732768994643458, 2710779818735064066, 2710650428550286850, 2710255832725394434, 2707761401619059714, 2705812800726715906, 2702972272565670914, 2697452475086138882, 2694321890603832322, 2693201574154481154, 2692133982723789826, 2690951810155361794, 2690057220007136770, 2690006152845997570, 2689328295337555458, 2689167328553497602, 2707831839082419714, 2706365090571021826, 2704791964309927426, 2703370879890478082, 2701283706173518850, 2697519407856389634, 2695526663290269186, 2695082503542287874, 2694570749598914050, 2694370423734447618, 2693291261661580802, 2692403148324123650, 2692350766902989826, 2692350612284288514, 2690200087799411714, 2689316346738546178, 2701816892003388930, 2701285200821972482, 2694350555215605250, 2710735314284475394, 2709680616344916994, 2708591240840246274, 2708576955778821634, 2707717180635509762, 2707345245057846786, 2700396176951375362, 2699652288615552002, 2695854764432027138, 2694599457160312322, 2694519673847811586, 2694037331840620034, 2693292412712900098, 2691718332968736258, 2691718109630305794, 2691397662120390146, 2691368696860956162, 2690352988635018754, 2686555404321691138, 2708080663717794818, 2706979880779822082, 2705952954099215874, 2701478379860790786, 2692174114898128386, 2691631883867108354, 2684209175357174274, 2683976456849183746, 2515568452710911490, 2695159821543585282, 2687011461129074178, 2687011160481426946";
        System.out.println(ids.replace(", ", ","));
    }
}
