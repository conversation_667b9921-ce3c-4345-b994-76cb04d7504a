package com.jdh.o2oservice.application.medicalpromise.event;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.jsf.gd.util.JsonUtils;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseAggregateEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseCheckEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseHistory;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 检测单历史记录
 * @Author: wangpengfei144
 * @Date: 2024/5/3
 */
@Component
@Slf4j
public class MedPromiseHistorySubscriber {
    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 检测单历史仓储层
     */
    @Autowired
    private MedPromiseHistoryRepository medPromiseHistoryRepository;

    /**
     * 事件订阅
     */
    @PostConstruct
    public void registerEventConsumer() {

        //=======>>>>>> 检测单 监听创建检测单任务消息 -> 保存检测单创建历史记录
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_CREATE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseCreateHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单绑码
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseBindSpecimenCodeHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单提交
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseSubmitHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单收样
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseStationReceiveHistory", this::medPromiseCheckHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单出报告
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseCompleteHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));


        //=======>>>>>> 检测单完成
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseCompleteHistory", this::medPromiseCompleteHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单作废
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_INVALID, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseInvalidHistory", this::medPromiseInvalidHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单结算（天算）
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SETTLEMENT_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseInvalidHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 重置报告状态（可重传报告）
        eventConsumerRegister.register(MedPromiseEventTypeEnum.RESET_REPORT_STATUS, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseInvalidHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));
        //=======>>>>>> 换绑条码
        eventConsumerRegister.register(MedPromiseEventTypeEnum.RESET_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseInvalidHistory", this::saveMedPromiseHistory, Boolean.TRUE, Boolean.TRUE));
    }

    /**
     *
     * @param event
     */
    private void  saveMedPromiseHistory(Event event){
        log.info("MedPromiseHistorySubscriber->saveMedPromiseHistory,event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody medicalPromiseEventBody = JsonUtils.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedicalPromiseRepQuery medicalPromiseRepQuery = new MedicalPromiseRepQuery();
        medicalPromiseRepQuery.setMedicalPromiseId(medicalPromiseEventBody.getMedicalPromiseId());
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(medicalPromiseRepQuery);

        MedPromiseHistory medPromiseHistory = MedPromiseHistory.builder()
                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                .afterStatus(medicalPromiseEventBody.getStatus())
                .afterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseEventBody.getStatus()))
                .beforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseEventBody.getBeforeStatus()))
                .beforeStatus(medicalPromiseEventBody.getBeforeStatus())
                .extend(event.getBody())
                .version(event.getVersion())
                .providerId(medicalPromise.getProviderId())
                .operator(medicalPromise.getUpdateUser())
                .eventCode(event.getEventCode())
                .eventDesc(Objects.nonNull(MedPromiseEventTypeEnum.getByCode(event.getEventCode())) ? MedPromiseEventTypeEnum.getByCode(event.getEventCode()).getDesc() : "")
                .beforeSubStatus(medicalPromiseEventBody.getSubStatus())
                .afterSubStatus(medicalPromise.getSubStatus())
                .flowCode(medicalPromise.getFlowCode())
                .build();

        medPromiseHistoryRepository.save(medPromiseHistory);

    }

    /**
     * 作废
     * @param event
     */
    private void  medPromiseInvalidHistory(Event event){
        try {
            log.info("MedPromiseHistorySubscriber->medPromiseInvalidHistory,event={}", JSON.toJSONString(event));
            MedicalPromiseEventBody medicalPromiseEventBody = JsonUtils.parseObject(event.getBody(), MedicalPromiseEventBody.class);
            MedPromiseHistory medPromiseHistory = new MedPromiseHistory();
            MedicalPromiseRepQuery medicalPromiseRepQuery = new MedicalPromiseRepQuery();
            medicalPromiseRepQuery.setMedicalPromiseId(medicalPromiseEventBody.getMedicalPromiseId());
            MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(medicalPromiseRepQuery);

            if (Objects.isNull(medicalPromise)){
                log.info("MedPromiseHistorySubscriber->medPromiseInvalidHistory,medicalPromise is null,medicalPromiseId={}",medicalPromiseEventBody.getMedicalPromiseId());
                return;
            }

            medPromiseHistory.setAfterStatus(medicalPromiseEventBody.getStatus());
            medPromiseHistory.setAfterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseEventBody.getStatus()));
            medPromiseHistory.setBeforeStatus(medicalPromiseEventBody.getBeforeStatus());
            medPromiseHistory.setBeforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseEventBody.getBeforeStatus()));
            medPromiseHistory.setEventCode(event.getEventCode());
            medPromiseHistory.setEventDesc(Objects.nonNull(MedPromiseEventTypeEnum.getByCode(event.getEventCode()))?MedPromiseEventTypeEnum.getByCode(event.getEventCode()).getDesc():"");
            medPromiseHistory.setProviderId(medicalPromise.getProviderId());
            medPromiseHistory.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
            medPromiseHistory.setVersion(event.getVersion());
            medPromiseHistory.setExtend(JsonUtil.toJSONString(medicalPromise));
            medPromiseHistory.setOperator(medicalPromise.getUpdateUser());
            medPromiseHistoryRepository.save(medPromiseHistory);
        }catch (Exception e){
            log.error("MedPromiseHistorySubscriber->medPromiseInvalidHistory,error",e);
        }


    }

    /**
     * 收样
     * @param event
     */
    private void  medPromiseCheckHistory(Event event){
        log.info("MedPromiseHistorySubscriber->medPromiseCheckHistory,event={}", JSON.toJSONString(event));

        MedicalPromiseEventBody medicalPromiseEventBody = JsonUtils.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedPromiseHistory medPromiseHistory = new MedPromiseHistory();
        MedicalPromiseRepQuery medicalPromiseRepQuery = new MedicalPromiseRepQuery();
        medicalPromiseRepQuery.setMedicalPromiseId(medicalPromiseEventBody.getMedicalPromiseId());
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(medicalPromiseRepQuery);
        medPromiseHistory.setAfterStatus(medicalPromiseEventBody.getStatus());
        medPromiseHistory.setAfterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseEventBody.getStatus()));
        medPromiseHistory.setBeforeStatus(medicalPromiseEventBody.getBeforeStatus());
        medPromiseHistory.setBeforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseEventBody.getBeforeStatus()));
        medPromiseHistory.setEventCode(event.getEventCode());
        medPromiseHistory.setEventDesc(Objects.nonNull(MedPromiseEventTypeEnum.getByCode(event.getEventCode()))?MedPromiseEventTypeEnum.getByCode(event.getEventCode()).getDesc():"");
        medPromiseHistory.setProviderId(medicalPromise.getProviderId());
        medPromiseHistory.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medPromiseHistory.setVersion(event.getVersion());
        medPromiseHistory.setExtend(JsonUtil.toJSONString(medicalPromise));
        medPromiseHistory.setOperator(medicalPromise.getUpdateUser());
        medPromiseHistoryRepository.save(medPromiseHistory);

    }

    /**
     * 完成
     * @param event
     */
    private void  medPromiseCompleteHistory(Event event){
        log.info("MedPromiseHistorySubscriber->medPromiseCompleteHistory,event={}", JSON.toJSONString(event));
        MedicalPromiseAggregateEventBody medicalPromiseAggregateEventBody = JsonUtils.parseObject(event.getBody(), MedicalPromiseAggregateEventBody.class);
        List<MedicalPromiseCheckEventBody> medicalPromiseCheckEventBodies = medicalPromiseAggregateEventBody.getMedicalPromiseCheckEventBodies();
        List<MedPromiseHistory> medPromiseHistories = Lists.newArrayList();
        for (MedicalPromiseCheckEventBody medicalPromiseCheckEventBody : medicalPromiseCheckEventBodies){
            MedPromiseHistory medPromiseHistory = new MedPromiseHistory();
            MedicalPromiseRepQuery medicalPromiseRepQuery = new MedicalPromiseRepQuery();
            medicalPromiseRepQuery.setMedicalPromiseId(medicalPromiseCheckEventBody.getMedicalPromiseId());
            MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(medicalPromiseRepQuery);
            medPromiseHistory.setAfterStatus(medicalPromise.getStatus());
            medPromiseHistory.setAfterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
            medPromiseHistory.setBeforeStatus(medicalPromiseCheckEventBody.getStatus());
            medPromiseHistory.setBeforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseCheckEventBody.getStatus()));
            medPromiseHistory.setEventCode(event.getEventCode());
            medPromiseHistory.setEventDesc(Objects.nonNull(MedPromiseEventTypeEnum.getByCode(event.getEventCode()))?MedPromiseEventTypeEnum.getByCode(event.getEventCode()).getDesc():"");
            medPromiseHistory.setProviderId(medicalPromise.getProviderId());
            medPromiseHistory.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
            medPromiseHistory.setVersion(event.getVersion());
            medPromiseHistory.setExtend(JsonUtil.toJSONString(medicalPromise));
            medPromiseHistory.setOperator(medicalPromise.getUpdateUser());
            medPromiseHistories.add(medPromiseHistory);

        }
        medPromiseHistoryRepository.insertBatch(medPromiseHistories);

    }





}
