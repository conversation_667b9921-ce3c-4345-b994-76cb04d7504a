package com.jdh.o2oservice.application.trade.service;

import com.jdh.o2oservice.core.domain.trade.bo.RefundResult;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundDetail;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundTask;
import com.jdh.o2oservice.export.trade.dto.OrderRefundAmountDto;
import com.jdh.o2oservice.export.trade.dto.OrderRefundTaskDto;
import com.jdh.o2oservice.export.trade.query.QueryRefundAmountParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * JdOrderRefundApplication
 *
 * <AUTHOR>
 * @version 2024/4/23 23:15
 **/
public interface JdOrderRefundApplication {

    /**
     * 保存退款任务
     * @param jdOrderRefundTask
     * @param jdOrderRefundDetailList
     */
    void saveJdOrderRefundTask(JdOrderRefundTask jdOrderRefundTask, List<JdOrderRefundDetail> jdOrderRefundDetailList);

    /**
     * 退款明细列表
     * @param jdOrderRefundDetail
     * @return
     */
    List<JdOrderRefundDetail> findJdOrderRefundDetailList(JdOrderRefundDetail jdOrderRefundDetail);

    /**
     * 查询订单退款操作记录
     *
     * @param orderId
     * @return
     */
    List<OrderRefundTaskDto> queryOrderRefundRecord(Long orderId);

    /**
     * 查询加项订单退款操作记录
     *
     * @param orderId
     * @return
     */
    List<OrderRefundTaskDto> queryAddOrderRefundRecord(Long orderId);
    /**
     * 查询订单退款操作记录
     *
     * @param jdOrderRefundTask
     * @return
     */
    List<OrderRefundTaskDto> queryOrderRefundTaskByPromiseId(JdOrderRefundTask jdOrderRefundTask);

    /**
     * 退款状态更新
     * @param refundResultList
     * @return
     */
    int updateRefundStatusByTransactionNum(List<RefundResult> refundResultList);

    /**
     * 申请退款
     * @param detail
     * @return
     */
    Boolean appointmentRefund(JdOrderRefundDetail detail);
    /**
     * 退款明细列表
     * @param transactionNum
     * @return
     */
    JdOrderRefundDetail findJdOrderRefundDetailByTransactionNum(String transactionNum);
    /**
     * 退款成功，处理后续
     * @param transactionNum
     * @param jdOrderRefundDetail
     * @return
     */
    int updateRefundSuccByTransactionNum(String transactionNum,JdOrderRefundDetail jdOrderRefundDetail);

    /**
     * 获取订单已退款金额
     * @param orderId
     * @return
     */
    BigDecimal getOrderRefundAmount(Long orderId);

    /**
     * 查询订单退款金额
     *
     * @param queryRefundAmountParam
     * @return
     */
    OrderRefundAmountDto queryOrderRefundAmount(QueryRefundAmountParam queryRefundAmountParam);

    /**
     * 查询退款任务
     * @param jdOrderRefundTask
     * @return
     */
    List<JdOrderRefundTask> findJdOrderRefundTaskList(JdOrderRefundTask jdOrderRefundTask);
}
