package com.jdh.o2oservice.application.angel.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.angel.cmd.*;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.*;

import java.util.List;

/**
 * @Description: 服务者应用层
 * @Author: zhangxiaojie17
 * @Date: 2024/4/18
 **/
public interface AngelApplication {

    /**
     * @param angelRequest
     * @Description: 根据服务者三要素查询服务者信息(服务者id 、 互医医生id 、 服务者pin 只需入参一个要素就能确定唯一的服务者)
     * @Return: com.jdh.o2oservice.export.angel.dto.JdhAngelDto
     * @Author: zhangxiaojie17
     * @Date: 2024/4/21
     **/
    JdhAngelDto queryByAngelInfo(AngelRequest angelRequest);

    /**
     * @param angelStationRequest
     * @Description: 根据服务站id查询服务者列表信息
     * @Return: java.util.List<com.jdh.o2oservice.export.angel.dto.JdhAngelDto>
     * @Author: zhangxiaojie17
     * @Date: 2024/5/8
     **/
    List<JdhAngelDto> queryByStationId(AngelStationRequest angelStationRequest);

    /**
     * @param request
     * @Description: 分页查询服务者信息
     * @Return: com.jdh.o2oservice.common.result.response.PageDto<com.jdh.o2oservice.export.angel.dto.JdhAngelDto>
     * @Author: zhangxiaojie17
     * @Date: 2024/4/22
     **/
    PageDto<JdhAngelDto> queryAngelByPage(AngelPageRequest request);

    /**
     * @param angelDetailRequest
     * @Description: 查询服务者详情(运营端使用)
     * @Return: com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
     **/
    JdhAngelDetailDto queryAngelDetail(AngelDetailRequest angelDetailRequest);

    /**
     * @param angelBindStationMasterCmd
     * @Description: 批量绑定服务者站长信息
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/24
     **/
    Boolean batchBindStationMaster(AngelBindStationMasterCmd angelBindStationMasterCmd);

    /**
     * @param angelBindStationMasterCmd
     * @Description: 批量解绑服务站站长
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/24
     **/
    Boolean batchUnBindStationMaster(AngelBindStationMasterCmd angelBindStationMasterCmd);

    /**
     * @param angelBindStationCmd
     * @Description: 批量绑定服务站
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/28
     **/
    Boolean batchBindStation(AngelBindStationCmd angelBindStationCmd);

    /**
     * @param angelBindStationCmd
     * @Description: 批量解绑服务站
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/28
     **/
    Boolean batchUnBindStation(AngelBindStationCmd angelBindStationCmd);

    /**
     * @param submitAngelCmd
     * @Description: 运营端修改人员标签 0兼职 1全职
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
     **/
    Boolean updateAngelJobNature(SubmitAngelCmd submitAngelCmd);

    /**
     * @param angelBindSkillCmd
     * @Description: 服务者批量绑定技能
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/4/25
     **/
    Boolean batchBindAngelSkill(AngelBindSkillCmd angelBindSkillCmd);

    /**
     * @param angelBindSkillCmd
     * @Description: 服务者批量解绑技能
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/5/6
     **/
    Boolean batchUnBindAngelSkill(AngelBindSkillCmd angelBindSkillCmd);

    /**
     * @param angelUpdateTakeOrderStatusCmd
     * @Description: 修改服务者接单状态
     * @Return: java.lang.Boolean
     * @Author: zhangxiaojie17
     * @Date: 2024/5/2
     **/
    Boolean updateTakeOrderStatus(AngelUpdateTakeOrderStatusCmd angelUpdateTakeOrderStatusCmd);


    /**
     * @param angelSyncCmd
     * @Description: 从互医侧同步服务者审核信息
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/8
     **/
    void syncAngelAuditInfoFromNethp(AngelSyncCmd angelSyncCmd);

    /**
     * @param angelNethpChangeCmd
     * @Description: 从互医侧同步服务者变更信息
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/13
     **/
    void syncAngelChangeFromNethp(AngelNethpChangeCmd angelNethpChangeCmd);

    /**
     * 获取护士职称列表
     *
     * @return
     */
    List<JdhAngelProfessionTitleDictDto> getAngelProfessionTitleDictList();

    //====================================================初始化数据方法=============================================================
    Boolean submitAngel(SubmitAngelCmd submitAngelCmd);

    Boolean removeAllAngel(Long angelId);

    /**
     * 导入科室数据
     *
     * @param importDepartmentCmd
     */
    void importDepartmentData(ImportDepartmentCmd importDepartmentCmd);

    /**
     * 查询科室数据
     *
     * @param getDepartmentRequest
     * @return
     */
    List<JdhDepartmentDto> getDepartments(GetDepartmentRequest getDepartmentRequest);

    /**
     * 保存护士邀请码
     *
     * @param angelId
     * @return
     */
    Boolean saveAngelInviteCode(Long angelId);

    /**
     * 保存护士联盟号
     *
     * @param cmd
     * @return
     */
    Boolean saveAngelUnionId(AngelUnionIdCmd cmd);

    /**
     * 查询CPS页面商品列表
     *
     * @param query
     * @return
     */
    JdUnionPageDto queryGoodsListByCpsPage(JdUnionGoodsListQuery query);

    /**
     * CPS转链
     *
     * @param query
     * @return
     */
    String generateClickUrl(GenerateClickUrlQuery query);


    /**
     * 更新工作身份
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    Boolean updateWorkIdentity(UpdateAngelWorkIdentityCmd cmd);

    /**
     * 查询护士资格证书
     *
     * @param request
     * @return
     */
    List<AngelCertificateDto> getAngelCertificates(AngelCertificatesRequest request);
}