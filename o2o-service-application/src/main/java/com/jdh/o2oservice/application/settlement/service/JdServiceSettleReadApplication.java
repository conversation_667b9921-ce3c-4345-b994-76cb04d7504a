package com.jdh.o2oservice.application.settlement.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.export.settlement.SettlementEbsRequest;
import com.jdh.o2oservice.export.settlement.cmd.AngelCashOutCmd;
import com.jdh.o2oservice.export.settlement.cmd.JdSettlementStatusCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelBankDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementMoneyDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:36 下午
 * @Description:
 */
public interface JdServiceSettleReadApplication {


    /**
     * 查询护士结算信息
     *
     * @param queryContext
     * @return
     */
    PageDto<AngelSettlementDto> querySettlementPage(AngelSettleQuery queryContext);

    /**
     * 查询账单明细
     *
     * @param queryContext
     * @return
     */
    AngelSettlementDto querySettlementDetailList(AngelSettleQuery queryContext);


    /**
     * 查询账单明细
     *
     * @param queryContext
     * @return
     */
    AngelSettlementDto querySettlement(AngelSettleQuery queryContext);

    /**
     * 查询护士账户金额
     *
     * @param queryContext
     * @return
     */
    AngelSettlementMoneyDto queryAngelSettlementMoneyDto(AngelSettleQuery queryContext);

    /**
     * 查询护士明细汇总金额
     *
     * @param query
     * @return
     */
    Map<String, BigDecimal> querySettleTotal(AngelSettleQuery query);

    /**
     * 查询护士绑定银行卡
     *
     * @param queryContext
     * @return
     */
    AngelBankDto queryBindBank(AngelSettleQuery queryContext);

    /**
     * 提交提现
     *
     * @param cashOutCmd
     * @return
     */
    Long submitCashOut(AngelCashOutCmd cashOutCmd);


    /**
     * 更新提现结果
     *
     * @param dto
     * @return
     */
    Boolean updateCashOutResult(AngelSettlementDto dto);

    /**
     * 查询护士结算信息
     *
     * @param angelSettlQuery
     * @return
     */
    PageDto<AngelSettlementDto> querySettlementPageBySettleTime(AngelSettleQuery angelSettlQuery);

    /**
     * @param jdSettlementStatusCmd
     */
    Integer updateSettleStatusBySettleIdList(JdSettlementStatusCmd jdSettlementStatusCmd);

    /**
     * 查询ebs收入
     *
     * @param settlementEbsRequest
     * @return
     */
    List<JdhSettlementEbs> querySettlementEbs(SettlementEbsRequest settlementEbsRequest);
}
