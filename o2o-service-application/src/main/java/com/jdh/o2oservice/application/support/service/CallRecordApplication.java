package com.jdh.o2oservice.application.support.service;

import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.export.support.command.SecurityNumberBindAxbCmd;
import com.jdh.o2oservice.export.support.dto.*;
import com.jdh.o2oservice.export.support.query.QueryCallRecordByWorkRequest;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import com.jdh.o2oservice.export.support.query.SyncAngelPhoneRequest;

import java.util.List;

/**
 * @Description 外呼服务
 * @Date 2024/12/19 下午5:31
 * <AUTHOR>
 **/
public interface CallRecordApplication {

    /**
     * 虚拟号绑定
     * @param cmd
     * @return
     */
    SecurityNumberBindAxbResultDto bindAxb(SecurityNumberBindAxbCmd cmd);

    /**
     * 接收话单
     * @param request
     */
    Boolean receiveCallBilling(CallBillingNotificationDto request);

    /**
     * 录音调取
     * @param request
     */
    Boolean callRecordingRetrieval(CallRecordingRetrievalDto request);

    /**
     * 外呼记录列表
     * @param request
     */
    List<CallRecordDto> queryCallRecordList(QueryCallRecordRequest request);

    /**
     * 外呼记录分组列表
     * @param request
     * @return
     */
    CallRecordGroupDto queryCallRecordByGroup(QueryCallRecordRequest request);

    /**
     * 查询外呼url
     * @param request
     */
    String queryCallRecordUrl(QueryCallRecordRequest request);

    /**
     * 虚拟号解绑
     * @param request
     */
    Boolean callBindRelease(SecurityNumberReleaseDto request);

    /**
     * 同步护士手机号
     * @param request
     * @return
     */
    Boolean syncAngelPhone(SyncAngelPhoneRequest request);

    /**
     * 查询预约人电话
     * @param promise
     * @return
     */
    String getAppointmentPhone(JdhPromise promise);
    /**
     * 外呼记录列表
     * @param request
     */
    List<CallRecordDto> queryO2oCallRecordList(QueryCallRecordRequest request);

    /**
     * 查询外呼记录列表
     *
     * @param request
     * @return
     */
    List<CallRecordDto> queryO2oRecordByAngelIdList(QueryCallRecordByWorkRequest request);
}