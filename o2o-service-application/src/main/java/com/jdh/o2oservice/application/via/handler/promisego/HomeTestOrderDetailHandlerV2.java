package com.jdh.o2oservice.application.via.handler.promisego;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.handler.AbstractViaDataFillHandler;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.IsAddedEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.core.domain.trade.enums.AvailableAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ModifyAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentTimeValueObject;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderExtDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderItemDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderServiceFeeInfoDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 基于promiseGo优化的自检测订单详情页
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Slf4j
@Service
public class HomeTestOrderDetailHandlerV2 extends AbstractViaDataFillHandler implements MapAutowiredKey {


    /**
     * 支付过期时间，默认半小时，之后应该去订单中台取数据
     */
    private static final Long PAY_EXPIRE_TIME = 30 * 60 * 1000L;
    /**
     * 需要预测的阶段
     */
    private static final Set<String> AGGREGATE_STATUS_ETA = Sets.newHashSet(PromiseAggregateStatusEnum.DISPATCH.getCode(),
            PromiseAggregateStatusEnum.TO_HOME.getCode(), PromiseAggregateStatusEnum.SERVICING.getCode(),
            PromiseAggregateStatusEnum.TO_LAB.getCode(), PromiseAggregateStatusEnum.TESTING.getCode()
    );
    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * promiseHistoryRepository
     */
    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * angelApplication
     */
    @Autowired
    private AngelApplication angelApplication;

    /**
     * angelPromiseApplication
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;
    @Resource
    private ViaComponentDomainService viaComponentDomainService;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private MedicalReportApplication medicalReportApplication;
    @Resource
    private ViaConfigRepository viaConfigRepository;
    /**
     * jdhSkuPoMapper
     */
    @Resource
    private JdhSkuPoMapper jdhSkuPoMapper;


    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx) {
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        AssertUtils.hasText(ctx.getOrderId(), SupportErrorCode.VIA_ORDER_ID_NOT_EXIT);
    }

    /**
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     */
    private void handleStepGuideInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping) {
        for (ViaFloorConfig viaFloorConfig : viaFloorInfo.getFloorConfigList()) {
            String stepCode = viaFloorConfig.getStepCode();
            //完成
            if (statusMapping.getStepGuideFinishCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.FINISH.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideFinishIcon());
            }
            //进行中
            if (statusMapping.getStepGuideProcessCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.PROCESS.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideProcessIcon());
            }
            //等待
            if (statusMapping.getStepGuideWaitCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.WAIT.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideWaitIcon());
            }
        }
        log.info("HomeTestOrderDetailHandlerV2 handleStepGuideInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 获取动态字段值
     *
     * @param dynamicFieldList 动态字段列表
     * @return {@link Map}<{@link String},{@link String}>
     */
    @SuppressWarnings("JdJDClassCast")
    private String formatDynamicField(Map<String, Object> sourceData,
                                      List<String> dynamicFieldList, String expression) {
        if (CollUtil.isEmpty(dynamicFieldList)) {
            return expression;
        }
        Map<String, String> result = new HashMap<>(dynamicFieldList.size());
        for (String field : dynamicFieldList) {
            // 退款金额
            if (ViaDynamicFieldEnum.REFUND_AMOUNT.getField().equals(field)) {
                JdOrderDTO jdOrder = (JdOrderDTO) sourceData.get(TradeAggregateEnum.ORDER.getCode());
                String refundAmount = Objects.isNull(jdOrder.getRefundAmount()) ? "" : jdOrder.getRefundAmount().toPlainString();
                result.put(ViaDynamicFieldEnum.REFUND_AMOUNT.getField(), refundAmount);
            }
            // 订单支付过期时间戳
            if (ViaDynamicFieldEnum.PAY_EXPIRE_TIME.getField().equals(field)) {
                JdOrderDTO jdOrder = (JdOrderDTO) sourceData.get(TradeAggregateEnum.ORDER.getCode());
                long expireTime = jdOrder.getCreateTime().getTime() + PAY_EXPIRE_TIME;
                result.put(ViaDynamicFieldEnum.PAY_EXPIRE_TIME.getField(), String.valueOf(expireTime));
            }
        }
        expression = StrUtil.format(expression, result);
        log.info("HomeTestOrderDetailHandlerV2 -> formatDynamicField expression:{}", expression);
        return expression;
    }

    /**
     * 处理摘要信息
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdhPromise    promise
     */
    private void handleSummaryInfo(ViaFloorInfo viaFloorInfo,
                                   ViaStatusMapping statusMapping,
                                   JdhPromise jdhPromise,
                                   Map<String, Object> sourceData) {
        List<ViaFloorConfig> floorConfig = new ArrayList<>();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();

        viaFloorConfig.setMainIcon(statusMapping.getMainIcon());
        // 获取表达式配置value配置的容器
        Map<String, Object> containerMap = statusMapping.getContainer();
        // 从promiseGo获取相关预测数据
        try {
            String aggregateStatus = statusMapping.getAggregateStatus();
            if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)) {
                JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                PromiseStation store = jdhPromise.getStore();
                UserPromisegoBo userPromisegoBo = promiseGoRpcService.queryUserPromisego(
                        UserPromisegoRequestBo.builder()
                                .aggregateStatus(statusMapping.getAggregateStatus())
                                .promiseId(jdhPromise.getPromiseId())
                                .businessMode(jdhVerticalBusiness.getBusinessModeCode())
                                .appointmentTime(PromisegoRequestAppointmentTime.builder()
                                        .immediately(EntityUtil.getFiledDefaultNull(jdhPromise.getAppointmentTime(), PromiseAppointmentTime::getIsImmediately))
                                        .appointmentStartTime(EntityUtil.getFiledDefaultNull(jdhPromise.getAppointmentTime(), PromiseAppointmentTime::getAppointmentStartTime))
                                        .appointmentEndTime(EntityUtil.getFiledDefaultNull(jdhPromise.getAppointmentTime(), PromiseAppointmentTime::getAppointmentEndTime))
                                        .build())
                                .appointmentAddress(PromisegoRequestAddress.builder()
                                        .provinceId(store.getProvinceCode())
                                        .cityId(store.getCityCode())
                                        .countyId(store.getDistrictCode())
                                        .townId(store.getTownCode())
                                        .provinceName(store.getProvinceName())
                                        .cityName(store.getCityName())
                                        .countyName(store.getDistrictName())
                                        .townName(store.getTownName())
                                        .fullAddress(store.getStoreAddr())
                                        .build())
                                .queryTermScript(Boolean.TRUE)
                                .build());

                containerMap.put("currScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getCurrScript(), ScriptBo::getScriptContent));
                containerMap.put("termScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getTermScript(), ScriptBo::getScriptContent));
                containerMap.put("warmTipScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getWarmTipScript(), ScriptBo::getScriptContent));
            }

        } catch (Throwable e) {
            log.error("HomeTestOrderDetailHandlerV2 -> handleSummaryInfo error", e);
        }

        // 解析出mainTile
        String mainTitle = statusMapping.getMainTitle();
        Expression expression = AviatorEvaluator.compile(mainTitle, true);
        Object res = expression.execute(containerMap);
        mainTitle = Objects.toString(res, "");

        // 解析出二级标题
        String title = statusMapping.getTitle();
        if (StringUtils.isNotBlank(title)) {
            Expression titleExpression = AviatorEvaluator.compile(title, true);
            Object titleRes = titleExpression.execute(containerMap);
            title = Objects.toString(titleRes, "");
        }

        // 解析出异常兜底文案
        String warmTip = statusMapping.getWarmTip();
        if (StringUtils.isNotBlank(warmTip)) {
            Expression exceptionTitleExpression = AviatorEvaluator.compile(warmTip, true);
            Object exeRes = exceptionTitleExpression.execute(containerMap);
            warmTip = Objects.toString(exeRes, "");
        }

        // 填充变量"退款金额<span id='appointTitleId'>{refundAmount}</span>元";当前状态不是promiseGo返回的title时，需要手动填充变量
        mainTitle = formatDynamicField(sourceData, statusMapping.getDynamicField(), mainTitle);
        title = formatDynamicField(sourceData, statusMapping.getDynamicField(), title);

        viaFloorConfig.setMainTitle(mainTitle);
        viaFloorConfig.setTitle(title);
        viaFloorConfig.setWarmTip(warmTip);
        floorConfig.add(viaFloorConfig);
        viaFloorInfo.setFloorConfigList(floorConfig);
        log.info("HomeTestOrderDetailHandlerV2 handleSummaryInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * @param viaFloorInfo     通用楼层信息
     * @param statusMapping    状态映射
     * @param jdhPromise       jdhPromise
     * @param promiseHistories promiseHistories
     */
    private void handlePromiseAngelInfo(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping,
                                        JdhPromise jdhPromise, JdOrderDTO jdOrder, List<JdhPromiseHistory> promiseHistories) {
        // 查询护士信息
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();


        AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
        angelWorkQuery.setPromiseId(jdhPromise.getPromiseId());
        AngelWorkDetailDto angelDto = angelPromiseApplication.queryAngelWork(angelWorkQuery);
        log.info("HomeTestOrderDetailHandlerV2 handlePromiseAngelInfo angelWork:{}", JSON.toJSONString(angelDto));
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();

            // 处理按钮
            if (CollectionUtils.isNotEmpty(viaFloorConfig.getBtnList())) {
                Iterator<ViaBtnInfo> btnInfoIterator = viaFloorConfig.getBtnList().iterator();
                while (btnInfoIterator.hasNext()) {
                    ViaBtnInfo btnInfo = btnInfoIterator.next();
                    // 不支持这个按钮则不初始化返回
                    if (!statusMapping.supportFiled(ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode(), btnInfo.getCode())) {
                        btnInfoIterator.remove();
                        continue;
                    }
                    // 联系服务者按钮
                    if (ViaAngelInfoBtnEnum.CONTACT_ANGEL.getBtn().equals(btnInfo.getCode())) {
                        ViaActionInfo action = btnInfo.getAction();
                        Map<String, Object> params = Maps.newHashMap();
                        params.put(ViaAngelInfoFieldEnum.ANGEL_PHONE.getField(), angelDto.getAngelPhone());
                        action.setParams(params);
                    } else if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
                        buildContactCustomerBtn(btnInfo, ctx, jdOrder, btnInfo.getAction());
                    }
                }
                continue;
            }
            // 楼层属性处理
            if (!statusMapping.supportFiled(ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode(), viaFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }

            if (ViaAngelInfoFieldEnum.ANGEL_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelDto)) {
                    iterator.remove();
                } else {
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    String angelName = angelDto.getAngelName();
                    String angelType = AngelWorkTypeEnum.matchType(jdhVerticalBusiness.getBusinessModeCode()).getAngelType();
                    viaFloorConfig.setFieldValue(angelType + new UserName(angelName).maskPersonal() + "为您服务");
                }
                continue;
            }
            if (ViaAngelInfoFieldEnum.ANGEL_PHONE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelDto)) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(angelDto.getAngelPhone());
                }
                continue;
            }

            if (ViaAngelInfoFieldEnum.HEAD_IMG.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.nonNull(angelDto)) {
                    viaFloorConfig.setFieldValue(angelDto.getAngelHeadImg());
                    viaFloorConfig.setTargetUrl(String.format(viaFloorConfig.getTargetUrl(),jdhPromise.getPromiseId()));
                } else {
                    iterator.remove();
                }
                continue;
            }
        }
        log.info("HomeTestOrderDetailHandlerV2 handlePromiseAngelInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 处理楼层列表
     *
     * @param ctx                上下文
     * @param statusMapping      状态映射
     * @param jdOrder            JD订单
     * @param jdhPromise         jdhPromise
     * @param medicalPromiseList medicalPromiseList
     * @param promiseHistories   promiseHistories
     */
    private void dealFloorList(FillViaConfigDataContext ctx,
                               ViaStatusMapping statusMapping,
                               JdOrderDTO jdOrder,
                               JdhPromise jdhPromise,
                               List<JdhPromiseHistory> promiseHistories,
                               List<MedicalPromiseDTO> medicalPromiseList,
                               JdhSkuDto jdhSkuDto) {
        ViaConfig viaConfig = ctx.getViaConfig();
        // 填充模版数据
        Map<String, Object> sourceData = Maps.newHashMap();
        sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
        sourceData.put(TradeAggregateEnum.ORDER.getCode(), jdOrder);
        sourceData.put("statusMapping", statusMapping);
        sourceData.put("ctx", ctx);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : viaConfig.getFloorList()) {
            //概要 summaryInfo
            if (ViaFloorEnum.PROMISE_SUMMARY_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleSummaryInfo(viaFloorInfo, statusMapping, jdhPromise, sourceData), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handleSummaryInfo exception", exception);
                    return null;
                }));
            }

            //步骤条 stepGuideInfo
            if (ViaFloorEnum.PROMISE_STEP_GUIDE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleStepGuideInfo(viaFloorInfo, statusMapping), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handleStepGuideInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 服务者 promiseAngelInfo
            if (ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseAngelInfo(ctx, viaFloorInfo, statusMapping, jdhPromise, jdOrder, promiseHistories), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handlePromiseAngelInfo exception", exception);
                    return null;
                }));
            }

            //地图信息
            if (ViaFloorEnum.PROMISE_MAP_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseMapInfo(viaFloorInfo, sourceData), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handlePromiseMapInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 消费码 promiseCodeInfo
            if (ViaFloorEnum.PROMISE_CODE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseCodeInfo(viaFloorInfo, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handlePromiseCodeInfo exception", exception);
                    return null;
                }));
            }


            //样本信息楼层 materialInfo
            if (ViaFloorEnum.PROMISE_MATERIAL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterMedicalPromise(viaFloorInfo, sourceData, medicalPromiseList, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handleFooterMedicalPromise exception", exception);
                    return null;
                }));
            }

            // 科普知识
            if (ViaFloorEnum.SERVICE_SCIENCE_KNOWLEDGE.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelScienceKnowledge(viaFloorInfo, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handelScienceKnowledge exception", exception);
                    return null;
                }));
            }
            //购买商品信息 orderSkuInfo
            if (ViaFloorEnum.PROMISE_ORDER_SKU_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderSkuInfo(viaConfig, viaFloorInfo, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handelOrderSkuInfo exception", exception);
                    return null;
                }));
            }

            //企微卡片信息 weChatCardInfo
            if (ViaFloorEnum.PROMISE_WECHAT_CARD_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelWeChatCardInfo(viaFloorInfo, jdOrder, ctx.getUserPin()), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handelWeChatCardInfo exception", exception);
                    return null;
                }));
            }

            //订单信息 orderInfo
            if (ViaFloorEnum.PROMISE_ORDER_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderInfo(viaFloorInfo, jdOrder, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handelOrderInfo exception", exception);
                    return null;
                }));
            }

            /**
             * 采样教程楼层（自采样需要）
             */
            if (ViaFloorEnum.PROMISE_SAMPLE_COURSE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterSampleCourse(viaFloorInfo, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList sampleCourseInfo exception", exception);
                    return null;
                }));
            }
            //底部按钮 footerButtons
            if (ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterButtons(ctx, viaFloorInfo, statusMapping, jdOrder, jdhPromise, medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV2 dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 订单信息
     * {
     * "floorCode": "orderInfo",
     * "floorName": "订单信息",
     * "floorConfigList": [
     * {
     * "fieldKey":"orderTotalAmount",
     * "fieldValue":"0.01",
     * "showDetail":true,
     * "feeItemList":[
     * <p>
     * ]
     * },
     * {
     * "fieldKey":"orderId",
     * "fieldValue":"284362257873",
     * "copyBtn":true
     * },
     * {
     * "fieldKey":"paymentTime",
     * "fieldValue":"2023-12-04 17:30:53"
     * },
     * {
     * "fieldKey":"paymentWayDesc",
     * "fieldValue":"在线支付"
     * },
     * {
     * "fieldKey":"orderUserPhone",
     * "fieldValue":"176****3020"
     * },
     * {
     * "fieldKey":"orderUserName",
     * "fieldValue":"*先生"
     * },
     * {
     * "fieldKey":"addressDetail",
     * "fieldValue":"北京大兴区旧宫地区旧宫新苑南区14号楼二单元302"
     * }
     * ]
     * }
     *
     * @param viaFloorInfo 通用楼层信息
     * @param jdOrder      JD订单
     * @param jdhPromise
     */
    private void handelOrderInfo(ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder, JdhPromise jdhPromise) {
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();

        // 获取订单预约信息
        OrderAppointmentInfoValueObject vo = null;
        JdOrderExtDTO appointmentDto = jdOrder.getJdOrderExtList().stream()
                .filter(e -> StringUtils.equals(e.getExtType(), OrderExtTypeEnum.APPOINTMENT_INFO.getType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(appointmentDto)) {
            String json = appointmentDto.getExtContext();
            vo = JSON.parseObject(json, OrderAppointmentInfoValueObject.class);
        }

        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            if (ViaOrderInfoFieldEnum.ORDER_ID.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderId())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderId().toString());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_AMOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderAmount())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderAmount().toPlainString());
                    List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = jdOrder.getJdOrderServiceFeeInfos();
                    viaFloorConfig.setValue(JSON.toJSONString(jdOrderServiceFeeInfos));
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_CREATE_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getCreateTime())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(jdOrder.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));
                }
                continue;
            }
//            if (ViaOrderInfoFieldEnum.PAY_TYPE_DESC.getField().equals(viaFloorConfig.getFieldKey())) {
//                if (Objects.isNull(jdOrder.getPayType())) {
//                    iterator.remove();
//                } else {
//                    viaFloorConfig.setFieldValue(PayTypeEnum.getDescOfType(jdOrder.getPayType().toString()));
//                }
//                continue;
//            }
//            if (ViaOrderInfoFieldEnum.ORDER_USER_PHONE.getField().equals(viaFloorConfig.getFieldKey())) {
//                if (Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getMobile())) {
//                    iterator.remove();
//                } else {
//                    viaFloorConfig.setFieldValue(new PhoneNumber(jdOrder.getAddressInfo().getMobile()).mask());
//                }
//                continue;
//            }

            if (ViaOrderInfoFieldEnum.ORDER_USER_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                List<String> patientNames = vo.getPatients().stream().map(e -> UserName.maskTool(e.getName())).collect(Collectors.toList());
                String name = String.join(",", patientNames);
                viaFloorConfig.setFieldValue(name);
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ADDRESS_DETAIL.getField().equals(viaFloorConfig.getFieldKey())) {
                viaFloorConfig.setFieldValue(vo.getAddressInfo().getFullAddress());
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_REMARK.getField().equals(viaFloorConfig.getFieldKey())) {
                if (StrUtil.isEmpty(jdOrder.getRemark())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getRemark());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_DISCOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderDiscount()) || jdOrder.getOrderDiscount().compareTo(new BigDecimal("0")) <= 0) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderDiscount().toString());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_COUPON.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderCoupon()) || jdOrder.getOrderCoupon().compareTo(new BigDecimal("0")) <= 0) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderCoupon().toString());
                }
                continue;
            }

            // 预约时间 APPOINTMENT_TIME
            if (ViaOrderInfoFieldEnum.APPOINTMENT_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                //优先判断是否存在履约单，是否存在履约单预约时间，如果有优先取履约单上的预约时间
                if (Objects.nonNull(jdhPromise) && Objects.nonNull(jdhPromise.getAppointmentTime())) {
                    OrderAppointmentTimeValueObject appointmentTime = new OrderAppointmentTimeValueObject();
                    appointmentTime.setDateType(jdhPromise.getAppointmentTime().getDateType());
                    appointmentTime.setIsImmediately(jdhPromise.getAppointmentTime().getIsImmediately());
                    appointmentTime.setAppointmentStartTime(jdhPromise.getAppointmentTime().formatAppointmentStartTime());
                    appointmentTime.setAppointmentEndTime(jdhPromise.getAppointmentTime().formatAppointmentEndTime());

                    if (Objects.nonNull(appointmentTime.getIsImmediately()) && appointmentTime.getIsImmediately()) {
                        viaFloorConfig.setFieldValue("立即预约");
                    } else {
                        // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                        String time = appointmentTime.formatAppointTimeDesc();
                        viaFloorConfig.setFieldValue(time);
                    }
                    continue;
                }
                OrderAppointmentTimeValueObject appointmentTime = vo.getAppointmentTime();
                if (Objects.nonNull(jdhPromise.getAppointmentTime().getIsImmediately()) && jdhPromise.getAppointmentTime().getIsImmediately()) {
                    viaFloorConfig.setFieldValue("立即预约");
                } else {
                    // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                    if(DateTypeEnum.SCHEDULE_BY_TIME.getType().equals(appointmentTime.getDateType())){
                        String time = jdhPromise.formatAppointTimeDesc();
                        viaFloorConfig.setFieldValue(time);
                    }else {
                        viaFloorConfig.setFieldValue(StrUtil.EMPTY);
                    }
                }
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
            }

        }
    }


    private JdhSkuDto findMainSku(JdhPromise jdhPromise) {
        // 多个商品取主品的采样教程
        List<Long> skuIds = jdhPromise.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toList());
        LambdaQueryWrapper<JdhSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhSkuPo::getSkuId, skuIds).eq(JdhSkuPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSkuPo::getCreateTime);
        List<JdhSkuPo> skuPos = jdhSkuPoMapper.selectList(queryWrapper);
        Optional<JdhSkuPo> skuPo = skuPos.stream().filter(e -> Objects.equals(e.getSkuType(), SkuRelTypeEnum.MAIN_ITEM.getType())).findFirst();
        JdhSkuRequest request = new JdhSkuRequest();
        if (!skuPo.isPresent()) {
            request.setSkuId(jdhPromise.findBasicService().getServiceId());
        } else {
            request.setSkuId(skuPo.get().getSkuId());
        }
        JdhSkuDto skuDto = productApplication.queryAggregationJdhSkuInfo(request);
        return skuDto;
    }

    /**
     * 处理检测方法科普楼层
     */
    private void handelScienceKnowledge(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
        JdhSkuDto skuDto = findMainSku(jdhPromise);
        if (StringUtils.isNotBlank(skuDto.getTutorialMethodUrl())){
            ViaFloorConfig header = new ViaFloorConfig();
            header.setFieldKey("tutorialMethodUrl");
            header.setFieldValue(skuDto.getTutorialMethodUrl());

            // 跳转链接
            ViaActionInfo action = new ViaActionInfo();
            action.setType(ActionType.JUMP.getCode());
            action.setUrl(skuDto.getTutorialMethodJumpUrl());
            header.setAction(action);
            viaFloorConfigList.add(header);
        }
        viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        log.info("HomeTestOrderDetailHandlerV2 handelScienceKnowledge viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * @param viaConfig    VIA配置
     * @param viaFloorInfo 通用楼层信息
     * @param jdOrder      JD订单
     */
    private void handelOrderSkuInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        List<JdOrderItemDTO> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();

        for (JdOrderItemDTO jdOrderItemDTO : jdOrderItemList) {
            // action
            ViaActionInfo action = new ViaActionInfo();
            action.setUrl(MessageFormat.format(viaConfig.getSkuDetailUrl(), jdOrderItemDTO.getSkuId().toString()));
            action.setType(ActionType.JUMP.getCode());
            //如果是加项商品，跳转url为空，即在订详无法跳转
            if (IsAddedEnum.IS_ADDED.getValue().equals(jdOrderItemDTO.getIsAdded())) {
                action.setUrl(null);
            }
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(jdOrderItemDTO)).action(action).build());
        }
        viaFloorInfo.setFloorConfigList(floorConfigList);
    }


    /**
     * 处理履约服务地图信息
     * 当状态为派单中时，仅立即预约展示地图。预约单不展示地图
     */
    private void handlePromiseMapInfo(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData) {
        ViaFloorConfig floorConfig = viaFloorInfo.getFloorConfigList().get(0);
        ViaActionInfo action = floorConfig.getAction();
        action.init(sourceData);
        log.info("HomeTestOrderDetailHandlerV2 handlePromiseMapInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));

    }

    /**
     * fillMedPromiseFloorConfig
     *
     * @param viaFloorConfig       viaFloorConfig
     * @param medPromise           medPromise
     * @param medicalStatusMapping medicalStatusMapping
     * @param jdhPromisePatient    jdhPromisePatient
     * @param jdhPromise           jdhPromise
     */
    private void fillMedPromiseFloorConfig(ViaFloorConfig viaFloorConfig,
                                           MedicalPromiseDTO medPromise,
                                           List<ViaStatusMapping> medicalStatusMapping,
                                           JdhPromisePatient jdhPromisePatient,
                                           JdhPromise jdhPromise) {
        for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
            if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                if (CollUtil.isNotEmpty(btnList)) {
                    List<ViaBtnInfo> newBtnList = new ArrayList<>();
                    for (ViaBtnInfo viaBtnInfo : btnList) {
                        if (ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())) {
                            if (Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())) {
                                ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo), ViaBtnInfo.class);
                                newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(), jdhPromise.getSourceVoucherId(), jdhPromisePatient.getPatientId().toString()));
                                newBtnList.add(newBtnInfo);
                            }
                        }
                    }
                    viaFloorConfig.setBtnList(newBtnList);
                }
                break;
            }
        }
    }

    /**
     * 处理底部按钮
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     */
    private void handleFooterButtons(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping,
                                     JdOrderDTO jdOrder, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        //申请退款 refundBtn
        //再次购买 rePurchaseBtn
        //联系客服 contactCustomerBtn
        //取消订单 cancelOrderBtn
        //立即支付 payNowBtn
        ViaConfig viaConfig = ctx.getViaConfig();
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
        // 退款标识
        boolean hasInvalid = false;
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            hasInvalid = medicalPromiseList.stream().anyMatch(m -> MedicalPromiseStatusEnum.INVALID.getStatus().equals(m.getStatus()));
        }

        // 按钮楼层是否展示title由配置决定
        if (statusMapping.supportFiled(ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode(), "btnFloorTitle")) {
            // 根据样本数量展示不同的title
            if (CollectionUtils.isNotEmpty(medicalPromiseList)) {
                try {
                    Map<String, Object> map = viaConfigRepository.findAssemblyConfig(ViaAssemblyCodeEnum.BTN_FLOOR.getCode());
                    String title = "";
                    if (Objects.equals(medicalPromiseList.size(), 1)) {
                        title = Objects.toString(map.get("onceSpecimenTitle"), "");
                    } else if (medicalPromiseList.size() > 1) {
                        title = Objects.toString(map.get("multiSpecimenTitle"));
                    }
                    viaFloorConfig.setTitle(title);
                } catch (Exception e) {
                    // title设置失败，不影响按钮处理
                    log.error("HomeTestOrderDetailHandlerV2->handleFooterButtons setTitle error", e);
                }
            }


        }
        while (btnInfoIterator.hasNext()) {
            ViaBtnInfo btnInfo = btnInfoIterator.next();

            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode", EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getVerticalCode));
            actionCommonParams.put("serviceType", viaConfig.getServiceType());
            actionCommonParams.put("envType", ctx.getEnvType());
            actionCommonParams.put("promiseId", EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseId));
            actionCommonParams.put("orderId", EntityUtil.getFiledDefaultNull(jdOrder, JdOrderDTO::getOrderId));

            if (!statusMapping.getFooterButtonCodeList().contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }

            //申请退款
            ViaActionInfo action = btnInfo.getAction();
            if (ViaBtnCodeEnum.REFUND_BTN.getCode().equals(btnInfo.getCode())) {
                if (hasInvalid){
                    btnInfoIterator.remove();
                    continue;
                }
                action.setParams(new HashMap<>(actionCommonParams));
                ViaActionInfo nextAction = action.getNextAction();
                actionCommonParams.put("orderId", jdOrder.getOrderId());
                actionCommonParams.put("refundType", 1);
                actionCommonParams.put("refundSource", "1");
                actionCommonParams.put("voucherId", jdhPromise.getVoucherId());
                actionCommonParams.put("promiseId", jdhPromise.getPromiseId());
                nextAction.setParams(actionCommonParams);
            } else if (ViaBtnCodeEnum.RE_PURCHASE_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    String jumpUrl = MessageFormat.format(btnInfo.getJumpUrlRule(), jdOrder.getJdOrderItemList().get(0).getSkuId().toString());
                    action.setUrl(jumpUrl);
                } catch (Exception e) {
                    log.error("HomeTestOrderDetailHandlerV2 handleFooterButtons RE_PURCHASE_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }

            } else if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
                buildContactCustomerBtn(btnInfo, ctx, jdOrder, action);
            } else if (ViaBtnCodeEnum.CANCEL_ORDER_BTN.getCode().equals(btnInfo.getCode())) {
                actionCommonParams.put("orderId", jdOrder.getOrderId().toString());
                action.setParams(actionCommonParams);

            } else if (ViaBtnCodeEnum.PAY_NOW_BTN.getCode().equals(btnInfo.getCode())) {
                actionCommonParams.put("orderId", jdOrder.getOrderId().toString());
                if (StrUtil.isNotBlank(ctx.getOpenId())) {
                    actionCommonParams.put("openId", ctx.getOpenId());
                }
                if (StrUtil.isNotBlank(ctx.getCallWxType())) {
                    actionCommonParams.put("callWxType", ctx.getCallWxType());
                }
                action.setParams(actionCommonParams);
            } else if (ViaBtnCodeEnum.SCANNING_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.ENTER_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.MULTI_ENTER_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())) {
                action.init(actionCommonParams);
            } else if (ViaBtnCodeEnum.MODIFY_DATETIME_BTN.getCode().equals(btnInfo.getCode())) {
                if (Objects.nonNull(jdhPromise) && Objects.equals(JdhPromiseStatusEnum.MODIFY_ING.getStatus(), jdhPromise.getPromiseStatus())) {
                    log.error("HomeTestOrderDetailHandlerV2 handleFooterButtons 修改预约中状态的履约单不展示修改时间按钮");
                    btnInfoIterator.remove();
                    continue;
                }
                Map<String, Object> actionParams = new HashMap<>();
                actionParams.put("verticalCode",viaConfig.getVerticalCode());
                actionParams.put("serviceType",viaConfig.getServiceType());
                actionParams.put("envType",ctx.getEnvType());
                actionParams.put("scene", AvailableAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName());
                actionParams.put("promiseId",jdhPromise.getPromiseId());
                action.setParams(actionParams);
                ViaActionInfo nextAction = action.getNextAction();
                Map<String, Object> nextActionParams = new HashMap<>();
                nextActionParams.put("verticalCode",viaConfig.getVerticalCode());
                nextActionParams.put("serviceType",viaConfig.getServiceType());
                nextActionParams.put("envType",ctx.getEnvType());
                nextActionParams.put("scene", ModifyAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName());
                nextActionParams.put("reasonType", 1);
                nextActionParams.put("reasonContent", "");
                nextActionParams.put("promiseId", String.valueOf(jdhPromise.getPromiseId()));
                nextAction.setParams(nextActionParams);
            }
        }

        log.info("HomeTestOrderDetailHandlerV2 handleFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 获取完整预约日期描述
     *
     * @param appointmentTime 预约时间
     * @return {@link String}
     */
    private String getFullAppointmentDateDesc(DomainAppointmentTime appointmentTime) {
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        String dateDesc = " ";
        if (DateUtil.isSameDay(new Date(), TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))) {
            dateDesc = "[今天]";
        }
        if (DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))) {
            dateDesc = "[明天]";
        }
        return appointmentTime.formatAppointDate() +
                dateDesc +
                appointmentTime.formatAppointTimeDesc();
    }

    /**
     * handlePromiseCodeInfo
     * {
     * "floorCode": "promiseCodeInfo",
     * "floorName": "履约消费码信息",
     * "floorConfigList": [
     * {
     * "promiseCode":"1231",
     * "noticeTip":"请在护士上门后出示"
     * }
     * ]
     * }
     *
     * @param viaFloorInfo viaFloorInfo
     * @param jdhPromise   jdhPromise
     */
    private void handlePromiseCodeInfo(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        viaFloorConfig.setPromiseCode(jdhPromise.getCode());
        log.info("HomeTestOrderDetailHandlerV2 handlePromiseCodeInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 隐藏楼层
     *
     * @param statusMapping 状态映射
     * @param floorList     楼层列表
     */
    private void clearHiddenFloor(ViaStatusMapping statusMapping, List<ViaFloorInfo> floorList, JdhSkuDto jdhSkuDto) {
        Iterator<ViaFloorInfo> iterator = floorList.iterator();
        while (iterator.hasNext()) {
            ViaFloorInfo viaFloorInfo = iterator.next();
            if (!statusMapping.getShowFloorCode().contains(viaFloorInfo.getFloorCode())) {
                iterator.remove();
            }
        }
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    @SuppressWarnings("all")
    public void handle(FillViaConfigDataContext ctx) {
        log.info("HomeTestOrderDetailHandlerV2 handle ctx:{}", JSON.toJSONString(ctx));
        // ==>>>> 入参校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();

        // ==>>>> 数据获取
        //查订单
        JdOrderDTO jdOrder = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(ctx.getOrderId()).pin(ctx.getUserPin()).querySource("C").build());
        log.info("HomeTestOrderDetailHandlerV2 handle jdOrder:{}", JSON.toJSONString(jdOrder));
        if (Objects.isNull(jdOrder)) {
            throw new SystemException(SupportErrorCode.VIA_ORDER_INFO_NOT_EXIT);
        }

        //查履约单
        String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
        JdhPromise jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).userPin(jdOrder.getUserPin()).build());
        log.info("HomeTestOrderDetailHandlerV2 handle jdhPromise:{}", JSON.toJSONString(jdhPromise));

        List<CompletableFuture> futures = new ArrayList<>();
        //查商品配置
        CompletableFuture<JdhSkuDto> skuDtoCf = null;
        if (CollUtil.isNotEmpty(jdOrder.getJdOrderItemList())) {
            JdOrderItemDTO jdOrderItemDTO = jdOrder.getJdOrderItemList().get(0);
            Long skuId = jdOrderItemDTO.getSkuId();
            if (Objects.nonNull(skuId)) {
                skuDtoCf = CompletableFuture.supplyAsync(() -> querySkuInfo(skuId), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
                futures.add(skuDtoCf);
            }
        }

        //查履约单历史记录
        CompletableFuture<List<JdhPromiseHistory>> promiseHistoryCf = null;
        if (Objects.nonNull(jdhPromise)) {
            promiseHistoryCf = CompletableFuture.supplyAsync(() -> queryPromiseHistory(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(promiseHistoryCf);
        }

        //查检测单
        CompletableFuture<List<MedicalPromiseDTO>> medPromiseListCf = null;
        if (Objects.nonNull(jdhPromise)) {
            medPromiseListCf = CompletableFuture.supplyAsync(() -> queryMedicalPromiseList(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(medPromiseListCf);
        }

        //异步编译 Aviator
        futures.add(CompletableFuture.runAsync(() -> compileAviator(viaConfig), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)));

        if (CollUtil.isNotEmpty(futures)) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }


        try {
            // ==>>>> 过滤statusMapping
            ViaStatusMapping statusMapping = viaComponentDomainService.parseHomeTestMapping(
                    EntityUtil.getFiledDefaultNull(jdOrder, JdOrderDTO::getOrderStatus),
                    EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseStatus),
                    EntityUtil.getFiledDefaultNull(Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(), MedicalPromiseDTO::getStatus),
                    // promise不为空，且appointmentTime不为空，再取isImmediately
                    EntityUtil.getFiledDefaultNull(jdhPromise,
                            e -> EntityUtil.getFiledDefaultNull(e.getAppointmentTime(), time -> time.getIsImmediately())
                    ),
                    null,
                    viaConfig
            );
            log.info("HomeTestOrderDetailHandlerV2 handle statusMapping:{}", JSON.toJSONString(statusMapping));

            // ==>>>> 移除当前状态下隐藏的楼层
            List<ViaFloorInfo> floorList = viaConfig.getFloorList();
            clearHiddenFloor(statusMapping, floorList, Objects.isNull(skuDtoCf) ? null : skuDtoCf.get());
            log.info("HomeTestOrderDetailHandlerV2 handle floorList:{}", JSON.toJSONString(floorList));

            // ==>>>> 楼层处理
            dealFloorList(ctx,
                    statusMapping,
                    jdOrder,
                    jdhPromise,
                    Objects.isNull(promiseHistoryCf) ? null : promiseHistoryCf.get(),
                    Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(),
                    Objects.isNull(skuDtoCf) ? null : skuDtoCf.get());
            log.info("HomeTestOrderDetailHandlerV2 handle viaConfig:{}", JSON.toJSONString(viaConfig));
        } catch (Exception e) {
            log.error("HomeTestOrderDetailHandlerV2 handle error", e);
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_HAND_ERROR);
        }
    }


    public static void main(String[] args) {

//        ViaConfig viaConfig = JSON.parseObject(json, ViaConfig.class);
        String foolorJson = "{\"floorCode\":\"promiseSpecimenCode\",\"floorName\":\"检测样本信息\",\"floorConfigList\":[{\"groupInfoList\":[],\"btnList\":[{\"code\":\"bindSpecimenCode\",\"name\":\"提交样本信息\",\"style\":\"request\",\"action\":{\"type\":\"request\",\"functionId\":\"\",\"params\":{\"verticalCode\":\"verticalCode\",\"serviceType\":\"serviceType\",\"envType\":\"envType\"},\"parseMap\":{\"verticalCode\":{\"filedKey\":\"verticalCode\",\"parseType\":1,\"parseExpression\":\"promise.verticalCode\"},\"serviceType\":{\"filedKey\":\"verticalCode\",\"parseType\":1,\"parseExpression\":\"promise.serviceType\"},\"envType\":{\"filedKey\":\"envType\",\"parseType\":1,\"parseExpression\":\"ctx.envType\"}},\"extendParams\":[{\"paramField\":\"fuzzyStoreName\"}]}}]}]}";
        ViaFloorInfo viaFloorInfo = JSON.parseObject(foolorJson, ViaFloorInfo.class);
        HomeTestOrderDetailHandlerV2 handler = new HomeTestOrderDetailHandlerV2();
        Map<String, Object> sourceData = Maps.newHashMap();

        JdhPromise promise = new JdhPromise();
        List<PromiseService> services = Lists.newArrayList();
        PromiseService service = new PromiseService();
        service.setServiceId(1243565L);
        services.add(service);
        promise.setServices(services);
        promise.setVerticalCode("xfylHomeTest");
        promise.setServiceType("test");
        sourceData.put("promise", promise);

        FillViaConfigDataContext ctx = new FillViaConfigDataContext();
        ctx.setEnvType("envType");
        sourceData.put("ctx", ctx);

        List<MedicalPromiseDTO> medicalPromiseList = Lists.newArrayList();
        MedicalPromiseDTO dto1 = new MedicalPromiseDTO();
        dto1.setServiceItemId("1111");
        dto1.setServiceItemName("检测项目1");
        dto1.setMedicalPromiseId(2345667L);
        dto1.setName("用户一");
        medicalPromiseList.add(dto1);

        MedicalPromiseDTO dto2 = new MedicalPromiseDTO();
        dto2.setServiceItemId("33333");
        dto2.setServiceItemName("检测项目3");
        dto2.setMedicalPromiseId(22345647L);
        dto2.setName("用户一");
        medicalPromiseList.add(dto2);

        MedicalPromiseDTO dto3 = new MedicalPromiseDTO();
        dto3.setServiceItemId("1111");
        dto3.setServiceItemName("检测项目1");
        dto3.setMedicalPromiseId(2143546757L);
        dto3.setName("测试用户二");
        medicalPromiseList.add(dto3);


        MedicalPromiseDTO dto4 = new MedicalPromiseDTO();
        dto4.setServiceItemId("33333");
        dto4.setServiceItemName("检测项目3");
        dto4.setMedicalPromiseId(23469837439L);
        dto4.setName("测试用户二");
        medicalPromiseList.add(dto3);

//        handler.handleFooterMedicalPromise(viaFloorInfo, sourceData, medicalPromiseList);

        System.out.println(JSON.toJSONString(viaFloorInfo));

        ViaFloorInfo tutorialUrlFloor = JSON.parseObject("{\"floorCode\":\"sampleCourseInfo\",\"floorName\":\"采样教程\",\"floorConfigList\":[{\"action\":{\"type\":\"jump\",\"initSwitch\":true,\"url\":\"{JdhService.tutorialUrl}\"}}]}", ViaFloorInfo.class);
        System.out.println("==================");
        System.out.println(JSON.toJSONString(tutorialUrlFloor));

    }

    /**
     * compileAviator
     *
     * @param viaConfig VIA配置
     */
    private void compileAviator(ViaConfig viaConfig) {
        try {
            for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
                if (StrUtil.isNotBlank(viaStatusMapping.getStatusExpression())) {
                    AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.info("HomeTestOrderDetailHandlerV2 handle compileAviator exception", e);
        }
    }

    /**
     * 查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link JdhSkuDto}
     */
    private JdhSkuDto querySkuInfo(Long skuId) {
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    .queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            return jdhSkuDtoMap.get(skuId);
        } catch (Exception e) {
            log.info("HomeTestOrderDetailHandlerV2 handle querySkuInfo exception", e);
            return null;
        }
    }

    /**
     * queryPromiseHistory
     *
     * @param jdhPromise jdh承诺
     * @return {@link List}<{@link JdhPromiseHistory}>
     */
    private List<JdhPromiseHistory> queryPromiseHistory(JdhPromise jdhPromise) {
        try {
            List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(jdhPromise.getPromiseId()).build());
            log.info("HomeTestOrderDetailHandlerV2 handle queryPromiseHistory promiseHistories:{}", JSON.toJSONString(promiseHistories));
            return promiseHistories;
        } catch (Exception e) {
            log.info("HomeTestOrderDetailHandlerV2 handle queryPromiseHistory exception", e);
            return null;
        }
    }

    /**
     * queryMedicalPromiseList
     *
     * @param jdhPromise jdhPromise
     * @return {@link List}<{@link MedicalPromiseDTO}>
     */
    private List<MedicalPromiseDTO> queryMedicalPromiseList(JdhPromise jdhPromise) {
        try {
            MedicalPromiseListRequest medPromiseRequest = new MedicalPromiseListRequest();
            medPromiseRequest.setPromiseId(jdhPromise.getPromiseId());
            medPromiseRequest.setItemDetail(Boolean.TRUE);
            medPromiseRequest.setPatientDetail(Boolean.TRUE);
            List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(medPromiseRequest);
            log.info("HomeTestOrderDetailHandlerV2 handle queryMedicalPromiseList medicalPromiseList:{}", JSON.toJSONString(medicalPromiseList));
            return medicalPromiseList;
        } catch (Exception e) {
            log.info("HomeTestOrderDetailHandlerV2 handle queryMedicalPromiseList exception", e);
            return null;
        }
    }

    /**
     * @param viaFloorInfo       楼层配置
     * @param sourceData         原始数据
     * @param medicalPromiseList 检测单
     * @param jdhPromise         履约单数据
     */
    private void handleFooterMedicalPromise(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData
            , List<MedicalPromiseDTO> medicalPromiseList, JdhPromise jdhPromise) {
        // 过滤已经冻结和作废的检测单
        medicalPromiseList = medicalPromiseList.stream().filter(e -> !Objects.equals(e.getFreeze(), YnStatusEnum.YES.getCode()))
                .filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus())).collect(Collectors.toList());

        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getName));
        List<ViaFloorConfig> floorConfigList = Lists.newArrayList();

        // 获取按钮
        String btnJson;
        Optional<ViaBtnInfo> reportBtn =  viaFloorInfo.getFloorConfigList().stream().filter(e -> CollectionUtils.isNotEmpty(e.getBtnList()))
                .flatMap(e -> e.getBtnList().stream())
                .filter(e -> StringUtils.equals(e.getCode(), ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode()))
                .findFirst();
        if (reportBtn.isPresent()){
            btnJson =  JSON.toJSONString(reportBtn);
        } else {
            btnJson = null;
        }
        medicalPromiseMap.forEach((name, list) -> {
            // 获取有效的的检测样本信息
            List<MedicalPromiseDTO> survive = list.stream()
                    .filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus()))
                    .filter(e -> !Objects.equals(e.getFreeze(), JdhFreezeEnum.FREEZE.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(survive)) {
                return;
            }
            ViaFloorConfig config = new ViaFloorConfig();
            MedicalPromiseDTO dto = survive.get(0);
            List<ViaFormItem> formItemList = Lists.newArrayList();
            ViaFormItem nameItem = new ViaFormItem();
            nameItem.setFormName(ViaSpecimenFieldEnum.PATIENT_NAME.getField());
            nameItem.setValue(UserName.maskTool(dto.getName()));

            ViaFormItem ageItem = new ViaFormItem();
            ageItem.setFormName(ViaSpecimenFieldEnum.PATIENT_AGE.getField());
            ageItem.setValue(Objects.toString(dto.getAge(), null));

            ViaFormItem genderItem = new ViaFormItem();
            genderItem.setFormName(ViaSpecimenFieldEnum.PATIENT_GENDER.getField());
            genderItem.setValue(GenderEnum.getDescOfType(dto.getGender()));

            // 头像
            ViaFormItem headerImageUrl = new ViaFormItem();
            headerImageUrl.setFormName(ViaSpecimenFieldEnum.PATIENT_HEADER_IMAGE.getField());
            String url = viaComponentDomainService.queryPatientHeadImage(dto.getGender(), dto.getAge());
            headerImageUrl.setValue(url);


            ViaFormItem codeItem = new ViaFormItem();
            codeItem.setFormName(ViaSpecimenFieldEnum.SPECIMEN_CODE.getField());
            List<String> codes = survive.stream().map(MedicalPromiseDTO::getSpecimenCode).collect(Collectors.toList());
            codeItem.setValue(String.join(",", codes));
            formItemList.add(codeItem);

            formItemList.add(nameItem);
            formItemList.add(ageItem);
            formItemList.add(genderItem);
            formItemList.add(headerImageUrl);

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            Map<Long, JdhPromisePatient> patientMap = patients.stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity(), (o, n) -> o));

            // 报告按钮，当然人下存在任意一个已出报告的样本，则可以查看报告
            if (survive.stream().anyMatch(e -> Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.COMPLETED.getStatus()))) {

                if (StringUtils.isNotBlank(btnJson)){
                    // 此处不能使用原生的sourceData，保证隔离
                    //TODO 修改按扭展示逻辑
                    Map<String, Object> dataMap = Maps.newHashMap(sourceData);
                    JdhPromisePatient patient = patientMap.get(dto.getPromisePatientId());
                    dataMap.put(SupportAggregateEnum.PATIENT.getCode(), patient);
                    ViaBtnInfo btn = JSON.parseObject(btnJson, ViaBtnInfo.class);
                    btn.init(dataMap);
                    List<ViaBtnInfo> resBtns = Lists.newArrayList(btn);

                    List<Long> medicalPromiseIds = survive.stream()
                            .filter(e -> MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(e.getStatus()))
                            .map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());

                    Integer count = medicalReportApplication.queryAbnormalCount(medicalPromiseIds);
                    if (Objects.nonNull(count)) {
                        ViaFormItem abnormal = new ViaFormItem();
                        abnormal.setFormName(ViaSpecimenFieldEnum.ABNORMAL_NUM.getField());
                        abnormal.setValue(String.valueOf(count));
                        formItemList.add(abnormal);
                    }
                    config.setBtnList(resBtns);
                    // 统计异常项目
                    config.setStatusDesc("报告已出");
                    config.setViaStatus(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                }

            } else {
                Set<Integer> statusSet = survive.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toSet());
                if (statusSet.contains(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus())) {
                    config.setStatusDesc("检测中");
                    config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
                } else {
                    config.setStatusDesc("送检中");
                    config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                }
            }
            config.setFormItemList(formItemList);
            floorConfigList.add(config);
        });
        viaFloorInfo.setFloorConfigList(floorConfigList);
        log.info("HomeTestOrderDetailHandlerV2 handleFooterMedicalPromise viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 处理采样教程楼层，当前只有自采样的业务需要采样教程。
     * 互医的场景可能有多个SKU，但是只取了一个SKU的采样教程，业务上保证互医渠道的SKU配置一个通用的采样教程。
     * 理想情况下采样教程应该取检测项目维度的。
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleFooterSampleCourse(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
        JdhSkuDto skuDto = findMainSku(jdhPromise);
        if (StringUtils.isNotBlank(skuDto.getTutorialVideoThumbnailUrl())) {
            ViaFloorConfig header = new ViaFloorConfig();
            header.setFieldKey(ViaSampleCourseFiledEnum.VIDEO_HEADER_IMAGE_URL.getField());
            header.setFieldValue(skuDto.getTutorialVideoThumbnailUrl());
            viaFloorConfigList.add(header);
        }

        if (StringUtils.isNotBlank(skuDto.getTutorialVideoUrl())) {
            ViaFloorConfig video = new ViaFloorConfig();
            video.setFieldKey(ViaSampleCourseFiledEnum.VIDEO_URL.getField());
            video.setFieldValue(skuDto.getTutorialVideoUrl());
            viaFloorConfigList.add(video);
        }

        if (CollectionUtils.isNotEmpty(skuDto.getTutorialCarouselUrl())) {
            ViaFloorConfig image = new ViaFloorConfig();
            image.setFieldKey(ViaSampleCourseFiledEnum.IMAGES_URL.getField());
            image.setFieldValue(JSON.toJSONString(skuDto.getTutorialCarouselUrl()));
            viaFloorConfigList.add(image);
        }

        viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        log.info("HomeTestOrderDetailHandlerV2 handleFooterSampleCourse viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    private void handelMaterialInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        if (CollUtil.isNotEmpty(medicalPromiseList)) {
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();
                ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                JdhPromisePatient jdhPromisePatient = promisePatientMap.get(medPromiseList.get(0).getPromisePatientId());
                String userName = jdhPromisePatient.getUserName().getName();
                String genderLabel = GenderEnum.getDescOfType(jdhPromisePatient.getGender());
                String age = jdhPromisePatient.getBirthday().getAge() + "岁";
//                String title = String.join(" ", userName, genderLabel, age)
//                viaFloorConfig.setTitle(title);

                //是否有已出报告,且没有退款
                List<MedicalPromiseDTO> reportedList = medPromiseList.stream().filter(ele ->
                        (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) &&
                                !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                                        || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))
                ).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(reportedList)) {
                    MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                    fillMedPromiseFloorConfig(viaFloorConfig, reportedMedPromise, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                    viaFloorConfigList.add(viaFloorConfig);
                } else {
                    for (MedicalPromiseDTO medicalPromiseDTO : medPromiseList) {
                        // 冻结或者作废不展示
                        if (JdhFreezeEnum.FREEZE.getStatus().equals(medicalPromiseDTO.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDTO.getStatus())) {
                            continue;
                        }
                        fillMedPromiseFloorConfig(viaFloorConfig, medicalPromiseDTO, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                        viaFloorConfigList.add(viaFloorConfig);
                    }
                }
            }
            viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        }
        log.info("HomeTestOrderDetailHandlerV2 handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.HOME_ORDER_DETAIL.getScene() + "_" + BusinessModeEnum.SELF_TEST.getCode()
                + "_" + ServiceTypeEnum.TEST.getServiceType() + "_" + "V2";
    }


    /**
     * 构建联系客服的按钮
     *
     * @param btnInfo
     * @param ctx
     * @param jdOrder
     * @param action
     */
    private void buildContactCustomerBtn(ViaBtnInfo btnInfo, FillViaConfigDataContext ctx, JdOrderDTO jdOrder, ViaActionInfo action) {
        //联系客服
        if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
            String envCode = EnvTypeEnum.get(ctx.getEnvType()).getCode();
            Map<String, String> urlMap = JSON.parseObject(btnInfo.getJumpUrlRule(), new TypeReference<Map<String, String>>() {
            });
            String jumpUrl = urlMap.get(envCode);
            if (EnvTypeEnum.JD_APP.getCode().equals(ctx.getEnvType())) {
                //openapp.jdmobile://virtual?params={"category":"jump","des":"jd_dongdong_chat","entry":"jd_sdk_kjzydxy","orderId":"{0}"}
                action.setUrl(jumpUrl.replace("{0}", jdOrder.getVenderId()).replace("{1}", jdOrder.getOrderId().toString()));
            } else {
                //https://jdcs.m.jd.com/chat/index.action?entry=jd_sdk_kjzydxy&orderId={0}
                action.setUrl(MessageFormat.format(jumpUrl, jdOrder.getVenderId(), jdOrder.getOrderId().toString()));
            }
        }
    }
}
