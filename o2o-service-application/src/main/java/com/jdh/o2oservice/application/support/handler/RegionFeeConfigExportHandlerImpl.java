package com.jdh.o2oservice.application.support.handler;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfig;
import com.jdh.o2oservice.core.domain.product.repository.JdhRegionFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhRegionFeeConfigQuery;
import com.jdh.o2oservice.core.domain.support.file.context.FileExportContext;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileExportHandler;
import com.jdh.o2oservice.export.support.query.ExportRegionFeeConfigRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.BlockingQueue;

@Slf4j
@Service
public class RegionFeeConfigExportHandlerImpl extends AbstractFileExportHandler {

    @Resource
    private JdhRegionFeeConfigRepository jdhRegionFeeConfigRepository;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private DuccConfig duccConfig;

    @Override
    public String getMapKey() {
        return FileExportTypeEnum.REGION_FEE_CONFIG_EXPORT.getType();
    }

    @Override
    protected void preHandle(FileExportContext ctx) {

    }

    @Override
    protected void getData(FileExportContext ctx) {
        log.info("RegionFeeConfigExportHandlerImpl getData ctx={}", JSON.toJSONString(ctx));
        Map<String, Object> queryParam = ctx.getQueryParam();
        ExportRegionFeeConfigRequest query = Convert.convert(ExportRegionFeeConfigRequest.class, queryParam.get("query"));
        log.info("RegionFeeConfigExportHandlerImpl getData query={}", JSON.toJSONString(query));
        boolean hasNextPage = true;
        int pageNum = 1;
        BlockingQueue<Map<String, Object>> queue = ctx.getQueue();
        do {
            JdhRegionFeeConfigQuery regionFeeConfigQuery = new JdhRegionFeeConfigQuery();
            regionFeeConfigQuery.setPageNum(pageNum);
            regionFeeConfigQuery.setPageSize(NumConstant.NUM_500);
            regionFeeConfigQuery.setChannelId(query.getChannelId());
            regionFeeConfigQuery.setServiceType(query.getServiceType());
            log.info("RegionFeeConfigExportHandlerImpl getData do query:{}",JSON.toJSONString(query));
            Page<JdhRegionFeeConfig> page = jdhRegionFeeConfigRepository.queryPageRegionFeeConfig(regionFeeConfigQuery);
            log.info("RegionFeeConfigExportHandlerImpl getData do page={}",JSON.toJSONString(page));
            if(Objects.isNull(page) || CollUtil.isEmpty(page.getRecords())){
                log.info("RegionFeeConfigExportHandlerImpl getData do page empty query:{}",JSON.toJSONString(query));
                hasNextPage = false;
                continue;
            }
            List<JdhRegionFeeConfig> list = page.getRecords();
            if(CollUtil.isNotEmpty(list)){
                for (JdhRegionFeeConfig regionFeeConfig : list) {
                    Map<String, String> prefixDescMap = duccConfig.getRegionFeeDestPrefixDesc();
                    if (StringUtil.isNotBlank(regionFeeConfig.getDestPrefix())){
                        regionFeeConfig.setDestPrefixDesc(prefixDescMap.get(regionFeeConfig.getDestPrefix()));
                    }
                    Map<String, Object> column = new HashMap<>();
                    column.put(ProductAggregateEnum.PRODUCT_REGION_FEE_CONFIG.getCode(), regionFeeConfig);
                    queue.add(column);
                }
            }
            pageNum++;
        }while (hasNextPage);
        log.info("RegionFeeConfigExportHandlerImpl getData 结束, queue={}", JSON.toJSONString(queue));
    }
}
