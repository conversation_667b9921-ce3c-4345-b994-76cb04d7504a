package com.jdh.o2oservice.application.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.addresstranslation.api.base.JDAddressRequestInfo;
import com.jd.boundaryless.lbs.center.spi.store.to.NearStoreInfoTO;
import com.jd.common.util.StringUtils;
import com.jd.health.medical.examination.export.dto.GoodsStoreDTO;
import com.jd.health.medical.examination.export.param.AppointStoreParam;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.trade.guide.sdk.domain.result.GuideMultiResultModule;
import com.jd.trade.guide.sdk.domain.result.GuideResultModule;
import com.jdh.market.core.promo.coupon.query.SalesAndCouponBatchQuery;
import com.jdh.market.core.promo.coupon.query.SkuStoreSalesAndCouponBatchQuery;
import com.jdh.market.material.export.dto.TttMaterialStoreDTO;
import com.jdh.o2oservice.application.product.service.ProductPoolApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.SuitableEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.BizCategorySceneEnum;
import com.jdh.o2oservice.common.enums.ProgramSaleShowMethodTypeEnum;
import com.jdh.o2oservice.core.domain.product.bo.ProgramItemIndicatorBo;
import com.jdh.o2oservice.core.domain.product.bo.ServiceProgramItemIndicatorRelBo;
import com.jdh.o2oservice.core.domain.product.bo.UserAddressDetailBO;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.*;
import com.jdh.o2oservice.core.domain.product.repository.query.*;
import com.jdh.o2oservice.core.domain.product.service.ProductDomainService;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseTypeEnum;
import com.jdh.o2oservice.core.domain.provider.rpc.VenderBasicRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.StoreGuaranteeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.PopLocStoreInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.ServiceDetailRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.ServiceDetailRpcParam;
import com.jdh.o2oservice.core.domain.support.category.model.JdhBizCategory;
import com.jdh.o2oservice.core.domain.support.category.repository.JdhBizCategoryRepository;
import com.jdh.o2oservice.core.domain.support.category.repository.query.BizCategoryRepQuery;
import com.jdh.o2oservice.core.domain.trade.bo.XfylOrderUserAddressBO;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderTrackRpcService;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.ProductServiceGoodsListRequest;
import com.jdh.o2oservice.export.product.query.ServiceGoodsContrastRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;



/**
 * @ClassName ProductApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 11:40
 **/
@Service
@Slf4j
public class ProductPoolApplicationImpl implements ProductPoolApplication {

    /**
     * jdhUserContrastPoolRepository
     */
    @Autowired
    private JdhUserContrastPoolRepository jdhUserContrastPoolRepository;
    /**
     * jdhUserContrastPoolRepository
     */
    @Autowired
    private JdhContrastSourceRepository jdhContrastSourceRepository;
    /**
     * 标准项目仓储
     */
    @Resource
    private JdhStandardItemRepository jdhStandardItemRepository;
    /**
     * jdhProgramRepository
     */
    @Autowired
    private JdhProgramRepository jdhProgramRepository;

    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;
    /**
     * skuInfoRpc
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * popLocStoreInfoRpc
     */
    @Resource
    private PopLocStoreInfoRpc popLocStoreInfoRpc;
    /**
     * serviceDetailRpc
     */
    @Autowired
    private ServiceDetailRpc serviceDetailRpc;
    /** */
    @Resource
    private VenderBasicRpc venderBasicRpc;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;
    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;
    /**
     * orderTrackRpcService
     */
    @Autowired
    private OrderTrackRpcService orderTrackRpcService;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 标准指标仓储
     */
    @Resource
    private JdhStandardIndicatorRepository jdhStandardIndicatorRepository;
    /**
     * jdhBizItemRepository
     */
    @Resource
    private JdhBizItemRepository jdhBizItemRepository;
    /**
     * JdhIndicatorCategoryRepository
     */
    @Resource
    private JdhBizCategoryRepository jdhBizCategoryRepository;

    @Resource
    private ProductDomainService productDomainService;

    /**
     * 检查项数量是否符合
     *
     * @param skuId
     * @return
     */
    @Override
    public Boolean goodsCheckItemAccord(String skuId) {
        Integer result = this.getGoodsCacheCount(skuId);
        return result >= CommonConstant.FIVE;
    }

    /**
     * 检查项数量
     * @param skuId
     * @return
     */
    private Integer getGoodsCacheCount(String skuId){
        // 0.查询缓存（10分钟）是否符合
        RedisKeyEnum keyEnum = RedisKeyEnum.SERVICE_ITEM_COUNT_LOCK_KEY;
        String cacheKey = RedisKeyEnum.getRedisKey(keyEnum,skuId);
        String redisResult = jimClient.get(cacheKey);
        if(StringUtils.isNotBlank(redisResult)){
            return Integer.parseInt(redisResult);
        }

        RpcSkuBO rpcSkuBO = skuInfoRpc.getCrsSkuBoBySkuId(skuId);
        Boolean judgeIsPopRes = skuInfoRpc.judgeIsPopBySku(skuId);
        List<JdhProgram> jdhProgramPoList = getJdhProgramPoList(skuId,judgeIsPopRes,rpcSkuBO);
        if(CollUtil.isEmpty(jdhProgramPoList)){
            return 0;
        }

        try{
            this.checkServiceGoodsToPool(rpcSkuBO,judgeIsPopRes);
        }catch (Exception e){
            log.error("ProductPoolApplicationImpl -> checkServiceGoodsToPool error:", e);
            return 0;
        }
        Integer result = queryProgramIndicatorCount(jdhProgramPoList);
        jimClient.setEx(cacheKey,String.valueOf(result),keyEnum.getExpireTime(),keyEnum.getExpireTimeUnit());
        return result;
    }

    /**
     *
     * @param skuId
     * @param judgeIsPopRes
     * @param rpcSkuBO
     * @return
     */
    private List<JdhProgram> getJdhProgramPoList(String skuId,Boolean judgeIsPopRes,RpcSkuBO rpcSkuBO){
        JdhProgramSkuQuery jdhProgramQuery = new JdhProgramSkuQuery();
        if(judgeIsPopRes){
            jdhProgramQuery.setProductId(rpcSkuBO.getProductId());
        }else{
            jdhProgramQuery.setSkuId(skuId);
        }
        List<JdhProgram> jdhProgramList = jdhProgramRepository.queryProgramListBySkuOrProductId(jdhProgramQuery);
        if(CollUtil.isNotEmpty(jdhProgramList)){
            JdhProgram jdhProgram = jdhProgramList.get(0);
            if (ProgramSaleShowMethodTypeEnum.COMPOSE.getTypeNo().equals(jdhProgram.getSaleShowMethod())) {
                return jdhProgram.getSubProgramList();
            }else{
                return jdhProgramList;
            }
        }
        return new ArrayList<>();
    }

    /**
     *
     * @param skuId
     * @return
     */
    private JdhProgramCategory getJdhProgramPoList(String skuId){
        JdhProgramSkuQuery jdhProgramQuery = new JdhProgramSkuQuery();
        RpcSkuBO rpcSkuBO = skuInfoRpc.getCrsSkuBoBySkuId(skuId);
        Boolean judgeIsPopRes = skuInfoRpc.judgeIsPopBySku(skuId);
        if(judgeIsPopRes){
            jdhProgramQuery.setProductId(rpcSkuBO.getProductId());
        }else{
            jdhProgramQuery.setSkuId(skuId);
        }
        JdhProgramCategory jdhProgramCategory = new JdhProgramCategory();
        List<JdhProgram> jdhProgramList = jdhProgramRepository.queryProgramListBySkuOrProductId(jdhProgramQuery);
        if(CollUtil.isNotEmpty(jdhProgramList)){
            JdhProgram jdhProgram = jdhProgramList.get(0);
            if (ProgramSaleShowMethodTypeEnum.COMPOSE.getTypeNo().equals(jdhProgram.getSaleShowMethod())) {
                jdhProgramCategory.setSaleShowMethod(jdhProgram.getSaleShowMethod());
                jdhProgramCategory.setProgramList(jdhProgram.getSubProgramList());
                return jdhProgramCategory;
            }else{
                jdhProgramCategory.setProgramList(jdhProgramList);
                return jdhProgramCategory;
            }
        }
        return jdhProgramCategory;
    }

    /**
     *
     * @param skuId
     * @return
     */
    private ServiceProgramItemIndicatorRelBo getProgramItemIndicatorRelList(String skuId){
        JdhProgramCategory jdhProgramCategory = getJdhProgramPoList(skuId);
        List<JdhProgram> jdhProgramList = jdhProgramCategory.getProgramList();
        if(CollUtil.isEmpty(jdhProgramList)){
            log.error("ProductPoolApplicationImpl -> getProgramItemIndicatorRelList jdhProgramList is null:");
            return null;
        }
        ServiceProgramItemIndicatorRelBo serviceProgramItemIndicatorRelBo = new ServiceProgramItemIndicatorRelBo();
        serviceProgramItemIndicatorRelBo.setSkuNo(skuId);

        List<ProgramItemIndicatorBo> programItemIndicatorBoList = new ArrayList<>();
        Set<String> skuSuitableSet = new HashSet<>();
        // 查询查询自营体检商品服务指标对象
        Map<Long,List<JdhProgramBizItemRel>> map = getProgramBizItemMap(jdhProgramList);
        if(CollectionUtil.isNotEmpty(map)){
            for(JdhProgram jdhProgram : jdhProgramList){
                ProgramItemIndicatorBo programItemIndicatorBo = new ProgramItemIndicatorBo();
                programItemIndicatorBo.setProgramId(jdhProgram.getProgramId());
                programItemIndicatorBo.setProgramName(jdhProgram.getProgramName());

                serviceProgramItemIndicatorRelBo.setSaleShowMethod(jdhProgramCategory.getSaleShowMethod());

                List<BizItemCategory> bizItemCategoryList = new ArrayList<>();
                programItemIndicatorBo.setBizItemCategoryList(bizItemCategoryList);

                List<JdhProgramBizItemRel> programBizItemRelList = map.get(jdhProgram.getProgramId());
                if(CollectionUtil.isNotEmpty(programBizItemRelList)){
                    // 套餐对应的项目id List
                    List<Long> bizItemIdList = programBizItemRelList.stream().map(JdhProgramBizItemRel::getBizItemId).collect(Collectors.toList());
                    // 重点项目list
                    Map<Long,JdhProgramBizItemRel> importantMap = programBizItemRelList.stream().filter(item -> item.getIsImportant() == NumConstant.NUM_1).
                            collect(Collectors.toMap(JdhProgramBizItemRel::getBizItemId, Function.identity(), (o1, o2) -> o2));
                    // 项目id List 查项目list
                    JdhBizItemReqQuery query = JdhBizItemReqQuery.builder().bizItemIdList(bizItemIdList).build();
                    List<JdhBizItem> jdhBizItemList = jdhBizItemRepository.queryList(query);
                    if(CollectionUtil.isNotEmpty(jdhBizItemList)){
                        List<Long> standardItemIdList = jdhBizItemList.stream().map(JdhBizItem::getStandardItemId).collect(Collectors.toList());
                        Map<Long,String> standardItemNameMap = getStandardItemNameMap(standardItemIdList);
                        Map<Long,Integer> bizItemAndIndicatorCountMap = getBizItemAndIndicatorCountMap(bizItemIdList);
                        Map<Long,JdhBizCategory> indicatorCategoryMap = getIndicatorCategoryMap(jdhBizItemList);
                        for(JdhBizItem jdhBizItem : jdhBizItemList){
                            BizItemCategory bizItemCategory = new BizItemCategory();
                            bizItemCategory.setBizItemId(jdhBizItem.getBizItemId());
                            bizItemCategory.setProgramId(jdhProgram.getProgramId());
                            bizItemCategory.setStandardItemId(jdhBizItem.getStandardItemId());
                            bizItemCategory.setSecondBizCategoryId(jdhBizItem.getSecondBizCategory());
                            if(CollUtil.isEmpty(indicatorCategoryMap) || Objects.isNull(indicatorCategoryMap.get(jdhBizItem.getSecondBizCategory()))){
                                log.error("ProductPoolApplicationImpl secondBizCategory is null:{}",jdhBizItem.getSecondBizCategory());
                                continue;
                            }
                            bizItemCategory.setSecondBizCategoryName(indicatorCategoryMap.get(jdhBizItem.getSecondBizCategory()).getCategoryName());
                            bizItemCategory.setItemSuitable(jdhBizItem.getSuitable());
                            if(CollUtil.isEmpty(jdhProgram.getSuitable())){
                                skuSuitableSet.addAll(jdhBizItem.getSuitable());
                            }

                            String standardItemName = standardItemNameMap.get(jdhBizItem.getStandardItemId());
                            if(StringUtil.isBlank(standardItemName)){
                                standardItemName = jdhBizItem.getBizItemName();
                            }
                            bizItemCategory.setImportantItem(Objects.nonNull(importantMap.get(jdhBizItem.getBizItemId())));
                            Integer indicatorCount = bizItemAndIndicatorCountMap.get(jdhBizItem.getBizItemId());
                            if(Objects.nonNull(indicatorCount) && indicatorCount > NumConstant.NUM_0){
                                String itmeNameSuffix = "(X项)";
                                itmeNameSuffix = itmeNameSuffix.replace("X",indicatorCount.toString());
                                bizItemCategory.setBizItemName(standardItemName + itmeNameSuffix);
                            }else{
                                bizItemCategory.setBizItemName(standardItemName);
                            }
                            bizItemCategoryList.add(bizItemCategory);
                        }

                        if(CollUtil.isEmpty(jdhProgram.getSuitable())){
                            jdhProgram.setSuitable(new ArrayList<>(skuSuitableSet));
                        }else{
                            skuSuitableSet.addAll(jdhProgram.getSuitable());
                            jdhProgram.setSuitable(jdhProgram.getSuitable());
                        }
                        programItemIndicatorBo.setProgramSuitable(jdhProgram.getSuitable());
                    }
                }
                programItemIndicatorBoList.add(programItemIndicatorBo);
            }
        }
        serviceProgramItemIndicatorRelBo.setProgramSuitable(new ArrayList<>(skuSuitableSet));
        serviceProgramItemIndicatorRelBo.setProgramItemIndicatorBoList(programItemIndicatorBoList);
        return serviceProgramItemIndicatorRelBo;
    }

    /**
     * 指标分类列表
     * @param jdhBizItemList
     * @return
     */
    private Map<Long,JdhBizCategory> getIndicatorCategoryMap(List<JdhBizItem> jdhBizItemList){
        List<Long> secondBizCategoryList = jdhBizItemList.stream().map(JdhBizItem::getSecondBizCategory).collect(Collectors.toList());
        BizCategoryRepQuery bizCategoryRepQuery = BizCategoryRepQuery.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode())
                .categoryIdList(secondBizCategoryList)
                .categoryLevel(NumConstant.NUM_2)
                .build();
        List<JdhBizCategory> bizCategories = jdhBizCategoryRepository.queryJdhBizCategoryList(bizCategoryRepQuery);
        if (CollectionUtils.isNotEmpty(bizCategories)){
            return bizCategories.stream().collect(Collectors.toMap(JdhBizCategory::getCategoryId, Function.identity(), (o1, o2) -> o2));
        }
        return null;
    }

    /**
     * 标准项目名称
     * @param standardItemIdList
     * @return
     */
    private Map<Long,String> getStandardItemNameMap(List<Long> standardItemIdList){
        // 根据业务项目idList查询业务项目标准指标关联关系
        JdhStandardItemRepQuery standardItemRepQuery = JdhStandardItemRepQuery.builder().itemIdList(standardItemIdList).build();
        List<JdhStandardItem> standardItems = jdhStandardItemRepository.queryList(standardItemRepQuery);

        Map<Long,String> standardItemNameMap = new HashMap<>();
        for (JdhStandardItem jdhStandardItem : standardItems) {
            standardItemNameMap.put(jdhStandardItem.getItemId(),jdhStandardItem.getItemName());
        }
        return standardItemNameMap;
    }

    /**
     * 指标分类列表
     * @param bizItemIdList
     * @return
     */
    private Map<Long,Integer> getBizItemAndIndicatorCountMap(List<Long> bizItemIdList){
        // 根据业务项目idList查询业务项目标准指标关联关系
        List<JdhBizItemStandardIndicatorRel> bizItemStandardIndicatorRelList = jdhBizItemRepository.queryItemIndicatorRelByBizItemIds(bizItemIdList);
        // 按照业务项目id分组
        Map<Long, List<JdhBizItemStandardIndicatorRel>> bizItemStandardIndicatorRelMap = bizItemStandardIndicatorRelList.stream().collect(Collectors
                .groupingBy(JdhBizItemStandardIndicatorRel::getBizItemId));

        Map<Long,Integer> bizItemAndIndicatorCountMap = new HashMap<>();
        for (Long bizItemId : bizItemIdList) {
            List<JdhBizItemStandardIndicatorRel> itemIndicatorRelList = bizItemStandardIndicatorRelMap.get(bizItemId);
            if (CollectionUtils.isEmpty(itemIndicatorRelList)){
                continue;
            }
            bizItemAndIndicatorCountMap.put(bizItemId,itemIndicatorRelList.size());
        }
        return bizItemAndIndicatorCountMap;
    }

    /**
     * 查询业务项目对应的指标
     *
     * @param bizItemId
     * @return
     */
    public List<JdhStandardIndicator> queryIndicatorListById(Long bizItemId) {
        JdhBizItemStandardIndicatorRel jdhBizItemStandardIndicatorRel = new JdhBizItemStandardIndicatorRel();
        jdhBizItemStandardIndicatorRel.setBizItemId(bizItemId);
        List<JdhBizItemStandardIndicatorRel> list = jdhBizItemRepository.queryBizItemStandardIndicatorRelList(jdhBizItemStandardIndicatorRel);
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<Long> standardIndicatorIdList = list.stream().map(JdhBizItemStandardIndicatorRel::getStandardIndicatorId).collect(Collectors.toList());
        JdhStandardIndicatorRepQuery query = JdhStandardIndicatorRepQuery.builder().indicatorIdList(standardIndicatorIdList).build();
        List<JdhStandardIndicator> jdhStandardIndicatorList = jdhStandardIndicatorRepository.queryList(query);
        return jdhStandardIndicatorList;
    }

    /**
     * 查询自营体检商品服务指标数量
     * @param jdhProgramPoList
     * @return
     */
    private Integer queryProgramIndicatorCount(List<JdhProgram> jdhProgramPoList){
        // 查询查询自营体检商品服务指标对象
        Map<Long,List<JdhProgramBizItemRel>> map = getProgramBizItemMap(jdhProgramPoList);
        // 统计体检服务指标数量
        if(CollectionUtil.isNotEmpty(map)){
            for(JdhProgram jdhProgram : jdhProgramPoList){
                List<JdhProgramBizItemRel> programBizItemRelList = map.get(jdhProgram.getProgramId());
                if(CollectionUtil.isNotEmpty(programBizItemRelList)){
                    Integer indicatorCount = programBizItemRelList.size();
                    if(indicatorCount >= CommonConstant.FIVE) {
                        return indicatorCount;
                    }
                }
            }
        }
        return 0;
    }


    /**
     * 查询自营体检商品服务指标数量
     * @param jdhProgramPoList
     * @return
     */
    private Map<Long,List<JdhProgramBizItemRel>> getProgramBizItemMap(List<JdhProgram> jdhProgramPoList){
        log.info("ProductPoolApplicationImpl -> getProgramBizItemMap jdhProgramPoList:{}",jdhProgramPoList);

        // 查询查询自营体检商品服务指标对象
        Set<Long> programIdList = jdhProgramPoList.stream().map(JdhProgram::getProgramId).collect(Collectors.toSet());
        List<JdhProgramBizItemRelQuery> jdhProgramBizItemRelQuery = programIdList.stream().map(s -> JdhProgramBizItemRelQuery.builder().programId(s).build()).collect(Collectors.toList());
        List<JdhProgramBizItemRel> programBizItemRelList = jdhProgramRepository.queryJdhProgramItemRelList(jdhProgramBizItemRelQuery);
        // 统计体检服务指标数量
        if(CollectionUtil.isNotEmpty(programBizItemRelList)){
            return programBizItemRelList.stream().collect(Collectors.groupingBy(JdhProgramBizItemRel::getProgramId));
        }
        return null;
    }


    /**
     * 套餐对比加入套餐池
     * @return
     */
    @Override
    public Boolean addServiceGoodsToPool(String userPin,String skuId) {
        List<JdhUserContrastPool> list = jdhUserContrastPoolRepository.listByUserPin(userPin);
        if(CollectionUtil.isNotEmpty(list)){
            AssertUtils.isTrue(list.size() >= CommonConstant.NUMBER_FIFTEEN, ProductErrorCode.SERVICE_POOL_RANG_OUT);
            Long count = list.stream().filter(item -> item.getContrastId().equals(skuId)).count();
            AssertUtils.isTrue(count > CommonConstant.ZERO, ProductErrorCode.SERVICE_IN_POOL_EXIST);
        }
        Integer indicatorCount = this.getGoodsCacheCount(skuId);
        if(indicatorCount < CommonConstant.FIVE){
            return false;
        }
        if(saveServiceGoodsToSource(skuId,indicatorCount)){
            this.saveServicePool(userPin,skuId);
        }
        return true;
    }

    /**
     *
     * @param rpcSkuBO
     * @param judgeIsPopRes
     */
    private void checkServiceGoodsToPool(RpcSkuBO rpcSkuBO,Boolean judgeIsPopRes){
        if(Objects.isNull(rpcSkuBO) || Objects.isNull(rpcSkuBO.getSkuStatus()) || rpcSkuBO.getSkuStatus() != 1){
            throw new BusinessException(ProductErrorCode.SERVICE_NOT_EXIST);
        }
        log.info("ProductPoolApplicationImpl -> checkServiceGoodsToPool rpcSkuBO={}",rpcSkuBO);
        // 1.查询商品是自营还是pop，
        // 2.自营：运营端查询商品 group   pop：查询商品中台，
        if(judgeIsPopRes){
            if(StringUtils.isBlank(rpcSkuBO.getDdsplx()) || (!"1".equals(rpcSkuBO.getDdsplx()) && !"2".equals(rpcSkuBO.getDdsplx()))){
                throw new BusinessException(ProductErrorCode.SERVICE_NOT_LOC);
            }
            Integer secondCategoryId = rpcSkuBO.getSecondCategoryId();
            if(Objects.isNull(secondCategoryId) || (!secondCategoryId.equals(27451))){
                throw new BusinessException(ProductErrorCode.SERVICE_NOT_LOC);
            }
            Integer thirdCategoryId = rpcSkuBO.getThirdCategoryId();
            log.info("ProductPoolApplicationImpl -> checkServiceGoodsToPool thirdCategoryId={}",thirdCategoryId);
            if(Objects.isNull(thirdCategoryId) || (!thirdCategoryId.equals(27452) && !thirdCategoryId.equals(27453))){
                throw new BusinessException(ProductErrorCode.SERVICE_NOT_LOC);
            }
        }else{
            if(StringUtils.isBlank(rpcSkuBO.getXfyl()) || (!"1".equals(rpcSkuBO.getXfyl()))){
                throw new BusinessException(ProductErrorCode.SERVICE_NOT_LOC);
            }
        }
    }

    /**
     *
     * @param skuId
     */
    private Boolean saveServiceGoodsToSource(String skuId,Integer indicatorCount){
        JdhContrastSource jdhContrastSource = jdhContrastSourceRepository.queryContrastSourceByContrastId(skuId);
        if(Objects.nonNull(jdhContrastSource)){
            Date updateTime = jdhContrastSource.getUpdateTime();
            Long diffTime = System.currentTimeMillis() - updateTime.getTime();
            if(diffTime < duccConfig.getSkuServiceExpireTime()){
                log.info("ProductPoolApplicationImpl -> saveServiceGoodsToSource skuId={}, diffTime={}", skuId,diffTime);
                return true;
            }
        }

        ServiceProgramItemIndicatorRelBo serviceProgramItemIndicatorRelBo = getProgramItemIndicatorRelList(skuId);
        if(Objects.isNull(serviceProgramItemIndicatorRelBo) || CollUtil.isEmpty(serviceProgramItemIndicatorRelBo.getProgramItemIndicatorBoList())){
            return false;
        }
        Boolean judgeIsPopRes = skuInfoRpc.judgeIsPopBySku(skuId);
        serviceProgramItemIndicatorRelBo.setIsPopSku(judgeIsPopRes);
        serviceProgramItemIndicatorRelBo.setIndicatorCount(indicatorCount);
        String lockKey = MessageFormat.format(RedisKeyEnum.SERVICE_SOURCE_UPDATE_LOCK_KEY.getRedisKeyPrefix(), skuId);
        try{
            boolean lockFlag = redisLockUtil.tryLockWithRetry(lockKey,UUID.fastUUID().toString(),3000,3,200);
            if(lockFlag){
                log.info("ProductPoolApplicationImpl -> saveServiceGoodsToSource lockFlag");
                if(Objects.isNull(jdhContrastSource)){
                    RpcSkuBO rpcSkuBO = skuInfoRpc.getCrsSkuBoBySkuId(skuId);
                    saveJdhContrastSource(rpcSkuBO,serviceProgramItemIndicatorRelBo);
                }else{
                    if(Objects.nonNull(serviceProgramItemIndicatorRelBo)){
                        updateJdhContrastSource(skuId,serviceProgramItemIndicatorRelBo);
                    }
                }
            }
            return true;
        }catch (Exception ex) {
            throw ex;
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     *
     * @param skuIds
     * @return
     */
    private Map<String, PriceResultBO> getSkuJdPrice(Set<String> skuIds){
        Map<String, PriceResultBO> skuPrices = skuInfoRpc.getSkuPriceByPin(skuIds,null);
        if(CollUtil.isNotEmpty(skuPrices)){
            return skuPrices;
        }
        return Maps.newHashMap();
    }
    /**
     * 保存服务资源
     * @param rpcSkuBO
     * @param serviceProgramItemIndicatorRelBo
     */
    private void saveJdhContrastSource(RpcSkuBO rpcSkuBO,ServiceProgramItemIndicatorRelBo serviceProgramItemIndicatorRelBo){
        JdhContrastSource jdhContrastSource = JdhContrastSource.builder().build();
        jdhContrastSource.setSourceId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdhContrastSource.setContrastId(rpcSkuBO.getSkuId());
        jdhContrastSource.setMainImg(rpcSkuBO.getImgDfsUrl());
        jdhContrastSource.setName(rpcSkuBO.getSkuName());
        jdhContrastSource.setCabinetStatus(String.valueOf(rpcSkuBO.getSkuStatus()));
        jdhContrastSource.setContrastContent(JsonUtil.toJSONString(serviceProgramItemIndicatorRelBo));
        jdhContrastSourceRepository.save(jdhContrastSource);
    }

    /**
     * 保存服务资源
     * @param skuId
     * @param serviceProgramItemIndicatorRelBo
     */
    private void updateJdhContrastSource(String skuId,ServiceProgramItemIndicatorRelBo serviceProgramItemIndicatorRelBo){
        JdhContrastSource jdhContrastSource = JdhContrastSource.builder().build();
        jdhContrastSource.setContrastId(skuId);
        jdhContrastSource.setContrastContent(JsonUtil.toJSONString(serviceProgramItemIndicatorRelBo));
        jdhContrastSourceRepository.update(jdhContrastSource);
    }



    /**
     * 从套餐池删除服务套餐
     * @param userPin
     * @param skuIdList
     * @return
     */
    @Override
    public Boolean removeServiceGoodsFromPool(String userPin, List<String> skuIdList) {
        jdhUserContrastPoolRepository.removeServiceGoodsFromPool(userPin,skuIdList);
        return true;
    }

    /**
     * 查看用户套餐池列表
     * @param request
     * @return
     */
    @Override
    public List<ServiceGoodsPoolDto> queryServiceGoodsPoolListOfPin(ProductServiceGoodsListRequest request) {
        List<JdhUserContrastPool> list = jdhUserContrastPoolRepository.listByUserPin(request.getUserPin());
        if(CollectionUtil.isNotEmpty(list)){
            List<String> contrastIds = list.stream().map(JdhUserContrastPool::getContrastId).collect(Collectors.toList());
            Map<String,JdhUserContrastPool> goodsPoolMap = list.stream().collect(Collectors.toMap(JdhUserContrastPool::getContrastId, Function.identity(), (k1, k2) -> k1));

            Set<String> skuNos = Sets.newHashSet(contrastIds);
            Map<String, RpcSkuBO> rpcSkuMap = skuInfoRpc.getSkuInfo(skuNos);
            // 获取经纬度或收货地址
            XfylOrderUserAddressBO xfylOrderUserAddressBO = getUserLatAndLng(request.getLat(),request.getLng(),request.getUserPin());
            StoreRpcBO storeRpcBO = new StoreRpcBO();
            if(Objects.nonNull(xfylOrderUserAddressBO)){
                storeRpcBO.setLat(String.valueOf(xfylOrderUserAddressBO.getLat()));
                storeRpcBO.setLng(String.valueOf(xfylOrderUserAddressBO.getLng()));
            }
            // 套餐商品到手价
            Map<Long, GuideMultiResultModule> guideMultiMap = querySkuFinalPrice(contrastIds,request.getUserPin(),xfylOrderUserAddressBO);
            Map<String, PriceResultBO> skuPrices = getSkuJdPrice(skuNos);
            // 套餐商品销量与优惠入参
            List<SalesAndCouponBatchQuery> skuList = new ArrayList<>();
            List<ServiceGoodsPoolDto> serviceGoodsPoolDtos = new ArrayList<>();
            List<JdhContrastSource> jdhContrastSourceList = jdhContrastSourceRepository.queryJdhContrastSourceByContrastIds(contrastIds);
            jdhContrastSourceList.forEach(jdhContrastSource -> {
                ServiceGoodsPoolDto serviceGoodsPoolDto = dealJdhContrastSource(jdhContrastSource,rpcSkuMap,storeRpcBO,skuList,goodsPoolMap);
                // 套餐商品到手价
                setDeliveryPrice(jdhContrastSource,guideMultiMap,skuPrices,serviceGoodsPoolDto);
                serviceGoodsPoolDtos.add(serviceGoodsPoolDto);
            });
            // 补充商品销量，优惠券
            queryNearStoreAndSkuSalesAndCouponList(serviceGoodsPoolDtos,skuList,request.getUserPin());
            List<ServiceGoodsPoolDto> serviceGoodsPoolDtoList = serviceGoodsPoolDtos.stream().sorted(Comparator.comparing(ServiceGoodsPoolDto::getCabinetStatus)
                    .thenComparing(ServiceGoodsPoolDto::getCreateTime).reversed()).collect(Collectors.toList());
            return serviceGoodsPoolDtoList;
        }
        return null;
    }

    public String getPurchasePrice(String skuId, Long addressId, String userPin){
        try {
            log.info("ProductPoolApplicationImpl getPurchasePrice skuId={}, addressId={}, userPin={}", skuId, addressId, userPin);
            if (org.apache.commons.lang3.StringUtils.isAnyBlank(skuId, userPin)){
                return null;
            }
            UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(userPin, addressId);
            log.info("ProductPoolApplicationImpl getPurchasePrice userAddressDetail={}", JSON.toJSONString(userAddressDetail));
            if (userAddressDetail == null){
                return null;
            }
            XfylOrderUserAddressBO xfylOrderUserAddressBO = JSON.parseObject(JSON.toJSONString(userAddressDetail), XfylOrderUserAddressBO.class);
            xfylOrderUserAddressBO.setProvinceId(userAddressDetail.getProvinceId());
            xfylOrderUserAddressBO.setCityId(userAddressDetail.getCityId());
            xfylOrderUserAddressBO.setCountryId(userAddressDetail.getCountyId());
            log.info("ProductPoolApplicationImpl getPurchasePrice xfylOrderUserAddressBO={}", JSON.toJSONString(xfylOrderUserAddressBO));
            // 套餐商品到手价
            Map<Long, GuideMultiResultModule> guideMultiMap = querySkuFinalPrice(Collections.singletonList(skuId), userPin, xfylOrderUserAddressBO);
            log.info("ProductPoolApplicationImpl getPurchasePrice guideMultiMap={}", JSON.toJSONString(guideMultiMap));
            if (MapUtils.isEmpty(guideMultiMap)){
                log.info("ProductPoolApplicationImpl getPurchasePrice guideMultiMap empty");
                return null;
            }
            // 查询到手价
            String finalPrice = getFinalPrice(guideMultiMap, Long.parseLong(skuId));
            log.info("ProductPoolApplicationImpl getPurchasePrice finalPrice={}",finalPrice);
            return finalPrice;
        } catch (Exception e) {
            log.error("ProductPoolApplicationImpl getPurchasePrice error e:", e);
            return null;
        }
    }

    @Override
    @LogAndAlarm(jKey = "ProductPoolApplicationImpl.getProductPurchasePriceMap")
    public Map<String, ProductPurchasePriceDto> getProductPurchasePriceMap(Set<String> skuIds, UserAddressDetailBO userAddressDetail, String userPin) {
        try {
            if (CollectionUtils.isEmpty(skuIds)){
                return Collections.emptyMap();
            }
            if (StringUtils.isBlank(userPin)){
                return Collections.emptyMap();
            }
            if (Objects.isNull(userAddressDetail)){
                return Collections.emptyMap();
            }
            XfylOrderUserAddressBO xfylOrderUserAddressBO = JSON.parseObject(JSON.toJSONString(userAddressDetail), XfylOrderUserAddressBO.class);
            xfylOrderUserAddressBO.setProvinceId(userAddressDetail.getProvinceId());
            xfylOrderUserAddressBO.setCityId(userAddressDetail.getCityId());
            xfylOrderUserAddressBO.setCountryId(userAddressDetail.getCountyId());
            log.info("ProductPoolApplicationImpl getProductPurchasePriceMap xfylOrderUserAddressBO={}", JSON.toJSONString(xfylOrderUserAddressBO));

            List<CompletableFuture<ProductPurchasePriceDto>> purchasePriceFutureList = Collections.synchronizedList(Lists.newArrayList());
            skuIds.forEach(skuId->{
                CompletableFuture<ProductPurchasePriceDto> purchasePriceFuture = CompletableFuture.supplyAsync(()->{
                    try {
                        // 套餐商品到手价
                        Map<Long, GuideMultiResultModule> guideMultiMap = querySkuFinalPrice(Collections.singletonList(skuId), userPin, xfylOrderUserAddressBO);
                        log.info("ProductPoolApplicationImpl getProductPurchasePriceMap guideMultiMap={}", JSON.toJSONString(guideMultiMap));
                        if (MapUtils.isEmpty(guideMultiMap)){
                            log.info("ProductPoolApplicationImpl getProductPurchasePriceMap guideMultiMap empty");
                            return null;
                        }
                        // 查询到手价
                        String finalPrice = getFinalPrice(guideMultiMap, Long.parseLong(skuId));
                        log.info("ProductPoolApplicationImpl getProductPurchasePriceMap finalPrice={}", finalPrice);
                        ProductPurchasePriceDto res = new ProductPurchasePriceDto();
                        res.setSkuId(skuId);
                        res.setPurchasePrice(finalPrice);

                        GuideMultiResultModule guideMultiResultModule = guideMultiMap.get(Long.parseLong(skuId));
                        if (Objects.nonNull(guideMultiResultModule)){
                            GuideResultModule guideResultModule = guideMultiResultModule.getGuideResultModule();
                            if (Objects.nonNull(guideResultModule)){
                                res.setSecondPriceType(guideResultModule.getSecondPriceType());
                            }
                        }
                        return res;
                    } catch (Exception e) {
                        log.error("ProductApplicationImpl -> getProductPurchasePriceMap error e", e);
                    }
                    return null;
                }, executorPoolFactory.get(ThreadPoolConfigEnum.SKU_C_HAND_POOL)).exceptionally(throwable -> {
                    log.error("ProductApplicationImpl -> getProductPurchasePriceMap exceptionally error "+throwable.getMessage(),throwable);
                    return null;
                });
                purchasePriceFutureList.add(purchasePriceFuture);
            });

            CompletableFuture.allOf(purchasePriceFutureList.toArray(new CompletableFuture<?>[0])).get(NumConstant.NUM_5, TimeUnit.SECONDS);

            List<ProductPurchasePriceDto> purchasePriceList = purchasePriceFutureList.stream().filter(Objects::nonNull).map(e -> e.getNow(null)).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            log.info("ProductPoolApplicationImpl getProductPurchasePriceMap purchasePriceList={}", JSON.toJSONString(purchasePriceList));
            if (CollectionUtils.isEmpty(purchasePriceList)){
                return Collections.emptyMap();
            }
            return purchasePriceList.stream().collect(Collectors.toMap(ProductPurchasePriceDto::getSkuId
                    , Function.identity(), (key1, key2) -> key2));
        } catch (Exception e) {
            log.error("ProductPoolApplicationImpl getProductPurchasePriceMap error e", e);
            return Collections.emptyMap();
        }
    }

    /**
     *
     * @param jdhContrastSource
     * @param rpcSkuMap
     * @param storeRpcBO
     * @param skuList
     * @return
     */
    private ServiceGoodsPoolDto dealJdhContrastSource(JdhContrastSource jdhContrastSource,Map<String, RpcSkuBO> rpcSkuMap,
                                                      StoreRpcBO storeRpcBO,List<SalesAndCouponBatchQuery> skuList,Map<String,JdhUserContrastPool> goodsPoolMap){
        ServiceGoodsPoolDto serviceGoodsPoolDto = ServiceGoodsPoolDto.builder().build();
        serviceGoodsPoolDto.setSkuId(jdhContrastSource.getContrastId());
        serviceGoodsPoolDto.setSkuImg(jdhContrastSource.getMainImg());
        serviceGoodsPoolDto.setSkuName(jdhContrastSource.getName());
        JdhUserContrastPool jdhUserContrastPool = goodsPoolMap.get(jdhContrastSource.getContrastId());
        serviceGoodsPoolDto.setCreateTime(jdhUserContrastPool.getCreateTime().getTime());
        serviceGoodsPoolDto.setCabinetStatus(jdhContrastSource.getCabinetStatus());
        RpcSkuBO rpcSkuBO = rpcSkuMap.get(jdhContrastSource.getContrastId());
        if(isPopSku(jdhContrastSource)){
            bulidPopGoodsStoreToPool(rpcSkuBO,serviceGoodsPoolDto,storeRpcBO,skuList);
        }else{
            bulidSelfGoodsStoreToPool(rpcSkuBO,serviceGoodsPoolDto,storeRpcBO);
        }
        return serviceGoodsPoolDto;
    }

    /**
     * 套餐商品到手价
     * @param jdhContrastSource
     * @param guideMultiMap
     * @param serviceGoodsPoolDto
     */
    private void setDeliveryPrice(JdhContrastSource jdhContrastSource,Map<Long,GuideMultiResultModule> guideMultiMap,Map<String,PriceResultBO> skuPrices,ServiceGoodsPoolDto serviceGoodsPoolDto){
        String contrastContent = jdhContrastSource.getContrastContent();
        ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo = JsonUtil.parseObject(contrastContent, ServiceProgramItemIndicatorRelBo.class);
        serviceGoodsPoolDto.setDeliveryPrice(serviceGoodsContrastAttrBo.getPrice());
        serviceGoodsPoolDto.setIndicatorCount(serviceGoodsContrastAttrBo.getIndicatorCount());
        if(CollUtil.isNotEmpty(guideMultiMap)) {
            String finalPrice = getFinalPrice(guideMultiMap, Long.parseLong(jdhContrastSource.getContrastId()));
            if (StringUtils.isNotEmpty(finalPrice)) {
                serviceGoodsPoolDto.setDeliveryPrice(finalPrice);
            }else{
                if(CollUtil.isNotEmpty(skuPrices)){
                    serviceGoodsPoolDto.setDeliveryPrice(skuPrices.get(jdhContrastSource.getContrastId()).getJdPrice());
                }else{
                    serviceGoodsPoolDto.setDeliveryPrice(serviceGoodsContrastAttrBo.getPrice());
                }
            }
        }
    }

    /**
     * pop 还是自营
     * @param jdhContrastSource
     * @return
     */
    private Boolean isPopSku(JdhContrastSource jdhContrastSource){
        ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo = jdhContrastSource.getServiceGoodsContrastAttrBo();
        if(Objects.nonNull(serviceGoodsContrastAttrBo)) {
            return serviceGoodsContrastAttrBo.getIsPopSku();
        }else{
            return skuInfoRpc.judgeIsPopBySku(jdhContrastSource.getContrastId());
        }
    }

    /**
     * pop最近门店信息
     * @param rpcSkuBO
     * @param serviceGoodsPoolDto
     * @param storeRpcBO
     * @param skuList
     */
    private void bulidPopGoodsStoreToPool(RpcSkuBO rpcSkuBO,ServiceGoodsPoolDto serviceGoodsPoolDto, StoreRpcBO storeRpcBO,List<SalesAndCouponBatchQuery> skuList){
        if(Objects.nonNull(rpcSkuBO)){
            String cabinetStatus = String.valueOf(rpcSkuBO.getSkuStatus());
            serviceGoodsPoolDto.setCabinetStatus(cabinetStatus);
            serviceGoodsPoolDto.setIsPopSku(true);
            serviceGoodsPoolDto.setDdsplx(rpcSkuBO.getDdsplx());
            serviceGoodsPoolDto.setVenderId(rpcSkuBO.getVenderId());
            this.getPopNearStoreInfo(serviceGoodsPoolDto,storeRpcBO,rpcSkuBO);
            String storeId = "";
            if(Objects.nonNull(serviceGoodsPoolDto.getStoreId())){
                storeId = String.valueOf(serviceGoodsPoolDto.getStoreId());
            }
            skuList.add(getSalesAndCouponBatchQuery(rpcSkuBO,storeId));
        }
    }

    /**
     * 自营商品最近门店信息
     * @param rpcSkuBO
     * @param serviceGoodsPoolDto
     * @param storeRpcBO
     */
    private void bulidSelfGoodsStoreToPool(RpcSkuBO rpcSkuBO,ServiceGoodsPoolDto serviceGoodsPoolDto, StoreRpcBO storeRpcBO){
        if(Objects.nonNull(rpcSkuBO)){
            String cabinetStatus = String.valueOf(rpcSkuBO.getSkuStatus());
            serviceGoodsPoolDto.setCabinetStatus(cabinetStatus);
            serviceGoodsPoolDto.setIsPopSku(false);
            serviceGoodsPoolDto.setDdsplx(rpcSkuBO.getDdsplx());
            serviceGoodsPoolDto.setVenderId(rpcSkuBO.getVenderId());
            GoodsStoreDTO goodsStoreDTO = this.getSelfNearStoreInfo(storeRpcBO,rpcSkuBO);
            if(Objects.nonNull(goodsStoreDTO)){
                serviceGoodsPoolDto.setStoreName(goodsStoreDTO.getStoreName());
                serviceGoodsPoolDto.setDistance(goodsStoreDTO.getDistance() + CommonConstant.DISTANCE_UNIT);
                serviceGoodsPoolDto.setStoreCategoryName(goodsStoreDTO.getStoreTypeDesc());
            }
        }
    }

    /**
     * pop商品最近门店
     * @param serviceGoodsPoolDto
     * @param storeRpcBO
     * @param rpcSkuBO
     */
    private void getPopNearStoreInfo(ServiceGoodsPoolDto serviceGoodsPoolDto,StoreRpcBO storeRpcBO,RpcSkuBO rpcSkuBO){
        storeRpcBO.setGroupId(Long.parseLong(rpcSkuBO.getLocGroupId()));
        List<NearStoreInfoTO> nearStoreInfoTOS = popLocStoreInfoRpc.queryNearStores(storeRpcBO);
        if(CollectionUtil.isNotEmpty(nearStoreInfoTOS)){
            // 处理最近门店信息
            NearStoreInfoTO nearStoreInfoTO = nearStoreInfoTOS.get(0);
            dealPopStoreInfo(nearStoreInfoTO,serviceGoodsPoolDto);
        }
    }

    /**
     * 自营商品最近门店
     * @param storeRpcBO
     * @param rpcSkuBO
     */
    private GoodsStoreDTO getSelfNearStoreInfo(StoreRpcBO storeRpcBO,RpcSkuBO rpcSkuBO){
        AppointStoreParam appointStoreParam = new AppointStoreParam();
        appointStoreParam.setSkuNo(rpcSkuBO.getSkuId());
        appointStoreParam.setLat(Double.valueOf(storeRpcBO.getLat()));
        appointStoreParam.setLng(Double.valueOf(storeRpcBO.getLng()));
        appointStoreParam.setPageSize(CommonConstant.ONE);
        appointStoreParam.setPageNum(CommonConstant.ONE);
        appointStoreParam.setSearchType(CommonConstant.ZERO);

        PageInfo<GoodsStoreDTO> dtoPageInfo = popLocStoreInfoRpc.queryAppointStoreList(appointStoreParam);
        if(Objects.nonNull(dtoPageInfo) && CollectionUtil.isNotEmpty(dtoPageInfo.getList())){
            // 处理最近门店信息
            GoodsStoreDTO goodsStoreDTO = dtoPageInfo.getList().get(0);
            return goodsStoreDTO;
        }
        return null;
    }

    /**
     * 处理商品销量，优惠券
     */
    private void queryNearStoreAndSkuSalesAndCouponList(List<ServiceGoodsPoolDto> serviceGoodsPoolDtos,List<SalesAndCouponBatchQuery> skuList,String userPin){
        Map<String, String> features = new HashMap<>();
//        features.put("salesCoupon","salesCoupon");
        features.put("storeCategoryName","storeCategoryName");
        if(CollUtil.isNotEmpty(skuList)){
            Map<String, TttMaterialStoreDTO> skuSalesAndCouponGetMap = skuSalesAndCouponGetMap(skuList,userPin,features);
            if(MapUtils.isNotEmpty(skuSalesAndCouponGetMap)){
                serviceGoodsPoolDtos.forEach(serviceGoodsPoolDto -> {
                    if(serviceGoodsPoolDto.getIsPopSku()){
                        TttMaterialStoreDTO skuSalesAndCouponGet = skuSalesAndCouponGetMap.get(String.valueOf(serviceGoodsPoolDto.getStoreId()));
                        if(Objects.nonNull(skuSalesAndCouponGet)){
//                            List<CouponInfoDTO> couponList = skuSalesAndCouponGet.getCouponList();
//                            serviceGoodsPoolDto.setSkuCouponList(ProductApplicationConverter.instance.convertToSkuCouponInfoDtoList(couponList));
//                            serviceGoodsPoolDto.setSales(skuSalesAndCouponGet.getSales());
                            serviceGoodsPoolDto.setStoreCategoryName(skuSalesAndCouponGet.getStoreCategoryName2());
                        }

                    }
                });
            }
        }

    }
    /**
     * 处理商品销量，优惠券
     */
    private void queryNearStoreCategoryName(List<StoreBaseInfoDto> storeBaseInfoList,List<SalesAndCouponBatchQuery> skuList,String userPin){
        Map<String, TttMaterialStoreDTO> skuSalesAndCouponGetMap = new HashMap<>();
        if(CollUtil.isNotEmpty(skuList)){
            Map<String, String> features = new HashMap<>();
            features.put("storeCategoryName","storeCategoryName");
            skuSalesAndCouponGetMap = skuSalesAndCouponGetMap(skuList,userPin,features);
        }
        Map<String, TttMaterialStoreDTO> finalSkuSalesAndCouponGetMap = skuSalesAndCouponGetMap;
        storeBaseInfoList.forEach(storeBaseInfoDto -> {
            if(StringUtil.isNotBlank(storeBaseInfoDto.getStoreId())){
                if(MapUtils.isNotEmpty(finalSkuSalesAndCouponGetMap)){
                    TttMaterialStoreDTO skuSalesAndCouponGet = finalSkuSalesAndCouponGetMap.get(storeBaseInfoDto.getStoreId());
                    if(Objects.nonNull(skuSalesAndCouponGet)){
                        storeBaseInfoDto.setStoreCategoryName(skuSalesAndCouponGet.getStoreCategoryName2());
                    }
                }
                storeBaseInfoDto.setDistance(storeBaseInfoDto.getDistanceNum() + CommonConstant.DISTANCE_UNIT);
            }
        });
    }

    /**
     *
     * @param skuList
     * @param userPin
     * @param features
     * @return
     */
    private Map<String, TttMaterialStoreDTO> skuSalesAndCouponGetMap(List<SalesAndCouponBatchQuery> skuList, String userPin,Map<String, String> features){
        // 查询商品销量，优惠券
        SkuStoreSalesAndCouponBatchQuery skuStoreSalesAndCouponBatchQuery = new SkuStoreSalesAndCouponBatchQuery();
        skuStoreSalesAndCouponBatchQuery.setFeatures(features);
        skuStoreSalesAndCouponBatchQuery.setUserPin(userPin);
        skuStoreSalesAndCouponBatchQuery.setSalesAndCouponBatchQueryList(skuList);
        List<TttMaterialStoreDTO> list = skuInfoRpc.getSkuSalesAndCoupon(skuStoreSalesAndCouponBatchQuery);
        Map<String, TttMaterialStoreDTO> skuSalesAndCouponGetMap = new HashMap<>();
        if(CollUtil.isNotEmpty(list)){
            for(TttMaterialStoreDTO ttMaterialStore : list){
                skuSalesAndCouponGetMap.put(ttMaterialStore.getStoreId().toString(),ttMaterialStore);
            }
        }
        return skuSalesAndCouponGetMap;
    }

    /**
     * 查看套餐对比列表
     *
     * @param request
     * @return
     */
    @Override
    public ServiceBizItemContrastlDto showGoodsContrast(ServiceGoodsContrastRequest request) {
        List<String> skuIdList = request.getSkuIdList();
        AssertUtils.isTrue(skuIdList.size() > CommonConstant.THREE, ProductErrorCode.SERVICE_CONTRAST_RANG_OUT);
        List<JdhContrastSource> jdhContrastSourceList = jdhContrastSourceRepository.queryJdhContrastSourceByContrastIds(skuIdList);
        List<SuitableEnum> suitableList = getGoodsContrastSuitable(jdhContrastSourceList);
        SuitableEnum suitableEnum = suitableList.get(0);
        String programSuitable = suitableEnum.getCode();
        if(StringUtil.isNotBlank(request.getProgramSuitable())){
            programSuitable = SuitableEnum.getEnumByDesc(request.getProgramSuitable());
        }

        Set<String> skuNos = Sets.newHashSet(skuIdList);
        Map<String, RpcSkuBO> rpcSkuMap = skuInfoRpc.getSkuInfo(skuNos);

        // 获取经纬度或默认收货地址
        XfylOrderUserAddressBO xfylOrderUserAddressBO = getUserLatAndLng(request.getLat(),request.getLng(),request.getUserPin());
        StoreRpcBO storeRpcBO = new StoreRpcBO();
        if(Objects.nonNull(xfylOrderUserAddressBO)){
            storeRpcBO.setLat(String.valueOf(xfylOrderUserAddressBO.getLat()));
            storeRpcBO.setLng(String.valueOf(xfylOrderUserAddressBO.getLng()));
        }
        // 套餐商品到手价
        Map<Long, GuideMultiResultModule> guideMultiMap = querySkuFinalPrice(skuIdList,request.getUserPin(),xfylOrderUserAddressBO);
        Map<String, PriceResultBO> skuPrices = getSkuJdPrice(skuNos);
        // 商品信息
        List<SkuBaseInfoDto> skuBaseInfoList = new ArrayList<>();
        // 门店信息
        List<StoreBaseInfoDto> storeBaseInfoList = new ArrayList<>();
        // 套餐信息
        List<GoodsGuaranteeInfoDto> goodsBaseInfoList = new ArrayList<>();
        // 销量与优惠券查询
        List<SalesAndCouponBatchQuery> skuList = new ArrayList<>();
        // 项目数量
        List<BizItemCountDto> bizItemCountDtoList = new ArrayList<>();
        // 项目明细
        List<ServiceBizProgramContrastlDto> programContrastlDtoList = new ArrayList<>();
        // 重点项目
        List<ItemBizCategoryDto> importItemList = new ArrayList<>();
        Map<String,List<ItemBizCategoryDto>> programContrastlMap = new LinkedHashMap<>();
        int count = 0;
        int size = jdhContrastSourceList.size();
        for(JdhContrastSource jdhContrastSource : jdhContrastSourceList){
            RpcSkuBO rpcSkuBO = rpcSkuMap.get(jdhContrastSource.getContrastId());
            AssertUtils.nonNull(rpcSkuBO, ProductErrorCode.SERVICE_NOT_EXIST);
            ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo = jdhContrastSource.getServiceGoodsContrastAttrBo();
            if(Objects.isNull(serviceGoodsContrastAttrBo) || CollUtil.isEmpty(serviceGoodsContrastAttrBo.getProgramItemIndicatorBoList())){
                serviceGoodsContrastAttrBo = getProgramItemIndicatorRelList(jdhContrastSource.getContrastId());
            }
            StoreBaseInfoDto storeBaseInfoDto = getStoreBaseInfoDto(jdhContrastSource,storeRpcBO,rpcSkuBO,skuList);
            // 套餐商品信息
            skuBaseInfoList.add(getSkuBaseInfoDto(jdhContrastSource,serviceGoodsContrastAttrBo,guideMultiMap,skuPrices,rpcSkuBO));
            // 套餐最近门店信息
            storeBaseInfoList.add(storeBaseInfoDto);
            // 套餐体检检查项目
            if(Objects.nonNull(serviceGoodsContrastAttrBo)){
                GoodsGuaranteeInfoDto goodsGuaranteeInfoDto = getGoodsGuaranteeInfoDto(serviceGoodsContrastAttrBo,rpcSkuBO);
                if(!isPopSku(jdhContrastSource)){
                    goodsGuaranteeInfoDto.setBusinessHours(storeBaseInfoDto.getBusinessHours());
                }
                goodsBaseInfoList.add(goodsGuaranteeInfoDto);
            }

            // 项目列表 + 项目数量
            List<BizItemCategory> bizItemCategoryList = fitlerBizItemCategoryList(serviceGoodsContrastAttrBo,programSuitable);
            BizItemCountDto bizItemCountDto = new BizItemCountDto();
            bizItemCountDto.setItemCount(bizItemCategoryList.size());
            bizItemCountDto.setSkuNo(jdhContrastSource.getContrastId());
            bizItemCountDtoList.add(bizItemCountDto);
            // 重点项目，二级分类项目
            ItemBizCategoryDto itemBizCategoryDto = dealItemBizCategoryMap(bizItemCategoryList,count,size,programContrastlMap,jdhContrastSource.getContrastId());
            importItemList.add(itemBizCategoryDto);
            count++;
        }

        for(Map.Entry<String, List<ItemBizCategoryDto>> entry : programContrastlMap.entrySet()){
            ServiceBizProgramContrastlDto serviceBizProgramContrastlDto = ServiceBizProgramContrastlDto.builder().build();
            serviceBizProgramContrastlDto.setSecondBizCategoryName(entry.getKey());
            serviceBizProgramContrastlDto.setItemBizCategoryList(entry.getValue());
            programContrastlDtoList.add(serviceBizProgramContrastlDto);
        }

        List<StoreBaseInfoDto> storeSortList = storeBaseInfoList.stream().filter(item -> (Objects.nonNull(item.getDistanceNum()) && item.getDistanceNum() > 0)).collect(Collectors.toList());
        if(storeSortList.size() > 0){
            Long distanceSameCount = storeSortList.stream().map(StoreBaseInfoDto::getDistanceNum).distinct().count();
            if(distanceSameCount > storeSortList.size() - 1){
                StoreBaseInfoDto storeBaseInfoDto = storeSortList.stream().min(Comparator.comparing(StoreBaseInfoDto::getDistanceNum)).get();
                storeBaseInfoDto.setNearestDistance(true);
            }
        }
        Long indicatorSameCount = bizItemCountDtoList.stream().map(BizItemCountDto::getItemCount).distinct().count();
        if(indicatorSameCount > bizItemCountDtoList.size() - 1){
            BizItemCountDto bizItemCountDto = bizItemCountDtoList.stream().max(Comparator.comparing(BizItemCountDto::getItemCount)).get();
            bizItemCountDto.setItemCountMost(true);
        }

        // 补充最近门店类型
        queryNearStoreCategoryName(storeBaseInfoList,skuList,request.getUserPin());
        ServiceBizItemContrastlDto serviceGoodsContrastlDto = ServiceBizItemContrastlDto.builder().build();
        serviceGoodsContrastlDto.setSkuBaseInfoList(skuBaseInfoList);
        serviceGoodsContrastlDto.setStoreBaseInfoList(storeBaseInfoList);
        serviceGoodsContrastlDto.setGoodsBaseInfoList(goodsBaseInfoList);

        serviceGoodsContrastlDto.setGoodsSuitableList(suitableList.stream().map(SuitableEnum::getDesc).collect(Collectors.toList()));
        serviceGoodsContrastlDto.setGoodsNumList(bizItemCountDtoList);
        serviceGoodsContrastlDto.setImportItemList(importItemList);
        serviceGoodsContrastlDto.setProgramContrastlDtoList(programContrastlDtoList);
        return serviceGoodsContrastlDto;
    }

    /**
     * 套餐之适用人群对比列表
     *
     * @param request
     * @return
     */
    @Override
    public ServiceBizItemContrastlDto programSuitableContrast(ServiceGoodsContrastRequest request) {
        List<String> skuIdList = request.getSkuIdList();
        AssertUtils.isTrue(skuIdList.size() > CommonConstant.THREE, ProductErrorCode.SERVICE_CONTRAST_RANG_OUT);
        List<JdhContrastSource> jdhContrastSourceList = jdhContrastSourceRepository.queryJdhContrastSourceByContrastIds(skuIdList);
        // 项目数量
        List<BizItemCountDto> bizItemCountDtoList = new ArrayList<>();
        // 项目明细
        List<ServiceBizProgramContrastlDto> programContrastlDtoList = new ArrayList<>();
        // 重点项目
        List<ItemBizCategoryDto> importItemList = new ArrayList<>();
        Map<String,List<ItemBizCategoryDto>> programContrastlMap = new LinkedHashMap<>();
        int count = 0;
        int size = jdhContrastSourceList.size();
        for(JdhContrastSource jdhContrastSource : jdhContrastSourceList){
            String contrastContent = jdhContrastSource.getContrastContent();
            ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo;
            if(StringUtil.isNotBlank(contrastContent)){
                serviceGoodsContrastAttrBo = JsonUtil.parseObject(contrastContent,ServiceProgramItemIndicatorRelBo.class);
                if(CollUtil.isEmpty(serviceGoodsContrastAttrBo.getProgramItemIndicatorBoList())){
                    serviceGoodsContrastAttrBo = getProgramItemIndicatorRelList(jdhContrastSource.getContrastId());
                }
            }else{
                serviceGoodsContrastAttrBo = getProgramItemIndicatorRelList(jdhContrastSource.getContrastId());
            }
            if(Objects.isNull(serviceGoodsContrastAttrBo)){
                log.error("[ProductPoolApplicationImpl.programSuitableContrast] serviceGoodsContrastAttrBo is null,skuId={}",jdhContrastSource.getContrastId());
                continue;
            }
            String programSuitable = SuitableEnum.getEnumByDesc(request.getProgramSuitable());
            // 项目列表 + 项目数量
            List<BizItemCategory> bizItemCategoryList = fitlerBizItemCategoryList(serviceGoodsContrastAttrBo, programSuitable);
            BizItemCountDto bizItemCountDto = new BizItemCountDto();
            bizItemCountDto.setItemCount(bizItemCategoryList.size());
            bizItemCountDto.setSkuNo(jdhContrastSource.getContrastId());
            bizItemCountDtoList.add(bizItemCountDto);
            // 重点项目，二级分类项目
            // 重点项目，二级分类项目
            ItemBizCategoryDto itemBizCategoryDto = dealItemBizCategoryMap(bizItemCategoryList,count,size,programContrastlMap,jdhContrastSource.getContrastId());
            importItemList.add(itemBizCategoryDto);
            count++;
        }

        for(Map.Entry<String, List<ItemBizCategoryDto>> entry : programContrastlMap.entrySet()){
            ServiceBizProgramContrastlDto serviceBizProgramContrastlDto = ServiceBizProgramContrastlDto.builder().build();
            serviceBizProgramContrastlDto.setSecondBizCategoryName(entry.getKey());
            serviceBizProgramContrastlDto.setItemBizCategoryList(entry.getValue());
            programContrastlDtoList.add(serviceBizProgramContrastlDto);
        }

        if(request.getShowContrastDiff() == 1){
            programContrastlDtoList = diffProgramContrastlDtoList(programContrastlDtoList);
        }

        Long indicatorSameCount = bizItemCountDtoList.stream().map(BizItemCountDto::getItemCount).distinct().count();
        if(indicatorSameCount > bizItemCountDtoList.size() - 1){
            BizItemCountDto bizItemCountDto = bizItemCountDtoList.stream().max(Comparator.comparing(BizItemCountDto::getItemCount)).get();
            bizItemCountDto.setItemCountMost(true);
        }

        ServiceBizItemContrastlDto serviceGoodsContrastlDto = ServiceBizItemContrastlDto.builder().build();
        serviceGoodsContrastlDto.setGoodsNumList(bizItemCountDtoList);
        serviceGoodsContrastlDto.setImportItemList(importItemList);
        serviceGoodsContrastlDto.setProgramContrastlDtoList(programContrastlDtoList);
        return serviceGoodsContrastlDto;
    }

    /**
     * 套餐对比适用人群
     * @param jdhContrastSourceList
     * @return
     */
    private List<SuitableEnum> getGoodsContrastSuitable(List<JdhContrastSource> jdhContrastSourceList){
        List<String> skuSuitableList = new ArrayList<>();
        for(JdhContrastSource jdhContrastSource : jdhContrastSourceList){
            String contrastContent = jdhContrastSource.getContrastContent();
            ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo = JsonUtil.parseObject(contrastContent,ServiceProgramItemIndicatorRelBo.class);
            if(Objects.isNull(serviceGoodsContrastAttrBo) || CollUtil.isEmpty(serviceGoodsContrastAttrBo.getProgramItemIndicatorBoList())){
                serviceGoodsContrastAttrBo = getProgramItemIndicatorRelList(jdhContrastSource.getContrastId());
            }
            jdhContrastSource.setServiceGoodsContrastAttrBo(serviceGoodsContrastAttrBo);
            jdhContrastSource.setContrastContent("");
            List<String> skuSuitable = serviceGoodsContrastAttrBo.getProgramSuitable();
            if(CollUtil.isNotEmpty(skuSuitable)){
                skuSuitableList.addAll(skuSuitable);
            }
        }
        Set<String> skuSuitableSet = new HashSet<>(skuSuitableList);
        List<SuitableEnum> suitableEnumList = SuitableEnum.sortSuitable(skuSuitableSet);
        AssertUtils.isNotEmpty(suitableEnumList,ProductErrorCode.SERVICE_CONTRAST_SUITABLE_ERROR);
        return suitableEnumList;
    }


    /**
     *
     * @param serviceGoodsContrastAttrBo
     * @param suitable
     * @return
     */
    private List<BizItemCategory> fitlerBizItemCategoryList(ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo,String suitable){
        List<String> skuSuitable = serviceGoodsContrastAttrBo.getProgramSuitable();
        if(skuSuitable.contains(suitable)){
            Integer saleShowMethod = serviceGoodsContrastAttrBo.getSaleShowMethod();
            List<ProgramItemIndicatorBo> programItemIndicatorBoList = serviceGoodsContrastAttrBo.getProgramItemIndicatorBoList();
            ProgramItemIndicatorBo programItemIndicatorBo = programItemIndicatorBoList.get(0);
            if(ProgramSaleShowMethodTypeEnum.COMPOSE.getTypeNo().equals(saleShowMethod)){
                for(ProgramItemIndicatorBo tempProgramItemIndicatorBo : programItemIndicatorBoList){
                    if(CollUtil.isNotEmpty(programItemIndicatorBoList) && tempProgramItemIndicatorBo.getProgramSuitable().contains(suitable)){
                        programItemIndicatorBo = tempProgramItemIndicatorBo;
                    }
                }
            }
            List<BizItemCategory> bizItemCategoryList = programItemIndicatorBo.getBizItemCategoryList();
            if(CollUtil.isNotEmpty(bizItemCategoryList)){
                List<BizItemCategory> bizItemCategorys = new ArrayList<>();
                for(BizItemCategory bizItemCategory : bizItemCategoryList){
                    if(CollUtil.isNotEmpty(bizItemCategory.getItemSuitable()) && bizItemCategory.getItemSuitable().contains(suitable)){
                        bizItemCategorys.add(bizItemCategory);
                    }
                }
                log.info("[ProductPoolApplicationImpl.fitlerBizItemCategoryList] bizItemCategorys={}",JsonUtil.toJSONString(bizItemCategorys));
                return bizItemCategorys;
            }
        }
        return new ArrayList<>();
    }

    /**
     *
     * @param bizItemCategoryList
     * @param count 游标
     * @param programContrastlMap
     * @return
     */
    private ItemBizCategoryDto dealItemBizCategoryMap(List<BizItemCategory> bizItemCategoryList,int count,int size,Map<String,List<ItemBizCategoryDto>> programContrastlMap,String skuNo){
        ItemBizCategoryDto importBizItemDto = new ItemBizCategoryDto();
        if(CollUtil.isEmpty(bizItemCategoryList)){
            return importBizItemDto;
        }
        Map<String, List<BizItemCategory>> bizItemCategoryMap = bizItemCategoryList.stream().collect(Collectors.groupingBy(BizItemCategory::getSecondBizCategoryName));
        List<BizItemSimpleDto> importBizItems = new ArrayList<>();
        for(Map.Entry<String, List<BizItemCategory>> entry : bizItemCategoryMap.entrySet()){
            List<BizItemCategory> tempList = entry.getValue();
            ItemBizCategoryDto itemBizCategoryDto = new ItemBizCategoryDto();
            importBizItemDto.setSkuNo(skuNo);
            itemBizCategoryDto.setSkuNo(skuNo);
            List<BizItemSimpleDto> bizItemList = new ArrayList<>();
            for(BizItemCategory bizItemCategory : tempList){
                BizItemSimpleDto bizItemSimpleDto = new BizItemSimpleDto();
                bizItemSimpleDto.setBizItemId(bizItemCategory.getBizItemId());
                bizItemSimpleDto.setStandardItemId(bizItemCategory.getStandardItemId());
                bizItemSimpleDto.setBizItemName(bizItemCategory.getBizItemName());
                bizItemList.add(bizItemSimpleDto);
                if(bizItemCategory.getImportantItem()){
                    BizItemSimpleDto importBizItem = new BizItemSimpleDto();
                    importBizItem.setBizItemId(bizItemCategory.getBizItemId());
                    importBizItem.setBizItemName(bizItemCategory.getBizItemName());
                    importBizItems.add(importBizItem);
                }
            }
            itemBizCategoryDto.setBizItemList(bizItemList);

            List<ItemBizCategoryDto> itemBizCategoryList = programContrastlMap.get(entry.getKey());
            if(CollUtil.isEmpty(itemBizCategoryList)){
                itemBizCategoryList = getItemBizCategoryList(size);
            }
            itemBizCategoryList.set(count,itemBizCategoryDto);
            programContrastlMap.put(entry.getKey(),itemBizCategoryList);
        }
        log.info("[ProductPoolApplicationImpl.dealItemBizCategoryMap] programContrastlMap={}",JsonUtil.toJSONString(programContrastlMap));
        importBizItemDto.setBizItemList(importBizItems);
        return importBizItemDto;
    }

    /**
     *
     * @param size
     * @return
     */
    private List<ItemBizCategoryDto> getItemBizCategoryList(Integer size){
        List<ItemBizCategoryDto> itemBizCategoryList = new ArrayList<>();
        for(int i=0;i<size;i++){
            ItemBizCategoryDto itemBizCategoryDto = new ItemBizCategoryDto();
            itemBizCategoryDto.setBizItemList(new ArrayList<>());
            itemBizCategoryList.add(itemBizCategoryDto);
        }
        return itemBizCategoryList;
    }
    /**
     * 商品基本信息
     * @param jdhContrastSource
     * @param guideMultiMap
     * @return
     */
    private SkuBaseInfoDto getSkuBaseInfoDto(JdhContrastSource jdhContrastSource,ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo,
                                             Map<Long, GuideMultiResultModule> guideMultiMap,Map<String,PriceResultBO> skuPrices,RpcSkuBO rpcSkuBO){
        SkuBaseInfoDto skuBaseInfoDto = SkuBaseInfoDto.builder().build();
        skuBaseInfoDto.setSkuId(jdhContrastSource.getContrastId());
        skuBaseInfoDto.setSkuImg(jdhContrastSource.getMainImg());
        skuBaseInfoDto.setSkuName(jdhContrastSource.getName());
        skuBaseInfoDto.setDdsplx(rpcSkuBO.getDdsplx());
        skuBaseInfoDto.setVenderId(rpcSkuBO.getVenderId());
        Boolean isPopSku = false;
        if(Objects.nonNull(serviceGoodsContrastAttrBo)) {
            isPopSku = serviceGoodsContrastAttrBo.getIsPopSku();
        }else{
            isPopSku = skuInfoRpc.judgeIsPopBySku(jdhContrastSource.getContrastId());
        }
        skuBaseInfoDto.setIsPopSku(isPopSku);
        if(CollUtil.isNotEmpty(guideMultiMap)) {
            String finalPrice = getFinalPrice(guideMultiMap, Long.parseLong(jdhContrastSource.getContrastId()));
            if (StringUtils.isNotEmpty(finalPrice)) {
                skuBaseInfoDto.setDeliveryPrice(finalPrice);
            }else{
                if(CollUtil.isNotEmpty(skuPrices)){
                    skuBaseInfoDto.setDeliveryPrice(skuPrices.get(jdhContrastSource.getContrastId()).getJdPrice());
                }else{
                    skuBaseInfoDto.setDeliveryPrice(serviceGoodsContrastAttrBo.getPrice());
                }
            }
        }

        return skuBaseInfoDto;
    }

    /**
     * 商品基本信息
     * @param jdhContrastSource
     * @return
     */
    private StoreBaseInfoDto getStoreBaseInfoDto(JdhContrastSource jdhContrastSource,StoreRpcBO storeRpcBO,RpcSkuBO rpcSkuBO,List<SalesAndCouponBatchQuery> skuList){
        StoreBaseInfoDto storeBaseInfoDto = StoreBaseInfoDto.builder().build();
        storeBaseInfoDto.setServiceId(rpcSkuBO.getSkuId());
        if(isPopSku(jdhContrastSource)){
            storeRpcBO.setGroupId(Long.parseLong(rpcSkuBO.getLocGroupId()));
            List<NearStoreInfoTO> nearStoreInfoTOS = popLocStoreInfoRpc.queryNearStores(storeRpcBO);
            if(CollectionUtil.isNotEmpty(nearStoreInfoTOS)){
                NearStoreInfoTO nearStoreInfoTO = nearStoreInfoTOS.get(0);
                storeBaseInfoDto.setStoreId(String.valueOf(nearStoreInfoTO.getStoreId()));
                storeBaseInfoDto.setStoreName(nearStoreInfoTO.getStoreName());
                storeBaseInfoDto.setDistanceNum(nearStoreInfoTO.getDistance());
                storeBaseInfoDto.setStoreAddr(nearStoreInfoTO.getStoreAddress());
                storeBaseInfoDto.setBusinessHours(nearStoreInfoTO.getStoreBusinessTime());
                skuList.add(getSalesAndCouponBatchQuery(rpcSkuBO,storeBaseInfoDto.getStoreId()));
            }
        }else{
            GoodsStoreDTO goodsStoreDTO = this.getSelfNearStoreInfo(storeRpcBO,rpcSkuBO);
            if(Objects.nonNull(goodsStoreDTO)){
                storeBaseInfoDto.setStoreId(goodsStoreDTO.getStoreId());
                storeBaseInfoDto.setStoreName(goodsStoreDTO.getStoreName());
                storeBaseInfoDto.setDistanceNum(goodsStoreDTO.getDistance());
                storeBaseInfoDto.setStoreCategoryName(goodsStoreDTO.getStoreTypeDesc());
                storeBaseInfoDto.setStoreAddr(goodsStoreDTO.getStoreAddr());
                storeBaseInfoDto.setBusinessHours(goodsStoreDTO.getStoreHours());
            }
        }

        return storeBaseInfoDto;
    }

    /**
     *
     * @param serviceGoodsContrastAttrBo
     * @return
     */
    private GoodsGuaranteeInfoDto getGoodsGuaranteeInfoDto(ServiceProgramItemIndicatorRelBo serviceGoodsContrastAttrBo,RpcSkuBO rpcSkuBO){
        GoodsGuaranteeInfoDto goodsGuaranteeInfoDto = GoodsGuaranteeInfoDto.builder().build();
//        goodsGuaranteeInfoDto.setBusinessHours(serviceGoodsContrastAttrBo.getBusinessHours());
        if(serviceGoodsContrastAttrBo.getIsPopSku()){
            String result = venderBasicRpc.getVenderDynamicSubjectVo(rpcSkuBO.getVenderId(),"LOCGQT");
            String guaranteeCode = result + getAppointType(rpcSkuBO.getSkuId());
            goodsGuaranteeInfoDto.setGoodsGuarantee(StoreGuaranteeTypeEnum.getStoreGuaranteeType(guaranteeCode).getDesc());
        }else{
            goodsGuaranteeInfoDto.setGoodsGuarantee(StoreGuaranteeTypeEnum.GUARANTEE_ONLINE_APPOINTMENT.getDesc());
        }

        serviceGoodsContrastAttrBo.getSaleShowMethod();
        goodsGuaranteeInfoDto.setServiceSuitable(SuitableEnum.getSuitableList(serviceGoodsContrastAttrBo.getProgramSuitable()));
        return goodsGuaranteeInfoDto;
    }

    /**
     *
     * @param skuId
     * @return
     */
    private Integer getAppointType(String skuId){
        //查询服务信息，无论哪个预约方式，都生产履约单
        ServiceDetailBo serviceDetailBo = serviceDetailRpc.queryServiceDetail(ServiceDetailRpcParam.builder().skuNo(skuId).build());
        return PromiseTypeEnum.getPromiseType(serviceDetailBo.getAppointmentType()).getCode();
    }

    /**
     *
     * @param rpcSkuBO
     * @param storeId
     * @return
     */
    private SalesAndCouponBatchQuery getSalesAndCouponBatchQuery(RpcSkuBO rpcSkuBO,String storeId){
        SalesAndCouponBatchQuery salesAndCouponBatchQuery = new SalesAndCouponBatchQuery();
        salesAndCouponBatchQuery.setIsCanUseDQ(rpcSkuBO.getIsCanUseDQ());
        salesAndCouponBatchQuery.setIsCanUseJQ(rpcSkuBO.getIsCanUseJQ());
        salesAndCouponBatchQuery.setIsGlobalPurchase(rpcSkuBO.getIsGlobalPurchase());
        salesAndCouponBatchQuery.setMsbybt(rpcSkuBO.getMsbybt());
        salesAndCouponBatchQuery.setSkuId(rpcSkuBO.getSkuId());
        if(StringUtil.isNotBlank(storeId)){
            salesAndCouponBatchQuery.setStoreId(Long.parseLong(storeId));
        }else{
            salesAndCouponBatchQuery.setStoreId(0L);
        }
        salesAndCouponBatchQuery.setVenderId(rpcSkuBO.getVenderId());
        return salesAndCouponBatchQuery;
    }
    /**
     * 查看检查项目详情
     *
     * @param request
     * @return
     */
    @Override
    public List<BizIndicatorItemCategoryDto> getIndicatorDetail(ProductServiceGoodsListRequest request) {
        List<Long> itemIdList = request.getItemIdList();
        AssertUtils.isNotEmpty(itemIdList,"");

        List<BizIndicatorItemCategoryDto> bizIndicatorItemCategoryDtos = new ArrayList<>();
        // 项目id List 查项目list
        JdhBizItemReqQuery query = JdhBizItemReqQuery.builder().bizItemIdList(itemIdList).build();
        List<JdhBizItem> jdhBizItemList = jdhBizItemRepository.queryList(query);
        if(CollectionUtil.isNotEmpty(jdhBizItemList)){
            List<Long> standardItemIdList = jdhBizItemList.stream().map(JdhBizItem::getStandardItemId).collect(Collectors.toList());
            Map<Long,String> standardItemNameMap = getStandardItemNameMap(standardItemIdList);
            for(JdhBizItem jdhBizItem : jdhBizItemList){
                BizIndicatorItemCategoryDto bizIndicatorItemCategoryDto = new BizIndicatorItemCategoryDto();
                bizIndicatorItemCategoryDto.setBizItemId(jdhBizItem.getBizItemId());
                String standardItemName = standardItemNameMap.get(jdhBizItem.getStandardItemId());
                if(StringUtil.isBlank(standardItemName)){
                    standardItemName = jdhBizItem.getBizItemName();
                }
                bizIndicatorItemCategoryDto.setBizItemName(standardItemName);
                bizIndicatorItemCategoryDto.setBizItemSignificance(jdhBizItem.getMean());
                List<JdhStandardIndicator> jdhStandardIndicatorList = queryIndicatorListById(jdhBizItem.getBizItemId());
                if(CollUtil.isNotEmpty(jdhStandardIndicatorList)){
                    List<IndicatorItemDto> indicators = new ArrayList<>();
                    for(JdhStandardIndicator standardIndicator : jdhStandardIndicatorList){
                        IndicatorItemDto indicatorItemDto = new IndicatorItemDto();
                        indicatorItemDto.setIndicatorName(standardIndicator.getIndicatorName());
                        indicatorItemDto.setSignificance(standardIndicator.getIndicatorMean());
                        indicators.add(indicatorItemDto);
                    }
                    bizIndicatorItemCategoryDto.setIndicators(indicators);
                }
                bizIndicatorItemCategoryDtos.add(bizIndicatorItemCategoryDto);
            }
        }
        return bizIndicatorItemCategoryDtos;
    }

    /**
     * 查看套餐对比差异项
     *
     * @param request
     * @return
     */
    @Override
    public ServiceBizItemContrastlDto showGoodsContrastDiff(ServiceGoodsContrastRequest request) {
        List<String> skuIdList = request.getSkuIdList();
        AssertUtils.isTrue(skuIdList.size() > CommonConstant.THREE, ProductErrorCode.SERVICE_CONTRAST_RANG_OUT);

        ServiceBizItemContrastlDto serviceBizItemContrastlDto = showGoodsContrast(request);
        // 项目明细
        List<ServiceBizProgramContrastlDto> programContrastlDtoList = serviceBizItemContrastlDto.getProgramContrastlDtoList();
        if(CollUtil.isEmpty(programContrastlDtoList)){
            return serviceBizItemContrastlDto;
        }

        log.info("ProductPoolApplicationImpl->showGoodsContrastDiff start programContrastlDtoList={}", programContrastlDtoList);
        List<ServiceBizProgramContrastlDto> diffProgramContrastlDtoList = diffProgramContrastlDtoList(programContrastlDtoList);
        serviceBizItemContrastlDto.setProgramContrastlDtoList(diffProgramContrastlDtoList);
        return serviceBizItemContrastlDto;
    }


    /**
     * 对比差异项目列表
     * @param programContrastlDtoList
     * @return
     */
    private List<ServiceBizProgramContrastlDto> diffProgramContrastlDtoList(List<ServiceBizProgramContrastlDto> programContrastlDtoList){
        List<ServiceBizProgramContrastlDto> diffProgramContrastlDtoList = new ArrayList<>();
        for(ServiceBizProgramContrastlDto serviceBizProgramContrastlDto : programContrastlDtoList){
            List<Long> tempBizItemId = getItemRetainAll(serviceBizProgramContrastlDto.getItemBizCategoryList());
            if(CollUtil.isNotEmpty(tempBizItemId)){
                ServiceBizProgramContrastlDto diffServiceBizProgramContrastl = ServiceBizProgramContrastlDto.builder().build();
                diffServiceBizProgramContrastl.setSecondBizCategoryName(serviceBizProgramContrastlDto.getSecondBizCategoryName());
                List<ItemBizCategoryDto> itemBizCategoryList = new ArrayList<>();
                Integer itemCount = 0;
                for(ItemBizCategoryDto itemCategoryDto : serviceBizProgramContrastlDto.getItemBizCategoryList()){
                    List<BizItemSimpleDto> bizItemList = itemCategoryDto.getBizItemList();
                    List<BizItemSimpleDto> diffBizItemList = new ArrayList<>();
                    ItemBizCategoryDto itemCategoryTemp = new ItemBizCategoryDto();
                    for(BizItemSimpleDto bizItemSimpleDto : bizItemList){
                        if(!tempBizItemId.contains(bizItemSimpleDto.getStandardItemId())){
                            diffBizItemList.add(bizItemSimpleDto);
                            itemCount ++;
                        }
                    }
                    itemCategoryTemp.setSkuNo(itemCategoryDto.getSkuNo());
                    itemCategoryTemp.setBizItemList(diffBizItemList);
                    itemBizCategoryList.add(itemCategoryTemp);
                }
                if(itemCount > 0){
                    diffServiceBizProgramContrastl.setItemBizCategoryList(itemBizCategoryList);
                    diffProgramContrastlDtoList.add(diffServiceBizProgramContrastl);
                }
            }else{
                diffProgramContrastlDtoList.add(serviceBizProgramContrastlDto);
            }
        }
        return diffProgramContrastlDtoList;
    }
    /**
     * 项目id 交集
     * @param itemCategoryDtos
     * @return
     */
    private List<Long> getItemRetainAll(List<ItemBizCategoryDto> itemCategoryDtos){
        List<Long> tempBizItemId = new ArrayList<>();
        if(itemCategoryDtos.size() > 1){
            for(ItemBizCategoryDto itemCategoryDto : itemCategoryDtos){
                List<BizItemSimpleDto> bizItemList = itemCategoryDto.getBizItemList();
                List<Long> bizItemIdList = bizItemList.stream().map(BizItemSimpleDto::getStandardItemId).collect(Collectors.toList());
                if(CollUtil.isEmpty(bizItemIdList)){
                    return new ArrayList<>();
                }
                if(CollUtil.isNotEmpty(tempBizItemId)){
                    tempBizItemId.retainAll(bizItemIdList);
                }else{
                    tempBizItemId.addAll(bizItemIdList);
                }
            }
        }
        log.info("ProductPoolApplicationImpl->getItemRetainAll result tempBizItemId={}", tempBizItemId);
        return tempBizItemId;
    }
    /**
     * 查询SKU最终价格
     * @param skuIds
     * @param userPin
     * @param xfylOrderUserAddressBO
     * @return
     */
    private Map<Long, GuideMultiResultModule> querySkuFinalPrice(List<String> skuIds,String userPin,XfylOrderUserAddressBO xfylOrderUserAddressBO){
        if(CollUtil.isNotEmpty(skuIds) && Objects.nonNull(xfylOrderUserAddressBO)){
            Map<Long, GuideMultiResultModule> result = new HashMap<>(skuIds.size());
            List<List<String>> partition = Lists.partition(skuIds,25);
            CompletableFuture<Map<Long, GuideMultiResultModule>>[] futures = new CompletableFuture[partition.size()];
            for (int i = 0; i < partition.size(); i++){
                GuidePriceQueryBo guidePriceQueryBo = new GuidePriceQueryBo();
                guidePriceQueryBo.setPin(userPin);
                Set<String> skuIdQuerySet = new HashSet<>(partition.get(i));
                guidePriceQueryBo.setSkuIds(skuIdQuerySet);
                guidePriceQueryBo.setProvinceId(xfylOrderUserAddressBO.getProvinceId());
                guidePriceQueryBo.setCityId(xfylOrderUserAddressBO.getCityId());
                guidePriceQueryBo.setCountryId(xfylOrderUserAddressBO.getCountryId());

                CompletableFuture<Map<Long, GuideMultiResultModule>> future= CompletableFuture.supplyAsync(()->{
                    return skuInfoRpc.batchFinalPrice(guidePriceQueryBo);
                }, executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
                futures[i] = future;
            }

            for (CompletableFuture<Map<Long, GuideMultiResultModule>> future : futures) {
                try {
                    Map<Long, GuideMultiResultModule> multiResult = future.get(1200, TimeUnit.MILLISECONDS);
                    result.putAll(multiResult);
                }catch (Exception e){
                    log.warn("ProductPoolApplicationImpl->querySkuFinalPrice error msg={}", e.getMessage());
                }
            }
            log.info("ProductPoolApplicationImpl->querySkuFinalPrice result size={}", result.size());
            return result;
        }
        return null;
    }

    /**
     * 处理最近门店信息
     * @param nearStoreInfoTO
     * @param serviceGoodsPoolDto
     */
    private void dealPopStoreInfo(NearStoreInfoTO nearStoreInfoTO,ServiceGoodsPoolDto serviceGoodsPoolDto){
        if(Objects.nonNull(nearStoreInfoTO)){
            Long storeId = nearStoreInfoTO.getStoreId();
            serviceGoodsPoolDto.setStoreId(storeId);
            serviceGoodsPoolDto.setStoreName(nearStoreInfoTO.getStoreName());
            serviceGoodsPoolDto.setDistance(nearStoreInfoTO.getDistance() + CommonConstant.DISTANCE_UNIT);
            // 查询门店评分
//            String storeScore = popLocStoreInfoRpc.queryStoreScoreInfo(storeId);
//            serviceGoodsPoolDto.setStoreScore(storeScore);
//            if(CommonConstant.NEW_STORE_TAG_SCORE.equals(storeScore)){
//                serviceGoodsPoolDto.setStoreTag(getStoreTag(storeId));
//            }
        }
    }



    /**
     * 获取用户经纬度 或默认收货地址经纬度
     * @param lat
     * @param lng
     * @param userPin
     * @return
     */
    private XfylOrderUserAddressBO getUserLatAndLng(String lat,String lng,String userPin){
        try{
            XfylOrderUserAddressBO xfylOrderUserAddressBO = new XfylOrderUserAddressBO();
            if(StringUtils.isBlank(lat) || StringUtils.isBlank(lng)){
                xfylOrderUserAddressBO = orderTrackRpcService.queryUserDefaultAddressLngAndLat(userPin);
            }else{
                xfylOrderUserAddressBO.setLng(Double.valueOf(lng));
                xfylOrderUserAddressBO.setLat(Double.valueOf(lat));
            }

            if(Objects.isNull(xfylOrderUserAddressBO.getFirstAddressArea()) || Objects.isNull(xfylOrderUserAddressBO.getSecondAddressArea())){
                JDAddressRequestInfo addressRequestInfo = new JDAddressRequestInfo();
                addressRequestInfo.setLng(new BigDecimal(xfylOrderUserAddressBO.getLng()));
                addressRequestInfo.setLat(new BigDecimal(xfylOrderUserAddressBO.getLat()));
                BaseAddressInfo addressInfo = orderTrackRpcService.getAddressByLatAndLon(addressRequestInfo);
                xfylOrderUserAddressBO.setProvinceId(addressInfo.getProvinceCode());
                xfylOrderUserAddressBO.setCityId(addressInfo.getCityCode());
                xfylOrderUserAddressBO.setCountryId(addressInfo.getDistrictCode());
            }else{
                xfylOrderUserAddressBO.setProvinceId(xfylOrderUserAddressBO.getFirstAddressArea().intValue());
                xfylOrderUserAddressBO.setCityId(xfylOrderUserAddressBO.getSecondAddressArea().intValue());
                xfylOrderUserAddressBO.setCountryId(xfylOrderUserAddressBO.getThirdAddressArea().intValue());
            }
            return xfylOrderUserAddressBO;
        }catch (Exception e){
            log.error("ProductPoolApplicationImpl -> getUserLatAndLng error:", e);
            return null;
        }
    }

    /**
     * 套餐保存到套餐池中
     * @param userPin
     * @param skuId
     */
    private void saveServicePool(String userPin,String skuId){
        JdhUserContrastPool jdhUserContrastPool = JdhUserContrastPool.builder().build();
        jdhUserContrastPool.setPoolId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        jdhUserContrastPool.setUserPin(userPin);
        jdhUserContrastPool.setContrastId(skuId);
        jdhUserContrastPool.setCabinetStatus(1);
        jdhUserContrastPool.setContrastType(1);
        jdhUserContrastPoolRepository.save(jdhUserContrastPool);
    }

    /**
     * 获取到手价格
     *
     * @param resultModuleMap resultModuleMap
     * @param skuId           SKU ID
     * @return {@link String}
     */
    private String getFinalPrice(Map<Long, GuideMultiResultModule> resultModuleMap,Long skuId){
        try {
            GuideMultiResultModule resultModule = resultModuleMap.get(skuId);
            if(Objects.nonNull(resultModule)){
                GuideResultModule guideResultModule = resultModule.getGuideResultModule();
                if(Objects.nonNull(guideResultModule) && guideResultModule.isSuccess()){
                    Integer basisPriceType = guideResultModule.getBasisPriceType();
                    //不是50 不是51 代表 实时到手价
                    if(!CommonConstant.NUMBER_FIFTY.equals(basisPriceType) && !CommonConstant.FIFTY_ONE.equals(basisPriceType)){
                        BigDecimal purchasePrice = resultModule.getPurchasePrice();
                        log.info("ProductPoolApplicationImpl -> getFinalPrice 获取实时到手价成功 purchasePrice:{}",purchasePrice);
                        return purchasePrice.setScale(2, RoundingMode.HALF_UP).toString();
                    }
                }
            }
            return StringUtils.EMPTY;
        }catch (Exception e){
            log.error("ProductPoolApplicationImpl -> getFinalPrice error",e);
            return StringUtils.EMPTY;
        }
    }

}