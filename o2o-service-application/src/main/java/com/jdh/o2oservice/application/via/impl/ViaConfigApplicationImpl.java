package com.jdh.o2oservice.application.via.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.via.ViaConfigApplication;
import com.jdh.o2oservice.application.via.convert.ViaConfigApplicationConverter;
import com.jdh.o2oservice.application.via.handler.AbstractViaDataFillHandler;
import com.jdh.o2oservice.application.via.handler.ViewPromisePageHandler;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.model.Pagination;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepPageQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfigIdentifier;
import com.jdh.o2oservice.core.domain.support.via.model.ViaFloorInfo;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import com.jdh.o2oservice.export.via.cmd.ViaLocalStorageCreateCmd;
import com.jdh.o2oservice.export.via.dto.ViaConfigDto;
import com.jdh.o2oservice.export.via.dto.ViaPromiseListDto;
import com.jdh.o2oservice.export.via.dto.ViaPromisePage;
import com.jdh.o2oservice.export.via.query.ViaConfigRequest;
import com.jdh.o2oservice.export.via.query.ViaLocalStorageRequest;
import com.jdh.o2oservice.export.via.query.ViaPagePromiseRequest;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 页面配置应用层 实现类
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Slf4j
@Service
public class ViaConfigApplicationImpl implements ViaConfigApplication {

    /**
     * viaConfigRepository
     */
    @Resource
    private ViaConfigRepository viaConfigRepository;
    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;
    @Resource
    private AngelWorkRepository angelWorkRepository;
    @Resource
    private JdhStationRepository jdhStationRepository;

    /**
     * viewPromisePageHandler
     */
    @Resource
    private ViewPromisePageHandler viewPromisePageHandler;

    /**
     * handlerMap
     */
    @MapAutowired
    private Map<String,AbstractViaDataFillHandler> handlerMap;

    /**
     * cluster
     */
    @Resource
    private Cluster cluster;

    /**
     * localStorageCacheKey
     */
    private final String localStorageCacheKey = "jdh-o2o-service:localStorage:{0}:{1}";

    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;



    /**
     * 查询页面配置
     *
     * @param req 查询
     * @return {@link ViaConfigDto}
     */
    @Override
    public ViaConfigDto queryViaConfig(ViaConfigRequest req) {
        log.info("ViaConfigApplicationImpl queryViaConfig req:{}", JSON.toJSONString(req));
        ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(req.getScene()).build());
        log.info("ViaConfigApplicationImpl queryViaConfig viaConfig:{}", JSON.toJSONString(viaConfig));
        if(Objects.isNull(viaConfig)){
            throw new BusinessException(SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        }
        // 优先根据业务类型获取，如果不存在匹配的handler则根据业务模式匹配
        AbstractViaDataFillHandler fillHandler = handlerMap.get(viaConfig.getPage() + "_" +
                Objects.requireNonNull(BusinessModeEnum.getEnumByCode(viaConfig.getBusinessModeCode())).getCode() + "_" +
                Objects.requireNonNull(ServiceTypeEnum.getEnumByCode(viaConfig.getServiceType())).getServiceType());

        // 如果fillHandler为空，尝试获取兜底的handler进行处理
        if (Objects.isNull(fillHandler)){
            String businessModeKey = viaConfig.getPage() + "_" +
                    Objects.requireNonNull(BusinessModeEnum.getEnumByCode(viaConfig.getBusinessModeCode())).getCode();
            fillHandler = handlerMap.get(businessModeKey);
        }

         // 如果fillHandler为空，尝试页面的兜底处理
        if (Objects.isNull(fillHandler)){
            String businessModeKey = viaConfig.getPage();
            fillHandler = handlerMap.get(businessModeKey);
        }
        FillViaConfigDataContext ctx = new FillViaConfigDataContext();
        BeanUtils.copyProperties(req,ctx);
        ctx.setViaConfig(viaConfig);

        fillHandler.handle(ctx);
        this.removeSomeFloor(ctx);
        log.info("ViaConfigApplicationImpl queryViaConfig ctx={}",JSON.toJSONString(ctx));
        ViaConfigDto res = ViaConfigApplicationConverter.INSTANCE.convert2Dto(ctx.getViaConfig());
        res.setPage(viaConfig.getPage());
        return res;
    }

    /**
     * 移除一些需要过滤的楼层
     * @param ctx
     */
    private void removeSomeFloor(FillViaConfigDataContext ctx){
        if(ctx==null){
            log.info("ctx 为空,逻辑终止!!!");
            return;
        }
        if(ctx.getViaConfig()==null|| CollectionUtils.isEmpty(ctx.getViaConfig().getFloorList())){
            log.info("ctx.getViaConfig()为空,逻辑终止!!! ");
            return;
        }

        Iterator<ViaFloorInfo> iterator = ctx.getViaConfig().getFloorList().iterator();
        while(iterator.hasNext()){
            ViaFloorInfo viaFloorInfo = iterator.next();
            if(viaFloorInfo.getRemove()){
                log.info("ViaConfigApplicationImpl queryViaConfig floorCode={} 已过滤!!!!",viaFloorInfo.getFloorCode());
                iterator.remove();
            }
        }
    }

    /**
     * 分页查询履约单列表
     * 1、获取业务身份，构建查询参数
     * 2、从仓储获取详细数据
     * 3、构建按钮的集合
     *
     * @param query query
     * @return {@link Pagination}<{@link PromiseServiceDetailDto}>
     */
    @Override
    @LogAndAlarm()
    public Response<ViaPromisePage<ViaPromiseListDto>> queryByPage(ViaPagePromiseRequest query) {
        ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(query.getScene()).build());
        log.info("ViaConfigApplicationImpl->queryByPage viaConfig viaConfig:{}", JSON.toJSONString(viaConfig));
        PromiseRepPageQuery promiseRepPageQuery = new PromiseRepPageQuery();
        //如果直接传voucherId集合，用voucherId集合
        if (CollectionUtil.isNotEmpty(query.getVoucherIds())){
            List<Long> voucherIdList = query.getVoucherIds().stream().map(Long::parseLong).collect(Collectors.toList());
            promiseRepPageQuery.setVoucherIds(voucherIdList);

        //如果没传voucherId集合，但是传了orderId，用orderId匹配
        }else if(StrUtil.isNotEmpty(query.getOrderId())){
            // 后续可优化，针对业务模式进行处理
            String businessMode = viaConfig.getBusinessModeCode();
            if (StringUtils.equals(businessMode, BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())){
                List<JdhVoucher> vouchers = voucherRepository.listByQuery(VoucherRepQuery
                        .builder()
                        .sourceVoucherIds(Lists.newArrayList(query.getOrderId()))
                        .build());
                List<Long> voucherIds = vouchers.stream().map(JdhVoucher::getVoucherId).collect(Collectors.toList());
                query.setVoucherIds(voucherIds.stream().map(String::valueOf).collect(Collectors.toList()));
                promiseRepPageQuery.setVoucherIds(voucherIds);
            }else {
                // 如果有orderId，用orderId
                List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(Long.parseLong(query.getOrderId()));
                List<JdhVoucher> vouchers = voucherRepository.listByQuery(VoucherRepQuery
                        .builder()
                        .sourceVoucherIds(jdOrderItems.stream().map(ele -> ele.getOrderItemId().toString()).collect(Collectors.toList()))
                        .build());
                promiseRepPageQuery.setVoucherIds(vouchers.stream().map(JdhVoucher::getVoucherId).collect(Collectors.toList()));
            }


        }


        if(CollUtil.isEmpty(promiseRepPageQuery.getVoucherIds())){
            throw new BusinessException(SupportErrorCode.VIA_PROMISE_PAGE_INFO_NOT_EXIT);
        }

        promiseRepPageQuery.setPageNum(query.getPageNum());
        promiseRepPageQuery.setPageSize(query.getPageSize());
        promiseRepPageQuery.setUserPin(query.getUserPin());
        Page<JdhPromise> page = promiseRepository.page(promiseRepPageQuery);
        if(page==null){
            throw new BusinessException(new DynamicErrorCode("-1","样本信息已重置，请重新进入页面录入样本信息"));
        }
        log.info("ViaConfigApplicationImpl->queryByPage viaConfig page:{}", JSON.toJSONString(page));
        return viewPromisePageHandler.assemble(viaConfig, page, query);
    }


    /**
     * 添加本地存储器
     *
     * @param cmd cmd
     * @return {@link String}
     */
    @Override
    public String addLocalStorage(ViaLocalStorageCreateCmd cmd) {
        String storageId = String.valueOf(System.currentTimeMillis());
        String cacheKey = MessageFormat.format(localStorageCacheKey, cmd.getUserPin(), storageId);
        cluster.setEx(cacheKey, StrUtil.isBlank(cmd.getContent()) ? "" : cmd.getContent(),NumConstant.NUM_1,TimeUnit.DAYS);
        return storageId;
    }

    /**
     * 获取本地存储
     *
     * @param query query
     * @return {@link String}
     */
    @Override
    public String getLocalStorage(ViaLocalStorageRequest query) {
        String cacheKey = MessageFormat.format(localStorageCacheKey, query.getUserPin(), query.getStorageId());
        String value = cluster.get(cacheKey);
        return StrUtil.isBlank(value) ? StrUtil.EMPTY : value;
    }

}
