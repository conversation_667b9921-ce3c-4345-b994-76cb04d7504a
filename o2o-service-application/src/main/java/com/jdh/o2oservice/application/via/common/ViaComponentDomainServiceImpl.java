package com.jdh.o2oservice.application.via.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaAssemblyCodeEnum;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ViaStatusMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 16:33
 */
@Component
@Slf4j
public class ViaComponentDomainServiceImpl implements ViaComponentDomainService {

    @Resource
    private ViaConfigRepository viaConfigRepository;

    /**
     * }
     *
     * @return {@link ViaStatusMapping}
     */
    @Override
    public ViaStatusMapping parseHomeTestMapping(Integer orderStatus,
                                                 Integer promiseStatus,
                                                 List<Integer> medicalPromiseStatus,
                                                 Boolean isImmediately,
                                                 String stepCode,
                                                 ViaConfig viaConfig) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus", orderStatus);
        param.put("promiseStatus", promiseStatus);
        param.put("medPromiseStatusList", medicalPromiseStatus);
        param.put("isImmediately", isImmediately);
        param.put("stepCode", stepCode);

        List<Integer> completedMedicalPromiseStatus = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(medicalPromiseStatus)) {
            for (Integer status : medicalPromiseStatus) {
                if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(status) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(status)) {
                    completedMedicalPromiseStatus.add(status);
                }
                param.put("completedMedicalPromiseStatus", completedMedicalPromiseStatus);
            }
        }

        List<ViaStatusMapping> showMapping = Lists.newArrayList();
        for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                showMapping.add(viaStatusMapping);
                viaConfig.setStatusMapping(showMapping);
                viaConfig.setAggregateStatus(viaStatusMapping.getAggregateStatus());
                log.info("ViaStatusMappingParseImpl->parseHomeTestMapping showMapping={}", JSON.toJSONString(showMapping));
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    public static void main(String[] args) {
        AviatorEvaluator.compile("include(seq.list(3,6,15，16),promiseStatus) && stepCode==\"modify\"", Boolean.TRUE).execute(new HashMap<>());
    }
    /**
     * }
     *
     * @return {@link ViaStatusMapping}
     */
    @Override
    public ViaStatusMapping parseHomeTransportTestMapping(Integer promiseStatus,
                                                          List<Integer> medicalPromiseStatus,
                                                          Integer shipStatus,
                                                          String stepCode,
                                                          ViaConfig viaConfig) {
        Map<String, Object> param = new HashMap<>();
        param.put("promiseStatus", promiseStatus);
        param.put("medPromiseStatusList", medicalPromiseStatus);
        param.put("shipStatus", shipStatus);
        param.put("stepCode", stepCode);

        List<Integer> completedMedicalPromiseStatus = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(medicalPromiseStatus)) {
            for (Integer status : medicalPromiseStatus) {
                if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(status) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(status)) {
                    completedMedicalPromiseStatus.add(status);
                }
                param.put("completedMedicalPromiseStatus", completedMedicalPromiseStatus);
            }
        }

        List<ViaStatusMapping> showMapping = Lists.newArrayList();
        for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                showMapping.add(viaStatusMapping);
                viaConfig.setAggregateStatus(viaStatusMapping.getAggregateStatus());
                log.info("ViaStatusMappingParseImpl->parseHomeTestMapping showMapping={}", JSON.toJSONString(showMapping));
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    /**
     * 解析到家检测服务的VIA页面的状态映射
     *
     * @param orderStatus
     * @param promiseStatus
     * @param medicalPromiseStatus
     * @param isImmediately        是否立即预约
     * @param workStatus
     * @param viaConfig
     * @return
     */
    @Override
    public ViaStatusMapping parseHomeTestMappingV2(Integer orderStatus,
                                                   Integer promiseStatus,
                                                   List<Integer> medicalPromiseStatus,
                                                   Boolean isImmediately,
                                                   Integer workStatus,
                                                   ViaConfig viaConfig,
                                                   Integer riskAssessmentStatus) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus", orderStatus);
        param.put("promiseStatus", promiseStatus);
        param.put("medPromiseStatusList", medicalPromiseStatus);
        param.put("isImmediately", isImmediately);
        param.put("workStatus", workStatus);
        param.put("riskAssessmentStatus", riskAssessmentStatus);

        List<Integer> completedMedicalPromiseStatus = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(medicalPromiseStatus)) {
            for (Integer status : medicalPromiseStatus) {
                if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(status) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(status)) {
                    completedMedicalPromiseStatus.add(status);
                }
                param.put("completedMedicalPromiseStatus", completedMedicalPromiseStatus);
            }
        }

        log.info("ViaStatusMappingParseImpl -> parseHomeTestMappingV2 param={}", JSON.toJSONString(param));

        List<ViaStatusMapping> showMapping = Lists.newArrayList();
        for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                showMapping.add(viaStatusMapping);
                viaConfig.setStatusMapping(showMapping);
                viaConfig.setAggregateStatus(viaStatusMapping.getAggregateStatus());
                log.info("ViaStatusMappingParseImpl -> parseHomeTestMappingV2 showMapping={}", JSON.toJSONString(showMapping));
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    @Override
    public ViaStatusMapping parseHomeTestMappingV3(Integer orderStatus,
                                                   Integer promiseStatus,
                                                   List<Integer> medicalPromiseStatus,
                                                   Boolean isImmediately, Integer workStatus,
                                                   ViaConfig viaConfig,
                                                   Integer riskAssessmentStatus,
                                                   Boolean highRiskAssessmentFlag) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus", orderStatus);
        param.put("promiseStatus", promiseStatus);
        param.put("medPromiseStatusList", medicalPromiseStatus);
        param.put("isImmediately", isImmediately);
        param.put("workStatus", workStatus);
        param.put("riskAssessmentStatus", riskAssessmentStatus);
        param.put("highRiskAssessmentFlag", highRiskAssessmentFlag);
        log.info("ViaStatusMappingParseImpl -> parseHomeTestMappingV3 param={}", JSON.toJSONString(param));

        List<ViaStatusMapping> showMapping = Lists.newArrayList();
        for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                showMapping.add(viaStatusMapping);
                viaConfig.setStatusMapping(showMapping);
                viaConfig.setAggregateStatus(viaStatusMapping.getAggregateStatus());
                log.info("ViaStatusMappingParseImpl -> parseHomeTestMappingV3 showMapping={}", JSON.toJSONString(showMapping));
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    @Override
    public String queryPatientHeadImage(Integer gender, Integer age) {

        Map<String, Object> patientMap = viaConfigRepository.findAssemblyConfig(ViaAssemblyCodeEnum.PATIENT.getCode());
        String hederImageUrl = Objects.toString(patientMap.get("defaultHeadImage"), "");
        if (Objects.nonNull(age) && Objects.nonNull(gender)) {

            if (age >= 18 && GenderEnum.MAN.getType().equals(gender)) {
                hederImageUrl = Objects.toString(patientMap.get("manHeadImage"), "");
            } else if (age < 18 && GenderEnum.MAN.getType().equals(gender)) {
                hederImageUrl = Objects.toString(patientMap.get("boyHeadImage"), "");
            } else if (age >= 18 && GenderEnum.WOMEN.getType().equals(gender)) {
                hederImageUrl = Objects.toString(patientMap.get("womanHeadImage"), "");
            } else if (age < 18 && GenderEnum.WOMEN.getType().equals(gender)) {
                hederImageUrl = Objects.toString(patientMap.get("girlHeadImage"), "");
            }
        }
        return hederImageUrl;
    }

}
