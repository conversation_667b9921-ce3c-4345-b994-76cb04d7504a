package com.jdh.o2oservice.application.medicalpromise.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplicationV2;
import com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseExtApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderEquipmentApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AbnormalTemplateConfig;
import com.jdh.o2oservice.base.enums.IndicatorTemplateEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GeoDistanceUtil;
import com.jdh.o2oservice.base.util.NumberUtils;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationModeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.InventoryChannelEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.bo.MedPromiseExceptionRecordBO;
import com.jdh.o2oservice.core.domain.medpromise.context.JdhServiceItemContext;
import com.jdh.o2oservice.core.domain.medpromise.context.JdhStationServiceItemRelContext;
import com.jdh.o2oservice.core.domain.medpromise.context.MedicalPromiseDispatchContext;
import com.jdh.o2oservice.core.domain.medpromise.context.StationTransferContext;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.factory.StoreDispatchRuleFactory;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseDeliveryStep;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseHistory;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.StoreDispatchRule;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.service.MedicalPromiseDomainService;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.export.angel.cmd.ReduceInventoryCmd;
import com.jdh.o2oservice.export.angel.dto.SkuAngelStationDto;
import com.jdh.o2oservice.export.angel.dto.SkuAngelStationResultDto;
import com.jdh.o2oservice.export.angel.dto.StationDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.QuerySkuAngelStationRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import com.jdh.o2oservice.export.laboratory.dto.JdhStoreTransferStationDto;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantMultiStoreRequest;
import com.jdh.o2oservice.export.medicalpromise.AbnormalDealDTO;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.AbnormalTemplateDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.AbnormalTypeDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseCallbackResultDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.AbnormalTemplateRequest;
import com.jdh.o2oservice.export.medicalpromise.query.QueryMedPromiseAndShipRequest;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.VoucherIdRequest;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.ProviderEquipmentDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.export.report.dto.RangeValueDTO;
import com.jdh.o2oservice.export.report.dto.StructQuickReportResultIndicatorDTO;
import com.jdh.o2oservice.export.report.enums.SampleAnomalyOperateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/4
 */
@Service
@Slf4j
public class MedicalPromiseApplicationV2Impl implements MedicalPromiseApplicationV2 {

    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;

    /**
     * 地址服务
     */
    @Autowired
    private AddressRpc addressRpc;

    /**
     * 履约应用层
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * jdhSkuRepository
     */
    @Autowired
    private JdhSkuRepository jdhSkuRepository;


    /**
     * 顺序规则factory
     */
    @Autowired
    private StoreDispatchRuleFactory storeDispatchRuleFactory;

    /**
     *
     */
    @Autowired
    private VerticalBusinessRepository businessRepository;

    @Autowired
    private DuccConfig duccConfig;


    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    /**
     * 履约检测单domain层
     */
    @Autowired
    private MedicalPromiseDomainService medicalPromiseDomainService;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;
    /**
     * 线程池工厂
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;


    /**
     * 供应商门店
     */
    @Autowired
    private ProviderStoreApplication providerStoreApplication;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    @Resource
    private MedPromiseHistoryRepository medPromiseHistoryRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;


    /**
     * 服务站应用层
     */
    @Autowired
    private StationApplication stationApplication;

    /**
     * 距离路径规划
     */
    @Resource
    private DirectionServiceRpc directionServiceRpc;

    /**
     * medPromiseExtApplication
     */
    @Autowired
    private MedPromiseExtApplication medPromiseExtApplication;


    /**
     * productServiceItemApplication
     */
    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    @Autowired
    private VoucherApplication voucherApplication;

    @Autowired
    private JdOrderApplication jdOrderApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Autowired
    private ProviderEquipmentApplication providerEquipmentApplication;


    /**
     * 实验室派发
     *
     * @param medicalPromiseDispatchCmd
     * @return
     */
    @Override
    public Boolean storeDisPatch(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        // 护理类不需要派发实验室
        if (ServiceTypeEnum.CARE.getServiceType().equalsIgnoreCase(medicalPromiseDispatchCmd.getServiceType())) {
            return Boolean.TRUE;
        }
        //加锁,同一个履约单派发实验室
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STORE_DISPATCH_LOCK_KEY, medicalPromiseDispatchCmd.getPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.STORE_DISPATCH_LOCK_KEY.getExpireTime(), RedisKeyEnum.STORE_DISPATCH_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.invalidMedicalPromiseBatch],实验室履约单派发加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            checkDispatchParam(medicalPromiseDispatchCmd);
            //1.根据promiseId获取履约单
            PromiseDto jdhPromise = getJdhPromise(medicalPromiseDispatchCmd);
            //2.根据promiseId和受检人IDList获取检测单
            List<MedicalPromise> medicalPromises = this.getValidMedicalPromises(medicalPromiseDispatchCmd);
            Set<Long> serviceId = medicalPromises.stream().map(p->Long.valueOf(p.getServiceId())).collect(Collectors.toSet());
            List<JdhSku> jdhSkuList = jdhSkuRepository.queryMultiSku(serviceId.stream().map(t-> JdhSku.builder().skuId(t).build()).collect(Collectors.toList()));
            Set<Long> collect = jdhSkuList.stream().filter(p -> !Objects.equals(CommonConstant.ONE, p.getSkuType())).map(JdhSku::getSkuId).collect(Collectors.toSet());
            medicalPromiseDispatchCmd.setServiceIdSet(collect);
            //组装预约时间
            packBookTime(medicalPromiseDispatchCmd, jdhPromise);

            String msg = "订单号："+jdhPromise.getSourceVoucherId();
            medicalPromiseDispatchCmd.setMsg(msg);
            //3.根据skuNo获取排序规则
            List<StoreDispatchRule> storeDispatchRules = this.getStoreDispatchRules(jdhPromise, medicalPromises);
            //6.获取businessMode
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhPromise.getVerticalCode());
            medicalPromiseDispatchCmd.setBusinessModeCode(jdhVerticalBusiness.getBusinessModeCode());
            //4.获取用户地址命中服务站对应的实验室列表
            Long orderId = Objects.nonNull(medicalPromiseDispatchCmd.getOrderId()) ? medicalPromiseDispatchCmd.getOrderId() : Long.valueOf(jdhPromise.getSourceVoucherId());
            medicalPromiseDispatchCmd.setOrderId(orderId);
            Map<String,List<SkuAngelStationDto>> stationToAngelStation = Maps.newHashMap();
            Set<Long> existJdTransferStationId = Sets.newHashSet();
            Map<Long,List<Long>> transferToAngelStationMap = Maps.newHashMap();
            //非快检模式,不会走服务站逻辑
            Set<String> existStation = getExistStationSet(medicalPromiseDispatchCmd,stationToAngelStation,existJdTransferStationId,transferToAngelStationMap);
            //5.根据检测项目获取实验室列表
            List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts = this.getJdhStationServiceItemRelContexts(medicalPromiseDispatchCmd, medicalPromises,existStation,existJdTransferStationId);

            if (CollectionUtils.isEmpty(jdhStationServiceItemRelContexts)){
                sendDongDongMsg("分派实验室失败",medicalPromiseDispatchCmd.getMsg()+",无项目");
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
            }

            //7.获取context
            MedicalPromiseDispatchContext medicalPromiseDispatchContext = this.getMedicalPromiseDispatchContext(medicalPromiseDispatchCmd, jdhPromise, medicalPromises, storeDispatchRules, jdhStationServiceItemRelContexts);
            if(CollectionUtils.isNotEmpty(jdhSkuList.get(0).getHighQualityStoreId())){
                //非快检模式,配置了定向实验室,走定向
                medicalPromiseDispatchContext.setHighQualityStoreId(jdhSkuList.get(0).getHighQualityStoreId().get(0));
            }
            //8.计算最佳实验室组，返回的 key：站点ID，value：站点要做的项目
            List<Map<String, Set<String>>> maps = medicalPromiseDomainService.storeDispatch(medicalPromiseDispatchContext);
            if (CollectionUtils.isNotEmpty(maps)){
                //派发实验室
                disPatchStation(medicalPromises, jdhStationServiceItemRelContexts, maps ,stationToAngelStation,medicalPromiseDispatchContext,transferToAngelStationMap);
                log.info("MedicalPromiseApplicationImpl->storeDisPatch,合管前 medicalPromises={},jdhStationServiceItemRelContexts={}",JsonUtil.toJSONString(medicalPromises), JSON.toJSONString(jdhStationServiceItemRelContexts));

                //合管
                Map<String, List<String>> mergeMedicalPromiseConfig = duccConfig.getMergeMedicalPromiseConfig();
                if (MapUtils.isNotEmpty(mergeMedicalPromiseConfig)){
                    merge(medicalPromises, mergeMedicalPromiseConfig);
                }

                jdhStationServiceItemRelContexts = this.getJdhStationServiceItemRelContexts(medicalPromiseDispatchCmd, medicalPromises,existStation,existJdTransferStationId);
                log.info("MedicalPromiseApplicationImpl->storeDisPatch,合管后 重新查询jdhStationServiceItemRelContexts medicalPromises={},jdhStationServiceItemRelContexts={}",JsonUtil.toJSONString(medicalPromises),JSON.toJSONString(jdhStationServiceItemRelContexts));
                //批量更新
                List<JdhStationServiceItemRelContext> finalJdhStationServiceItemRelContexts = jdhStationServiceItemRelContexts;
                medicalPromises.forEach(medicalPromise -> {

                    log.info("MedicalPromiseApplicationImpl->storeDisPatch,medicalPromise={}",JSON.toJSONString(medicalPromise));
                    //维护reportShowType字段
                    if(CollectionUtils.isNotEmpty(finalJdhStationServiceItemRelContexts)){
                        Map<String, JdhStationServiceItemRelContext> stationIdToObj = finalJdhStationServiceItemRelContexts.stream().collect(Collectors.toMap(JdhStationServiceItemRelContext::getStationId, p -> p));
                        JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(medicalPromise.getStationId());
                        if(jdhStationServiceItemRelContext!=null){
                            List<JdhServiceItemContext> jdhServiceItemContexts = jdhStationServiceItemRelContext.getJdhServiceItemContexts().stream().filter(jdhServiceItemContext->jdhServiceItemContext.getServiceItemId().equals(medicalPromise.getServiceItemId())).collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(jdhServiceItemContexts)){
                                Integer reportShowType = jdhServiceItemContexts.get(0).getReportShowType();
                                if(reportShowType!=null){
                                    log.info("MedicalPromiseApplicationImpl->storeDisPatch,检测单 使用实验室项目配置的reportShowType={}",medicalPromise.getReportShowType());
                                    medicalPromise.setReportShowType(reportShowType);
                                }
                            }else {
                                //告警
                                Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                                JSONObject jsonObject = robotAlarmMap.get("实验室未配项目");
                                dongDongRobotRpc.sendDongDongRobotMessage(String.format("实验室未配项目，检测单ID：%s，检测项目名称：%s，实验室名称：%s，请关注",
                                                medicalPromise.getMedicalPromiseId(), medicalPromise.getServiceItemName(),  medicalPromise.getStationName()),
                                        jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
                            }
                        }
                    }
                    if (medicalPromiseRepository.save(medicalPromise)<=0){
                        log.info("MedicalPromiseApplicationImpl->storeDisPatch,saveError,medicalPromise={}",JsonUtil.toJSONString(medicalPromise));
                        throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATUS_CHANGE_ERROR);
                    }
                    //保存检测单派发历史
                    executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->this.saveMedPromiseHistory(medicalPromise));

                    // 实验室派发事件
                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_LAB_DISTRIBUTE,
                            MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).build()));
                    log.info("MedicalPromiseApplicationImpl storeDisPatch MED_PROMISE_LAB_DISTRIBUTE");
                });
//                medicalPromiseRepository.updateStoreDispatch(medicalPromises);
                return Boolean.TRUE;
            }
            //如果派送不成功抛异常，因为实验室派送是必要环节
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }

    }

    @LogAndAlarm
    @Override
    public List<MedicalPromiseDTO> queryMedPromiseAndShip(QueryMedPromiseAndShipRequest queryMedPromiseAndShipRequest) {
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(queryMedPromiseAndShipRequest.getPromiseId()).build());
        if(CollectionUtils.isEmpty(medicalPromises)){
            log.info("MedicalPromiseApplicationImpl.queryMedPromiseAndShip medicalPromises为空");
            return null;
        }
        List<MedicalPromiseDTO> medicalPromiseDTOList = MedicalPromiseConvert.INSTANCE.convert(medicalPromises);

        AngelWork angelWork = angelWorkRepository.findAngelWork(AngelWorkDBQuery.builder().promiseId(queryMedPromiseAndShipRequest.getPromiseId()).statusList(AngelWorkStatusEnum.getValidStatus()).build());
        if(angelWork==null){
            log.info("MedicalPromiseApplicationImpl.queryMedPromiseAndShip angelWork为空");
            return medicalPromiseDTOList;
        }
        List<AngelShip> angelShipList = angelShipRepository.findList(AngelShipDBQuery.builder().workId(angelWork.getWorkId()).build());
        if (CollectionUtils.isEmpty(angelShipList)){
            log.info("MedicalPromiseApplicationImpl.queryMedPromiseAndShip angelShipList为空");
            return medicalPromiseDTOList;
        }
        List<AngelShipDto> angelShipDtos = AngelPromiseApplicationConverter.instance.convertToAngelShipDtoList(angelShipList);
        Map<String,List<AngelShipDto>> receiverIdAndShipMap = angelShipDtos.stream().collect(Collectors.groupingBy(AngelShipDto::getReceiverId));

        medicalPromiseDTOList.forEach(medicalPromiseDTO -> {
            List<AngelShipDto> angelShipDtoList = receiverIdAndShipMap.get(medicalPromiseDTO.getStationId());
            if(CollectionUtils.isNotEmpty(angelShipDtoList)){
                medicalPromiseDTO.setAngelShipDto(angelShipDtoList.get(0));
            }
        });
        return medicalPromiseDTOList;
    }

    /**
     * 异常操作
     *
     * @param anomalyOperateCmd
     * @return
     */
    @Override
    public Boolean sampleAnomalyOperate(AnomalyOperateCmd anomalyOperateCmd) {
        AssertUtils.nonNull(anomalyOperateCmd, "入参为空");
        AssertUtils.hasText(anomalyOperateCmd.getSampleId(), "异常上报服务单ID为空");
        AssertUtils.nonNull(anomalyOperateCmd.getSampleAnomalyOperateType(), "异常上报类型为空");
        AssertUtils.nonNull(anomalyOperateCmd.getSampleAnomalyOperateReasonType(), "异常上报原因类型为空");
        AssertUtils.hasText(anomalyOperateCmd.getSampleAnomalyOperateReason(), "异常上报原因为空");
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("实验室异常上报");
        if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
            log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
            return false;
        }
        String msg = null;
        MedicalPromiseRepQuery repQuery = new MedicalPromiseRepQuery();
        repQuery.setMedicalPromiseId(StringUtils.isNotBlank(anomalyOperateCmd.getSampleId()) ? Long.parseLong(anomalyOperateCmd.getSampleId()) : null);
        repQuery.setSpecimenCode(anomalyOperateCmd.getSampleBarcode());
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(repQuery);
        VoucherDto jdhVoucher = voucherApplication.findByVoucherId(VoucherIdRequest.builder().voucherId(medicalPromise.getVoucherId()).build());

        if (SampleAnomalyOperateTypeEnum.REPORT_RETRACT.getType().equals(anomalyOperateCmd.getSampleAnomalyOperateType())) {
            msg = String.format("实验室在%s（检测单状态）阶段反馈由于%s（异常类型），需撤回报告，请及时处理报告重置并联系实验室重传，订单号%s，样本编码%s，检测项目%s，实验室%s",
                    MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()), anomalyOperateCmd.getSampleAnomalyOperateReason(),
                    jdhVoucher.getSourceVoucherId(), medicalPromise.getSpecimenCode(),
                    medicalPromise.getServiceItemName(), medicalPromise.getStationName());

        } else if (SampleAnomalyOperateTypeEnum.RESAMPLE.getType().equals(anomalyOperateCmd.getSampleAnomalyOperateType())) {
            msg = String.format("实验室在%s（检测单状态）阶段反馈由于%s（异常类型），需联系用户重新下单采样，订单号%s，样本编码%s，检测项目%s，实验室%s",
                    MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()), anomalyOperateCmd.getSampleAnomalyOperateReason(),
                    jdhVoucher.getSourceVoucherId(), medicalPromise.getSpecimenCode(),
                    medicalPromise.getServiceItemName(), medicalPromise.getStationName());
        } else if (SampleAnomalyOperateTypeEnum.RETEST.getType().equals(anomalyOperateCmd.getSampleAnomalyOperateType())) {
            msg = String.format("实验室在%s（检测单状态）阶段反馈由于%s（异常类型），需重新上机检测，请知悉，订单号%s，样本编码%s，检测项目%s，实验室%s",
                    MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()), anomalyOperateCmd.getSampleAnomalyOperateReason(),
                    jdhVoucher.getSourceVoucherId(), medicalPromise.getSpecimenCode(),
                    medicalPromise.getServiceItemName(), medicalPromise.getStationName());

        } else if (SampleAnomalyOperateTypeEnum.CONCESSION_TEST.getType().equals(anomalyOperateCmd.getSampleAnomalyOperateType())) {
            msg = String.format("实验室在%s（检测单状态）阶段反馈由于%s（异常类型），需联系用户是否让步检测，订单号%s，样本编码%s，检测项目%s，实验室%s",
                    MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()), anomalyOperateCmd.getSampleAnomalyOperateReason(),
                    jdhVoucher.getSourceVoucherId(), medicalPromise.getSpecimenCode(),
                    medicalPromise.getServiceItemName(), medicalPromise.getStationName());

        }
        if (StringUtils.isNotBlank(msg)) {

            //保存异常原因
            MedPromiseExceptionRecordBO bo = new MedPromiseExceptionRecordBO();
            BeanUtils.copyProperties(anomalyOperateCmd, bo);
            bo.setAnomalyTime(new Date());
            //修改状态
            medicalPromise.setExceptionRecord(JsonUtil.toJSONString(bo));
            medicalPromiseRepository.save(medicalPromise);

            dongDongRobotRpc.sendDongDongRobotMessage(msg, jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
        return true;
    }

    /**
     * 检测单状态回传
     *
     * @param medicalPromiseCallbackCmd
     * @return
     */
    @Override
    public MedicalPromiseCallbackResultDTO medicalPromiseCallBack(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd) {
        //查找检测单
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromiseCallbackCmd.getMedicalPromiseId()).build());
        if (Objects.isNull(medicalPromise)){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
        }


        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_CALL_BACK,
                MedicalPromiseEventBody.builder()
                        .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                        .status(medicalPromise.getStatus())
                        .coreStatus(medicalPromiseCallbackCmd.getCoreStatus())
                        .resultType(medicalPromiseCallbackCmd.getResultType())
                        .subStatus(medicalPromiseCallbackCmd.getSubStatus())
                        .operator(medicalPromiseCallbackCmd.getOperator())
                        .operateSystem(medicalPromiseCallbackCmd.getOperateSystem())
                        .flowCode(medicalPromiseCallbackCmd.getFlowCode())
                        .sampleAnomalyOperateType(medicalPromiseCallbackCmd.getSampleAnomalyOperateType())
                        .sampleAnomalyOperateReasonType(medicalPromiseCallbackCmd.getSampleAnomalyOperateReasonType())
                        .sampleAnomalyOperateReason(medicalPromiseCallbackCmd.getSampleAnomalyOperateReason())
                        .outerId(medicalPromiseCallbackCmd.getAppointmentNo())
                        .verticalCode(medicalPromise.getVerticalCode())
                        .serviceType(medicalPromise.getServiceType())
                        .param(JsonUtil.toJSONString(medicalPromiseCallbackCmd))
                        .build()
                )
        );

        MedicalPromiseCallbackResultDTO medicalPromiseCallbackResultDTO = new MedicalPromiseCallbackResultDTO();
        medicalPromiseCallbackResultDTO.setResult(Boolean.TRUE);
        return medicalPromiseCallbackResultDTO;
    }

    /**
     * 根据请求查询异常模板
     *
     * @param templateRequest 异常模板请求对象
     * @return 异常模板DTO对象
     */
    @Override
    public AbnormalTemplateDTO queryAbnormalTemplate(AbnormalTemplateRequest templateRequest) {
        if (Objects.isNull(templateRequest.getMedicalPromiseId()) && StringUtil.isBlank(templateRequest.getSpecimenCode())){
            throw new BusinessException(MedPromiseErrorCode.PARAM_NULL);
        }


        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(
                MedicalPromiseRepQuery.builder()
                        .medicalPromiseId(templateRequest.getMedicalPromiseId())
                        .specimenCode(templateRequest.getSpecimenCode())
                        .build()
        );

        if (Objects.isNull(medicalPromise)){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
        }

        //查询根据实验室ID和项目查询设备配置

        List<AbnormalTemplateConfig> abnormalTemplateConfigs = duccConfig.getAbnormalTemplateConfig();


        ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(medicalPromise.getStationId(), Long.valueOf(medicalPromise.getServiceItemId()));

        log.info("queryAbnormalTemplate->providerEquipmentDto={}",JsonUtil.toJSONString(providerEquipmentDto));
        Integer detectingNodesNum  = Objects.nonNull(providerEquipmentDto) ? providerEquipmentDto.getDetectingNodesNum() : CommonConstant.THREE;
        log.info("queryAbnormalTemplate->detectingNodesNum={}",JsonUtil.toJSONString(detectingNodesNum));

        Map<String, Object> expParam = Maps.newHashMap();
        expParam.put("status",medicalPromise.getStatus());
        expParam.put("subStatus",medicalPromise.getSubStatus());
        expParam.put("detectingNodesNum",detectingNodesNum);
        expParam.put("verticalCode",medicalPromise.getVerticalCode());
        log.info("queryAbnormalTemplate->expParam={}",JsonUtil.toJSONString(expParam));


        //3个节点的实验室端
        //1.已送达待收样 异常模版类型 medicalPromise.status in (0,1,2,3)  && 3节点
        //2.样本检测中 异常模版类型 medicalPromise.status in (4) && subStatus not in (601,651)  && 3节点
        //3.已出报告



        //5个节点的实验室端
        //1.已送达待收样 异常模版类型 medicalPromise.status in (0,1,2,3)  && 5节点
        //2.样本处理中 异常模版类型 medicalPromise.status in (4) && subStatus in (301,302) && 5节点
        //3.样本检测中 异常模版类型 medicalPromise.status in (4) && subStatus not in (501,502) && 5节点
        //4.报告审核中 异常模版类型 medicalPromise.status in (4) && subStatus in (601) && 5节点

        log.info("queryAbnormalTemplate->queryAbnormalTemplate,");
        for (AbnormalTemplateConfig abnormalTemplateConfig : abnormalTemplateConfigs) {
            log.info("queryAbnormalTemplate->abnormalTemplateConfig={}",JsonUtil.toJSONString(abnormalTemplateConfig));

            if ((Boolean) AviatorEvaluator.compile(abnormalTemplateConfig.getExpression(), Boolean.TRUE).execute(expParam)) {

                log.info("queryAbnormalTemplate->true={}",JsonUtil.toJSONString(abnormalTemplateConfig));

                AbnormalTemplateDTO abnormalTemplateDTO = new AbnormalTemplateDTO();
                abnormalTemplateDTO.setAbnormalDealList(JsonUtil.parseArray(JsonUtil.toJSONString(abnormalTemplateConfig.getAbnormalDealList()), AbnormalDealDTO.class));
                abnormalTemplateDTO.setAbnormalTypeDTOS(JsonUtil.parseArray(JsonUtil.toJSONString(abnormalTemplateConfig.getAbnormalTypeDTOS()), AbnormalTypeDTO.class));
                return abnormalTemplateDTO;
            }
        }

        return null;
    }

    /**
     * 根据预约信息设置派发命令的预约日期和时间段。
     * @param medicalPromiseDispatchCmd 派发命令对象
     * @param jdhPromise 预约信息对象
     */
    private  void packBookTime(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd, PromiseDto jdhPromise) {
        if (StringUtils.isBlank(medicalPromiseDispatchCmd.getScheduleDay()) || StringUtils.isBlank(medicalPromiseDispatchCmd.getBookTimeSpan())){
            String scheduleDay = DateUtil.format(jdhPromise.getAppointmentTime().getAppointmentStartTime(), CommonConstant.YMD);
            String startTime = DateUtil.format(jdhPromise.getAppointmentTime().getAppointmentStartTime(), CommonConstant.HM);
            String endTime = DateUtil.format(jdhPromise.getAppointmentTime().getAppointmentEndTime(), CommonConstant.HM);
            medicalPromiseDispatchCmd.setScheduleDay(scheduleDay);
            medicalPromiseDispatchCmd.setBookTimeSpan(startTime+"-"+endTime);
        }
    }


    /**
     * 查询履约单
     * @param medicalPromiseDispatchCmd
     * @return
     */
    private PromiseDto getJdhPromise(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(medicalPromiseDispatchCmd.getPromiseId());
        PromiseDto jdhPromise = promiseApplication.findByPromiseId(promiseIdRequest);
        log.info("MedicalPromiseApplicationImpl->getJdhPromise,jdhPromise={}",JsonUtil.toJSONString(jdhPromise));
        if (Objects.isNull(jdhPromise) || CollectionUtils.isEmpty(jdhPromise.getPatients())){
            //没有履约信息或者受检人信息
            throw new BusinessException(MedPromiseErrorCode.PROMISE_MEDICAL_PROMISE_NULL);
        }
        //校验状态,是否冻结
        if (Objects.equals(CommonConstant.ONE,jdhPromise.getFreeze())){
            // 冻结 抛异常
            throw new BusinessException(MedPromiseErrorCode.PROMISE_MEDICAL_PROMISE_FREEZE);
        }
        return jdhPromise;
    }

    /**
     * 查询检测单
     * @param medicalPromiseDispatchCmd
     * @return
     */
    private List<MedicalPromise> getValidMedicalPromises(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(medicalPromiseDispatchCmd.getPromiseId());
        medicalPromiseListQuery.setPromisePatientIdList(medicalPromiseDispatchCmd.getPromisePatientIdList());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        log.info("MedicalPromiseApplicationImpl->getValidMedicalPromises,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));
        if (CollectionUtils.isEmpty(medicalPromises)){
            // 没有检测单信息 抛异常
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_MATCH_STATUS_LIST_NULL);
        }
        //去除不符合派发状态的检测单
        Set<Integer> dispatchBlackStatus = MedicalPromiseStatusEnum.getDispatchBlackStatus();
        medicalPromises.removeIf(p->Objects.equals(CommonConstant.ONE,p.getFreeze()) || dispatchBlackStatus.contains(p.getStatus()));
        log.info("MedicalPromiseApplicationImpl->getValidMedicalPromises,medicalPromisesAfter={}",JsonUtil.toJSONString(medicalPromises));
        if (CollectionUtils.isEmpty(medicalPromises)){
            //没有符合的，抛异常
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_MATCH_STATUS_LIST_NULL);
        }
        return medicalPromises;
    }

    /**
     * 获取派送实验室顺序规则
     * @param jdhPromise
     * @param medicalPromises
     * @return
     */
    private List<StoreDispatchRule> getStoreDispatchRules(PromiseDto jdhPromise, List<MedicalPromise> medicalPromises) {
        List<StoreDispatchRule> storeDispatchRules = Lists.newArrayList();
        log.info("MedicalPromiseApplicationImpl->getStoreDispatchRules,jdhPromise={},medicalPromises={}",JsonUtil.toJSONString(jdhPromise),JsonUtil.toJSONString(medicalPromises));
        //互医场景下(或者家医场景下)，护士上门检测，实验室排序规则是确定的，整单>距离>成本
        if ((StringUtils.equals("nhHomeTest", jdhPromise.getVerticalCode())&&StringUtils.equals(ServiceTypeEnum.TEST.getServiceType(), jdhPromise.getServiceType()))
                || (StringUtils.equals("fhhHomeTest", jdhPromise.getVerticalCode())&&StringUtils.equals(ServiceTypeEnum.TEST.getServiceType(), jdhPromise.getServiceType()))
        ){
            //整单
            storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.ONE)));
            //距离
            storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.TWO)));
            //成本
            storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.THREE)));
        }else {
            //根据skuNo获取排序，非互医情况下，一个履约单只有一个sku
            String serviceId = medicalPromises.get(0).getServiceId();
            //获取排序规则
            JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(Long.valueOf(serviceId)).build());
            log.info("MedicalPromiseApplicationImpl->getStoreDispatchRules,jdhSku={}",JsonUtil.toJSONString(jdhSku));
            if (Objects.isNull(jdhSku)){
                throw new BusinessException(MedPromiseErrorCode.DISPATCH_LOGIC_NULL);
            }
            String stationAssignType = jdhSku.getStationAssignType();
            if (StringUtils.isBlank(stationAssignType)){
                throw new BusinessException(MedPromiseErrorCode.DISPATCH_LOGIC_NULL);
            }
            List<Integer> stationAssignTypeList = JsonUtil.parseArray(stationAssignType, Integer.class);
            for (Integer type : stationAssignTypeList){
                storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, type)));
            }
        }
        return storeDispatchRules;
    }

    /**
     * 获取用户地址命中服务站对应的实验室列表
     * @param medicalPromiseDispatchCmd
     * @return
     */
    private Set<String> getExistStationSet(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd,Map<String,List<SkuAngelStationDto>> skuAngelStation,Set<Long> existJdTransferStationId,Map<Long,List<Long>> transferToAngelStationMap) {
        log.info("MedicalPromiseApplicationImpl->getExistStationSet,medicalPromiseDispatchCmd={}", JsonUtil.toJSONString(medicalPromiseDispatchCmd));
        //护士不需要判断京东的服务站
        if (duccConfig.getDispatchStationWithNoAngelStation().contains(medicalPromiseDispatchCmd.getBusinessModeCode())){
            return Sets.newHashSet();
        }
        //骑手上门要校验库存、扣减库存，以父单为纬度
        SkuAngelStationResultDto skuAngelStationResultDto = stationApplication.querySkuAngelStations(getQuerySkuAngelStationRequest(medicalPromiseDispatchCmd));
        if (Objects.isNull(skuAngelStationResultDto) || CollectionUtil.isEmpty(skuAngelStationResultDto.getSkuAngelStationDtos())) {
            sendDongDongMsg("分派实验室失败",medicalPromiseDispatchCmd.getMsg()+",没有可用的服务站");
            throw new BusinessException(new DynamicErrorCode("61005","没有可用的服务站"));
        }
        log.info("MedicalPromiseApplicationImpl->getExistStationSet,skuAngelStationResultDto={}", JsonUtil.toJSONString(skuAngelStationResultDto));

        boolean needKnight = StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(), medicalPromiseDispatchCmd.getBusinessModeCode());
        for (SkuAngelStationDto skuAngelStationDto : skuAngelStationResultDto.getSkuAngelStationDtos()) {
            if (needKnight && (Objects.isNull(skuAngelStationDto.getSkuInventoryDto()) || !(skuAngelStationDto.getSkuInventoryDto().getInventoryNum() > 0))) {
                if (!skuAngelStationDto.getHavePreemption()) {
                    continue;
                }
            }
            existJdTransferStationId.add(skuAngelStationDto.getJdTransferStationId());
            List<Long> orDefault = transferToAngelStationMap.getOrDefault(skuAngelStationDto.getJdTransferStationId(), Lists.newArrayList());
            if (!orDefault.contains(skuAngelStationDto.getAngelStationId())){
                orDefault.add(skuAngelStationDto.getAngelStationId());
            }
            transferToAngelStationMap.put(skuAngelStationDto.getJdTransferStationId(), orDefault);

            List<StationDto> stationDtoList = skuAngelStationDto.getStationDtoList();
            for (StationDto stationDto : stationDtoList) {
                String stationId = String.valueOf(stationDto.getStationId());
                skuAngelStation.computeIfAbsent(stationId, k -> new ArrayList<>()).add(skuAngelStationDto);
            }
        }
        if (MapUtils.isEmpty(skuAngelStation)) {
            sendDongDongMsg("分派实验室失败",medicalPromiseDispatchCmd.getMsg()+",没有可用的服务站");
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
        }

        Set<String> existStation = Sets.newHashSet();
        skuAngelStation.forEach((k, v) -> {
            existStation.add(k);
        });
        medicalPromiseDispatchCmd.setMsg(medicalPromiseDispatchCmd.getMsg()+"，服务站命中实验室"+ StringUtil.join(existStation, ","));
        return existStation;

    }

    /**
     * 根据MedicalPromiseDispatchCmd对象构建QuerySkuAngelStationRequest对象。
     * @param medicalPromiseDispatchCmd MedicalPromiseDispatchCmd对象，包含请求的相关信息。
     * @return 构建好的QuerySkuAngelStationRequest对象。
     */
    private  QuerySkuAngelStationRequest getQuerySkuAngelStationRequest(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setFullAddress(medicalPromiseDispatchCmd.getStartAddress());
        addressDetail.setAddressId(StringUtils.isBlank(medicalPromiseDispatchCmd.getAddressId()) ? String.valueOf(medicalPromiseDispatchCmd.getPromiseId()) : medicalPromiseDispatchCmd.getAddressId());
        QuerySkuAngelStationRequest querySkuAngelStationRequest = new QuerySkuAngelStationRequest();
        querySkuAngelStationRequest.setAngelType(AngelTypeEnum.DELIVERY.getType());
        querySkuAngelStationRequest.setModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        querySkuAngelStationRequest.setScheduleDay(medicalPromiseDispatchCmd.getScheduleDay());
        querySkuAngelStationRequest.setBookTimeSpan(medicalPromiseDispatchCmd.getBookTimeSpan());
        querySkuAngelStationRequest.setBusinessId(String.valueOf(medicalPromiseDispatchCmd.getOrderId()));
        querySkuAngelStationRequest.setBusinessType(CommonConstant.ONE);
        querySkuAngelStationRequest.setAddressList(Lists.newArrayList(addressDetail));
        querySkuAngelStationRequest.setSkuNos(medicalPromiseDispatchCmd.getServiceIdSet());
        querySkuAngelStationRequest.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        return querySkuAngelStationRequest;
    }

    private void sendDongDongMsg(String key ,String msg){
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get(key);
        dongDongRobotRpc.sendDongDongRobotMessage(msg, jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
    }

    /**
     * 根据检测项目获取实验室列表
     * @param medicalPromiseDispatchCmd
     * @param medicalPromises
     * @return
     */
    private List<JdhStationServiceItemRelContext> getJdhStationServiceItemRelContexts(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd, List<MedicalPromise> medicalPromises,Set<String> existStation,Set<Long> existJdTransferStationId) {
        JdhStationServiceItemRelRequest jdhStationServiceItemRelRequest = new JdhStationServiceItemRelRequest();
        //查询门店详情
        jdhStationServiceItemRelRequest.setQueryStationDetail(Boolean.TRUE);
        //非快检模式,不受城市限制,全国查询符合条件的实验室
        if(!BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(medicalPromiseDispatchCmd.getBusinessModeCode())){
            //是否只查本市的实验室，直辖市按一级地址下筛选
            if (duccConfig.getDispatchStationLimitCity()){
                Integer cityId = medicalPromiseDispatchCmd.getCityId();
                Integer provinceId = medicalPromiseDispatchCmd.getProvinceId();
                if (Objects.isNull(cityId) || Objects.isNull(provinceId)){
                    BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(medicalPromiseDispatchCmd.getStartAddress().trim());
                    cityId = jdAddressFromAddress.getCityCode();
                    provinceId = jdAddressFromAddress.getProvinceCode();
                }
                //直辖市按一级地址筛选
                if (duccConfig.getMunicipalFirstLevelSet().contains(provinceId)){
                    jdhStationServiceItemRelRequest.setProvinceId(provinceId);
                }else {
                    //其余按二级地址筛选
                    jdhStationServiceItemRelRequest.setCityId(cityId);
                }
            }
        }
        List<Long> serviceItemIds = medicalPromises.stream().map(p -> Long.valueOf(p.getServiceItemId())).distinct().collect(Collectors.toList());
        jdhStationServiceItemRelRequest.setServiceItemIds(serviceItemIds);
        jdhStationServiceItemRelRequest.setStationIdSet(existStation);
        //查询符合检测项目和命中实验室id的 门店项目信息列表
        List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(jdhStationServiceItemRelRequest);
        log.info("MedicalPromiseApplicationImpl->getJdhStationServiceItemRelContexts,jdhStationServiceItemRelDtos={}",JsonUtil.toJSONString(jdhStationServiceItemRelDtos));
        if (CollectionUtils.isEmpty(jdhStationServiceItemRelDtos)){
            return Lists.newArrayList();
        }

        //查询实验室接驳点
        QueryMerchantMultiStoreRequest request = new QueryMerchantMultiStoreRequest();
        List<String> jdStoreIds = jdhStationServiceItemRelDtos.stream().map(JdhStationServiceItemRelDto::getStationId).collect(Collectors.toList());
        request.setJdStoreIds(jdStoreIds);
        Map<String, List<JdhStoreTransferStationDto>> storeTransferMap = providerStoreApplication.queryMultiStoreTransferStation(request);


        //聚合,key:stationId,value:JdhStationServiceItemRelContext
        Map<String,JdhStationServiceItemRelContext> contextMap = Maps.newHashMap();
        for (JdhStationServiceItemRelDto jdhStationServiceItemRelDto : jdhStationServiceItemRelDtos) {

            JdhStationServiceItemRelContext jdhStationServiceItemRelContext = contextMap.getOrDefault(jdhStationServiceItemRelDto.getStationId(), MedicalPromiseConvert.INSTANCE.convertRel(jdhStationServiceItemRelDto));
            //计算距离
            if (CollectionUtils.isEmpty(jdhStationServiceItemRelContext.getStationTransferContexts())){
                List<JdhStoreTransferStationDto> dtos = storeTransferMap.get(jdhStationServiceItemRelDto.getStationId());
                if (CollectionUtils.isNotEmpty(dtos)){
                    List<StationTransferContext> stationTransferContexts = Lists.newArrayList();
                    for (JdhStoreTransferStationDto dto : dtos) {
                        if (StringUtil.equals(BusinessModeEnum.SELF_TEST.getCode(),medicalPromiseDispatchCmd.getBusinessModeCode()) && !existJdTransferStationId.contains(dto.getJdStationId())){
                            continue;
                        }
                        StationTransferContext stationTransferContext = new StationTransferContext();
                        BeanUtils.copyProperties(dto, stationTransferContext);
                        stationTransferContext.setStationId(jdhStationServiceItemRelDto.getStationId());
                        double distance = getDistance(medicalPromiseDispatchCmd.getLatitude(),medicalPromiseDispatchCmd.getLongitude(), BigDecimal.valueOf(dto.getJdStationLatitude()), BigDecimal.valueOf(dto.getJdStationLongitude()));
                        stationTransferContext.setDistance(BigDecimal.valueOf(distance));
                        stationTransferContexts.add(stationTransferContext);
                    }
                    jdhStationServiceItemRelContext.setStationTransferContexts(stationTransferContexts);
                }
            }

            if (CollectionUtils.isEmpty(jdhStationServiceItemRelContext.getStationTransferContexts())){
                continue;
            }

            JdhServiceItemContext jdhServiceItemContext = MedicalPromiseConvert.INSTANCE.convert(jdhStationServiceItemRelDto);
            if (CollectionUtils.isEmpty(jdhStationServiceItemRelContext.getJdhServiceItemContexts())){
                jdhStationServiceItemRelContext.setJdhServiceItemContexts(Lists.newArrayList(jdhServiceItemContext));
            }else {
                jdhStationServiceItemRelContext.getJdhServiceItemContexts().add(jdhServiceItemContext);
            }

            contextMap.put(jdhStationServiceItemRelDto.getStationId(), jdhStationServiceItemRelContext);
        }
        log.info("MedicalPromiseApplicationImpl->getJdhStationServiceItemRelContexts,contextMap={}",JsonUtil.toJSONString(contextMap));

        return Lists.newArrayList(contextMap.values());
    }
    /**
     * 暂时抽出来，统计qps
     * @return
     */
    @LogAndAlarm
    public  double getDistance(BigDecimal startLatitude, BigDecimal startLLongitude, BigDecimal endLatitude, BigDecimal endLLongitude) {

        try {
            DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                    .fromLocation(String.format("%s,%s", startLatitude, startLLongitude))
                    .toLocation(String.format("%s,%s", endLatitude, endLLongitude))
                    .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
            if (Objects.nonNull(directionResult)) {
                return directionResult.getDistance();
            }
        }catch (Exception e){
            log.info("getDistance->getDirectionResult,error",e);
        }
        return GeoDistanceUtil.calculateDistance(startLLongitude.doubleValue(), startLatitude.doubleValue(), endLLongitude.doubleValue(), endLatitude.doubleValue());
    }

    /**
     *  分发实验室
     * @param medicalPromises 医疗承诺列表
     * @param jdhStationServiceItemRelContexts 站点服务项关联上下文列表
     * @param maps 字符串映射列表
     * @throws NullPointerException 如果参数为null
     */
    private  void disPatchStation(List<MedicalPromise> medicalPromises, List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts, List<Map<String, Set<String>>> maps,Map<String,List<SkuAngelStationDto>> stationToAngel,MedicalPromiseDispatchContext medicalPromiseDispatchContext,Map<Long,List<Long>> transferToAngelStationMap) {
        //获取第一个(如果最佳只有一个，则选择最佳；如果最佳的有多个，选第一个)
        Map<String, Set<String>>stringListMap = maps.get(0);
        //key:stationId,value:JdhStationServiceItemRelContext
        Map<String, JdhStationServiceItemRelContext> stationIdToObj = jdhStationServiceItemRelContexts.stream().collect(Collectors.toMap(JdhStationServiceItemRelContext::getStationId, p -> p));
        Map<String,String> itemToStation = Maps.newHashMap();
        for (Map.Entry<String,Set<String>> entry : stringListMap.entrySet()){
            String stationId = entry.getKey();
            for (String item : entry.getValue()){
                itemToStation.putIfAbsent(item,stationId);
            }
        }
        Map<String, Map<String, String>> labMigration = duccConfig.getLabMigration();
        Map<String,String> stationIdToAngel = Maps.newHashMap();
        Date appointmentStartTime = medicalPromiseDispatchContext.getAppointmentStartTime();
        Date appointmentEndTime = medicalPromiseDispatchContext.getAppointmentEndTime();
        AtomicReference<String> targetStation = new AtomicReference<>("");

        Map<String,List<MedPromiseDeliveryStep>> stationIdToStep = Maps.newHashMap();

        for (MedicalPromise medicalPromise : medicalPromises){
            String stationId = itemToStation.get(medicalPromise.getServiceItemId());



            //计算运力方案
            if (CollectionUtils.isNotEmpty(stationIdToStep.get(stationId))){
                medicalPromise.setDeliveryStepFlow(JsonUtil.toJSONString(stationIdToStep.get(stationId)));
                medicalPromise.setAngelStationId(stationIdToAngel.get(stationId));
            }else {

                List<MedPromiseDeliveryStep> medPromiseDeliverySteps = Lists.newArrayList();

                List<StationTransferContext> stationTransferContexts = stationIdToObj.get(stationId).getStationTransferContexts();
                StationTransferContext min = Collections.min(stationTransferContexts, Comparator.comparing(StationTransferContext::getDistance));


                //如果是无人机站点，则第一段叫运力去无人机，第二段无人机到实验室
                if (Objects.equals(min.getStationType(),CommonConstant.TWO)){


//                    StationTransferContext stationTransferContext = stationTransferContexts.stream().filter(p -> Objects.equals(1, p.getStationType())).findFirst().orElse(null);
//                    if (Objects.nonNull(stationTransferContext)){
                    MedPromiseDeliveryStep medPromiseDeliveryStep2 = new MedPromiseDeliveryStep();
                    medPromiseDeliveryStep2.setStartAddress(medicalPromiseDispatchContext.getStartAddress());
//                            medPromiseDeliveryStep2.setStartAddressId(medicalPromiseDispatchContext.getStartAddressId());
                    medPromiseDeliveryStep2.setEndAddress(min.getJdStationAddress());
                    //无人机到实验室，无人机运力
                    medPromiseDeliveryStep2.setDeliveryStepType(DeliveryStepTypeEnum.RIDER.getType());
//                    medPromiseDeliveryStep2.setThirdStationId(stationTransferContext.getThirdStationId());
//                    medPromiseDeliveryStep2.setThirdStationTargetId(stationTransferContext.getThirdStationTargetId());
                    medPromiseDeliveryStep2.setEndAddressId(String.valueOf(min.getJdStationId()));
                    medPromiseDeliveryStep2.setSort(CommonConstant.ONE);
                    medPromiseDeliverySteps.add(medPromiseDeliveryStep2);
//                    }

                    MedPromiseDeliveryStep medPromiseDeliveryStep = new MedPromiseDeliveryStep();
                    medPromiseDeliveryStep.setStartAddress(min.getJdStationAddress());
                    medPromiseDeliveryStep.setStartAddressId(String.valueOf(min.getJdStationId()));
                    medPromiseDeliveryStep.setEndAddress(stationIdToObj.get(stationId).getStationAddr());
                    medPromiseDeliveryStep.setEndAddressId(stationId);
                    //用户家到无人机站点，骑手运力
                    medPromiseDeliveryStep.setDeliveryStepType(DeliveryStepTypeEnum.UAV.getType());
                    medPromiseDeliveryStep.setThirdStationId(min.getThirdStationId());
                    medPromiseDeliveryStep.setThirdStationTargetId(min.getThirdStationTargetId());
                    medPromiseDeliveryStep.setSort(CommonConstant.TWO);
                    medPromiseDeliverySteps.add(medPromiseDeliveryStep);


                }else {
                    MedPromiseDeliveryStep medPromiseDeliveryStep = new MedPromiseDeliveryStep();
                    medPromiseDeliveryStep.setStartAddress(medicalPromiseDispatchContext.getStartAddress());
                    medPromiseDeliveryStep.setEndAddress(min.getJdStationAddress());
                    //用户家到无人机站点，骑手运力
                    medPromiseDeliveryStep.setDeliveryStepType(DeliveryStepTypeEnum.RIDER.getType());
                    medPromiseDeliveryStep.setThirdStationId(min.getThirdStationId());
                    medPromiseDeliveryStep.setThirdStationTargetId(min.getThirdStationTargetId());
                    medPromiseDeliveryStep.setEndAddressId(String.valueOf(min.getJdStationId()));
                    medPromiseDeliveryStep.setSort(CommonConstant.ONE);
                    medPromiseDeliverySteps.add(medPromiseDeliveryStep);
                }

                medicalPromise.setDeliveryStepFlow(JsonUtil.toJSONString(medPromiseDeliverySteps));
                stationIdToStep.put(stationId,medPromiseDeliverySteps);



                //计算服务站
                //接驳点对应服务站
                List<Long> angelStationIds = transferToAngelStationMap.get(min.getJdStationId());
                //自检测呼叫的是达达骑手，需要派服务站
                if (StringUtils.equals(medicalPromiseDispatchContext.getBusinessModeCode(),BusinessModeEnum.SELF_TEST.getCode())){
                    //派服务站
                    if (stationIdToAngel.containsKey(stationId)){
                        medicalPromise.setAngelStationId(stationIdToAngel.get(stationId));
                    }else {
                        List<SkuAngelStationDto> skuAngelStationDtos = stationToAngel.get(stationId);

                        //骑手
                        skuAngelStationDtos.removeIf(p -> !p.getHavePreemption() && Objects.isNull(p.getSkuInventoryDto()));
                        if (CollectionUtils.isEmpty(skuAngelStationDtos)) {
                            throw new BusinessException(MedPromiseErrorCode.MEDICAL_REDUCE_INVENTORY);
                        }
                        skuAngelStationDtos.removeIf(p ->
                                !angelStationIds.contains(p.getAngelStationId()) ||
                                        (!p.getHavePreemption() &&
                                        (Objects.isNull(p.getSkuInventoryDto().getInventoryNum()) || p.getSkuInventoryDto().getInventoryNum() <= 0)
                        ));
                        if (CollectionUtils.isEmpty(skuAngelStationDtos)) {
                            throw new BusinessException(MedPromiseErrorCode.MEDICAL_REDUCE_INVENTORY);
                        }

                        skuAngelStationDtos.sort(Comparator.comparing(p->p.getSkuInventoryDto().getInventoryNum()));
                        medicalPromise.setAngelStationId(String.valueOf(skuAngelStationDtos.get(skuAngelStationDtos.size()-1).getAngelStationId()));
                        stationIdToAngel.put(stationId,String.valueOf(skuAngelStationDtos.get(skuAngelStationDtos.size()-1).getAngelStationId()));
                    }


                }



            }

            log.info("disPatchStation->medicalPromiseDispatchContext,medicalPromise={}",JsonUtil.toJSONString(medicalPromise));


            if (labMigration.containsKey(medicalPromise.getStationId())){
                Map<String, String> stringStringMap = labMigration.get(medicalPromise.getStationId());
                stringStringMap.forEach((hours,sId)->{
                    String[] range = hours.split("~");
                    String startDateStr = range[0].trim();
                    String endDateStr = range[1].trim();

                    Date startDate = DateUtil.parse(startDateStr);
                    Date endDate = DateUtil.parse(endDateStr);
                    if (DateUtil.isIn(appointmentStartTime,startDate,endDate) && DateUtil.isIn(appointmentEndTime,startDate,endDate)){
                        targetStation.set(sId);
                    }
                });
            }
            String target = targetStation.get();
            //设置实验室具体信息
            packStationInfo(stationIdToObj, medicalPromise, stationId, target);

        }
        //如果需要占库存,此时是自检测，只会有一个实验室
        if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),medicalPromiseDispatchContext.getBusinessModeCode())){
            String angelStationId = Lists.newArrayList(stationIdToAngel.values()).get(0);
            ReduceInventoryCmd reduceInventoryCmd = new ReduceInventoryCmd();
            reduceInventoryCmd.setAngelStationId(Long.valueOf(angelStationId));
            reduceInventoryCmd.setPin(medicalPromises.get(0).getUserPin());
            reduceInventoryCmd.setBusinessId(String.valueOf(medicalPromiseDispatchContext.getOrderId()));
            reduceInventoryCmd.setBusinessType(CommonConstant.ONE);
            reduceInventoryCmd.setScheduleDay(medicalPromiseDispatchContext.getScheduleDay());
            reduceInventoryCmd.setScheduleTime(medicalPromiseDispatchContext.getBookTimeSpan());
            reduceInventoryCmd.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
            Boolean res = stationApplication.reduceInventory(reduceInventoryCmd);
            if (!res){
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_REDUCE_INVENTORY);
            }
        }


        //异步记录派发方案
        executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
            log.info("MedicalPromiseApplicationImpl->maps={}",JsonUtil.toJSONString(maps));
            List<String> serviceItemId = medicalPromises.stream().map(MedicalPromise::getServiceItemId).collect(Collectors.toList());
            saveDispatchPlans(serviceItemId, maps, medicalPromiseDispatchContext, stationIdToObj);
        });

    }





    /**
     * 获取context
     * @param medicalPromiseDispatchCmd
     * @param jdhPromise
     * @param medicalPromises
     * @param storeDispatchRules
     * @param jdhStationServiceItemRelContexts
     * @return
     */
    private  MedicalPromiseDispatchContext getMedicalPromiseDispatchContext(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd, PromiseDto jdhPromise, List<MedicalPromise> medicalPromises, List<StoreDispatchRule> storeDispatchRules, List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts) {
        MedicalPromiseDispatchContext medicalPromiseDispatchContext = new MedicalPromiseDispatchContext();
        medicalPromiseDispatchContext.setMedicalPromises(medicalPromises);
        medicalPromiseDispatchContext.setStoreDispatchRules(storeDispatchRules);
        medicalPromiseDispatchContext.setPromiseId(medicalPromiseDispatchCmd.getPromiseId());
        medicalPromiseDispatchContext.setServiceType(jdhPromise.getServiceType());
        medicalPromiseDispatchContext.setVerticalCode(jdhPromise.getVerticalCode());
        medicalPromiseDispatchContext.setBusinessModeCode(medicalPromiseDispatchCmd.getBusinessModeCode());
        medicalPromiseDispatchContext.setAppointmentStartTime(jdhPromise.getAppointmentTime().getAppointmentStartTime());
        medicalPromiseDispatchContext.setAppointmentEndTime(jdhPromise.getAppointmentTime().getAppointmentEndTime());
        medicalPromiseDispatchContext.setJdhStationServiceItemRelContexts(jdhStationServiceItemRelContexts);
        medicalPromiseDispatchContext.setOrderId(Long.valueOf(jdhPromise.getSourceVoucherId()));

        medicalPromiseDispatchContext.setScheduleDay(medicalPromiseDispatchCmd.getScheduleDay());
        medicalPromiseDispatchContext.setBookTimeSpan(medicalPromiseDispatchCmd.getBookTimeSpan());
        medicalPromiseDispatchContext.setStartAddress(medicalPromiseDispatchCmd.getStartAddress());

        return medicalPromiseDispatchContext;
    }

    /**
     * 设置实验室具体信息
     * @param stationIdToObj
     * @param medicalPromise
     * @param stationId
     * @param target
     */
    private void packStationInfo(Map<String, JdhStationServiceItemRelContext> stationIdToObj, MedicalPromise medicalPromise, String stationId, String target) {
        if (StringUtils.isNotBlank(target) && !StringUtils.equals(stationId, target)){
            //根据stationId查询相关信息
            if (stationIdToObj.containsKey(target)){
                JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(stationId);
                medicalPromise.setProviderId(jdhStationServiceItemRelContext.getChannelNo());
                medicalPromise.setStationId(jdhStationServiceItemRelContext.getStationId());
                medicalPromise.setStationAddress(jdhStationServiceItemRelContext.getStationAddr());
                medicalPromise.setStationName(jdhStationServiceItemRelContext.getStationName());
                medicalPromise.setStationPhone(jdhStationServiceItemRelContext.getStationPhone());
            }else {
                StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
                storeInfoRequest.setStationId(target);
                StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(storeInfoRequest);
                medicalPromise.setProviderId(storeInfoDto.getProviderId());
                medicalPromise.setStationId(storeInfoDto.getStationId());
                medicalPromise.setStationAddress(storeInfoDto.getStationAddress());
                medicalPromise.setStationName(storeInfoDto.getStationName());
                medicalPromise.setStationPhone(storeInfoDto.getStationPhone());
                JdhStationServiceItemRelContext itemRelContext = new JdhStationServiceItemRelContext();
                itemRelContext.setChannelNo(storeInfoDto.getProviderId());
                itemRelContext.setStationId(storeInfoDto.getStationId());
                itemRelContext.setStationAddr(storeInfoDto.getStationAddress());
                itemRelContext.setStationName(storeInfoDto.getStationName());
                itemRelContext.setStationPhone(storeInfoDto.getStationPhone());
                stationIdToObj.put(target,itemRelContext);
            }
        }else {
            JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(stationId);
            medicalPromise.setProviderId(jdhStationServiceItemRelContext.getChannelNo());
            medicalPromise.setStationId(jdhStationServiceItemRelContext.getStationId());
            medicalPromise.setStationAddress(jdhStationServiceItemRelContext.getStationAddr());
            medicalPromise.setStationName(jdhStationServiceItemRelContext.getStationName());
            medicalPromise.setStationPhone(jdhStationServiceItemRelContext.getStationPhone());
        }
    }
    /**
     * 合管
     * @param medicalPromises
     * @param mergeMedicalPromiseConfig
     */
    private void merge(List<MedicalPromise> medicalPromises, Map<String, List<String>> mergeMedicalPromiseConfig) {
        Set<Long> mergeItemIds = mergeMedicalPromiseConfig.keySet().stream().map(Long::valueOf).collect(Collectors.toSet());
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(mergeItemIds).build());
        Map<Long, String> itemIdToName = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serviceItemDtos)){
            itemIdToName = serviceItemDtos.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, ServiceItemDto::getItemName));
        }

        // 按照value的size进行排序
        LinkedHashMap<String, List<String>> sortedMap = mergeMedicalPromiseConfig.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.comparingInt(list -> -list.size())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        //以人分组
        Map<Long, List<MedicalPromise>> pidToList = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        //遍历
        for (Map.Entry<Long,List<MedicalPromise>> entry : pidToList.entrySet()){
            List<MedicalPromise> medicalPromiseList =entry.getValue();
            //以station分组
            Map<String, List<MedicalPromise>> stationToList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromise::getStationId));
            Map<Long, String> finalItemIdToName = itemIdToName;
            stationToList.forEach((stationId, mps)->{
                //合管
                List<String> serviceItemList = mps.stream().map(MedicalPromise::getServiceItemId).collect(Collectors.toList());
                Map<String, List<String>> mergeMedicalPromiseBestConfig = getMergeMedicalPromiseBestConfig(sortedMap, serviceItemList);
                if (MapUtils.isNotEmpty(mergeMedicalPromiseBestConfig)){
                    mergeMedicalPromiseBestConfig.forEach((k,v)->{
                        Long mergeId = generateIdFactory.getId();
                        MedicalPromise medicalPromise = mps.get(0);
                        Set<String> mpSet = mps.stream().map(MedicalPromise::getServiceId).collect(Collectors.toSet());
                        VoucherDto jdhVoucher = voucherApplication.findByVoucherId(VoucherIdRequest.builder().voucherId(medicalPromise.getVoucherId()).build());
                        JdOrder order = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(jdhVoucher.getSourceVoucherId()));
                        List<JdOrderItem> jdOrderItemList = order.getJdOrderItemList();
                        JdOrderItem jdOrderItem = jdOrderItemList.stream().filter(p -> mpSet.contains(String.valueOf(p.getSkuId())) && Objects.equals(CommonConstant.ZERO,p.getIsAdded())).findFirst().orElse(null);

                        List<Long> mergeMpIds = mps.stream().filter(p -> v.contains(p.getServiceItemId())).map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toList());
                        medicalPromiseRepository.deleteByMedicalPromiseIds(mergeMpIds,mergeId);
                        medicalPromises.removeIf(p->mergeMpIds.contains(p.getMedicalPromiseId()));
                        MedicalPromise transMergeMp = new MedicalPromise();
                        transMergeMp.setPromiseId(medicalPromise.getPromiseId());
                        transMergeMp.setPromisePatientId(medicalPromise.getPromisePatientId());
                        transMergeMp.setStatus(medicalPromise.getStatus());
                        transMergeMp.setAngelStationId(medicalPromise.getAngelStationId());
                        transMergeMp.setProviderId(medicalPromise.getProviderId());
                        transMergeMp.setStationId(medicalPromise.getStationId());
                        transMergeMp.setStationName(medicalPromise.getStationName());
                        transMergeMp.setStationAddress(medicalPromise.getStationAddress());
                        transMergeMp.setStationPhone(medicalPromise.getStationPhone());
                        transMergeMp.setServiceItemName(finalItemIdToName.get(Long.valueOf(k)));
                        transMergeMp.setServiceItemId(k);
                        transMergeMp.setMedicalPromiseId(mergeId);
                        transMergeMp.setVerticalCode(medicalPromise.getVerticalCode());
                        transMergeMp.setUserPin(medicalPromise.getUserPin());
                        transMergeMp.setServiceType(medicalPromise.getServiceType());
                        transMergeMp.setVoucherId(medicalPromise.getVoucherId());
                        transMergeMp.setDeliveryStepFlow(medicalPromise.getDeliveryStepFlow());
                        if (Objects.nonNull(jdOrderItem)){
                            transMergeMp.setServiceId(String.valueOf(jdOrderItem.getSkuId()));
                        }else {
                            transMergeMp.setServiceId(medicalPromise.getServiceId());
                        }
                        //加项订单
                        transMergeMp.setFlag(MedicalPromiseFlagEnum.ADD_SERVICE_ITEM.getFlag());
                        medicalPromises.add(transMergeMp);
                    });
                }
            });
        }
    }

    /**
     * 保存检测单派发历史
     * @param medicalPromise
     */
    private void saveMedPromiseHistory(MedicalPromise medicalPromise){
        MedPromiseHistory medPromiseHistory = new MedPromiseHistory();
        medPromiseHistory.setEventCode("暂无");
        medPromiseHistory.setEventDesc("暂无");
        medPromiseHistory.setAfterStatus(medicalPromise.getStatus());
        medPromiseHistory.setAfterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
        medPromiseHistory.setBeforeStatus(medicalPromise.getStatus());
        medPromiseHistory.setBeforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
        medPromiseHistory.setProviderId(medicalPromise.getProviderId());
        medPromiseHistory.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medPromiseHistory.setExtend(JsonUtil.toJSONString(medicalPromise));
        medPromiseHistory.setVersion(medicalPromise.getVersion());
        medPromiseHistory.setDesc("分派实验室");
        medPromiseHistory.setOperator("storeDisPatch");

        medPromiseHistoryRepository.save(medPromiseHistory);
    }
    /**
     * 记录选中方案
     * @param serviceItemId
     * @param maps
     * @param medicalPromiseDispatchContext
     * @param stationIdToObj
     */
    private  void saveDispatchPlans(List<String> serviceItemId , List<Map<String, Set<String>>> maps, MedicalPromiseDispatchContext medicalPromiseDispatchContext, Map<String, JdhStationServiceItemRelContext> stationIdToObj) {
        List<StationDispatchPlanCmd> stationDispatchPlanCmds = Lists.newArrayList();

        log.info("MedicalPromiseApplicationImpl->saveDispatchPlans,medicalPromiseDispatchContext={}",JsonUtil.toJSONString(medicalPromiseDispatchContext));

        // 如果只有一个方案，则直接返回
        Map<String, Set<String>> selectPlan = maps.get(0);
        StationDispatchPlanCmd stationDispatchPlanCmd = new StationDispatchPlanCmd();
        stationDispatchPlanCmd.setPromiseId(medicalPromiseDispatchContext.getPromiseId());
        stationDispatchPlanCmd.setPlanId(1);
        stationDispatchPlanCmd.setSelected(CommonConstant.ONE);
        List<PlanDetailCmd> planDetailCmds = Lists.newArrayList();
        selectPlan.forEach((stationId,serviceitemSet)->{
            PlanDetailCmd planDetailCmd = new PlanDetailCmd();
            planDetailCmd.setStartAddress(medicalPromiseDispatchContext.getStartAddress());
            JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(stationId);
            planDetailCmd.setStationName(jdhStationServiceItemRelContext.getStationName());
            planDetailCmd.setStationAddress(jdhStationServiceItemRelContext.getStationAddr());
            //结算价
            //实验室可检测项目
            List<JdhServiceItemContext> jdhServiceItemContexts = jdhStationServiceItemRelContext.getJdhServiceItemContexts();
            Map<String, BigDecimal> serviceItemToSettle = jdhServiceItemContexts.stream().collect(Collectors.toMap(JdhServiceItemContext::getServiceItemId, JdhServiceItemContext::getSettlementPrice));
            BigDecimal settleAmount = new BigDecimal(CommonConstant.ZERO);
            for (String medicalPromise : serviceItemId) {
                if (serviceitemSet.contains(medicalPromise)){
                    settleAmount = settleAmount.add(serviceItemToSettle.get(medicalPromise));
                }
            }
            planDetailCmd.setSettlementAmount(settleAmount);
            //距离
            StationTransferContext min = Collections.min(jdhStationServiceItemRelContext.getStationTransferContexts(), Comparator.comparing(StationTransferContext::getDistance));;
            planDetailCmd.setDistance(min.getDistance().setScale(2, RoundingMode.HALF_UP).toPlainString());
            planDetailCmd.setDetail(JsonUtil.toJSONString(serviceitemSet));
            planDetailCmds.add(planDetailCmd);

        });

        stationDispatchPlanCmd.setPlanDetailCmds(planDetailCmds);
        stationDispatchPlanCmds.add(stationDispatchPlanCmd);
        //保存方案
        medPromiseExtApplication.batchSaveDispatchPlan(stationDispatchPlanCmds);

    }

    /**
     * 获取最佳合管配置
     * @return
     */
    private Map<String,List<String>> getMergeMedicalPromiseBestConfig(LinkedHashMap<String, List<String>> configs,List<String> serviceItemList){
        Map<String,List<String>> res = Maps.newHashMap();
        Set<String> serviceItemSet = new HashSet<>(serviceItemList);
        for (Map.Entry<String,List<String>> entry : configs.entrySet()){
            String key = entry.getKey();
            List<String> value = entry.getValue();
            Set<String> configSet = new HashSet<>(value);
            HashSet<String> resSet = new HashSet<>(serviceItemSet);
            resSet.retainAll(configSet);
            if (resSet.size() == configSet.size()){
                res.put(key,value);
                break;
            }
        }
        return res;
    }

    /**
     * 检查调度参数
     * @param medicalPromiseDispatchCmd 医疗承诺调度命令
     * @throws MedPromiseErrorCode 当承诺ID参数为空时抛出承诺ID参数为空错误
     */
    private void checkDispatchParam(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        AssertUtils.nonNull(medicalPromiseDispatchCmd.getPromiseId(),MedPromiseErrorCode.PROMISE_ID_PARAM_NULL);
        if (Objects.isNull(medicalPromiseDispatchCmd.getLatitude()) || Objects.isNull(medicalPromiseDispatchCmd.getLongitude())){
            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(medicalPromiseDispatchCmd.getStartAddress());
            medicalPromiseDispatchCmd.setLatitude(lngLatByAddress.getLatitude());
            medicalPromiseDispatchCmd.setLongitude(lngLatByAddress.getLongitude());
        }
    }

    /**
     * 根据指标DTO获取CT值的范围和百分比。
     * @param indicatorDTO StructQuickReportResultIndicatorDTO对象，包含指标的正常范围值、浓度公式和CT值。
     * @return RangeValueDTO对象，包含范围最小值、范围最大值、实际值和百分比。
     */
    @Override
    public RangeValueDTO getCtRangeValue(StructQuickReportResultIndicatorDTO indicatorDTO) {
        String[] split = indicatorDTO.getReferenceRangeValue().split("或");
        String spiltNormalRange = split.length>1 ? split[1] : split[0];
        Pattern pattern = Pattern.compile("(?i)\\bct\\s*>\\s*([0-9.]+)|\\b>\\s*([0-9.]+)|\\bct\\s*值\\s*>\\s*([0-9.]+)|\\b([0-9.]+)\\b");
        Matcher matcher = pattern.matcher(spiltNormalRange);
        String rangeMin = null;
        if (matcher.find()){
            for (int i = 1; i <= matcher.groupCount(); i++) {
                String numStr = matcher.group(i);
                if (numStr != null) {
                    rangeMin = numStr;
                    break;
                }
            }
        }
        if (StringUtils.isBlank(rangeMin) ){
            indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType());
            return null;
        }

        if (StringUtils.isBlank(indicatorDTO.getIndicatorConcentration())
                || StringUtils.isBlank(indicatorDTO.getConcentrationFormula())
        ){
            return null;
        }

        BigDecimal min = new BigDecimal("0");
        BigDecimal max = new BigDecimal(rangeMin);


        RangeValueDTO rangeValueDTO = new RangeValueDTO();
        rangeValueDTO.setRangeMin(min.toPlainString());
        rangeValueDTO.setRangeMax(max.toPlainString());
        rangeValueDTO.setValue(indicatorDTO.getIndicatorConcentration());
        String plainString = max.subtract(new BigDecimal(indicatorDTO.getCtValue())).divide(max.subtract(min), 2, RoundingMode.HALF_UP).toPlainString();
        indicatorDTO.setPercentage(plainString);
        return rangeValueDTO;
    }

    /**
     * @param indicatorDTO
     * @param expression
     * @return
     */
    @Override
    public List<RangeValueDTO> packNumberRangeValue(StructQuickReportResultIndicatorDTO indicatorDTO, String expression) {
        List<RangeValueDTO> rangeValueDTOS = com.google.common.collect.Lists.newArrayList();
        Pattern pattern1 = Pattern.compile("^([0-9]*\\.?[0-9]+)~([0-9]+\\.?[0-9]*)$");
        Pattern pattern2 = Pattern.compile("^([0-9]*\\.?[0-9]+)--([0-9]+\\.?[0-9]*)$");
        Pattern pattern3 = Pattern.compile("^([0-9]*\\.?[0-9]+)-([0-9]+\\.?[0-9]*)$");
        Pattern pattern4 = Pattern.compile("^>([0-9]+\\.?[0-9]*)$");
        Pattern pattern5 = Pattern.compile("^<([0-9]+\\.?[0-9]*)$");
        Pattern pattern6 = Pattern.compile("^≤([0-9]+\\.?[0-9]*)$");


        Matcher matcher1 = pattern1.matcher(expression);
        Matcher matcher2 = pattern2.matcher(expression);
        Matcher matcher3 = pattern3.matcher(expression);
        Matcher matcher4 = pattern4.matcher(expression);
        Matcher matcher5 = pattern5.matcher(expression);
        Matcher matcher6 = pattern6.matcher(expression);

        if (matcher1.matches() || matcher2.matches() || matcher3.matches()) {
            String a = matcher1.matches() ? matcher1.group(1) : (matcher2.matches() ? matcher2.group(1) : matcher3.group(1));
            String b = matcher1.matches() ? matcher1.group(2) : (matcher2.matches() ? matcher2.group(2) : matcher3.group(2));
            if (NumberUtils.isZero(a)) {
                rangeValueDTOS.add(packRangeValueDTO("0",b,"正常", indicatorDTO.getValue()));
                rangeValueDTOS.add(packRangeValueDTO(b,"","异常", indicatorDTO.getValue()));
//                return "0~" + b + " 正常;" + b + " 异常";
            } else {
                rangeValueDTOS.add(packRangeValueDTO("0",a,"偏低", indicatorDTO.getValue()));
                rangeValueDTOS.add(packRangeValueDTO(a,b,"正常", indicatorDTO.getValue()));
                rangeValueDTOS.add(packRangeValueDTO(b,"","异常", indicatorDTO.getValue()));
//                return "0~" + a + " 偏低;" + a + "~" + b + " 正常;" + b + " 偏高";
            }
        } else if (matcher4.matches()) {
            String a = matcher4.group(1);
            rangeValueDTOS.add(packRangeValueDTO("0",a,"异常", indicatorDTO.getValue()));
            rangeValueDTOS.add(packRangeValueDTO(a,"","正常", indicatorDTO.getValue()));
//            return "0~" + a + " 异常;" + a + " 正常";
        } else if (matcher5.matches()) {
            String a = matcher5.group(1);
            rangeValueDTOS.add(packRangeValueDTO("0",a,"正常", indicatorDTO.getValue()));
            rangeValueDTOS.add(packRangeValueDTO(a,"","异常", indicatorDTO.getValue()));
//            return "0~" + a + " 正常;" + a + " 异常";
        }else if (matcher6.matches()) {
            String a = matcher6.group(1);
            rangeValueDTOS.add(packRangeValueDTO("0",a,"正常", indicatorDTO.getValue()));
            rangeValueDTOS.add(packRangeValueDTO(a,"","异常", indicatorDTO.getValue()));
//            return "0~" + a + " 正常;" + a + " 异常";
        }
        return rangeValueDTOS;
    }

    /**
     * 将指定的最小值、最大值和标签打包成一个RangeValueDTO对象。
     * @param min 最小值
     * @param max 最大值
     * @param label 标签
     * @return 打包后的RangeValueDTO对象
     */
    private RangeValueDTO packRangeValueDTO(String min,String max,String label,String value) {
        RangeValueDTO rangeValueDTO = new RangeValueDTO();
        rangeValueDTO.setRangeMin(min);
        rangeValueDTO.setRangeMax(max);
        rangeValueDTO.setRangeLabel(label);
        if (new BigDecimal(value).compareTo(new BigDecimal(min)) >= 0) {
            if (StringUtils.isBlank(max) || new BigDecimal(value).compareTo(new BigDecimal(max)) <= 0) {
                rangeValueDTO.setValue(value);
            }
        }
        return rangeValueDTO;
    }


}
