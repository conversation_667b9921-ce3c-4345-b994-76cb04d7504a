package com.jdh.o2oservice.application.angel.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.angel.convert.StationApplicationConverter;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.core.domain.angel.context.*;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.event.AngelStationEventBody;
import com.jdh.o2oservice.core.domain.angel.event.AngelStationInventoryEventBody;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.cmd.AngelStationModifyCmd;
import com.jdh.o2oservice.core.domain.angel.repository.db.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhStationAngelManQuery;
import com.jdh.o2oservice.core.domain.angel.service.AngelStationInventoryDomainService;
import com.jdh.o2oservice.core.domain.angel.service.JdhMapDomainService;
import com.jdh.o2oservice.core.domain.angel.service.NurseStationInventoryDomainService;
import com.jdh.o2oservice.core.domain.angel.vo.AngelJobTimeIntervalVo;
import com.jdh.o2oservice.core.domain.product.enums.ProductEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.domain.angel.core.ext.dto.ThirdStoreInfoDto;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName:AngelStationEventConsumer
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/4 12:35
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelStationEventConsumer {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private JdhStationRepository jdhStationRepository;

    @Resource
    private JdhStationSkuManRepository jdhStationSkuManRepository;

    @Resource
    private JdhStationAngelManRepository jdhStationAngelManRepository;

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private ProductApplication productApplication;

    @Resource
    private JdhStationSkuRelRepository jdhStationSkuRelRepository;

    @Resource
    private JdhMapDomainService jdhMapDomainService;

    @Resource
    private AngelStationInventoryDomainService angelStationInventoryDomainService;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    @Resource
    private NurseStationInventoryDomainService nurseStationInventoryDomainService;

    @PostConstruct
    public void registerEventConsumer(){
        eventConsumerRegister.register(AngelStationEventTypeEnum.ANGEL_STATION_MODIFY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelStationModify", this::modifyAngelStation, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelStationEventTypeEnum.ANGEL_STATION_ADD,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelStationInventoryAdd", this::addAngelStationInventory, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelStationEventTypeEnum.ANGEL_STATION_ADD,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "stationAdd", this::addAngelStation, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelStationEventTypeEnum.ANGEL_STATION_MODIFY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelStationUpdate", this::updateAngelStation, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_BASE_MODIFY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelModifyForStation", this::angelModifyForStation, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(ProductEventTypeEnum.UPDATE_SALE_STATUS,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "skuModifyStatusForStation", this::skuModifyStatusForStation, Boolean.TRUE, Boolean.FALSE));
        /**
         * 消费:服务站库存已用完
         */
        eventConsumerRegister.register(AngelStationEventTypeEnum.ANGEL_STATION_INVENTORY_IS_OUT,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelStationInventoryIsOut", this::sendDongDong, Boolean.TRUE, Boolean.FALSE));
    }

    /**
     * 新增服务站库存
     *
     * @param event
     */
    private void addAngelStationInventory(Event event) {
        try{
            angelStationInventoryAdd(event);
            angelStationNurseInventoryAdd(event);
        }catch (Exception ex) {
            log.error("AngelStationEventConsumer -> addAngelStationInventory, 新增服务站库存失败", ex);
            UmpUtil.showWarnMsg(UmpKeyEnum.STATION_INVENTORY_ADD_ERROR,"服务站库存修改失败");

            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("库存不一致预警");

            AngelStationEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationEventBody.class);
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("【库存】服务站信息变更库存处理失败，服务站ID：%s，请关注",
                            eventBody.getStationId()),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
    }


    /**
     * 新增服务站
     *
     * @param event
     */
    private void angelStationInventoryAdd(Event event) {
        log.info("[AngelStationEventConsumer->angelStationInventoryAdd],新建服务站，初始化服务站库存开始!event={}", JSON.toJSONString(event));
        AngelStationEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationEventBody.class);
        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(eventBody.getStationId()).build());

        if(Objects.isNull(jdhStation)) {
            log.error("[AngelStationEventConsumer->angelStationInventoryAdd],服务站信息不存在!");
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }

        if(!Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())) {
            log.error("[AngelStationEventConsumer->angelStationInventoryAdd],非兼职服务站不需要处理!");
            return;
        }

        List<AngelJobTimeIntervalVo> jobTimeIntervalVoList = jdhStation.splitScheduleSlot(duccConfig.getScheduleTimeSlice(), TimeUtils.getCurrentLocalDate(), duccConfig.getScheduleTimeSlice().getInitDays());
        if(CollectionUtils.isEmpty(jobTimeIntervalVoList)){
            log.error("[AngelStationEventConsumer->angelStationInventoryAdd],没有服务站工作时间信息!jdhStation={}", JSON.toJSONString(jdhStation));
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_EMPTY_ERROR);
        }

        List<JdhInventorySaveContext> inventorySaveContextList = Lists.newArrayList();
        jobTimeIntervalVoList.stream().forEach(vo -> {
            JdhInventorySaveContext context = new JdhInventorySaveContext();
            context.setAngelStationId(jdhStation.getAngelStationId());
            context.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
            context.setScheduleDay(vo.getScheduleDay());
            context.setScheduleTime(vo.getScheduleTime());
            context.setBaseNum(vo.getBaseNum());
            context.setOperator(eventBody.getOperator());
            inventorySaveContextList.add(context);
        });

        angelStationInventoryDomainService.saveJdhInventory(inventorySaveContextList, eventBody.getJdhInventoryReadjustContextList(), true);
    }


    /**
     * 新增服务站
     *
     * @param event
     */
    private void angelStationNurseInventoryAdd(Event event) {
        log.info("[AngelStationEventConsumer->angelStationNurseInventoryAdd],新建服务站，初始化服务站护士库存开始!event={}", JSON.toJSONString(event));
        AngelStationEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationEventBody.class);
        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(eventBody.getStationId()).build());

        if(Objects.isNull(jdhStation)) {
            log.error("[AngelStationEventConsumer->angelStationNurseInventoryAdd],服务站信息不存在!");
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }

        if(!Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())) {
            log.error("[AngelStationEventConsumer->angelStationNurseInventoryAdd],非兼职服务站不需要处理!");
            return;
        }

        List<AngelJobTimeIntervalVo> jobTimeIntervalVoList = jdhStation.splitScheduleSlot(duccConfig.getScheduleTimeSlice(), TimeUtils.getCurrentLocalDate(), duccConfig.getScheduleTimeSlice().getInitDays());
        if(CollectionUtils.isEmpty(jobTimeIntervalVoList)){
            log.error("[AngelStationEventConsumer->angelStationNurseInventoryAdd],没有服务站工作时间信息!jdhStation={}", JSON.toJSONString(jdhStation));
            throw new BusinessException(AngelErrorCode.ANGEL_INVENTORY_EMPTY_ERROR);
        }

        List<JdhInventorySaveContext> inventorySaveContextList = Lists.newArrayList();
        jobTimeIntervalVoList.stream().forEach(vo -> {
            JdhInventorySaveContext nurseContext = new JdhInventorySaveContext();
            nurseContext.setAngelStationId(jdhStation.getAngelStationId());
            nurseContext.setInventoryChannelNo(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel());
            nurseContext.setScheduleDay(vo.getScheduleDay());
            nurseContext.setScheduleTime(vo.getScheduleTime());
            nurseContext.setBaseNum(vo.getNurseBaseNum());
            nurseContext.setOperator(eventBody.getOperator());
            inventorySaveContextList.add(nurseContext);
        });

        angelStationInventoryDomainService.saveJdhNurseInventory(inventorySaveContextList, eventBody.getJdhNurseInventoryReadjustContextList(), true);
    }

    /**
     * 商品信息变更同步服务站
     *
     * @param event
     */
    private void skuModifyStatusForStation(Event event) {
        log.info("[JdhStationAngelManRepositoryImpl.skuModifyStatusForStation],商品信息变更开始处理!event={}", JSON.toJSONString(event));
        String skuId = event.getAggregateId();
        if(StringUtils.isBlank(skuId)){
            log.error("[JdhStationAngelManRepositoryImpl.skuModifyStatusForStation],商品信息聚合主键为空!");
            throw new BusinessException(AngelErrorCode.SKU_NAME_IS_EMPTY);
        }
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(Long.valueOf(skuId));
        JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);
        if(Objects.isNull(jdhSkuDto)){
            log.error("[JdhStationAngelManRepositoryImpl.skuModifyStatusForStation],商品信息聚合主键为空!");
            throw new BusinessException(AngelErrorCode.SKU_NAME_IS_EMPTY);
        }

        JdhStationSkuMan jdhStationSkuMan = JdhStationSkuMan.builder()
                .saleStatus(jdhSkuDto.getSaleStatus())
                .skuId(jdhSkuDto.getSkuId())
                .build();
        int modifyNum = jdhStationSkuManRepository.updateSkuSaleStatus(jdhStationSkuMan);

        JdhStationSkuRel jdhStationSkuRel = JdhStationSkuRel.builder()
                .saleStatus(jdhSkuDto.getSaleStatus())
                .skuId(jdhSkuDto.getSkuId())
                .build();
        int skuRelModifyNum = jdhStationSkuRelRepository.updateSkuSaleStatus(jdhStationSkuRel);
        log.info("[JdhStationAngelManRepositoryImpl.skuModifyStatusForStation],更新服务站商品运营数据{}条.服务站商品关联关系更新{}条", modifyNum, skuRelModifyNum);
    }

    /**
     * 服务者履约修改服务者信息
     *
     * @param event
     */
    private void angelModifyForStation(Event event) {
        log.info("[JdhStationAngelManRepositoryImpl.angelModifyForStation],服务者信息变更开始处理!event={}", JSON.toJSONString(event));
        JdhAngel jdhAngel = angelRepository.find(new JdhAngelIdentifier(Long.valueOf(event.getAggregateId())));
        if(Objects.isNull(jdhAngel)){
            log.error("[JdhStationAngelManRepositoryImpl.angelModifyForStation], 没有查询到对应的服务者信息!");
            return;
        }

        JdhStationAngelManQuery manQuery = new JdhStationAngelManQuery();
        manQuery.setAngelIdSet(Sets.newHashSet(jdhAngel.getAngelId()));
        List<JdhStationAngelMan> angelManList = jdhStationAngelManRepository.findList(manQuery);

        if(CollectionUtils.isEmpty(angelManList)){
            log.error("[JdhStationAngelManRepositoryImpl.angelModifyForStation], 服务者未绑定服务者!");
            return;
        }

        angelManList.forEach(angel -> {
            angel.setAngelName(angel.getAngelName());
            angel.setPhone(angel.getPhone());
            angel.setPhoneIndex(angel.getPhone());
            angel.setIdCard(angel.getIdCard());
            angel.setIdCardIndex(angel.getIdCard());
        });

        jdhStationAngelManRepository.batchUpdateAngel(angelManList);
    }

    /**
     * 服务站信息变更
     *
     * @param event
     */
    private void modifyAngelStation(Event event) {
        log.info("[JdhStationAngelManRepositoryImpl.batchUpdateByStation],服务站信息变更时间开始处理!event={}", JSON.toJSONString(event));
        String aggregateId = event.getAggregateId();
        if(StringUtils.isBlank(aggregateId)){
            log.error("[AngelStationEventConsumer.modifyAngelStation],服务站信息变更事件,未查询到服务站!");
            throw new BusinessException(AngelErrorCode.ANGEL_STATION_NOT_EXIST);
        }

        AngelStationEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationEventBody.class);
        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(Long.valueOf(aggregateId)).build());
        if(!AngelStationModeTypeEnum.FULL_TIME.getCode().equals(jdhStation.getStationModeType())){
            log.error("[AngelStationEventConsumer.modifyAngelStation],非全职服务站不处理!");
            return;
        }
        try{

            //更新服务站骑手库存基础数量
            if(eventBody.inventoryBaseNumChange() && Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())){
                log.info("[更新服务站库存数量]");
                JdhInventorySaveContext jdhInventorySaveContext = new JdhInventorySaveContext();
                jdhInventorySaveContext.setOperator(eventBody.getOperator());
                jdhInventorySaveContext.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
                jdhInventorySaveContext.setBaseNum(jdhStation.getAngelNum());
                jdhInventorySaveContext.setAngelStationId(jdhStation.getAngelStationId());
                try{
                    angelStationInventoryDomainService.updateAngelStationBaseNum(jdhInventorySaveContext, jdhStation);
                }catch (BusinessException ex) {
                    log.error("AngelStationEventConsumer -> modifyAngelStation,修改服务站骑手库存数量异常.", ex);
                    throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
                }
            }

            //更新服务站护士库存基础数量
            if(eventBody.inventoryNurseBaseNumChange() && Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())){
                log.info("[更新服务站护士库存数量]");
                JdhInventorySaveContext jdhInventorySaveContext = new JdhInventorySaveContext();
                jdhInventorySaveContext.setOperator(eventBody.getOperator());
                jdhInventorySaveContext.setInventoryChannelNo(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel());
                jdhInventorySaveContext.setBaseNum(jdhStation.getNurseNum());
                jdhInventorySaveContext.setAngelStationId(jdhStation.getAngelStationId());
                try{
                    angelStationInventoryDomainService.updateAngelStationBaseNum(jdhInventorySaveContext, jdhStation);
                }catch (BusinessException ex) {
                    log.error("AngelStationEventConsumer -> modifyAngelStation,修改服务站护士库存数量异常.", ex);
                    throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
                }
            }

            //更新服务站骑手异常调仓数量
            if(eventBody.inventoryReadjustChange() && Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())){
                log.info("[更新服务站异常调仓]");
                List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList = eventBody.getJdhInventoryReadjustContextList();
                List<Long> timeAndNumModifyReadjustIdList = eventBody.getTimeAndNumModifyReadjustIdList();
                List<Long> timeModifyReadjustIdList = eventBody.getTimeModifyReadjustIdList();
                List<Long> numModifyReadjustIdList = eventBody.getNumModifyReadjustIdList();
                List<Long> removeReadjustIdList = eventBody.getRemoveReadjustIdList();
                angelStationInventoryDomainService.saveAngelStationTimeNumReadjust(jdhInventoryReadjustContextList, timeAndNumModifyReadjustIdList, removeReadjustIdList);
                angelStationInventoryDomainService.saveAngelStationTimeReadjust(jdhInventoryReadjustContextList, timeModifyReadjustIdList, removeReadjustIdList);
                angelStationInventoryDomainService.saveAngelStationNumReadjust(jdhInventoryReadjustContextList, numModifyReadjustIdList);
                angelStationInventoryDomainService.removeAngelStationReadjust(jdhInventoryReadjustContextList, removeReadjustIdList);
                angelStationInventoryDomainService.saveAngelStationReadjust(jdhInventoryReadjustContextList);
            }

            //更新服务站护士异常调仓数量
            if(eventBody.inventoryNurseReadjustChange() && Objects.equals(AngelStationModeTypeEnum.FULL_TIME.getCode(), jdhStation.getStationModeType())){
                log.info("[更新服务站护士异常调仓]");
                List<JdhInventoryReadjustContext> jdhInventoryReadjustContextList = eventBody.getJdhNurseInventoryReadjustContextList();
                List<Long> timeAndNumModifyReadjustIdList = eventBody.getNurseTimeAndNumModifyReadjustIdList();
                List<Long> timeModifyReadjustIdList = eventBody.getNurseTimeModifyReadjustIdList();
                List<Long> numModifyReadjustIdList = eventBody.getNurseNumModifyReadjustIdList();
                List<Long> removeReadjustIdList = eventBody.getNurseRemoveReadjustIdList();
                nurseStationInventoryDomainService.saveAngelStationNurseTimeNumReadjust(jdhInventoryReadjustContextList, timeAndNumModifyReadjustIdList, removeReadjustIdList);
                nurseStationInventoryDomainService.saveAngelStationNurseTimeReadjust(jdhInventoryReadjustContextList, timeModifyReadjustIdList, removeReadjustIdList);
                nurseStationInventoryDomainService.saveAngelStationNurseNumReadjust(jdhInventoryReadjustContextList, numModifyReadjustIdList);
                nurseStationInventoryDomainService.removeAngelStationNurseReadjust(jdhInventoryReadjustContextList, removeReadjustIdList);
                nurseStationInventoryDomainService.saveAngelStationNurseReadjust(jdhInventoryReadjustContextList);
            }

            //更新服务站商品man信息和服务站资源man信息
            AngelStationModifyCmd angelStationModifyCmd = StationApplicationConverter.ins.convertToAngelStationModifyCmd(jdhStation);
            jdhStationSkuManRepository.batchUpdateByStation(angelStationModifyCmd);
            jdhStationAngelManRepository.batchUpdateByStation(angelStationModifyCmd);
        }catch (Throwable t) {
            log.error("JdhStationAngelManRepositoryImpl -> batchUpdateByStation, 处理服务站库存失败", t);
            UmpUtil.showWarnMsg(UmpKeyEnum.STATION_INVENTORY_MODIFY_ERROR,"服务站库存修改失败");

            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("库存不一致预警");

            dongDongRobotRpc.sendDongDongRobotMessage(String.format("【库存】服务站信息变更库存处理失败，服务站ID：%s，服务站名称：%s，请关注",
                            jdhStation.getAngelStationId(), jdhStation.getAngelStationName()),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
    }

    /**
     * 服务站-新增
     *
     * @param event
     */
    private void addAngelStation(Event event) {
        log.info("[AngelStationEventConsumer.addAngelStation],添加服务站事件开始处理!event={}", JSON.toJSONString(event));
        // 判断服务资源类型是否选择选中骑手
        AngelStationEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationEventBody.class);
        if(!AngelStationModeTypeEnum.FULL_TIME.getCode().equals(eventBody.getStationModeType())){
            log.info("[AngelStationEventConsumer.addAngelStation],添加服务站事件开始处理! 服务站业务模式非全职,事件逻辑终止0");
            return;
        }
        if(eventBody==null||eventBody.getAngelType()==null){
            log.info("[AngelStationEventConsumer.addAngelStation],添加服务站事件开始处理! 未选中达达骑手,事件逻辑终止1");
            return;
        }
        if((eventBody.getAngelType()&AngelTypeEnum.DELIVERY.getType())==AngelTypeEnum.DELIVERY.getType()){
            /// 调服务站域的能力->创建门店
            CreateThirdStoreContext createThirdStoreContext = StationApplicationConverter.ins.converToCreateThirdStoreContext(eventBody);
            Boolean result = jdhMapDomainService.createThirdStore(createThirdStoreContext);
            if(!result){
                log.info("[AngelStationEventConsumer.updateAngelStation],创建第三方服务站失败!!!");
                throw new BusinessException(AngelErrorCode.THIRD_STORE_ADD_ERROR);
            }
            return;
        }
        log.info("[AngelStationEventConsumer.addAngelStation],添加服务站事件开始处理! 未选中达达骑手,事件逻辑终止2");
    }


    /**
     * 服务站-编辑
     *
     * @param event
     */
    private void updateAngelStation(Event event) {
        log.info("[AngelStationEventConsumer.updateAngelStation],添加服务站事件开始处理!event={}", JSON.toJSONString(event));
        // 判断服务资源类型是否选择选中骑手
        AngelStationEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationEventBody.class);

        if(!AngelStationModeTypeEnum.FULL_TIME.getCode().equals(eventBody.getStationModeType())){
            log.info("[AngelStationEventConsumer.addAngelStation],添加服务站事件开始处理! 服务站业务模式非全职,事件逻辑终止0");
            return;
        }

        if(eventBody == null || eventBody.getAngelType() == null){
            log.info("[AngelStationEventConsumer.updateAngelStation],添加服务站事件开始处理! 未选中达达骑手,事件逻辑终止1");
            return;
        }
        if((eventBody.getAngelType() & AngelTypeEnum.DELIVERY.getType()) == AngelTypeEnum.DELIVERY.getType()){
            //判断达达门店是否已经存在
            QueryThirdStoreContext queryThirdStoreContext = StationApplicationConverter.ins.converToQueryThirdStoreContext(eventBody);
            ThirdStoreInfoDto thirdStoreInfoDto = jdhMapDomainService.queryThirdStore(queryThirdStoreContext);
            if (thirdStoreInfoDto == null){
                /// 调服务站域的能力->创建门店
                CreateThirdStoreContext createThirdStoreContext = StationApplicationConverter.ins.converToCreateThirdStoreContext(eventBody);
                Boolean result = jdhMapDomainService.createThirdStore(createThirdStoreContext);
                if(!result){
                    log.info("[AngelStationEventConsumer.updateAngelStation],创建第三方服务站失败!!!");
                    throw new BusinessException(AngelErrorCode.THIRD_STORE_ADD_ERROR);
                }
            }else{
                /// 调服务站域的能力->更新门店
                UpdateThirdStoreContext updateThirdStoreContext = StationApplicationConverter.ins.converToUpdateThirdStoreContext(eventBody);
                Boolean result = jdhMapDomainService.updateThirdStore(updateThirdStoreContext);
                if(!result){
                    log.info("[AngelStationEventConsumer.updateAngelStation],更新第三方服务站失败!!!");
                    throw new BusinessException(AngelErrorCode.THIRD_STORE_UPDATE_ERROR);
                }
            }
            return;

        }
        log.info("[AngelStationEventConsumer.updateAngelStation],添加服务站事件开始处理! 未选中达达骑手,事件逻辑终止2");
    }

    /**
     * 发送咚咚
     * @param event
     */
    public void sendDongDong(Event event){
        log.info("[AngelStationEventConsumer.sendDongDong],服务站库存已用完事件!event={}", JSON.toJSONString(event));
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("服务站库存用完");

        AngelStationInventoryEventBody eventBody = JSON.parseObject(event.getBody(), AngelStationInventoryEventBody.class);
        dongDongRobotRpc.sendDongDongRobotMessage(String.format("爆单预警，服务站ID：%s，服务站名称：%s，爆单时段：%s %s，库存已用完，请关注",
                        eventBody.getAngelStationId(),eventBody.getAngelStationName(),eventBody.getDay(),eventBody.getTimeSlot()),
                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
    }

    public static void main(String[] args) {
        System.out.println(3&AngelTypeEnum.DELIVERY.getType());
    }
}
