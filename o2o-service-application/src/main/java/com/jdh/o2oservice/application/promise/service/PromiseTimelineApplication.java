package com.jdh.o2oservice.application.promise.service;

import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;

/**
 * PromiseTimelineApplication
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface PromiseTimelineApplication {

    /**
     * 查询promise服务开始时间描述
     *
     * @param request
     * @return
     */
    PromiseTimelineDto queryPromiseTimeline(QueryPromiseTimelineRequest request);
}
