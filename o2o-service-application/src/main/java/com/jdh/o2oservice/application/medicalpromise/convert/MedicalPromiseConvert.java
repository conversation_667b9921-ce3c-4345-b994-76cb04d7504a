package com.jdh.o2oservice.application.medicalpromise.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.google.common.collect.Sets;
import com.jd.health.xfyl.merchant.export.param.AppointmentServiceItemParam;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.RSACode;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.medpromise.bo.MedicalPromiseExtendBo;
import com.jdh.o2oservice.core.domain.medpromise.context.*;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseReportEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseFull;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseSku;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseSpecimenCode;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseFreezeInfoQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorQueryPageBo;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.LabPromisegoBo;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.*;
import com.jdh.o2oservice.export.medicalpromise.query.LabQueryMedPromisePageRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseBindRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.report.cmd.MedicalReportIndicatorFlushToEsCmd;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.report.dto.MedicalReportDTO;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * @Description: 履约检测单Convert
 * @Interface: MedicalPromiseConvert
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Mapper
public interface MedicalPromiseConvert {

    /**
     * 初始化
     */
    MedicalPromiseConvert INSTANCE = Mappers.getMapper(MedicalPromiseConvert.class);


    /**
     * cmd2CreateContext
     * @param cmd
     * @return
     */
    MedicalPromiseCreateContext cmd2CreateContext(MedicalPromiseCreateCmd cmd);

    MedicalPromiseEventBody medicalPromise2MedicalPromiseEventBody(MedicalPromise medicalPromise);
    /**
     * cmd2MedicalPromiseBindContext
     * @param cmd
     * @return
     */
    MedicalPromiseBatchBindSpecimenCodeContext cmd2MedicalPromiseBindContext(MedicalPromiseBindSpecimenCodeCmd cmd);

    /**
     * jdhSku2MedicalPromiseSku
     * @param jdhSkuDtoMap
     * @return
     */
    Map<Long, MedicalPromiseSku> jdhSku2MedicalPromiseSku(Map<Long, JdhSkuDto> jdhSkuDtoMap);

    /**
     * 转化
     * @param medicalPromiseDispatchCmd medicalPromiseDispatchCmd
     * @return return
     */
    MedicalPromiseDispatchContext convert(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd);


    /**
     * 转化
     * @param medicalPromiseListRequest
     * @return
     */
    MedicalPromiseListQuery convert(MedicalPromiseListRequest medicalPromiseListRequest);

    /**
     * batchInvalidCmd2ListQuery
     * @param cmd
     * @return
     */
    @Mapping(source = "medicalPromiseStatusInfoCmds" ,target = "invalidList")
    MedicalPromiseBatchInvalidContext batchInvalidCmd2MedicalPromiseBatchInvalidContext(MedicalPromiseBatchInvalidCmd cmd);

    /**
     * 转化
     * @param medicalPromise
     * @return
     */
    @Mapping(source = "flag",target = "flagDesc",qualifiedByName = "getFlagDesc")
    @Mapping(source = "deliveryStepFlow",target = "deliveryStepFlow",qualifiedByName = "deliveryStepFlow")
    @Mapping(source = "extend",target = "extendDto")
    MedicalPromiseDTO convert(MedicalPromise medicalPromise);

    /**
     * convertToMedicalPromiseExtendDto
     *
     * @param medicalPromiseExtendBo
     * @return
     */
    MedicalPromiseExtendDto convertToMedicalPromiseExtendDto(MedicalPromiseExtendBo medicalPromiseExtendBo);

    /**
     * 转化
     * @param medicalPromises
     * @return
     */
    List<MedicalPromiseDTO> convert(List<MedicalPromise> medicalPromises);

    /**
     * convert
     * @param medicalPromisePageDto
     * @return
     */
    PageDto<MedicalPromiseDTO> convert(PageDto<MedicalPromise> medicalPromisePageDto);

    /**
     * 转化
     * @param medicalPromiseCallbackCmd
     * @return
     */
    MedicalPromise convert(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd);

    /**
     * 转化
     * @param medicalPromiseBindRequest
     * @return
     */
    MedicalPromiseListQuery convert(MedicalPromiseBindRequest medicalPromiseBindRequest);

    /**
     * convertCondition
     * @param medicalPromises
     * @return
     */
    List<MedicalPromiseConditionDTO> convertCondition(List<MedicalPromise> medicalPromises);

    /**
     * convertCondition
     * @param medicalPromise
     * @return
     */
    MedicalPromiseConditionDTO convertCondition(MedicalPromise medicalPromise);

    /**
     * 转化
     * @param m
     * @return
     */
    @Mapping(source = "medicalPromiseStatusInfoCmds" ,target = "medicalPromiseNestedQueries")
    MedicalPromiseListQuery convert(MedicalPromiseStatusCmd m);

    /**
     * 转化
     * @param m
     * @return
     */
    MedicalPromiseFreezeInfoQuery convert(MedicalPromiseStatusInfoCmd m);

//    /**
//     * 转化
//     * @param m
//     * @return
//     */
//    List<MedicalPromiseFreezeInfoQuery> convert(List<MedicalPromiseStatusInfoCmd> m);

    /**
     * 转化
     * @param batchMedicalPromiseSubmitCmd m
     * @return r
     */
    MedicalPromiseListQuery convertSubmit(BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd);

    /**
     * convertList
     * @param medicalPromises
     * @return
     */
    List<MedicalPromiseSubmitContext> convertList(List<MedicalPromise> medicalPromises);

    @Mapping(source = "specimenCode" ,target = "specimenCode",qualifiedByName = "toUppercase")
    MedicalPromiseSpecimenCode convert(MedicalPromiseCmdSpecimenCode medicalPromiseCmdSpecimenCode);

    MedicalReportIndicatorQueryPageBo convert(MedicalReportIndicatorFlushToEsCmd medicalReportIndicatorFlushToEsCmd);


    /**
     * convertMedicalPromiseSubmitContext
     * @param medicalPromise
     * @param promisePatientDto
     * @return
     */
    default MedicalPromiseSubmitContext convertMedicalPromiseSubmitContext(MedicalPromise medicalPromise, PromisePatientDto promisePatientDto){
        if ( medicalPromise == null) {
            return null;
        }
        MedicalPromiseSubmitContext medicalPromiseSubmitContext = convertTo(medicalPromise);
        medicalPromiseSubmitContext.setUserName(promisePatientDto.getUserName().getName());
        medicalPromiseSubmitContext.setUserBirth(promisePatientDto.getBirthday().getBirth());
        medicalPromiseSubmitContext.setUserAge(promisePatientDto.getBirthday().getAge());
        medicalPromiseSubmitContext.setUserGender(promisePatientDto.getGender());
        medicalPromiseSubmitContext.setUserMarriage(promisePatientDto.getMarriage());
        medicalPromiseSubmitContext.setUserPhone(promisePatientDto.getPhoneNumber().getPhone());
        return medicalPromiseSubmitContext;
    }

    /**
     *
     * @param medicalPromises
     * @param patientToDto
     * @return
     */
    default List<MedicalPromiseSubmitContext> convertList(List<MedicalPromise> medicalPromises, Map<Long, PromisePatientDto> patientToDto ){
        if ( medicalPromises == null ) {
            return null;
        }

        List<MedicalPromiseSubmitContext> list = new ArrayList<MedicalPromiseSubmitContext>( medicalPromises.size() );
        for ( MedicalPromise medicalPromise : medicalPromises ) {
            PromisePatientDto promisePatientDto = patientToDto.get(medicalPromise.getPromisePatientId());
            if (Objects.isNull(promisePatientDto)){
                //检测人信息为空
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_PERSON_INFO_NULL);
            }
            list.add( convertMedicalPromiseSubmitContext(medicalPromise, promisePatientDto) );
        }

        return list;
    }






    /**
     * convert
     * @param medicalPromiseHandCmd
     * @return
     */
    MedicalPromise convert(MedicalPromiseHandCmd medicalPromiseHandCmd);


    default MedicalPromiseSubmitContext convertTo(MedicalPromise medicalPromise){
        MedicalPromiseSubmitContext medicalPromiseSubmitContext = new MedicalPromiseSubmitContext();

        medicalPromiseSubmitContext.setPromisePatientId(medicalPromise.getPromisePatientId());
        medicalPromiseSubmitContext.setUserPin(medicalPromise.getUserPin());
        medicalPromiseSubmitContext.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        if (medicalPromise.getStationId() != null) {
            medicalPromiseSubmitContext.setStationId(String.valueOf(medicalPromise.getStationId()));
        }
        medicalPromiseSubmitContext.setProviderId(medicalPromise.getProviderId());
        medicalPromiseSubmitContext.setServiceId(medicalPromise.getServiceId());
        medicalPromiseSubmitContext.setOuterId(medicalPromise.getOuterId());
        if (medicalPromise.getVersion() != null) {
            medicalPromiseSubmitContext.setVersion(String.valueOf(medicalPromise.getVersion()));
        }
        medicalPromiseSubmitContext.setSampleBarcodeList(Lists.newArrayList(medicalPromise.getSpecimenCode()));
        List<AppointmentServiceItemParam> appointmentServiceItemParams = Lists.newArrayList();
        AppointmentServiceItemParam appointmentServiceItemParam = new AppointmentServiceItemParam();
        appointmentServiceItemParam.setItemId(medicalPromise.getServiceItemId());
        appointmentServiceItemParam.setItemName(medicalPromise.getServiceItemName());
        appointmentServiceItemParam.setSampleBarcode(medicalPromise.getSpecimenCode());
        appointmentServiceItemParams.add(appointmentServiceItemParam);
        medicalPromiseSubmitContext.setServiceItemList(appointmentServiceItemParams);
        return medicalPromiseSubmitContext;
    }

    /**
     * convert
     * @param jdhStationServiceItemRelDto
     * @return
     */
    JdhServiceItemContext convert(JdhStationServiceItemRelDto jdhStationServiceItemRelDto);

    /**
     * convertRel
     * @param jdhStationServiceItemRelDto
     * @return
     */
    @Mapping(source = "storePhone",target = "stationPhone")
    JdhStationServiceItemRelContext convertRel(JdhStationServiceItemRelDto jdhStationServiceItemRelDto);

    /**
     * convert
     * @param medicalPromiseRequest
     * @return
     */
    MedicalPromiseRepQuery convert(MedicalPromiseRequest medicalPromiseRequest);

    /**
     * convert
     * @param medicalPromiseReportCmd
     * @return
     */
    MedicalPromiseReportEventBody convert(MedicalPromiseReportCmd medicalPromiseReportCmd);

    /**
     * convert
     * @param medicalPromiseReportEventBody
     * @return
     */
    MedicalPromiseReportCmd convert(MedicalPromiseReportEventBody medicalPromiseReportEventBody);

    /**
     * convert
     * @param labQueryMedPromisePageRequest
     * @return
     */
    MedicalPromiseEsQuery convert(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);

    /**
     *
     * @param medicalPromiseFullPageDto
     * @return
     */
    PageDto<MedicalPromiseFullDTO> convertFull(PageDto<MedicalPromiseFull> medicalPromiseFullPageDto);

    /**
     *
     * @param medicalPromiseFullPageDto
     * @return
     */
    @Mapping(source = "workStatus",target = "workStatus",qualifiedByName = "convertWorkStatus")
    MedicalPromiseFullDTO convertFull(MedicalPromiseFull medicalPromiseFullPageDto);

    @Named("convertWorkStatus")
    default Integer convertWorkStatus(String workStatus){
        if (StringUtils.isBlank(workStatus)) {
            return null;
        }
        return Integer.valueOf(workStatus);

    }
    @Named("getFlagDesc")
    default String getFlagDesc(String flag){
        return MedicalPromiseFlagEnum.getDesc(flag);
    }

    @Named("deliveryStepFlow")
    default List<MedPromiseDeliveryStepDTO> deliveryStepFlow(String flag){
        if (StringUtils.isBlank(flag)) {
            return null;
        }
        return JSON.parseArray(flag, MedPromiseDeliveryStepDTO.class);
    }

    @Named("toUppercase")
    default String toUppercase(String str){
        if (StringUtil.isBlank(str)){
            return null;
        }
        return str.trim().toUpperCase();
    }

    @Mapping(source = "reportId", target = "medicalReportId")
    ReportCenterReport medicalReportSaveCmd2ReportCenterReport(MedicalReportSaveCmd medicalReportSaveCmd);

    /**
     * 组装报告信息
     * @param medicalPromiseReportCmd
     * @param medicalPromise
     * @return
     */
    default MedicalReportSaveCmd getMedicalReportSaveCmd(MedicalPromiseReportCmd medicalPromiseReportCmd, MedicalPromise medicalPromise, DuccConfig duccConfig, MedicalReportApplication medicalReportApplication, PromiseApplication promiseApplication, FileManageService fileManageService) {
        MedicalReportSaveCmd medicalReportSaveCmd = new MedicalReportSaveCmd();
        medicalReportSaveCmd.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
        medicalReportSaveCmd.setUserPin(medicalPromise.getUserPin());
        medicalReportSaveCmd.setChannelNo(medicalPromiseReportCmd.getChannelType());
        medicalReportSaveCmd.setSourceOss(medicalPromiseReportCmd.getStructReportStr());
        medicalReportSaveCmd.setStructReportOss(medicalPromiseReportCmd.getJdStructReportStr());
        medicalReportSaveCmd.setReportOss(medicalPromiseReportCmd.getReportUrl());
        medicalReportSaveCmd.setPromiseId(medicalPromise.getPromiseId());

        Date examinationTime = Objects.nonNull(medicalPromise.getCheckTime()) ? medicalPromise.getCheckTime() : new Date();
        medicalReportSaveCmd.setExaminationTime(DateUtil.format(examinationTime, CommonConstant.YMD));
        if (Objects.isNull(medicalPromise.getReportTime())){
            Date reportTime = Objects.nonNull(medicalPromiseReportCmd.getReportTime()) ? medicalPromiseReportCmd.getReportTime() : new Date();
            medicalReportSaveCmd.setReportTime(reportTime);
        }
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
        MedicalReportDTO byMedicalPromiseId = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        if (Objects.isNull(byMedicalPromiseId)){
            PromiseDto byPromiseId = getPromiseDto(medicalPromise,promiseApplication);
            if (Objects.isNull(byPromiseId) && StringUtil.equals(medicalPromise.getVerticalCode(),"xfylMerchantQualityTest")){
                medicalReportSaveCmd.setPatientId(11111L);
                medicalReportSaveCmd.setPatientName("测试质控");
            }else {
                List<PromisePatientDto> patients = byPromiseId.getPatients();
                PromisePatientDto promisePatientDto = patients.stream().filter(p -> Objects.equals(medicalPromise.getPromisePatientId(), p.getPromisePatientId())).findFirst().orElse(null);
                if (Objects.nonNull(promisePatientDto)){
                    medicalReportSaveCmd.setPatientId(promisePatientDto.getPatientId());
                    medicalReportSaveCmd.setPatientName(Objects.nonNull(promisePatientDto.getUserName())?promisePatientDto.getUserName().getName():null);
                }
            }

        }else {
            medicalReportSaveCmd.setReportCenterId(byMedicalPromiseId.getReportCenterId());
        }
        medicalReportSaveCmd.setMedicalType(Objects.nonNull(medicalPromiseReportCmd.getMedicalType()) ? medicalPromiseReportCmd.getMedicalType() : CommonConstant.ONE);
        medicalReportSaveCmd.setReportType(medicalPromiseReportCmd.getReportType());
        medicalReportSaveCmd.setReportStatus(Objects.nonNull(medicalPromiseReportCmd.getReportStatus()) ? medicalPromiseReportCmd.getReportStatus() : CommonConstant.ONE);
        medicalReportSaveCmd.setManufacturerNumber(medicalPromiseReportCmd.getManufacturerNumber());
        medicalReportSaveCmd.setSnCode(medicalPromiseReportCmd.getSnCode());

        //设置FileMd5
        ObjectMetadata objectMetadata = fileManageService.getObjectMetadata(medicalReportSaveCmd.getReportOss());


        medicalReportSaveCmd.setFileMd5(objectMetadata.getETag());
        medicalReportSaveCmd.setServiceItemName(medicalPromise.getServiceItemName());
        medicalReportSaveCmd.setServiceItemId(medicalPromise.getServiceItemId());
        medicalReportSaveCmd.setEquipmentModel(medicalPromiseReportCmd.getEquipmentModel());
        return medicalReportSaveCmd;
    }

    /**
     *
     * @param medicalPromise
     * @return
     */
    default PromiseDto getPromiseDto(MedicalPromise medicalPromise, PromiseApplication promiseApplication) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(medicalPromise.getPromiseId());
        return promiseApplication.findByPromiseId(promiseIdRequest);
    }

    default  MedicalPromiseFullDTO getMedicalPromiseFullDTO(MedicalPromiseEsQuery medicalPromiseEsQuery, LabQueryMedPromisePageRequest request, MedicalPromiseFull medicalPromiseFull, Map<String, String> jdlMessage,DuccConfig duccConfig,Map<Long, LabPromisegoBo> etaMap,Map<Long, AngelShip> finalShipMap) {
        MedicalPromiseFullDTO medicalPromiseFullDTO = new MedicalPromiseFullDTO();

        medicalPromiseFullDTO.setMedicalPromiseId(Long.valueOf(medicalPromiseFull.getMedicalPromiseId()));
        medicalPromiseFullDTO.setServiceItemName(medicalPromiseFull.getServiceItemName());
        medicalPromiseFullDTO.setServiceItemId(medicalPromiseFull.getServiceItemId());
        medicalPromiseFullDTO.setServiceId(Long.valueOf(medicalPromiseFull.getOrderId()));
        medicalPromiseFullDTO.setSerialNum(medicalPromiseFull.getSerialNum());
        //TODO
        medicalPromiseFullDTO.setDeliveryStoreTime(DateUtil.format(medicalPromiseFull.getDeliveryStoreTime(),CommonConstant.YMDHMS));
        medicalPromiseFullDTO.setCheckTime(DateUtil.format(medicalPromiseFull.getCheckTime(),CommonConstant.YMDHMS));
        medicalPromiseFullDTO.setReportTime(DateUtil.format(medicalPromiseFull.getReportTime(),CommonConstant.YMDHMS));
        medicalPromiseFullDTO.setSpecimenCode(medicalPromiseFull.getSpecimenCode());
        medicalPromiseFullDTO.setProviderId(StringUtils.isNotBlank(medicalPromiseFull.getProviderId()) ? Long.valueOf(medicalPromiseFull.getProviderId()) : null);
        medicalPromiseFullDTO.setShipStatus(medicalPromiseFull.getShipStatus());
        medicalPromiseFullDTO.setAppointmentStartTime(DateUtil.format(medicalPromiseFull.getAppointmentStartTime(),CommonConstant.YMDHMS));
        medicalPromiseFullDTO.setAppointmentEndTime(DateUtil.format(medicalPromiseFull.getAppointmentEndTime(),CommonConstant.YMDHMS));
        medicalPromiseFullDTO.setMedicalPromiseStatus(Integer.valueOf(medicalPromiseFull.getMedicalPromiseStatus()));
        medicalPromiseFullDTO.setReportStatus(medicalPromiseFull.getReportStatus());
        medicalPromiseFullDTO.setDetectingNodesNum(medicalPromiseFull.getDetectingNodesNum());
        medicalPromiseFullDTO.setSubStatus(medicalPromiseFull.getSubStatus());
        medicalPromiseFullDTO.setExceptionRecord(medicalPromiseFull.getExceptionRecord());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(medicalPromiseFull.getWorkStatus())){
            medicalPromiseFullDTO.setWorkStatus(Integer.valueOf(medicalPromiseFull.getWorkStatus()));
        }

        medicalPromiseFullDTO.setTestTime(medicalPromiseFull.getTestTime());
        medicalPromiseFullDTO.setTestFinishTime(medicalPromiseFull.getTestFinishTime());
        medicalPromiseFullDTO.setFlowCode(medicalPromiseFull.getFlowCode());

        Integer status = medicalPromiseFullDTO.getMedicalPromiseStatus();
        Integer reportStatus = medicalPromiseFullDTO.getReportStatus();
        Integer shipStatus = medicalPromiseFullDTO.getShipStatus();
        Integer subStatus = medicalPromiseFullDTO.getSubStatus();
        Integer workStatus = null;
        if (StringUtil.isNotBlank(medicalPromiseFull.getWorkStatus())){
            workStatus = Integer.valueOf(medicalPromiseFull.getWorkStatus());
        }

        Map<String, String> quickCheckStatusConfig = duccConfig.getQuickCheckStatusConfig();
        //未到达实验室
        Set<Integer> notArriveStatusSet = Sets.newHashSet(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),MedicalPromiseStatusEnum.COMPLETED.getStatus(),MedicalPromiseStatusEnum.INVALID.getStatus());
        //样本处理子状态
        Set<Integer> sampleDealSubSet = Sets.newHashSet(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus()
                ,MedicalPromiseSubStatusEnum.SAMPLE_DEAL_FINISH.getSubStatus()
                ,MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_CONNECT.getSubStatus()
                ,MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_AGREE.getSubStatus()
                ,MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_REFUSE.getSubStatus()
        );

        //样本上机子状态
        Set<Integer> sampleTestSubSet = Sets.newHashSet(
                MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus()
                ,MedicalPromiseSubStatusEnum.SAMPLE_TEST_FINISH.getSubStatus()
                ,MedicalPromiseSubStatusEnum.SAMPLE_TEST_ERROR_RE_TEST.getSubStatus()
        );

        //报告审核子状态
        Set<Integer> reportAuditSubSet = Sets.newHashSet(
                MedicalPromiseSubStatusEnum.REPORT_CHECK.getSubStatus(),
                MedicalPromiseSubStatusEnum.REPORT_CHECK_PASS.getSubStatus(),
                MedicalPromiseSubStatusEnum.REPORT_CHECK_ERROR.getSubStatus()
        );
        //让步检测确认中子状态
        Set<Integer> concessionTestSubSet = Sets.newHashSet(
                MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_CONNECT.getSubStatus()
        );
        //让步检测确认中子状态
        Set<Integer> concessionTestAgreeSubSet = Sets.newHashSet(
                MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_AGREE.getSubStatus()
        );

        Set<Integer> toHomeWorkStatus = Sets.newHashSet(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.ARRIVED.getType());
        Set<Integer> collect = Sets.newHashSet(AngelWorkStatusEnum.SERVICING.getType(),AngelWorkStatusEnum.SERVICED.getType());

        Set<Integer> deliveryShipStatus = Sets.newHashSet(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus(),AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());

        Set<Integer> reportReUploadSubStatus = Sets.newHashSet(MedicalPromiseSubStatusEnum.REPORT_RESET.getSubStatus(),MedicalPromiseSubStatusEnum.RE_CHECK.getSubStatus(),MedicalPromiseSubStatusEnum.REPORT_RE_CHECK_ERROR.getSubStatus());

        if (Objects.equals(CommonConstant.FIVE,medicalPromiseFullDTO.getDetectingNodesNum())){
            if (Objects.equals(CommonConstant.ONE,reportStatus)){
                //已出报告
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.REPORT.getCompositeStatus());
            }else if (Objects.equals(CommonConstant.ZERO,reportStatus) && reportReUploadSubStatus.contains(subStatus)){
                //报告重传
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.REPORT_RE_UPLOAD.getCompositeStatus());
            }else if (concessionTestSubSet.contains(subStatus)) {
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.CONCESSION_TEST_CONNECT.getCompositeStatus());
            } else if (concessionTestAgreeSubSet.contains(subStatus)) {
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.CONCESSION_TEST_AGREE.getCompositeStatus());
            } else if (
                    !deliveryShipStatus.contains(shipStatus) &&
                    !notArriveStatusSet.contains(status)  &&
                    (Objects.equals(AngelWorkStatusEnum.WAIT_RECEIVE.getType(), workStatus)
                    ||Objects.isNull(workStatus))
            ) {
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.WAITING_RECEIVE_ORDER.getCompositeStatus());
            } else if (!deliveryShipStatus.contains(shipStatus) && !notArriveStatusSet.contains(status) && toHomeWorkStatus.contains(workStatus)) {
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.WAITING_RECEIVE_GOODS.getCompositeStatus());
            }else if (!deliveryShipStatus.contains(shipStatus) && !notArriveStatusSet.contains(status) && collect.contains(workStatus)){
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.RECEIVE_GOODS.getCompositeStatus());
            }else if (Objects.equals(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus(),shipStatus) && !notArriveStatusSet.contains(status)){
                //送检中
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.DELIVERY.getCompositeStatus());
            }else if (Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),shipStatus) && !notArriveStatusSet.contains(status)) {
                //已送达待收样
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.WAITING_COLLECT.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),status)
                    && sampleDealSubSet.contains(subStatus)) {
                //样本处理中
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.SAMPLE_DEAL.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),status)
                    &&(sampleTestSubSet.contains(subStatus) || Objects.isNull(subStatus) )){
                //样本上机检测
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.TEST.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),status)
                    && reportAuditSubSet.contains(subStatus)) {
                //报告审核
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.AUDIT.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),status)){
                //已退款
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.INVALID.getCompositeStatus());
            }


        }else {
            //已出报告
            if (Objects.equals(CommonConstant.ONE,reportStatus)){
                //已出报告
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.REPORT.getCompositeStatus());
            }else if (Objects.equals(CommonConstant.ZERO,reportStatus) && reportReUploadSubStatus.contains(subStatus)){
                //报告重传
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.REPORT_RE_UPLOAD.getCompositeStatus());
            } else if (concessionTestSubSet.contains(subStatus)) {
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.CONCESSION_TEST_CONNECT.getCompositeStatus());
            } else if (concessionTestAgreeSubSet.contains(subStatus)) {
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.CONCESSION_TEST_AGREE.getCompositeStatus());
            } else if (!deliveryShipStatus.contains(shipStatus) && !notArriveStatusSet.contains(status) &&
                    (Objects.equals(AngelWorkStatusEnum.WAIT_RECEIVE.getType(), workStatus)
                            ||Objects.isNull(workStatus))){
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.WAITING_RECEIVE_ORDER.getCompositeStatus());
            }else if (!deliveryShipStatus.contains(shipStatus) &&!notArriveStatusSet.contains(status) &&  toHomeWorkStatus.contains(workStatus)){
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.WAITING_RECEIVE_GOODS.getCompositeStatus());
            }else if (!deliveryShipStatus.contains(shipStatus) &&!notArriveStatusSet.contains(status) && collect.contains(workStatus)){
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.RECEIVE_GOODS.getCompositeStatus());
            }else if (Objects.equals(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus(),shipStatus) && !notArriveStatusSet.contains(status)){
                //送检中
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.DELIVERY.getCompositeStatus());
            }else if (Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),shipStatus) && !notArriveStatusSet.contains(status)) {
                //已送达待收样
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.WAITING_COLLECT.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),status)
                    && reportAuditSubSet.contains(subStatus)) {
                //报告审核
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.AUDIT.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),status)) {
                //检测中
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.TEST.getCompositeStatus());
            }else if (Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),status)){
                //已退款
                medicalPromiseFullDTO.setCompositeStatus(LabCompositeStatusEnum.INVALID.getCompositeStatus());
            }
        }


        if (Objects.nonNull(medicalPromiseFullDTO.getCompositeStatus())){
            String s = quickCheckStatusConfig.get(String.valueOf(medicalPromiseFullDTO.getCompositeStatus()));
            Map<String,String> statusDescMap = JsonUtil.parseObject(s, Map.class);
            medicalPromiseFullDTO.setCompositeStatusName(statusDescMap.get("compositeStatusName"));
            medicalPromiseFullDTO.setCompositeStatusDesc(statusDescMap.get("compositeStatusDesc"));
        }
        if (CollUtil.isNotEmpty(jdlMessage) && medicalPromiseFull.getPromiseId() != null && jdlMessage.containsKey(medicalPromiseFull.getPromiseId()) && StringUtils.isNotBlank(jdlMessage.get(medicalPromiseFull.getPromiseId()))) {
            medicalPromiseFullDTO.setCompositeStatusName(medicalPromiseFullDTO.getCompositeStatusName() == null ? "" : medicalPromiseFullDTO.getCompositeStatusName() + "(" + jdlMessage.get(medicalPromiseFull.getPromiseId()) + ")");
        }

        medicalPromiseFullDTO.setServiceItemName(medicalPromiseFull.getServiceItemName());
        medicalPromiseFullDTO.setReportTimeOutStatus(CommonConstant.ZERO);
        if (!Boolean.TRUE.equals(request.getHeartSmart())){
            if (medicalPromiseFull.getCheckTime() != null && medicalPromiseEsQuery.getReportTimeOut() != null) {
                if (medicalPromiseFull.getReportTime() != null) {
                    medicalPromiseFullDTO.setReportTimeOutStatus((medicalPromiseFull.getReportTime().getTime() - medicalPromiseFull.getCheckTime().getTime()) / 1000 >= medicalPromiseFull.getInspectDuration() ? 1 : 0);
                    medicalPromiseFullDTO.setReportTimeOutRemain(0L);
                } else {
                    Date now = new Date();
                    long remain = medicalPromiseFull.getInspectDuration() - ((now.getTime() - medicalPromiseFull.getCheckTime().getTime()) / 1000);
                    medicalPromiseFullDTO.setReportTimeOutStatus(remain < 0 ? 1 : 0);
                    medicalPromiseFullDTO.setReportTimeOutRemain(remain < 0 ? 0 : remain);
                }
            }

            if (medicalPromiseFullDTO.getReportTimeOutStatus() == 0) {
                medicalPromiseFullDTO.setReportTimeOutStatusName(medicalPromiseEsQuery.getReportNotTimeOutStatusName());
            } else if (medicalPromiseFullDTO.getReportTimeOutStatus() == 1) {
                medicalPromiseFullDTO.setReportTimeOutStatusName(medicalPromiseEsQuery.getReportTimeOutStatusName());
            }

            //报告-送达时间 间隔超时
            medicalPromiseFullDTO.setReportCheckTimeOutStatus(0);
            if (medicalPromiseFull.getDeliveryStoreTime() != null && medicalPromiseFull.getInspectDuration() != null) {
                if (medicalPromiseFull.getReportTime() != null) {
                    medicalPromiseFullDTO.setReportCheckTimeOutStatus((medicalPromiseFull.getReportTime().getTime() - medicalPromiseFull.getDeliveryStoreTime().getTime()) / 1000 >= medicalPromiseFull.getInspectDuration() ? 1 : 0);
                    medicalPromiseFullDTO.setReportCheckTimeOutRemain(0L);
                } else {
                    Date now = new Date();
                    long remain = medicalPromiseFull.getInspectDuration() - ((now.getTime() - medicalPromiseFull.getDeliveryStoreTime().getTime()) / 1000);
                    medicalPromiseFullDTO.setReportCheckTimeOutStatus(remain < 0 ? 1 : 0);
                    medicalPromiseFullDTO.setReportCheckTimeOutRemain(remain < 0 ? 0 : remain);
                }
            }

            if (medicalPromiseFullDTO.getReportCheckTimeOutStatus() == 0) {
                medicalPromiseFullDTO.setReportCheckTimeOutStatusName(medicalPromiseEsQuery.getReportCheckNotTimeOutStatusName());
            } else if (medicalPromiseFullDTO.getReportCheckTimeOutStatus() == 1) {
                medicalPromiseFullDTO.setReportCheckTimeOutStatusName(medicalPromiseEsQuery.getReportCheckTimeOutStatusName());
            }
        }else {
            buildTestTimeOut(medicalPromiseFull, medicalPromiseFullDTO,medicalPromiseFullDTO.getCompositeStatus());
        }



        if (medicalPromiseFull.getDeliveryStoreTime() != null) {
            medicalPromiseFullDTO.setDeliveryStoreTime(TimeUtils.dateTimeToStr(medicalPromiseFull.getDeliveryStoreTime()));
        }

        // 已退款不展示是否超时
        if (MedicalPromiseStatusEnum.INVALID.getStatus().equals(Integer.valueOf(medicalPromiseFull.getMedicalPromiseStatus()))) {
            medicalPromiseFullDTO.setReportTimeOutStatus(null);
            medicalPromiseFullDTO.setReportTimeOutStatusName(null);
            medicalPromiseFullDTO.setReportTimeOutRemain(null);
            medicalPromiseFullDTO.setReportCheckTimeOutStatus(null);
            medicalPromiseFullDTO.setReportCheckTimeOutStatusName(null);
            medicalPromiseFullDTO.setReportCheckTimeOutRemain(null);
        }

        medicalPromiseFullDTO.setName(new UserName(medicalPromiseFull.getUserName()).maskPersonal());
        medicalPromiseFullDTO.setGender(StringUtils.isBlank(medicalPromiseFull.getUserGender()) ? null : Integer.parseInt(medicalPromiseFull.getUserGender()));
        medicalPromiseFullDTO.setGenderDesc(StringUtils.isBlank(medicalPromiseFull.getUserGender()) ? null : GenderEnum.getDescOfType(Integer.parseInt(medicalPromiseFull.getUserGender())));
        medicalPromiseFullDTO.setAge(Birthday.parseAge(medicalPromiseFull.getBirthday()));

        if (Boolean.TRUE.equals(request.getQueryEncryptionName())) {
            medicalPromiseFullDTO.setEncryptionName(RSACode.encryptByPublicKey(RandomStringUtils.randomAlphanumeric(6) + medicalPromiseFull.getUserName()));
        }
        if (Boolean.TRUE.equals(request.getQueryNoEncryptionName())) {
            medicalPromiseFullDTO.setEncryptionName(medicalPromiseFull.getUserName());
        }
        String deliveryStoreTime = EntityUtil.getFiledDefaultNull(medicalPromiseFull.getDeliveryStoreTime(), TimeUtils::dateTimeToStr);
        if (StringUtils.isBlank(deliveryStoreTime) && Objects.nonNull(etaMap)) {
            LabPromisegoBo etaBo = etaMap.get(Long.valueOf(medicalPromiseFull.getMedicalPromiseId()));
            if (Objects.nonNull(etaBo)) {
                if (Objects.nonNull(etaBo.getTermScript())) {
                    deliveryStoreTime = etaBo.getTermScript().getScriptContent();
                }
            }
        }
        medicalPromiseFullDTO.setDeliveryStoreTime(deliveryStoreTime);

        // 如果workId不为空，则获取最新创建的ship，并填充骑手姓名，电话数据
        if (StringUtils.isNotBlank(medicalPromiseFull.getWorkId())) {
            AngelShip ship = finalShipMap.get(Long.valueOf(medicalPromiseFull.getWorkId()));
            if (Objects.nonNull(ship)) {
                DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.getEnumByType(ship.getType());
                if (Objects.nonNull(deliveryTypeEnum)) {
                    medicalPromiseFullDTO.setDeliveryType(deliveryTypeEnum.getDesc());
                }
                medicalPromiseFullDTO.setTransferNameEncrypt(UserName.encrypt(ship.getTransferName()));
                medicalPromiseFullDTO.setTransferPhoneEncrypt(PhoneNumber.encrypt(ship.getTransferPhone()));
                medicalPromiseFullDTO.setFinishCode(ship.getFinishCode());
            }
        }


        return medicalPromiseFullDTO;
    }

    /**
     * 构建收样超时和检测超时信息
     *
     * @param t
     * @param dto
     * @param compositeStatus
     */
    default void buildTestTimeOut(MedicalPromiseFull t, MedicalPromiseFullDTO dto, Integer compositeStatus) {
        if (Objects.isNull(compositeStatus)){
            return;
        }

        if (Objects.nonNull(t.getWaitingTestTimeOutStatus())) {

            dto.setReportCheckTimeOutStatus(t.getWaitingTestTimeOutStatus());
            if (Objects.equals(CommonConstant.ZERO,dto.getReportCheckTimeOutStatus())) {
                dto.setReportCheckTimeOutStatusName("收样未超时");
            }else {
                dto.setReportCheckTimeOutStatusName("收样超时");
            }

            // 未超时 && 已送达待收样 计算收样剩余时间
            if (Objects.equals(CommonConstant.ZERO,dto.getReportCheckTimeOutStatus())
                    &&  Objects.equals(LabCompositeStatusEnum.WAITING_COLLECT.getCompositeStatus(),compositeStatus)
                    && Objects.nonNull(t.getWaitingTestTimeOutDate())
            ) {
                dto.setReportCheckTimeOutRemain(DateUtil.between(new Date(),t.getWaitingTestTimeOutDate(), DateUnit.SECOND));
            }
        }

        // 检测超时
        if (Objects.nonNull(t.getTestingTimeOutStatus())) {
            dto.setReportTimeOutStatus(t.getTestingTimeOutStatus());
            if (Objects.equals(CommonConstant.ZERO,dto.getReportTimeOutStatus())) {
                dto.setReportTimeOutStatusName("检测未超时");
            }else {
                dto.setReportTimeOutStatusName("检测超时");
            }

            if (Objects.nonNull(t.getReportTime())){
                dto.setReportTimeOutRemain(0L);

                // 未超时 && 已送达待检测 计算检测剩余时间
            }else if(Objects.equals(CommonConstant.ZERO,dto.getReportTimeOutStatus())
                    && Objects.nonNull(t.getTestingTimeOutDate())){
                dto.setReportTimeOutRemain(DateUtil.between(new Date(),t.getTestingTimeOutDate(), DateUnit.SECOND));
            }



//            if (Objects.equals(t.getTestingTimeOutStatus(), YnStatusEnum.YES.getCode())) {
//                dto.setReportTimeOutStatus(CommonConstant.ONE);
//            } else {
//                if (compositeStatus == ProviderQueryApplicationImpl.CompositeStatus.CHECK_ING) {
//                    dto.setReportTimeOutStatus(null);
//                } else {
//                    dto.setReportTimeOutStatus(CommonConstant.ZERO);
//                }
//                dto.setReportTimeOutStatus(CommonConstant.ZERO);
//            }
        }

//        dto.setReportTimeOutRemain(EntityUtil.getFiledDefaultNull(t.getTestingTimeOutDate(), TimeUtils::dateTimeToStr));
//        dto.setWaitingTestTimeOutDate(EntityUtil.getFiledDefaultNull(t.getWaitingTestTimeOutDate(), TimeUtils::dateTimeToStr));
    }
}
