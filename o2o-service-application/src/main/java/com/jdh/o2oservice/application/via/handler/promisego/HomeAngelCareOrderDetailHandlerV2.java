package com.jdh.o2oservice.application.via.handler.promisego;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelEcologyApplication;
import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.application.angel.service.AngelMainReadApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.application.support.service.UserFeedbackApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.application.via.handler.AbstractViaDataFillHandler;
import com.jdh.o2oservice.application.via.handler.floor.Floor;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angel.enums.AngelProfessionCodeEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelIdentifier;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelProfessionRel;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.promise.bo.PatientExtBo;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.enums.AvailableAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ModifyAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentTimeValueObject;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.query.JdhAngelEcologyQueryRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.product.query.PromiseDangerLevelRequest;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssQuestionDTO;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssUserDetailDTO;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentDetailManDTO;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentDetailRequest;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderExtDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderItemDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderServiceFeeInfoDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;
import com.jdh.o2oservice.export.user.query.UserFeedbackRequest;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName HomeAngelCareOrderDetailHandlerV2
 * @Description
 * <AUTHOR>
 * @Date 2025/6/13 10:32
 **/
@Slf4j
@Service
public class HomeAngelCareOrderDetailHandlerV2 extends AbstractViaDataFillHandler implements MapAutowiredKey {

    /**
     * 支付过期时间，默认半小时，之后应该去订单中台取数据
     */
    private static final Long PAY_EXPIRE_TIME = 30 * 60 * 1000L;

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * promiseHistoryRepository
     */
    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * angelApplication
     */
    @Autowired
    private AngelApplication angelApplication;

    /**
     * angelPromiseApplication
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;
    @Resource
    private ViaComponentDomainService viaComponentDomainService;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private MedicalReportApplication medicalReportApplication;
    @Resource
    private ViaConfigRepository viaConfigRepository;
    @Resource
    private UserFeedbackApplication userFeedbackApplication;
    @Resource
    private AngelEcologyApplication angelEcologyApplication;
    /**
     * jdhSkuPoMapper
     */
    @Resource
    private JdhSkuPoMapper jdhSkuPoMapper;

    /**
     * angelWorkApplication
     */
    @Autowired
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private CallRecordApplication callRecordApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @Resource
    private RiskAssessmentApplication riskAssessmentApplication;

    @Resource
    private JdhFileRepository jdhFileRepository;

    @Resource
    private FileManageService fileManageService;

    @Resource
    private AngelLocationApplication angelLocationApplication;

    @Resource
    private PromiseApplication promiseApplication;

    @Resource
    private JdhAngelPoMapper jdhAngelPoMapper;

    @Resource
    private AngelMainReadApplication angelMainReadApplication;


    /**
     * 检测单展示待送检的promsie状态
     */
    private static List<Integer> MEDICAL_WAIT_TOLAB_STATUS_LIST = Arrays.asList(JdhPromiseStatusEnum.COMPLETE.getStatus(),JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus());


    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx) {
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        AssertUtils.hasText(ctx.getOrderId(), SupportErrorCode.VIA_ORDER_ID_NOT_EXIT);
    }

    /**
     * 步骤条
     * @param viaFloorInfo
     * @param statusMapping
     * @param sourceData
     */
    private void handleStepGuideInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, Map<String, Object> sourceData, RiskAssessmentDetailManDTO riskAssessmentDetail) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleStepGuideInfo start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        for (ViaFloorConfig viaFloorConfig : floorConfigList) {
            String stepCode = viaFloorConfig.getStepCode();
            //完成
            if (statusMapping.getStepGuideFinishCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.FINISH.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideFinishIcon());
            }
            //进行中
            if (statusMapping.getStepGuideProcessCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.PROCESS.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideProcessIcon());
            }
            //等待
            if (statusMapping.getStepGuideWaitCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.WAIT.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideWaitIcon());
            }
        }

        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            // 非高危风险服务项目移除风险评估节点

            if (ViaStepGuideEnum.RISK_ASSESSMENT.getCode().equals(viaFloorConfig.getStepCode()) &&
                    (BooleanUtil.isFalse((Boolean) sourceData.get("highRiskAssessmentFlag")) || Objects.isNull(riskAssessmentDetail))){
                iterator.remove();
                continue;
            }
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handleStepGuideInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 获取动态字段值
     *
     * @param dynamicFieldList 动态字段列表
     * @return {@link Map}<{@link String},{@link String}>
     */
    @SuppressWarnings("JdJDClassCast")
    private String formatDynamicField(Map<String, Object> sourceData,
                                      List<String> dynamicFieldList, String expression) {
        if (CollUtil.isEmpty(dynamicFieldList)) {
            return expression;
        }
        Map<String, String> result = new HashMap<>(dynamicFieldList.size());
        for (String field : dynamicFieldList) {
            // 退款金额
            if (ViaDynamicFieldEnum.REFUND_AMOUNT.getField().equals(field)) {
                JdOrderDTO jdOrder = (JdOrderDTO) sourceData.get(TradeAggregateEnum.ORDER.getCode());
                String refundAmount = Objects.isNull(jdOrder.getRefundAmount()) ? "" : jdOrder.getRefundAmount().toPlainString();
                result.put(ViaDynamicFieldEnum.REFUND_AMOUNT.getField(), refundAmount);
            }
            // 订单支付过期时间戳
            if (ViaDynamicFieldEnum.PAY_EXPIRE_TIME.getField().equals(field)) {
                JdOrderDTO jdOrder = (JdOrderDTO) sourceData.get(TradeAggregateEnum.ORDER.getCode());
                long expireTime = jdOrder.getCreateTime().getTime() + PAY_EXPIRE_TIME;
                result.put(ViaDynamicFieldEnum.PAY_EXPIRE_TIME.getField(), String.valueOf(expireTime));
            }
        }
        expression = StrUtil.format(expression, result);
        log.info("HomeAngelCareOrderDetailHandlerV2 -> formatDynamicField expression:{}", expression);
        return expression;
    }

    /**
     * 处理摘要信息
     * @param ctx
     * @param viaFloorInfo
     * @param statusMapping
     * @param jdhPromise
     * @param jdhSkuDto
     * @param sourceData
     */
    private void handleSummaryInfo(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo,
                                   ViaStatusMapping statusMapping, JdhPromise jdhPromise,
                                   JdhSkuDto jdhSkuDto, Map<String, Object> sourceData,
                                   List<JdhPromiseHistory> promiseHistories) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleSummaryInfo start");
        List<ViaFloorConfig> floorConfig = new ArrayList<>();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setMainIcon(statusMapping.getMainIcon());
        // 获取表达式配置value配置的容器
        Map<String, Object> containerMap = statusMapping.getContainer();

        // todo 临时添加，修改预约时间不走promise预测
        if (CollectionUtils.isNotEmpty(promiseHistories)){
            boolean userSubmitModifyFlag = promiseHistories.stream().anyMatch(p -> NumConstant.NUM_5.equals(p.getAfterStatus()) && "userSubmitModify".equals(p.getEventCode()));
            containerMap.put("userSubmitModifyFlag", userSubmitModifyFlag);
        }

        // 从promiseGo获取相关预测数据
        try {
            String aggregateStatus = statusMapping.getAggregateStatus();
            if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)) {
                UserPromisegoBo userPromisegoBo = this.queryUserPromiseGoInfo(jdhPromise, aggregateStatus);
                log.info("HomeAngelCareOrderDetailHandlerV2 handleSummaryInfo userPromisegoBo={}", JSON.toJSONString(userPromisegoBo));
                if (Objects.nonNull(userPromisegoBo)){
                    if (Objects.nonNull(userPromisegoBo.getCurrScript())){
                        containerMap.put("currScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getCurrScript(), ScriptBo::getScriptContent));
                    }
                    if (Objects.nonNull(userPromisegoBo.getTermScript())){
                        containerMap.put("termScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getTermScript(), ScriptBo::getScriptContent));
                    }
                    if (Objects.nonNull(userPromisegoBo.getWarmTipScript())){
                        containerMap.put("warmTipScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getWarmTipScript(), ScriptBo::getScriptContent));
                    }
                }
            }

        } catch (Exception e) {
            log.error("HomeAngelCareOrderDetailHandlerV2 -> handleSummaryInfo error", e);
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handleSummaryInfo containerMap={}", JSON.toJSONString(containerMap));

        // 解析出mainTile
        String mainTitle = statusMapping.getMainTitle();
        if (StringUtils.isNotBlank(mainTitle)){
            Expression expression = AviatorEvaluator.compile(mainTitle, true);
            Object res = expression.execute(containerMap);
            mainTitle = Objects.toString(res, "");
        }

        // 解析出二级标题
        String title = statusMapping.getTitle();
        if (StringUtils.isNotBlank(title)) {
            Expression expression = AviatorEvaluator.compile(title, true);
            Object res = expression.execute(containerMap);
            title = Objects.toString(res, "");
        }

        // 解析出异常兜底文案
        String warmTip = statusMapping.getWarmTip();
        if (StringUtils.isNotBlank(warmTip)) {
            Expression exception = AviatorEvaluator.compile(warmTip, true);
            Object res = exception.execute(containerMap);
            warmTip = Objects.toString(res, "");
        }

        // 附加提示信息
        String noticeTip = statusMapping.getNoticeTip();
        if (StringUtils.isNotBlank(noticeTip)) {
            Expression exception = AviatorEvaluator.compile(noticeTip, true);
            Object res = exception.execute(containerMap);
            noticeTip = Objects.toString(res, "");
        }

        // 填充变量"退款金额<span id='appointTitleId'>{refundAmount}</span>元";当前状态不是promiseGo返回的title时，需要手动填充变量
        mainTitle = formatDynamicField(sourceData, statusMapping.getDynamicField(), mainTitle);
        title = formatDynamicField(sourceData, statusMapping.getDynamicField(), title);

        viaFloorConfig.setMainTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), mainTitle));
        viaFloorConfig.setTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), title));
        viaFloorConfig.setWarmTip(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), warmTip));
        viaFloorConfig.setNoticeTip(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), noticeTip));
        floorConfig.add(viaFloorConfig);
        viaFloorInfo.setFloorConfigList(floorConfig);
        log.info("HomeAngelCareOrderDetailHandlerV2 handleSummaryInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 履约信息 - 服务者
     * @param ctx
     * @param viaFloorInfo
     * @param statusMapping
     * @param jdhPromise
     * @param jdhSku
     * @param angelWork
     * @param sourceData
     */
    private void handlePromiseAngelInfo(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo,
                                        ViaStatusMapping statusMapping, JdhPromise jdhPromise,
                                        JdhSkuDto jdhSku, AngelWorkDetailDto angelWork,
                                        Map<String, Object> sourceData) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseAngelInfo start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        log.info("HomeTestOrderDetailHandlerV2 handlePromiseAngelInfo angelWork:{}", JSON.toJSONString(angelWork));
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            // 楼层属性处理
            if (!statusMapping.supportFiled(ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode(), viaFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }

            //平台认证
            if (ViaAngelInfoFieldEnum.ANGEL_HEAD_LABEL.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    //查询服务者主数据
                    JdhAngelRepQuery query = new JdhAngelRepQuery();
                    query.setAngelId(Long.valueOf(angelWork.getAngelId()));
                    JdhAngel jdhAngel = angelRepository.queryAngelWithProfession(query);

                    if(!this.angelAuthenticationTag(jdhAngel)){
                        iterator.remove();;
                    }
                }
                continue;
            }


            // 姓名
            if (ViaAngelInfoFieldEnum.ANGEL_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    String angelName = angelWork.getAngelName();
                    String angelType = Objects.requireNonNull(AngelWorkTypeEnum.matchType(jdhVerticalBusiness.getBusinessModeCode())).getAngelType();
                    angelType = productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), angelType);
                    viaFloorConfig.setFieldValue(MessageFormat.format(viaFloorConfig.getFieldValue(), angelName.charAt(0) + angelType));
                }
                continue;
            }

            // 头像
            if (ViaAngelInfoFieldEnum.HEAD_IMG.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelWork)){
                    iterator.remove();
                }else {
                    viaFloorConfig.setFieldValue(angelWork.getAngelHeadImg());


                    LambdaQueryWrapper<JdhAngelPo> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(JdhAngelPo::getAngelId, angelWork.getAngelId());
                    queryWrapper.eq(JdhAngelPo::getYn, YnStatusEnum.YES.getCode());
                    JdhAngelPo jdhAngelPo = jdhAngelPoMapper.selectOne(queryWrapper);
                    if(jdhAngelPo==null){
                        viaFloorConfig.setFieldValue(angelWork.getAngelHeadImg());
                    }else{
                        viaFloorConfig.setFieldValue(jdhAngelPo.getGender()==1?"https://img12.360buyimg.com/imagetools/jfs/t1/228512/34/5324/24330/65682a85F1acd24e0/be9495b716e612f3.png":"https://img12.360buyimg.com/imagetools/jfs/t1/226153/36/5693/24313/65682addFdf0d85da/4221c67c0bf38b9f.png");
                    }
                    viaFloorConfig.setTargetUrl(String.format(viaFloorConfig.getTargetUrl(),jdhPromise.getSourceVoucherId(),jdhPromise.getPromiseId(),angelWork.getAngelId()));


                }
                continue;
            }

            // 联系人
            if(ViaAngelInfoFieldEnum.CONTACT_ANGEL.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    if (Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType())
                            .contains(angelWork.getStatus())){
                        ViaActionInfo action = viaFloorConfig.getAction();
                        action.init(sourceData);
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }
        }
        log.info("HomeTestOrderDetailHandlerV2 handlePromiseAngelInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 服务者是否认证
     *
     * @param jdhAngel
     * @return
     */
    private boolean angelAuthenticationTag(JdhAngel jdhAngel) {
        if(CollectionUtils.isEmpty(jdhAngel.getJdhAngelProfessionRelList())) {
            return false;
        }

        JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
        if(AngelProfessionCodeEnum.SMHS.getCode().equals(Integer.valueOf(jdhAngelProfessionRel.getProfessionCode()))) {
            return StringUtils.isNotBlank(jdhAngel.getCertificateIssuingAuthority())
                    && StringUtils.isNotBlank(jdhAngel.getCertificateNo());
        }else if(AngelProfessionCodeEnum.SMKFS.getCode().equals(Integer.valueOf(jdhAngelProfessionRel.getProfessionCode()))) {
            return StringUtils.isNotBlank(jdhAngel.getCertificateNo())
                    && StringUtils.isNotBlank(jdhAngel.getGrade())
                    && StringUtils.isNotBlank(jdhAngel.getSpeciality());
        }
        return false;
    }

    /**
     * @param viaFloorInfo     通用楼层信息
     * @param statusMapping    状态映射
     * @param jdhPromise       jdhPromise
     */
    private void handlePromiseAngelInfoMergeMapInfo(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo,
                                                    ViaStatusMapping statusMapping, JdhPromise jdhPromise,
                                                    JdOrderDTO jdOrder, Map<String, Object> sourceData,
                                                    JdhSkuDto jdhSku, AngelWorkDetailDto angelWork) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseAngelInfoMergeMapInfo start");
        // 查询护士信息
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();

            // 处理按钮
            if (CollectionUtils.isNotEmpty(viaFloorConfig.getBtnList())) {
                Iterator<ViaBtnInfo> btnInfoIterator = viaFloorConfig.getBtnList().iterator();
                while (btnInfoIterator.hasNext()) {
                    ViaBtnInfo btnInfo = btnInfoIterator.next();
                    // 不支持这个按钮则不初始化返回
                    if (!statusMapping.supportFiled(ViaFloorEnum.PROMISE_ANGEL_MERGE_MAP_INFO.getFloorCode(), btnInfo.getCode())) {
                        btnInfoIterator.remove();
                        continue;
                    }
                    // 联系服务者按钮
                    if (ViaAngelInfoBtnEnum.CONTACT_ANGEL.getBtn().equals(btnInfo.getCode())) {
                        ViaActionInfo action = btnInfo.getAction();
                        Map<String, Object> params = Maps.newHashMap();
                        params.put(ViaAngelInfoFieldEnum.ANGEL_PHONE.getField(), angelWork.getAngelPhone());
                        action.setParams(params);
                    } else if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
                        buildContactCustomerBtn(btnInfo, ctx, jdOrder, btnInfo.getAction());
                    }
                }
                continue;
            }

            // 楼层属性处理
            if (!statusMapping.supportFiled(ViaFloorEnum.PROMISE_ANGEL_MERGE_MAP_INFO.getFloorCode(), viaFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }

            if (ViaAngelInfoFieldEnum.ANGEL_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    String angelName = angelWork.getAngelName();
                    String angelType = Objects.requireNonNull(AngelWorkTypeEnum.matchType(jdhVerticalBusiness.getBusinessModeCode())).getAngelType();
                    angelType = productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), angelType);
                    viaFloorConfig.setFieldValue(new UserName(angelName).mask() + "-" + angelType + "为您提供服务");
                }
                continue;
            }

            if (ViaAngelInfoFieldEnum.HEAD_IMG.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.nonNull(angelWork)) {
                    viaFloorConfig.setFieldValue(angelWork.getAngelHeadImg());
                } else {
                    iterator.remove();
                }
                continue;
            }

            if(ViaAngelInfoFieldEnum.CONTACT_ANGEL.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    if (Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType())
                            .contains(angelWork.getStatus())){
                        ViaActionInfo action = viaFloorConfig.getAction();
                        if (action != null){
                            action.getParams().put("promiseId",angelWork.getPromiseId());
                        }
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }

            if (ViaAngelInfoFieldEnum.ANGEL_MAP_INFO.getField().equals(viaFloorConfig.getFieldKey())) {
                ViaActionInfo action = viaFloorConfig.getAction();
                action.init(sourceData);
                continue;
            }
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseAngelInfoMergeMapInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 处理楼层列表
     *
     * @param ctx                上下文
     * @param statusMapping      状态映射
     * @param jdOrder            JD订单
     * @param jdhPromise         jdhPromise
     * @param medicalPromiseList medicalPromiseList
     * @param promiseHistories   promiseHistories
     */
    private void dealFloorList(FillViaConfigDataContext ctx, ViaStatusMapping statusMapping,
                               JdOrderDTO jdOrder, JdhPromise jdhPromise,
                               AngelWorkDetailDto angelWorkDetail, List<JdhPromiseHistory> promiseHistories,
                               List<MedicalPromiseDTO> medicalPromiseList, JdhSkuDto jdhSkuDto,
                               List<AngelTask> angelTaskList, RiskAssessmentDetailManDTO riskAssessmentDetail,
                               Boolean highRiskAssessmentFlag, Boolean middleRiskAssessmentFlag) {
        ViaConfig viaConfig = ctx.getViaConfig();
        String userPin = ctx.getUserPin();
        // 填充模版数据
        Map<String, Object> sourceData = Maps.newHashMap();
        sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
        sourceData.put(TradeAggregateEnum.ORDER.getCode(), jdOrder);
        sourceData.put("statusMapping", statusMapping);
        sourceData.put("ctx", ctx);
        sourceData.put("highRiskAssessmentFlag", highRiskAssessmentFlag);
        sourceData.put("middleRiskAssessmentFlag", middleRiskAssessmentFlag);
        sourceData.put("nurseSheetFlag", checkNurseSheetFlag(angelWorkDetail));
        log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList sourceData={}", JSON.toJSONString(sourceData));

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : viaConfig.getFloorList()) {
            //概要 summaryInfo
            if (ViaFloorEnum.PROMISE_SUMMARY_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleSummaryInfo(ctx, viaFloorInfo, statusMapping, jdhPromise, jdhSkuDto, sourceData, promiseHistories), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handleSummaryInfo exception", exception);
                    return null;
                }));
            }

            //步骤条 stepGuideInfo
            if (ViaFloorEnum.PROMISE_STEP_GUIDE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleStepGuideInfo(viaFloorInfo, statusMapping, sourceData, riskAssessmentDetail), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handleStepGuideInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 服务者 promiseAngelInfo
            if (ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseAngelInfo(ctx, viaFloorInfo, statusMapping, jdhPromise, jdhSkuDto, angelWorkDetail, sourceData), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handlePromiseAngelInfo exception", exception);
                    return null;
                }));
            }

            //地图信息 promiseMapInfo
            if (ViaFloorEnum.PROMISE_MAP_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseMapInfo(viaFloorInfo, sourceData), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handlePromiseMapInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 消费码 promiseCodeInfo
            if (ViaFloorEnum.PROMISE_CODE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseCodeInfo(ctx, viaFloorInfo, jdhPromise, jdhSkuDto), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handlePromiseCodeInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 被服务者 promisePatientInfo
            if(ViaFloorEnum.PROMISE_PATIENT_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handelPromisePatientInfo(viaFloorInfo, jdOrder, medicalPromiseList, jdhPromise, sourceData, angelTaskList, riskAssessmentDetail),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handelPromisePatientInfo exception",exception);
                    return null;
                }));
            }

            //购买商品信息 orderSkuInfo
            if (ViaFloorEnum.PROMISE_ORDER_SKU_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderSkuInfo(viaConfig, viaFloorInfo, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handelOrderSkuInfo exception", exception);
                    return null;
                }));
            }

            //企微卡片信息 weChatCardInfo
            if (ViaFloorEnum.PROMISE_WECHAT_CARD_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelWeChatCardInfo(viaFloorInfo, jdOrder, userPin), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handelWeChatCardInfo exception", exception);
                    return null;
                }));
            }

            //订单信息 orderInfo
            if (ViaFloorEnum.PROMISE_ORDER_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderInfo(viaFloorInfo, jdOrder, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handelOrderInfo exception", exception);
                    return null;
                }));
            }

            //订单信息与购买商品信息聚合楼层 orderInfoMergeSkuInfo
/*            if (ViaFloorEnum.PROMISE_ORDER_MERGE_SKU_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleOrderInfoMergeSkuInfo(viaConfig, viaFloorInfo, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList orderInfoMergeSkuInfo exception", exception);
                    return null;
                }));
            }*/

            //底部按钮 footerButtons
            if (ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterButtons(ctx, viaFloorInfo, statusMapping, jdOrder, jdhPromise, medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }

            //用户反馈 userFeedback
            if (ViaFloorEnum.PROMISE_USER_FEEDBACK_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleUserFeedbackInfo(viaFloorInfo, jdhPromise, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handleUserFeedbackInfo exception", exception);
                    return null;
                }));
            }

            //就医证明 medicalCertificate
            if(ViaFloorEnum.PROMISE_MEDICAL_CERTIFICATE.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handleMedicalCertificate(viaFloorInfo, jdhPromise, jdOrder, riskAssessmentDetail, jdhSkuDto, sourceData),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleMedicalCertificate exception",exception);
                    return null;
                }));
            }

            //信息评估 riskAssessment
            if (ViaFloorEnum.PROMISE_RISK_ASSESSMENT.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleRiskAssessment(viaFloorInfo, riskAssessmentDetail), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handleInfoEvaluate exception", exception);
                    return null;
                }));
            }

            //预约信息 appointmentInfo
            if (ViaFloorEnum.PROMISE_APPOINTMENT_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleAppointmentInfo(ctx, viaFloorInfo, jdhPromise, statusMapping, jdhSkuDto), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeAngelCareOrderDetailHandlerV2 dealFloorList handleAppointmentInfo exception", exception);
                    return null;
                }));
            }

            //处理楼层数据
            ViaFloorEnum viaFloorEnum = ViaFloorEnum.getByFloorCode(viaFloorInfo.getFloorCode());
            if(viaFloorEnum!=null&&StringUtils.isNotEmpty(viaFloorEnum.getBeanName())){
                if(ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                    //护理promiseAngelInfo楼层 和 检测类promiseAngelInfo楼层 返回的数据结构不一致;使用旧的写法,不走beanName逻辑
                    continue;
                }
                Floor floor = SpringUtil.getBean(viaFloorEnum.getBeanName());
                futures.add(CompletableFuture.runAsync(() -> floor.handleData(ctx,sourceData,viaFloorInfo,statusMapping,jdhPromise,jdOrder,promiseHistories,medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV3 dealFloorList {} exception",viaFloorEnum.getFloorCode(), exception);
                    return null;
                }));
            }
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 校验是否存在电子护理单
     * @param angelWorkDetail
     * @return
     */
    private Boolean checkNurseSheetFlag(AngelWorkDetailDto angelWorkDetail) {
        if (Objects.isNull(angelWorkDetail)){
            return false;
        }
        AngelServiceRecordQuery serviceRecordConfigQuery = AngelServiceRecordQuery.builder().workId(angelWorkDetail.getWorkId()).build();
        // 校验是否开启了护理单配置
        return angelServiceRecordApplication.checkAngelServiceRecordConfig(serviceRecordConfigQuery);
    }


    /**
     * 订单信息
     * @param viaFloorInfo
     * @param jdOrder
     */
    private void handelOrderInfo(ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder, JdhPromise jdhPromise) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handelOrderInfo start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();

        // 获取订单预约信息
        OrderAppointmentInfoValueObject vo = null;
        JdOrderExtDTO appointmentDto = jdOrder.getJdOrderExtList().stream()
                .filter(e -> StringUtils.equals(e.getExtType(), OrderExtTypeEnum.APPOINTMENT_INFO.getType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(appointmentDto)) {
            String json = appointmentDto.getExtContext();
            vo = JSON.parseObject(json, OrderAppointmentInfoValueObject.class);
        }

        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            // 订单号
            if (ViaOrderInfoFieldEnum.ORDER_ID.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderId())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderId().toString());
                }
                continue;
            }

            // 实付款
            if (ViaOrderInfoFieldEnum.ORDER_AMOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderAmount())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderAmount().toPlainString());
                    List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = jdOrder.getJdOrderServiceFeeInfos();
                    viaFloorConfig.setValue(JSON.toJSONString(jdOrderServiceFeeInfos));
                }
                continue;
            }

            // 下单时间
            if (ViaOrderInfoFieldEnum.ORDER_CREATE_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getCreateTime())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(jdOrder.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));
                }
                continue;
            }
//            if (ViaOrderInfoFieldEnum.PAY_TYPE_DESC.getField().equals(viaFloorConfig.getFieldKey())) {
//                if (Objects.isNull(jdOrder.getPayType())) {
//                    iterator.remove();
//                } else {
//                    viaFloorConfig.setFieldValue(PayTypeEnum.getDescOfType(jdOrder.getPayType().toString()));
//                }
//                continue;
//            }
//            if (ViaOrderInfoFieldEnum.ORDER_USER_PHONE.getField().equals(viaFloorConfig.getFieldKey())) {
//                if (Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getMobile())) {
//                    iterator.remove();
//                } else {
//                    viaFloorConfig.setFieldValue(new PhoneNumber(jdOrder.getAddressInfo().getMobile()).mask());
//                }
//                continue;
//            }

            // 被服务人
            if (ViaOrderInfoFieldEnum.ORDER_USER_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                List<String> patientNames = vo.getPatients().stream().map(e -> UserName.maskTool(e.getName())).collect(Collectors.toList());
                String name = String.join(",", patientNames);
                viaFloorConfig.setFieldValue(name);
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }

            // 上门地址
            if (ViaOrderInfoFieldEnum.ADDRESS_DETAIL.getField().equals(viaFloorConfig.getFieldKey())) {
                viaFloorConfig.setFieldValue(vo.getAddressInfo().getFullAddress());
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }

            // 备注
            if (ViaOrderInfoFieldEnum.ORDER_REMARK.getField().equals(viaFloorConfig.getFieldKey())) {
                if (StrUtil.isEmpty(jdOrder.getRemark())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getRemark());
                }
                continue;
            }

            // 优惠金额
            if (ViaOrderInfoFieldEnum.ORDER_DISCOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderDiscount()) || jdOrder.getOrderDiscount().compareTo(new BigDecimal("0")) <= 0) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderDiscount().toString());
                }
                continue;
            }

            // 优惠券金额
            if (ViaOrderInfoFieldEnum.ORDER_COUPON.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderCoupon()) || jdOrder.getOrderCoupon().compareTo(new BigDecimal("0")) <= 0) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderCoupon().toString());
                }
                continue;
            }

            // 预约时间（期望时间）
            if (ViaOrderInfoFieldEnum.APPOINTMENT_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                //优先判断是否存在履约单，是否存在履约单预约时间，如果有优先取履约单上的预约时间
                if (Objects.nonNull(jdhPromise) && Objects.nonNull(jdhPromise.getAppointmentTime())) {
                    OrderAppointmentTimeValueObject appointmentTime = new OrderAppointmentTimeValueObject();
                    appointmentTime.setDateType(jdhPromise.getAppointmentTime().getDateType());
                    appointmentTime.setIsImmediately(jdhPromise.getAppointmentTime().getIsImmediately());
                    appointmentTime.setAppointmentStartTime(jdhPromise.getAppointmentTime().formatAppointmentStartTime());
                    appointmentTime.setAppointmentEndTime(jdhPromise.getAppointmentTime().formatAppointmentEndTime());

                    if (Objects.nonNull(appointmentTime.getIsImmediately()) && appointmentTime.getIsImmediately()) {
                        viaFloorConfig.setFieldValue("立即预约");
                    } else {
                        // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                        String time = appointmentTime.formatAppointTimeDesc();
                        viaFloorConfig.setFieldValue(time);
                    }
                    continue;
                }
                OrderAppointmentTimeValueObject appointmentTime = vo.getAppointmentTime();
                if (Objects.nonNull(appointmentTime.getIsImmediately()) && appointmentTime.getIsImmediately()) {
                    viaFloorConfig.setFieldValue("立即预约");
                } else {
                    // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                    String time = appointmentTime.formatAppointTimeDesc();
                    viaFloorConfig.setFieldValue(time);
                }
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }

        }
    }

    /**
     * 履约信息 - 被服务者
     * @param viaFloorInfo
     * @param jdOrder
     * @param medicalPromiseList
     * @param jdhPromise
     * @param sourceData
     * @param angelTaskList
     * @param riskAssessmentDetail
     */
    private void handelPromisePatientInfo(ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder,
                                          List<MedicalPromiseDTO> medicalPromiseList,
                                          JdhPromise jdhPromise, Map<String, Object> sourceData,
                                          List<AngelTask> angelTaskList,
                                          RiskAssessmentDetailManDTO riskAssessmentDetail) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handelPromisePatientInfo start");
        List<ViaFloorConfig> floorConfigList = Lists.newArrayList();

        if (Objects.nonNull(jdhPromise)){
            log.info("HomeAngelCareOrderDetailHandlerV2 handelPromisePatientInfo jdhPromise");
            List<JdhPromisePatient> patientList = jdhPromise.getPatients();
            Map<Long, List<MedicalPromiseDTO>> patientIdMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPatientId));

            for (JdhPromisePatient patient : patientList) {
                ViaFloorConfig config = new ViaFloorConfig();
                List<ViaFormItem> formItemList = Lists.newArrayList();
                // 姓名
                ViaFormItem nameItem = new ViaFormItem();
                nameItem.setFormName(ViaSpecimenFieldEnum.PATIENT_NAME.getField());
                nameItem.setValue(new UserName(patient.getUserName().getName()).mask());
                formItemList.add(nameItem);

                // 性别
                ViaFormItem genderItem = new ViaFormItem();
                genderItem.setFormName(ViaSpecimenFieldEnum.PATIENT_GENDER.getField());
                genderItem.setValue(GenderEnum.getDescOfType(patient.getGender()));
                formItemList.add(genderItem);

                // 年龄
                ViaFormItem ageItem = new ViaFormItem();
                ageItem.setFormName(ViaSpecimenFieldEnum.PATIENT_AGE.getField());
                ageItem.setValue(Objects.toString(patient.getBirthday().getAge() + "岁", null));
                formItemList.add(ageItem);

                // 头像
                ViaFormItem headerImageItem = new ViaFormItem();
                headerImageItem.setFormName(ViaSpecimenFieldEnum.PATIENT_HEADER_IMAGE.getField());
                headerImageItem.setValue(viaComponentDomainService.queryPatientHeadImage(patient.getGender(), patient.getBirthday().getAge()));
                formItemList.add(headerImageItem);

                Map<String, Object> btnParam = new HashMap<>();
                btnParam.put("nurseSheetFlag", sourceData.get("nurseSheetFlag"));
                btnParam.put("orderStatus", Objects.isNull(jdOrder) ? null : jdOrder.getOrderStatus());
                btnParam.put("promiseStatus", Objects.isNull(jdhPromise) ? null : jdhPromise.getPromiseStatus());

                // 风险评估
                if (Objects.nonNull(riskAssessmentDetail)){
                    List<RiskAssUserDetailDTO> riskAssUserDetailList = riskAssessmentDetail.getRiskAssUserDetailDTOS().stream().filter(r-> patient.getPromisePatientId().equals(r.getPromisePatientId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(riskAssUserDetailList)){
                        boolean bool = riskAssUserDetailList.stream().anyMatch(r -> Arrays.asList(RiskAssessmentStatusEnum.PASS.getStatus(), RiskAssessmentStatusEnum.REFUSE.getStatus(), RiskAssessmentStatusEnum.INVALID.getStatus())
                                .contains(r.getRiskAssessmentStatus()));
                        if (bool){
                            btnParam.put("riskAssessmentStatus", RiskAssessmentStatusEnum.PASS.getStatus());// 评估完成
                        }else {
                            btnParam.put("riskAssessmentStatus", RiskAssessmentStatusEnum.WAITING_ASS.getStatus());// 待评估

                        }
                    }
                }

                // 护理单
                if (CollectionUtils.isNotEmpty(angelTaskList)){
                    Map<String, AngelTask> promisePatientIdAngelTaskMap = angelTaskList.stream().collect(Collectors.toMap(AngelTask::getPatientId, Function.identity(), (key1, key2) -> key2));
                    AngelTask angelTask = promisePatientIdAngelTaskMap.get(String.valueOf(patient.getPromisePatientId()));
                    if (Objects.nonNull(angelTask)){
                        AngelServiceRecord angelServiceRecord = angelServiceRecordApplication.queryAngelServiceRecordByTaskId(angelTask.getTaskId());
                        if (Objects.nonNull(angelServiceRecord)){
                            btnParam.put("serviceRecordStatus", angelServiceRecord.getStatus());
                        }
                    }
                }

                // 退款标识
                boolean hasInvalid = false;
                List<MedicalPromiseDTO> medicalPromiseDTOS = patientIdMap.get(patient.getPatientId());
                if (CollectionUtils.isNotEmpty(medicalPromiseDTOS)){
                    hasInvalid = medicalPromiseDTOS.stream().anyMatch(m -> MedicalPromiseStatusEnum.INVALID.getStatus().equals(m.getStatus()));
                }
                btnParam.put("hasInvalid", hasInvalid);
                log.info("HomeAngelCareOrderDetailHandlerV2 handelPromisePatientInfo btnParam={}", JSON.toJSONString(btnParam));

                // 评估报告||电子护理单操作按钮
                List<ViaBtnInfo> btnList = Lists.newArrayList();
                List<ViaBtnInfo> btnConfigList = viaFloorInfo.getFloorConfigList().get(0).getBtnList();
                for (ViaBtnInfo btnConfig : btnConfigList) {
                    if ((Boolean) AviatorEvaluator.compile(btnConfig.getStatusExpression(), Boolean.TRUE).execute(btnParam)){
                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put("promisePatient", patient);
                        dataMap.put("jdOrder", jdOrder);
                        if (CollectionUtils.isNotEmpty(angelTaskList)){
                            List<AngelTask> angelTasks = angelTaskList.stream().filter(t -> t.getPatientId().equals(String.valueOf(patient.getPromisePatientId()))).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(angelTasks)){
                                dataMap.put("angelTask", angelTasks.get(0));
                            }}
                        ViaBtnInfo btn = JSON.parseObject(JSON.toJSONString(btnConfig), ViaBtnInfo.class);
                        btn.init(dataMap);
                        btnList.add(btn);
                        break;
                    }
                }
                config.setFormItemList(formItemList);
                config.setBtnList(btnList);
                floorConfigList.add(config);
            }
            viaFloorInfo.setFloorConfigList(floorConfigList);

        } else {// 待支付场景
            JdOrderExtDTO appointmentDto = jdOrder.getJdOrderExtList().stream().filter(e -> StringUtils.equals(e.getExtType(), OrderExtTypeEnum.APPOINTMENT_INFO.getType())).findFirst().orElse(null);
            log.info("HomeCareOrderDetailHandlerV2 handelPromisePatientInfo appointmentDto={}", JSON.toJSONString(appointmentDto));
            if (Objects.isNull(appointmentDto)){
                return;
            }
            // 获取订单预约信息
            OrderAppointmentInfoValueObject appointmentInfo = JSON.parseObject(appointmentDto.getExtContext(), OrderAppointmentInfoValueObject.class);
            // 预约人
            Map<Long, Patient> patientMap = appointmentInfo.getPatients().stream().collect(Collectors.toMap(Patient::getPatientId, Function.identity(), (key1, key2) -> key2));
            patientMap.forEach((patientId, patient) -> {
                ViaFloorConfig config = new ViaFloorConfig();
                List<ViaFormItem> formItemList = Lists.newArrayList();
                // 姓名
                ViaFormItem nameItem = new ViaFormItem();
                nameItem.setFormName(ViaSpecimenFieldEnum.PATIENT_NAME.getField());
                nameItem.setValue(patient.getName());
                formItemList.add(nameItem);

                // 性别
                ViaFormItem genderItem = new ViaFormItem();
                genderItem.setFormName(ViaSpecimenFieldEnum.PATIENT_GENDER.getField());
                genderItem.setValue(GenderEnum.getDescOfType(patient.getGender()));
                formItemList.add(genderItem);

                // 年龄
                ViaFormItem ageItem = new ViaFormItem();
                ageItem.setFormName(ViaSpecimenFieldEnum.PATIENT_AGE.getField());
                ageItem.setValue(Objects.toString(patient.getAgeStr(), null));
                formItemList.add(ageItem);

                // 头像
                ViaFormItem headerImageItem = new ViaFormItem();
                headerImageItem.setFormName(ViaSpecimenFieldEnum.PATIENT_HEADER_IMAGE.getField());
                headerImageItem.setValue(viaComponentDomainService.queryPatientHeadImage(patient.getGender(), patient.getAge()));
                formItemList.add(headerImageItem);

                List<ViaBtnInfo> btnList = Lists.newArrayList();
                config.setFormItemList(formItemList);
                config.setBtnList(btnList);
                floorConfigList.add(config);
            });
            viaFloorInfo.setFloorConfigList(floorConfigList);
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handelPromisePatientInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取完整预约日期描述
     *
     * @param appointmentTime 预约时间
     * @return {@link String}
     */
    private String getFullAppointmentDateDesc(DomainAppointmentTime appointmentTime){
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        String dateDesc = " ";
        if(DateUtil.isSameDay(new Date(), TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))){
            dateDesc = "[今天]";
        }
        if(DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))){
            dateDesc = "[明天]";
        }
        return appointmentTime.formatAppointDate() +
                dateDesc +
                appointmentTime.formatAppointTimeDesc();
    }


    private JdhSkuDto findMainSku(JdhPromise jdhPromise) {
        // 多个商品取主品的采样教程
        List<Long> skuIds = jdhPromise.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toList());
        LambdaQueryWrapper<JdhSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhSkuPo::getSkuId, skuIds).eq(JdhSkuPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSkuPo::getCreateTime);
        List<JdhSkuPo> skuPos = jdhSkuPoMapper.selectList(queryWrapper);
        Optional<JdhSkuPo> skuPo = skuPos.stream().filter(e -> Objects.equals(e.getSkuType(), SkuRelTypeEnum.MAIN_ITEM.getType())).findFirst();
        JdhSkuRequest request = new JdhSkuRequest();
        if (!skuPo.isPresent()) {
            request.setSkuId(jdhPromise.findBasicService().getServiceId());
        } else {
            request.setSkuId(skuPo.get().getSkuId());
        }
        JdhSkuDto skuDto = productApplication.queryAggregationJdhSkuInfo(request);
        return skuDto;
    }

    /**
     * 处理检测方法科普楼层
     */
    private void handelScienceKnowledge(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handelScienceKnowledge start");
        List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
        JdhSkuDto skuDto = findMainSku(jdhPromise);
        if (StringUtils.isNotBlank(skuDto.getTutorialMethodUrl())){
            ViaFloorConfig header = new ViaFloorConfig();
            header.setFieldKey("tutorialMethodUrl");
            header.setFieldValue(skuDto.getTutorialMethodUrl());

            // 跳转链接
            ViaActionInfo action = new ViaActionInfo();
            action.setType(ActionType.JUMP.getCode());
            action.setUrl(skuDto.getTutorialMethodJumpUrl());
            header.setAction(action);
            viaFloorConfigList.add(header);
        }
        viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        log.info("HomeAngelCareOrderDetailHandlerV2 handelScienceKnowledge viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 购买商品信息
     * @param viaConfig
     * @param viaFloorInfo
     * @param jdOrder
     */
    private void handelOrderSkuInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handelOrderSkuInfo start");
        List<JdOrderItemDTO> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();

        for (JdOrderItemDTO jdOrderItemDTO : jdOrderItemList) {
            // action
            ViaActionInfo action = new ViaActionInfo();
            action.setUrl(MessageFormat.format(viaConfig.getSkuDetailUrl(), jdOrderItemDTO.getSkuId().toString()));
            action.setType(ActionType.JUMP.getCode());
            //如果是加项商品，跳转url为空，即在订详无法跳转
            if (IsAddedEnum.IS_ADDED.getValue().equals(jdOrderItemDTO.getIsAdded())) {
                action.setUrl(null);
            }
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(jdOrderItemDTO)).action(action).build());
        }
        viaFloorInfo.setFloorConfigList(floorConfigList);
    }

    /**
     * 订单信息与购买商品信息聚合楼层
     * @param viaConfig
     * @param viaFloorInfo
     * @param jdOrder
     */
    private void handleOrderInfoMergeSkuInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder, JdhPromise jdhPromise) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleOrderInfoMergeSkuInfo start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();

        // 获取订单预约信息
        OrderAppointmentInfoValueObject vo = null;
        JdOrderExtDTO appointmentDto = jdOrder.getJdOrderExtList().stream()
                .filter(e -> StringUtils.equals(e.getExtType(), OrderExtTypeEnum.APPOINTMENT_INFO.getType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(appointmentDto)) {
            String json = appointmentDto.getExtContext();
            vo = JSON.parseObject(json, OrderAppointmentInfoValueObject.class);
        }

        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            // 订单号
            if (ViaOrderInfoFieldEnum.ORDER_ID.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderId())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderId().toString());
                }
                continue;
            }

            // 实付款
            if (ViaOrderInfoFieldEnum.ORDER_AMOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderAmount())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderAmount().toPlainString());
                    List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = jdOrder.getJdOrderServiceFeeInfos();
                    viaFloorConfig.setValue(JSON.toJSONString(jdOrderServiceFeeInfos));
                }
                continue;
            }

            // 下单时间
            if (ViaOrderInfoFieldEnum.ORDER_CREATE_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getCreateTime())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(jdOrder.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));
                }
                continue;
            }

            // 被服务人
            if (ViaOrderInfoFieldEnum.ORDER_USER_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                List<String> patientNames = vo.getPatients().stream().map(e -> UserName.maskTool(e.getName())).collect(Collectors.toList());
                String name = String.join(",", patientNames);
                viaFloorConfig.setFieldValue(name);
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }

            // 上门地址
            if (ViaOrderInfoFieldEnum.ADDRESS_DETAIL.getField().equals(viaFloorConfig.getFieldKey())) {
                viaFloorConfig.setFieldValue(vo.getAddressInfo().getFullAddress());
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }

            // 备注
            if (ViaOrderInfoFieldEnum.ORDER_REMARK.getField().equals(viaFloorConfig.getFieldKey())) {
                if (StrUtil.isEmpty(jdOrder.getRemark())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getRemark());
                }
                continue;
            }

            // 优惠金额
            if (ViaOrderInfoFieldEnum.ORDER_DISCOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderDiscount()) || jdOrder.getOrderDiscount().compareTo(new BigDecimal("0")) <= 0) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderDiscount().toString());
                }
                continue;
            }

            // 优惠券金额
            if (ViaOrderInfoFieldEnum.ORDER_COUPON.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderCoupon()) || jdOrder.getOrderCoupon().compareTo(new BigDecimal("0")) <= 0) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderCoupon().toString());
                }
                continue;
            }

            // 预约时间（期望时间）
            if (ViaOrderInfoFieldEnum.APPOINTMENT_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                //优先判断是否存在履约单，是否存在履约单预约时间，如果有优先取履约单上的预约时间
                if (Objects.nonNull(jdhPromise) && Objects.nonNull(jdhPromise.getAppointmentTime())) {
                    OrderAppointmentTimeValueObject appointmentTime = new OrderAppointmentTimeValueObject();
                    appointmentTime.setDateType(jdhPromise.getAppointmentTime().getDateType());
                    appointmentTime.setIsImmediately(jdhPromise.getAppointmentTime().getIsImmediately());
                    appointmentTime.setAppointmentStartTime(jdhPromise.getAppointmentTime().formatAppointmentStartTime());
                    appointmentTime.setAppointmentEndTime(jdhPromise.getAppointmentTime().formatAppointmentEndTime());

                    if (Objects.nonNull(appointmentTime.getIsImmediately()) && appointmentTime.getIsImmediately()) {
                        viaFloorConfig.setFieldValue("立即预约");
                    } else {
                        // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                        String time = appointmentTime.formatAppointTimeDesc();
                        viaFloorConfig.setFieldValue(time);
                    }
                    continue;
                }
                OrderAppointmentTimeValueObject appointmentTime = vo.getAppointmentTime();
                if (Objects.nonNull(appointmentTime.getIsImmediately()) && appointmentTime.getIsImmediately()) {
                    viaFloorConfig.setFieldValue("立即预约");
                } else {
                    // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                    String time = appointmentTime.formatAppointTimeDesc();
                    viaFloorConfig.setFieldValue(time);
                }
                if (StringUtils.isBlank(viaFloorConfig.getFieldValue())) {
                    iterator.remove();
                }
                continue;
            }

            // 订单商品列表（图片+商品名称+价格+数量）
            if (ViaOrderInfoFieldEnum.ORDER_SKU_INFO_LIST.getField().equals(viaFloorConfig.getFieldKey())) {
                List<ViaFloorSubConfig> orderSkuInfoList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(jdOrder.getJdOrderItemList())) {
                    for (JdOrderItemDTO jdOrderItemDTO : jdOrder.getJdOrderItemList()) {
                        // action
                        ViaActionInfo action = new ViaActionInfo();
                        action.setUrl(MessageFormat.format(viaConfig.getSkuDetailUrl(), jdOrderItemDTO.getSkuId().toString()));
                        action.setType(ActionType.JUMP.getCode());
                        //如果是加项商品，跳转url为空，即在订详无法跳转
                        if (IsAddedEnum.IS_ADDED.getValue().equals(jdOrderItemDTO.getIsAdded())) {
                            action.setUrl(null);
                        }
                        ViaFloorSubConfig subConfig = new ViaFloorSubConfig();
                        subConfig.setValue(JSON.toJSONString(jdOrderItemDTO));
                        subConfig.setAction(action);
                        orderSkuInfoList.add(subConfig);
                    }
                }
                if (CollectionUtils.isNotEmpty(orderSkuInfoList)) {
                    viaFloorConfig.setSubConfigList(orderSkuInfoList);
                } else {
                    iterator.remove();
                }
                continue;
            }

        }
    }


    /**
     * 地图信息
     * @param viaFloorInfo
     * @param sourceData
     */
    private void handlePromiseMapInfo(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseMapInfo start");
        ViaFloorConfig floorConfig = viaFloorInfo.getFloorConfigList().get(0);
        ViaActionInfo action = floorConfig.getAction();
        action.init(sourceData);
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseMapInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));

    }

    /**
     * 用户反馈
     * @param viaFloorInfo
     * @param jdhPromise
     */
    private void handleUserFeedbackInfo(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, JdOrderDTO jdOrder) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleUserFeedbackInfo start");
        if(OrderTypeEnum.XFYL_ORDER_TYPE.getType().equals(jdOrder.getOrderType()) && JdhPromiseStatusEnum.COMPLETE.getStatus().equals(jdhPromise.getPromiseStatus())){
            log.info("UserServiceSurveyFloor.handleData 履约单状态未完成,nps问卷楼层不展示!!!");
            return;
        }
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();
        UserFeedbackAggregationDTO userFeedbackAggregationDTO = userFeedbackApplication.queryUserFeedback(UserFeedbackRequest.builder().userPin(jdhPromise.getUserPin()).promiseId(jdhPromise.getPromiseId()).businessScene("serviceStandardFeedback").build());
        if (Objects.nonNull(userFeedbackAggregationDTO)) {
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(userFeedbackAggregationDTO)).build());
        }
        viaFloorInfo.setFloorConfigList(floorConfigList);
        log.info("HomeAngelCareOrderDetailHandlerV2 handleUserFeedbackInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 信息评估
     * @param viaFloorInfo
     * @param riskAssessmentDetail
     */
    private void handleRiskAssessment(ViaFloorInfo viaFloorInfo, RiskAssessmentDetailManDTO riskAssessmentDetail) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleRiskAssessment start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()){
            ViaFloorConfig viaFloorConfig = iterator.next();
            if (ViaInfoRiskAssessmentFieldEnum.TITLE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(riskAssessmentDetail)) {
                    iterator.remove();
                }
                continue;
            }

            if (ViaInfoRiskAssessmentFieldEnum.CONTENT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(riskAssessmentDetail)) {
                    iterator.remove();
                }else {
                    List<RiskAssQuestionDTO> riskAssQuestionList = riskAssessmentDetail.getRiskAssUserDetailDTOS().get(0).getRiskAssQuestionDTOS();
                    List<ViaFormItem> formItemList = Lists.newArrayList();
                    riskAssQuestionList.forEach(q->{
                        ViaFormItem questionNameItem = new ViaFormItem();
                        questionNameItem.setValue(q.getName());
                        formItemList.add(questionNameItem);
                    });
                    viaFloorConfig.setFormItemList(formItemList);
                }
                continue;
            }
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handleRiskAssessment viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 预约信息
     * @param ctx
     * @param viaFloorInfo
     * @param jdhPromise
     * @param statusMapping
     * @param jdhSku
     */
    private void handleAppointmentInfo(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, ViaStatusMapping statusMapping, JdhSkuDto jdhSku) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleAppointmentInfo start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            // 楼层属性处理
            if (!statusMapping.supportFiled(ViaFloorEnum.PROMISE_APPOINTMENT_INFO.getFloorCode(), viaFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }

            // 预约地址
            if (ViaAppointmentFieldEnum.APPOINTMENT_ADDRESS.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdhPromise)) {
                    iterator.remove();
                } else {
                    if (Objects.nonNull(jdhPromise.getStore()) && StringUtils.isNotBlank(jdhPromise.getStore().getStoreAddr())){
                        viaFloorConfig.setFieldValue(jdhPromise.getStore().getStoreAddr());
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }

            // 预约时间
            if (ViaAppointmentFieldEnum.APPOINTMENT_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdhPromise)) {
                    iterator.remove();
                } else {
                    PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
                    if (Objects.nonNull(appointmentTime)){
                        if (Objects.nonNull(appointmentTime.getIsImmediately()) && appointmentTime.getIsImmediately()) {
                            viaFloorConfig.setFieldValue("立即预约");
                        } else {
                            // 非立即预约，需要展示具体的时间 格式 yyyy-MM-dd hh:mm-hh:mm
                            viaFloorConfig.setFieldValue(getFullAppointmentDateDesc(appointmentTime));
                        }
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }

            // 意向护士
            if (ViaAppointmentFieldEnum.INTENDED_NURSE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdhPromise)) {
                    iterator.remove();
                } else {
                    if (CollectionUtils.isNotEmpty(jdhPromise.getPromiseExtends())){
                        List<JdhPromiseExtend> intendedNurseList = jdhPromise.getPromiseExtends().stream().filter(p -> PromiseExtendKeyEnum.INTENDED_NURSE.getFiledKey().equals(p.getAttribute())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(intendedNurseList)){
                            PromiseIntendedNurse promiseIntendedNurse = JSON.parseObject(intendedNurseList.get(0).getValue(), PromiseIntendedNurse.class);
                            if (NumConstant.NUM_1.equals(promiseIntendedNurse.getRecommendType())){
                                viaFloorConfig.setFieldValue("平台推荐");
                            }else if (NumConstant.NUM_2.equals(promiseIntendedNurse.getRecommendType()) && Objects.nonNull(promiseIntendedNurse.getAngelId())){
                                JdhAngel jdhAngel = angelRepository.find(JdhAngelIdentifier.builder().angelId(promiseIntendedNurse.getAngelId()).build());
                                viaFloorConfig.setFieldValue(jdhAngel.getAngelName().charAt(0) + "*护士");
                                viaFloorConfig.setFieldValue(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), viaFloorConfig.getFieldValue()));
                            }
                        }else {
                            iterator.remove();
                            continue;
                        }
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }

            // 预约项目
            if (ViaAppointmentFieldEnum.APPOINTMENT_ITEM.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdhPromise)) {
                    iterator.remove();
                } else {
                    if (CollectionUtils.isNotEmpty(jdhPromise.getServices())){
                        viaFloorConfig.setFieldValue(jdhPromise.getServices().get(0).getServiceName());
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handleAppointmentInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }



    /**
     * fillMedPromiseFloorConfig
     *
     * @param viaFloorConfig       viaFloorConfig
     * @param medPromise           medPromise
     * @param medicalStatusMapping medicalStatusMapping
     * @param jdhPromisePatient    jdhPromisePatient
     * @param jdhPromise           jdhPromise
     */
    private void fillMedPromiseFloorConfig(ViaFloorConfig viaFloorConfig,
                                           MedicalPromiseDTO medPromise,
                                           List<ViaStatusMapping> medicalStatusMapping,
                                           JdhPromisePatient jdhPromisePatient,
                                           JdhPromise jdhPromise) {
        for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
            if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                if (CollUtil.isNotEmpty(btnList)) {
                    List<ViaBtnInfo> newBtnList = new ArrayList<>();
                    for (ViaBtnInfo viaBtnInfo : btnList) {
                        if (ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())) {
                            if (Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())) {
                                ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo), ViaBtnInfo.class);
                                newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(), jdhPromise.getSourceVoucherId(), jdhPromisePatient.getPatientId().toString()));
                                newBtnList.add(newBtnInfo);
                            }
                        }
                    }
                    viaFloorConfig.setBtnList(newBtnList);
                }
                break;
            }
        }
    }

    /**
     * 底部按钮
     * @param ctx
     * @param viaFloorInfo
     * @param statusMapping
     * @param jdOrder
     * @param jdhPromise
     * @param medicalPromiseList
     */
    private void handleFooterButtons(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping,
                                     JdOrderDTO jdOrder, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterButtons start");
        //申请退款 refundBtn
        //再次购买 rePurchaseBtn
        //联系客服 contactCustomerBtn
        //取消订单 cancelOrderBtn
        //立即支付 payNowBtn
        //修改时间
        ViaConfig viaConfig = ctx.getViaConfig();
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
        // 退款标识
        boolean hasInvalid = false;
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            hasInvalid = medicalPromiseList.stream().anyMatch(m -> MedicalPromiseStatusEnum.INVALID.getStatus().equals(m.getStatus()));
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterButtons hasInvalid={}", hasInvalid);
        // 按钮楼层是否展示title由配置决定
        if (statusMapping.supportFiled(ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode(), "btnFloorTitle")) {
            // 根据样本数量展示不同的title
            if (CollectionUtils.isNotEmpty(medicalPromiseList)) {
                try {
                    Map<String, Object> map = viaConfigRepository.findAssemblyConfig(ViaAssemblyCodeEnum.BTN_FLOOR.getCode());
                    String title = "";
                    if (Objects.equals(medicalPromiseList.size(), 1)) {
                        title = Objects.toString(map.get("onceSpecimenTitle"), "");
                    } else if (medicalPromiseList.size() > 1) {
                        title = Objects.toString(map.get("multiSpecimenTitle"));
                    }
                    viaFloorConfig.setTitle(title);
                } catch (Exception e) {
                    // title设置失败，不影响按钮处理
                    log.error("HomeAngelCareOrderDetailHandlerV2->handleFooterButtons setTitle error", e);
                }
            }


        }
        while (btnInfoIterator.hasNext()) {
            ViaBtnInfo btnInfo = btnInfoIterator.next();

            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode", EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getVerticalCode));
            actionCommonParams.put("serviceType", viaConfig.getServiceType());
            actionCommonParams.put("envType", ctx.getEnvType());
            actionCommonParams.put("promiseId", EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseId));
            actionCommonParams.put("orderId", EntityUtil.getFiledDefaultNull(jdOrder, JdOrderDTO::getOrderId));

            if (!statusMapping.getFooterButtonCodeList().contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }

            //申请退款
            ViaActionInfo action = btnInfo.getAction();
            if (ViaBtnCodeEnum.REFUND_BTN.getCode().equals(btnInfo.getCode())) {
                if (hasInvalid){
                    btnInfoIterator.remove();
                    continue;
                }
                action.setParams(new HashMap<>(actionCommonParams));
                ViaActionInfo nextAction = action.getNextAction();
                actionCommonParams.put("orderId", jdOrder.getOrderId());
                actionCommonParams.put("refundType", 1);
                actionCommonParams.put("refundSource", "1");
                actionCommonParams.put("voucherId", jdhPromise.getVoucherId());
                actionCommonParams.put("promiseId", jdhPromise.getPromiseId());
                nextAction.setParams(actionCommonParams);
            } else if (ViaBtnCodeEnum.RE_PURCHASE_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    String jumpUrl = MessageFormat.format(btnInfo.getJumpUrlRule(), jdOrder.getJdOrderItemList().get(0).getSkuId().toString());
                    action.setUrl(jumpUrl);
                } catch (Exception e) {
                    log.error("HomeAngelCareOrderDetailHandlerV2 handleFooterButtons RE_PURCHASE_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }

            } else if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
                buildContactCustomerBtn(btnInfo, ctx, jdOrder, action);
            } else if (ViaBtnCodeEnum.CANCEL_ORDER_BTN.getCode().equals(btnInfo.getCode())) {
                actionCommonParams.put("orderId", jdOrder.getOrderId().toString());
                action.setParams(actionCommonParams);

            } else if (ViaBtnCodeEnum.PAY_NOW_BTN.getCode().equals(btnInfo.getCode())) {
                actionCommonParams.put("orderId", jdOrder.getOrderId().toString());
                if (StrUtil.isNotBlank(ctx.getOpenId())) {
                    actionCommonParams.put("openId", ctx.getOpenId());
                }
                if (StrUtil.isNotBlank(ctx.getCallWxType())) {
                    actionCommonParams.put("callWxType", ctx.getCallWxType());
                }
                action.setParams(actionCommonParams);
            } else if (ViaBtnCodeEnum.SCANNING_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.ENTER_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.MULTI_ENTER_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())) {
                action.init(actionCommonParams);
            } else if (ViaBtnCodeEnum.EVALUATE_ANGEL_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    Map<Long, String> urlMap = angelEcologyApplication.queryAngelEcologyPageUrl(
                            JdhAngelEcologyQueryRequest.builder().envType(ctx.getEnvType()).orderId(jdOrder.getOrderId()).build());
                    String jumpUrl = urlMap.get(jdhPromise.getPromiseId());
                    //有链接设置url
                    if (StringUtils.isNotBlank(jumpUrl)) {
                        action.setUrl(jumpUrl);
                    } else {//无连接不返回按钮
                        btnInfoIterator.remove();
                    }
                } catch (Exception e) {
                    log.error("HomeAngelCareOrderDetailHandlerV2 handleFooterButtons EVALUATE_ANGEL_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            } else if (ViaBtnCodeEnum.CANCEL_APPOINT_BTN.getCode().equals(btnInfo.getCode())) {
                action.setParams(actionCommonParams);
            } else if (ViaBtnCodeEnum.MODIFY_DATETIME_BTN.getCode().equals(btnInfo.getCode())) {
                if (Objects.nonNull(jdhPromise) && Objects.equals(JdhPromiseStatusEnum.MODIFY_ING.getStatus(), jdhPromise.getPromiseStatus())) {
                    log.error("HomeAngelCareOrderDetailHandlerV2 handleFooterButtons 修改预约中状态的履约单不展示修改时间按钮");
                    btnInfoIterator.remove();
                    continue;
                }
                Map<String, Object> actionParams = new HashMap<>();
                actionParams.put("verticalCode",viaConfig.getVerticalCode());
                actionParams.put("serviceType",viaConfig.getServiceType());
                actionParams.put("envType",ctx.getEnvType());
                actionParams.put("scene", AvailableAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName());
                actionParams.put("promiseId",jdhPromise.getPromiseId());
                action.setParams(actionParams);
                ViaActionInfo nextAction = action.getNextAction();
                Map<String, Object> nextActionParams = new HashMap<>();
                nextActionParams.put("verticalCode",viaConfig.getVerticalCode());
                nextActionParams.put("serviceType",viaConfig.getServiceType());
                nextActionParams.put("envType",ctx.getEnvType());
                nextActionParams.put("scene", ModifyAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName());
                nextActionParams.put("reasonType", 1);
                nextActionParams.put("reasonContent", "");
                nextActionParams.put("promiseId", String.valueOf(jdhPromise.getPromiseId()));
                nextAction.setParams(nextActionParams);
            }
        }

        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * handlePromiseCodeInfo
     * {
     * "floorCode": "promiseCodeInfo",
     * "floorName": "履约消费码信息",
     * "floorConfigList": [
     * {
     * "promiseCode":"1231",
     * "noticeTip":"请在护士上门后出示"
     * }
     * ]
     * }
     *
     * @param viaFloorInfo viaFloorInfo
     * @param jdhPromise   jdhPromise
     */
    private void handlePromiseCodeInfo(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, JdhSkuDto jdhSku) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseCodeInfo start");
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        viaFloorConfig.setPromiseCode(jdhPromise.getCode());
        viaFloorConfig.setNoticeTip(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), viaFloorConfig.getNoticeTip()));
        log.info("HomeAngelCareOrderDetailHandlerV2 handlePromiseCodeInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 隐藏楼层
     *
     * @param statusMapping 状态映射
     * @param floorList     楼层列表
     */
    private void clearHiddenFloor(ViaStatusMapping statusMapping, List<ViaFloorInfo> floorList, AngelWorkDetailDto angelWorkDetail, Boolean middleRiskAssessmentFlag) {
        Iterator<ViaFloorInfo> iterator = floorList.iterator();
        while (iterator.hasNext()) {
            ViaFloorInfo viaFloorInfo = iterator.next();
            if (!statusMapping.getShowFloorCode().contains(viaFloorInfo.getFloorCode())) {
                iterator.remove();
                continue;
            }

            // 非中高危风险服务项目移除就医证明楼层
            if (ViaFloorEnum.PROMISE_MEDICAL_CERTIFICATE.getFloorCode().equals(viaFloorInfo.getFloorCode()) && !middleRiskAssessmentFlag){
                iterator.remove();
                continue;
            }

            // 地图楼层
            if (ViaFloorEnum.PROMISE_MAP_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode()) && Objects.nonNull(angelWorkDetail)){
                if (Objects.isNull(angelLocationApplication.getLocation(Long.valueOf(angelWorkDetail.getAngelId())))){
                    iterator.remove();
                    continue;
                }
            }

        }
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    @SuppressWarnings("all")
    public void handle(FillViaConfigDataContext ctx) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handle ctx:{}", JSON.toJSONString(ctx));
        ViaConfig viaConfig = ctx.getViaConfig();
        JdhPromise jdhPromise;
        JdOrderDTO jdOrder = null;
        Long skuId;
        if (Objects.nonNull(ctx.getOrderId())){// 订单详情页
            jdOrder = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(ctx.getOrderId()).pin(ctx.getUserPin()).querySource("C").build());
            if (Objects.isNull(jdOrder)) {
                throw new SystemException(SupportErrorCode.VIA_ORDER_INFO_NOT_EXIT);
            }
            String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
            jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).userPin(jdOrder.getUserPin()).build());
            skuId = jdOrder.getJdOrderItemList().get(0).getSkuId();
        } else if (Objects.nonNull(ctx.getPromiseId())){// 履约单详情页
            jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(Long.valueOf(ctx.getPromiseId())).userPin(ctx.getUserPin()).build());
            if (Objects.isNull(jdhPromise)) {
                throw new SystemException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
            }
            jdOrder = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(jdhPromise.getSourceVoucherId()).pin(ctx.getUserPin()).querySource("C").build());
            skuId = jdhPromise.getServices().get(0).getServiceId();
        } else {
            skuId = null;
            jdhPromise = null;
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handle jdOrder:{}", JSON.toJSONString(jdOrder));
        log.info("HomeAngelCareOrderDetailHandlerV2 handle jdhPromise:{}", JSON.toJSONString(jdhPromise));

        List<CompletableFuture> futures = new ArrayList<>();
        //查商品配置
        CompletableFuture<JdhSkuDto> skuDtoCf = null;
        if (Objects.nonNull(skuId)) {
            skuDtoCf = CompletableFuture.supplyAsync(() -> querySkuInfo(skuId), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(skuDtoCf);
        }

        //查履约单历史记录
        CompletableFuture<List<JdhPromiseHistory>> promiseHistoryCf = null;
        if (Objects.nonNull(jdhPromise)) {
            promiseHistoryCf = CompletableFuture.supplyAsync(() -> queryPromiseHistory(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(promiseHistoryCf);
        }

        //查检测单
        CompletableFuture<List<MedicalPromiseDTO>> medPromiseListCf = null;
        if (Objects.nonNull(jdhPromise)) {
            medPromiseListCf = CompletableFuture.supplyAsync(() -> queryMedicalPromiseList(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(medPromiseListCf);
        }

        //查work
        CompletableFuture<AngelWorkDetailDto> angelWorkDetailDtoCf = null;
        if(Objects.nonNull(jdhPromise)){
            angelWorkDetailDtoCf = CompletableFuture.supplyAsync(() ->
                            angelPromiseApplication.queryAngelWork(AngelWorkQuery.builder().promiseId(jdhPromise.getPromiseId()).build()), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(angelWorkDetailDtoCf);
        }

        //查服务者任务单
        CompletableFuture<List<AngelTask>> angelTaskListCf = null;
        if (Objects.nonNull(jdhPromise)) {
            angelTaskListCf = CompletableFuture.supplyAsync(() -> queryAngelTask(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(angelTaskListCf);
        }

        //查风险评估单
        CompletableFuture<RiskAssessmentDetailManDTO> riskAssessmentDetailCf = null;
        if (Objects.nonNull(jdhPromise)) {
            riskAssessmentDetailCf = CompletableFuture.supplyAsync(() -> queryRiskAssessment(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(angelTaskListCf);
        }

        //异步编译 Aviator
        futures.add(CompletableFuture.runAsync(() -> compileAviator(viaConfig), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)));

        if (CollUtil.isNotEmpty(futures)) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }

        // 查询履约单风险等级
        PromiseDangerLevelRequest highRiskLevelRequest = PromiseDangerLevelRequest.builder()
                .orderId(jdOrder.getOrderId())
                .skuId(skuId)
                .build();
        List<Integer> promiseDangerLevelList = promiseApplication.queryPromiseDangerLevel(highRiskLevelRequest);

        // 检查高危风险等级
        boolean highRiskAssessmentFlag = false;
        // 检查中高危风险等级
        boolean middleRiskAssessmentFlag = false;
        if (CollectionUtils.isNotEmpty(promiseDangerLevelList)){
            highRiskAssessmentFlag = promiseDangerLevelList.stream().anyMatch(s -> Arrays.asList(3).contains(s));
            middleRiskAssessmentFlag = promiseDangerLevelList.stream().anyMatch(s -> Arrays.asList(2,3).contains(s));
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handle promiseDangerLevelList={}, highRiskAssessmentFlag:{}, middleRiskAssessmentFlag={}",
                JSON.toJSONString(promiseDangerLevelList), highRiskAssessmentFlag, middleRiskAssessmentFlag);

        try {
            // ==>>>> 过滤statusMapping
            ViaStatusMapping statusMapping = viaComponentDomainService.parseHomeTestMappingV3(
                    EntityUtil.getFiledDefaultNull(jdOrder, JdOrderDTO::getOrderStatus),
                    EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseStatus),
                    EntityUtil.getFiledDefaultNull(Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(), MedicalPromiseDTO::getStatus),
                    // promise不为空，且appointmentTime不为空，再取isImmediately
                    EntityUtil.getFiledDefaultNull(jdhPromise,
                            e -> EntityUtil.getFiledDefaultNull(e.getAppointmentTime(), time -> time.getIsImmediately())
                    ),
                    EntityUtil.getFiledDefaultNull(Objects.isNull(angelWorkDetailDtoCf) ? null : angelWorkDetailDtoCf.get(), AngelWorkDetailDto::getStatus),
                    viaConfig,
                    EntityUtil.getFiledDefaultNull(Objects.isNull(riskAssessmentDetailCf) ? null : riskAssessmentDetailCf.get(), RiskAssessmentDetailManDTO::getRiskAssessmentStatus),
                    highRiskAssessmentFlag
            );
            log.info("HomeAngelCareOrderDetailHandlerV2 handle statusMapping:{}", JSON.toJSONString(statusMapping));

            // ==>>>> 移除当前状态下隐藏的楼层
            List<ViaFloorInfo> floorList = viaConfig.getFloorList();
            clearHiddenFloor(statusMapping, floorList, Objects.isNull(angelWorkDetailDtoCf) ? null : angelWorkDetailDtoCf.get(), middleRiskAssessmentFlag);
            log.info("HomeAngelCareOrderDetailHandlerV2 handle floorList:{}", JSON.toJSONString(floorList));

            // ==>>>> 楼层处理
            dealFloorList(ctx,
                    statusMapping,
                    jdOrder,
                    jdhPromise,
                    Objects.isNull(angelWorkDetailDtoCf) ? null : angelWorkDetailDtoCf.get(),
                    Objects.isNull(promiseHistoryCf) ? Collections.EMPTY_LIST : promiseHistoryCf.get(),
                    Objects.isNull(medPromiseListCf) ? Collections.EMPTY_LIST : medPromiseListCf.get(),
                    Objects.isNull(skuDtoCf) ? null : skuDtoCf.get(),
                    Objects.isNull(angelTaskListCf) ? Collections.EMPTY_LIST : angelTaskListCf.get(),
                    Objects.isNull(riskAssessmentDetailCf) ? null : riskAssessmentDetailCf.get(),
                    highRiskAssessmentFlag, middleRiskAssessmentFlag);
            log.info("HomeAngelCareOrderDetailHandlerV2 handle viaConfig:{}", JSON.toJSONString(viaConfig));
        } catch (Exception e) {
            log.error("HomeAngelCareOrderDetailHandlerV2 handle error", e);
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_HAND_ERROR);
        }
    }

    /**
     * compileAviator
     *
     * @param viaConfig VIA配置
     */
    private void compileAviator(ViaConfig viaConfig) {
        try {
            for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
                if (StrUtil.isNotBlank(viaStatusMapping.getStatusExpression())) {
                    AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.info("HomeAngelCareOrderDetailHandlerV2 handle compileAviator exception", e);
        }
    }

    /**
     * 查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link JdhSkuDto}
     */
    private JdhSkuDto querySkuInfo(Long skuId) {
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    .queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            return jdhSkuDtoMap.get(skuId);
        } catch (Exception e) {
            log.info("HomeAngelCareOrderDetailHandlerV2 handle querySkuInfo exception", e);
            return null;
        }
    }

    /**
     * queryPromiseHistory
     *
     * @param jdhPromise jdh承诺
     * @return {@link List}<{@link JdhPromiseHistory}>
     */
    private List<JdhPromiseHistory> queryPromiseHistory(JdhPromise jdhPromise) {
        try {
            List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(jdhPromise.getPromiseId()).build());
            log.info("HomeAngelCareOrderDetailHandlerV2 handle queryPromiseHistory promiseHistories:{}", JSON.toJSONString(promiseHistories));
            return promiseHistories;
        } catch (Exception e) {
            log.info("HomeAngelCareOrderDetailHandlerV2 handle queryPromiseHistory exception", e);
            return null;
        }
    }

    /**
     * 查服务者任务单
     * @param jdhPromise
     * @return
     */
    private List<AngelTask> queryAngelTask(JdhPromise jdhPromise) {
        try {
            AngelWorkDBQuery angelWorkDBQuery = AngelWorkDBQuery.builder().promiseId(jdhPromise.getPromiseId()).build();
            List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
            if (CollectionUtils.isEmpty(angelWorkList)){
                return Lists.newArrayList();
            }
            angelWorkList.sort((order1,order2)->order2.getCreateTime().compareTo(order1.getCreateTime()));
            AngelWork angelWork = angelWorkList.get(0);

            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setWorkId(angelWork.getWorkId());
            List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
            log.info("HomeAngelCareOrderDetailHandlerV2 handle queryAngelTask angelTaskList:{}", JSON.toJSONString(angelTaskList));
            return angelTaskList;
        } catch (Exception e) {
            log.info("HomeAngelCareOrderDetailHandlerV2 handle queryAngelTask exception", e);
            return null;
        }
    }

    /**
     * 查风险评估单
     * @param jdhPromise
     * @return
     */
    private RiskAssessmentDetailManDTO queryRiskAssessment(JdhPromise jdhPromise) {
        try {
            RiskAssessmentDetailRequest riskAssessmentDetailRequest = new RiskAssessmentDetailRequest();
            riskAssessmentDetailRequest.setPromiseId(jdhPromise.getPromiseId());
            riskAssessmentDetailRequest.setDetailQuery(true);
            RiskAssessmentDetailManDTO riskAssessmentDetail = riskAssessmentApplication.queryRiskAssDetailForMan(riskAssessmentDetailRequest);
            log.info("HomeAngelCareOrderDetailHandlerV2 queryRiskAssessment riskAssessmentDetail:{}", JSON.toJSONString(riskAssessmentDetail));
            return riskAssessmentDetail;
        } catch (Exception e) {
            log.info("HomeAngelCareOrderDetailHandlerV2 queryRiskAssessment exception", e);
            return null;
        }
    }

    /**
     * queryMedicalPromiseList
     *
     * @param jdhPromise jdhPromise
     * @return {@link List}<{@link MedicalPromiseDTO}>
     */
    private List<MedicalPromiseDTO> queryMedicalPromiseList(JdhPromise jdhPromise) {
        try {
            MedicalPromiseListRequest medPromiseRequest = new MedicalPromiseListRequest();
            medPromiseRequest.setPromiseId(jdhPromise.getPromiseId());
            medPromiseRequest.setItemDetail(Boolean.TRUE);
            medPromiseRequest.setPatientDetail(Boolean.TRUE);
            List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(medPromiseRequest);
            log.info("HomeAngelCareOrderDetailHandlerV2 handle queryMedicalPromiseList medicalPromiseList:{}", JSON.toJSONString(medicalPromiseList));
            return CollectionUtils.isEmpty(medicalPromiseList) ? Lists.newArrayList() : medicalPromiseList;
        } catch (Exception e) {
            log.info("HomeAngelCareOrderDetailHandlerV2 handle queryMedicalPromiseList exception", e);
            return null;
        }
    }


    /**
     * 就医证明
     * @param viaFloorInfo
     * @param jdhPromise
     * @param jdOrder
     * @param riskAssessmentDetail
     * @param jdhSkuDto
     * @param sourceData
     */
    private void handleMedicalCertificate(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, JdOrderDTO jdOrder, RiskAssessmentDetailManDTO riskAssessmentDetail, JdhSkuDto jdhSkuDto, Map<String, Object> sourceData) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleMedicalCertificate start");
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();

        if (Objects.nonNull(jdhPromise)){
            while (iterator.hasNext()){
                ViaFloorConfig viaFloorConfig = iterator.next();
                if (MedicalCertificateFieldEnum.MEDICAL_CERTIFICATE_FILES.getField().equals(viaFloorConfig.getFieldKey())) {
                    // 已完成评估的个人病例照片
                    List<PatientExtBo> patientExtBoList = jdhPromise.getPatients().stream().map(JdhPromisePatient::getPatientExtBo).
                            filter(Objects::nonNull).collect(Collectors.toList());
                    List<String> patientExtFileList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(patientExtBoList)){
                        patientExtBoList.forEach(patientExtBo->{
                            if (CollectionUtils.isEmpty(patientExtBo.getPatientDoctorAdviceFileList())){
                                return;
                            }
                            patientExtFileList.addAll(patientExtBo.getPatientDoctorAdviceFileList());
                        });
                    }
                    log.info("handleMedicalCertificate patientExtFileList={}", JSON.toJSONString(patientExtFileList));

                    // 获取就医证明
                    List<FileUrlDto> medicalCertificates = this.getPromiseMedicalCertificates(jdhPromise);
                    log.info("handleMedicalCertificate promise medicalCertificates={}", JSON.toJSONString(medicalCertificates));
                    List<ViaFormItem> formItemList = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(medicalCertificates)){
                        medicalCertificates.forEach(m->{
                            ViaFormItem viaFormItem = new ViaFormItem();
                            viaFormItem.setFormName(String.valueOf(m.getFileId()));
                            viaFormItem.setDisabled(false);
                            if (patientExtFileList.contains(String.valueOf(m.getFileId()))){
                                viaFormItem.setDisabled(true);
                            }
                            viaFormItem.setValue(m.getUrl());
                            formItemList.add(viaFormItem);
                        });
                        viaFloorConfig.setFormItemList(formItemList);
                    }
                    continue;
                }

                if (MedicalCertificateFieldEnum.UPLOAD_MEDICAL_CERTIFICATE.getField().equals(viaFloorConfig.getFieldKey())) {
                    Map<String, Object> actionParam = new HashMap<>();
                    actionParam.put("orderStatus", jdOrder.getOrderStatus());
                    actionParam.put("riskAssessmentStatus", Objects.isNull(riskAssessmentDetail) ? null : riskAssessmentDetail.getRiskAssessmentStatus());
                    actionParam.put("middleRiskAssessmentFlag", sourceData.get("middleRiskAssessmentFlag"));
                    actionParam.put("highRiskAssessmentFlag",  sourceData.get("highRiskAssessmentFlag"));
                    log.info("handleMedicalCertificate actionParam={}", JSON.toJSONString(actionParam));
                    if ((Boolean) AviatorEvaluator.compile(viaFloorConfig.getStatusExpression(), Boolean.TRUE).execute(actionParam)){
                        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
                        btnList.forEach(btn->{
                            if ("submitMedicalCertificate".equals(btn.getCode())){
                                ViaActionInfo action = btn.getAction();
                                Map<String, Object> params = new HashMap<>();
                                params.put("promiseId", jdhPromise.getPromiseId());
                                action.setParams(params);
                            }
                        });
                    }else {
                        log.info("handleMedicalCertificate remove fieldKey={}", viaFloorConfig.getFieldKey());
                        iterator.remove();
                    }
                    continue;
                }
            }
        }else {
            while (iterator.hasNext()){
                ViaFloorConfig viaFloorConfig = iterator.next();
                if (MedicalCertificateFieldEnum.MEDICAL_CERTIFICATE_FILES.getField().equals(viaFloorConfig.getFieldKey())) {
                    // 获取就医证明
                    List<FileUrlDto> medicalCertificates = this.getOrderMedicalCertificates(jdOrder);
                    log.info("handleMedicalCertificate order medicalCertificates={}", JSON.toJSONString(medicalCertificates));
                    List<ViaFormItem> formItemList = Lists.newArrayList();
                    medicalCertificates.forEach(m->{
                        ViaFormItem viaFormItem = new ViaFormItem();
                        viaFormItem.setFormName(String.valueOf(m.getFileId()));
                        viaFormItem.setDisabled(false);
                        viaFormItem.setValue(m.getUrl());
                        formItemList.add(viaFormItem);
                    });
                    viaFloorConfig.setFormItemList(formItemList);
                    continue;
                }

                if (MedicalCertificateFieldEnum.UPLOAD_MEDICAL_CERTIFICATE.getField().equals(viaFloorConfig.getFieldKey())) {
                    Map<String, Object> actionParam = new HashMap<>();
                    actionParam.put("orderStatus", jdOrder.getOrderStatus());
                    actionParam.put("riskAssessmentStatus", Objects.isNull(riskAssessmentDetail) ? null : riskAssessmentDetail.getRiskAssessmentStatus());
                    actionParam.put("middleRiskAssessmentFlag", sourceData.get("middleRiskAssessmentFlag"));
                    actionParam.put("highRiskAssessmentFlag",  sourceData.get("highRiskAssessmentFlag"));
                    log.info("handleMedicalCertificate order actionParam={}", JSON.toJSONString(actionParam));
                    if ((Boolean) AviatorEvaluator.compile(viaFloorConfig.getStatusExpression(), Boolean.TRUE).execute(actionParam)){
                        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
                        btnList.forEach(btn->{
                            if ("submitMedicalCertificate".equals(btn.getCode())){
                                ViaActionInfo action = btn.getAction();
                                Map<String, Object> params = new HashMap<>();
                                params.put("orderId", jdOrder.getOrderId());
                                action.setParams(params);
                            }
                        });
                    }else {
                        log.info("handleMedicalCertificate remove fieldKey={}", viaFloorConfig.getFieldKey());
                        iterator.remove();
                    }
                    continue;
                }
            }
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handleMedicalCertificate viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 就医证明
     * @param jdhPromise
     * @return
     */
    private List<FileUrlDto> getPromiseMedicalCertificates(JdhPromise jdhPromise) {
        List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
        JdhPromiseExtend promiseExtendDto = promiseExtends.stream().filter(p -> PromiseExtendKeyEnum.MEDICAL_CERTIFICATE_FILE_IDS.getFiledKey()
                .equals(p.getAttribute())).findFirst().orElse(null);
        List<FileUrlDto> medicalCertificates = Lists.newArrayList();
        if (Objects.nonNull(promiseExtendDto)){
            String value = promiseExtendDto.getValue();
            List<Long> fileIds = JSONObject.parseArray(value, Long.class);
            List<JdhFileIdentifier> identifiers = Lists.newArrayList();
            for (Long fileId : fileIds){
                JdhFileIdentifier jdhFileIdentifier = JdhFileIdentifier.builder().fileId(fileId).build();
                identifiers.add(jdhFileIdentifier);
            }
            List<JdhFile> list = jdhFileRepository.findList(identifiers, null);
            if (CollectionUtils.isNotEmpty(list)){
                Date expire = DateUtil.offsetMinute(new Date(), CommonConstant.NUMBER_THIRTY);
                for (JdhFile file : list){
                    FileUrlDto fileUrlDto = new FileUrlDto();
                    fileUrlDto.setFileId(file.getFileId());
                    fileUrlDto.setUrl(fileManageService.getPublicUrl(file.getFilePath(),Boolean.TRUE,expire));
                    medicalCertificates.add(fileUrlDto);
                }
            }
        }
        return medicalCertificates;
    }

    /**
     * 就医证明
     * @param jdOrder
     * @return
     */
    private List<FileUrlDto> getOrderMedicalCertificates(JdOrderDTO jdOrder) {
        List<FileUrlDto> medicalCertificates = Lists.newArrayList();
        JSONObject obj = JSON.parseObject(jdOrder.getExtend());
        if (obj.containsKey("medicalCertificateFileIds")){
            List<Long> fileIds = JSON.parseArray(obj.getString("medicalCertificateFileIds"), Long.class);
            List<JdhFileIdentifier> identifiers = Lists.newArrayList();
            for (Long fileId : fileIds){
                JdhFileIdentifier jdhFileIdentifier = JdhFileIdentifier.builder().fileId(fileId).build();
                identifiers.add(jdhFileIdentifier);
            }
            List<JdhFile> list = jdhFileRepository.findList(identifiers, null);
            if (CollectionUtils.isNotEmpty(list)){
                Date expire = DateUtil.offsetMinute(new Date(), CommonConstant.NUMBER_THIRTY);
                for (JdhFile file : list){
                    FileUrlDto fileUrlDto = new FileUrlDto();
                    fileUrlDto.setFileId(file.getFileId());
                    fileUrlDto.setUrl(fileManageService.getPublicUrl(file.getFilePath(),Boolean.TRUE,expire));
                    medicalCertificates.add(fileUrlDto);
                }
            }
        }
        return medicalCertificates;
    }

    /**
     * @param viaFloorInfo       楼层配置
     * @param sourceData         原始数据
     * @param medicalPromiseList 检测单
     * @param jdhPromise         履约单数据
     */
    private void handleFooterMedicalPromise(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData
            , List<MedicalPromiseDTO> medicalPromiseList, JdhPromise jdhPromise) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterMedicalPromise start");
        // 过滤已经冻结和作废的检测单
        medicalPromiseList = medicalPromiseList.stream().filter(e -> !Objects.equals(e.getFreeze(), YnStatusEnum.YES.getCode()))
                .filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus())).collect(Collectors.toList());

        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getName));
        List<ViaFloorConfig> floorConfigList = Lists.newArrayList();

        // 获取按钮
        String btnJson;
        Optional<ViaBtnInfo> reportBtn =  viaFloorInfo.getFloorConfigList().stream().filter(e -> CollectionUtils.isNotEmpty(e.getBtnList()))
                .flatMap(e -> e.getBtnList().stream())
                .filter(e -> StringUtils.equals(e.getCode(), ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode()))
                .findFirst();
        if (reportBtn.isPresent()){
            btnJson =  JSON.toJSONString(reportBtn);
        } else {
            btnJson = null;
        }
        //检测单按人分堆
        medicalPromiseMap.forEach((name, list) -> {
            // 获取有效的的检测样本信息
            List<MedicalPromiseDTO> survive = list.stream()
                    .filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus()))
                    .filter(e -> !Objects.equals(e.getFreeze(), JdhFreezeEnum.FREEZE.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(survive)) {
                return;
            }
            ViaFloorConfig config = new ViaFloorConfig();
            MedicalPromiseDTO dto = survive.get(0);
            List<ViaFormItem> formItemList = Lists.newArrayList();
            ViaFormItem nameItem = new ViaFormItem();
            nameItem.setFormName(ViaSpecimenFieldEnum.PATIENT_NAME.getField());
            nameItem.setValue(UserName.maskTool(dto.getName()));

            ViaFormItem ageItem = new ViaFormItem();
            ageItem.setFormName(ViaSpecimenFieldEnum.PATIENT_AGE.getField());
            ageItem.setValue(Objects.toString(dto.getAge(), null));

            ViaFormItem genderItem = new ViaFormItem();
            genderItem.setFormName(ViaSpecimenFieldEnum.PATIENT_GENDER.getField());
            genderItem.setValue(GenderEnum.getDescOfType(dto.getGender()));

            // 头像
            ViaFormItem headerImageUrl = new ViaFormItem();
            headerImageUrl.setFormName(ViaSpecimenFieldEnum.PATIENT_HEADER_IMAGE.getField());
            String url = viaComponentDomainService.queryPatientHeadImage(dto.getGender(), dto.getAge());
            headerImageUrl.setValue(url);


            ViaFormItem codeItem = new ViaFormItem();
            codeItem.setFormName(ViaSpecimenFieldEnum.SPECIMEN_CODE.getField());
            List<String> codes = survive.stream().map(MedicalPromiseDTO::getSpecimenCode).collect(Collectors.toList());
            codeItem.setValue(String.join(",", codes));
            formItemList.add(codeItem);

            formItemList.add(nameItem);
            formItemList.add(ageItem);
            formItemList.add(genderItem);
            formItemList.add(headerImageUrl);

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            Map<Long, JdhPromisePatient> patientMap = patients.stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity(), (o, n) -> o));

            //判断需要展示几个btn,按报告展示格式分组
            Map<Integer, List<MedicalPromiseDTO>> collect = survive.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getReportShowType));

            List<ViaBtnInfo> resBtns = Lists.newArrayList();
            config.setBtnList(resBtns);
            // 统计异常项目

            AtomicReference<Boolean> reportExist = new AtomicReference<>(Boolean.FALSE);
            collect.forEach((reportShowType,mps)->{

                //如果是结构化报告展示，判断有没有出报告
                if (Objects.equals(reportShowType, ReportShowTypeEnum.STRUCT.getType())){

                    if(mps.stream().anyMatch(e -> Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.COMPLETED.getStatus()))){
                        reportExist.set(Boolean.TRUE);
                        config.setStatusDesc("报告已出");
                        config.setViaStatus(MedicalPromiseStatusEnum.COMPLETED.getStatus());

                        //走原逻辑
                        if (StringUtils.isNotBlank(btnJson)){
                            // 此处不能使用原生的sourceData，保证隔离
                            Map<String, Object> dataMap = Maps.newHashMap(sourceData);
                            JdhPromisePatient patient = patientMap.get(dto.getPromisePatientId());
                            dataMap.put(SupportAggregateEnum.PATIENT.getCode(), patient);
                            ViaBtnInfo btn = JSON.parseObject(btnJson, ViaBtnInfo.class);
                            btn.init(dataMap);
                            btn.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                            String tip = mps.stream().map(MedicalPromiseDTO::getServiceItemName).collect(Collectors.joining(","));
                            btn.setBtnTip(tip);
                            if (collect.size()==1){
                                List<Long> medicalPromiseIds = mps.stream()
                                        .filter(e -> MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(e.getStatus()))
                                        .map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());

                                Integer count = medicalReportApplication.queryAbnormalCount(medicalPromiseIds);
                                if (Objects.nonNull(count)) {
                                    ViaFormItem abnormal = new ViaFormItem();
                                    abnormal.setFormName(ViaSpecimenFieldEnum.ABNORMAL_NUM.getField());
                                    abnormal.setValue(String.valueOf(count));
                                    formItemList.add(abnormal);
                                }
                            }

                            resBtns.add(btn);

                        }
                    }else {
                        if (StringUtils.isNotBlank(btnJson)){
                            ViaBtnInfo btn = JSON.parseObject(btnJson, ViaBtnInfo.class);
                            btn.setStyle("disabled");
                            Set<Integer> statusSet = mps.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toSet());
                            if (statusSet.contains(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus())) {
                                btn.setName("检测中");
                                btn.setLevel(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
                                if(!Boolean.TRUE.equals(reportExist.get())) {
                                    config.setStatusDesc("检测中");
                                    config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
                                }

                            } else {
                                if (!MEDICAL_WAIT_TOLAB_STATUS_LIST.contains(jdhPromise.getPromiseStatus())) {
                                    btn.setName("待送检");
                                    btn.setLevel(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                                    if(!Boolean.TRUE.equals(reportExist.get())) {
                                        config.setStatusDesc("待送检");
                                        config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                                    }
                                } else {
                                    btn.setName("送检中");
                                    btn.setLevel(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                                    if(!Boolean.TRUE.equals(reportExist.get())) {
                                        config.setStatusDesc("送检中");
                                        config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                                    }
                                }
                            }
                            resBtns.add(btn);
                        }

                    }

                }else if (Objects.equals(reportShowType,ReportShowTypeEnum.PDF.getType())){
                    //如果是PDF展示，手动创建按钮
                    for(MedicalPromiseDTO medicalPromiseDTO : mps){
                        if (StringUtils.isNotBlank(btnJson)){
                            ViaBtnInfo btn = JSON.parseObject(btnJson, ViaBtnInfo.class);
                            btn.setBtnTip(medicalPromiseDTO.getServiceItemName());
                            btn.setCode(ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode());
                            ViaActionInfo viaActionInfo = new ViaActionInfo();
                            btn.setAction(viaActionInfo);
                            //如果是已出报告
                            if (Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(),medicalPromiseDTO.getStatus())){
                                btn.setStyle("primaryBordered");
                                btn.setName("查看报告");
                                viaActionInfo.setType("request");
                                viaActionInfo.setFunctionId("jdh_o2oservice_queryUrlForCenter");
                                Map<String,Object> params = Maps.newHashMap();
                                params.put("medicalPromiseId",medicalPromiseDTO.getMedicalPromiseId());
                                viaActionInfo.setParams(params);
                                config.setStatusDesc("报告已出");
                                config.setViaStatus(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                reportExist.set(Boolean.TRUE);
                            }else{

                                Set<Integer> statusSet = mps.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toSet());
                                if (statusSet.contains(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus())) {
                                    btn.setName("检测中");
                                    btn.setLevel(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
                                    if(!Boolean.TRUE.equals(reportExist.get())) {
                                        config.setStatusDesc("检测中");
                                        config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
                                    }

                                }else {
                                    if(!MEDICAL_WAIT_TOLAB_STATUS_LIST.contains(jdhPromise.getPromiseStatus())){
                                        btn.setName("待送检");
                                        btn.setStyle("disabled");
                                        if(!Boolean.TRUE.equals(reportExist.get())) {
                                            config.setStatusDesc("待送检");
                                            config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                                        }
                                    }else{
                                        btn.setName("送检中");
                                        btn.setStyle("disabled");
                                        if(!Boolean.TRUE.equals(reportExist.get())) {
                                            config.setStatusDesc("送检中");
                                            config.setViaStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
                                        }
                                    }
                                }

                            }
                            resBtns.add(btn);

                        }
                    }

                }


            });

            if (CollectionUtils.isNotEmpty(resBtns)) {
                resBtns.sort(Comparator.comparing(ViaBtnInfo::getLevel).reversed());
            }
            if (!reportExist.get()){
                config.setBtnList(null);
            }
            config.setFormItemList(formItemList);
            floorConfigList.add(config);
        });
        viaFloorInfo.setFloorConfigList(floorConfigList);
        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterMedicalPromise viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 处理采样教程楼层，当前只有自采样的业务需要采样教程。
     * 互医的场景可能有多个SKU，但是只取了一个SKU的采样教程，业务上保证互医渠道的SKU配置一个通用的采样教程。
     * 理想情况下采样教程应该取检测项目维度的。
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleFooterSampleCourse(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterSampleCourse start");
        List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
        JdhSkuDto skuDto = findMainSku(jdhPromise);
        if (StringUtils.isNotBlank(skuDto.getTutorialVideoThumbnailUrl())) {
            ViaFloorConfig header = new ViaFloorConfig();
            header.setFieldKey(ViaSampleCourseFiledEnum.VIDEO_HEADER_IMAGE_URL.getField());
            header.setFieldValue(skuDto.getTutorialVideoThumbnailUrl());
            viaFloorConfigList.add(header);
        }

        if (StringUtils.isNotBlank(skuDto.getTutorialVideoUrl())) {
            ViaFloorConfig video = new ViaFloorConfig();
            video.setFieldKey(ViaSampleCourseFiledEnum.VIDEO_URL.getField());
            video.setFieldValue(skuDto.getTutorialVideoUrl());
            viaFloorConfigList.add(video);
        }

        if (CollectionUtils.isNotEmpty(skuDto.getTutorialCarouselUrl())) {
            ViaFloorConfig image = new ViaFloorConfig();
            image.setFieldKey(ViaSampleCourseFiledEnum.IMAGES_URL.getField());
            image.setFieldValue(JSON.toJSONString(skuDto.getTutorialCarouselUrl()));
            viaFloorConfigList.add(image);
        }

        viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        log.info("HomeAngelCareOrderDetailHandlerV2 handleFooterSampleCourse viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    private void handelMaterialInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        if (CollUtil.isNotEmpty(medicalPromiseList)) {
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();
                ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                JdhPromisePatient jdhPromisePatient = promisePatientMap.get(medPromiseList.get(0).getPromisePatientId());
                String userName = jdhPromisePatient.getUserName().getName();
                String genderLabel = GenderEnum.getDescOfType(jdhPromisePatient.getGender());
                String age = jdhPromisePatient.getBirthday().getAge() + "岁";
//                String title = String.join(" ", userName, genderLabel, age)
//                viaFloorConfig.setTitle(title);

                //是否有已出报告,且没有退款
                List<MedicalPromiseDTO> reportedList = medPromiseList.stream().filter(ele ->
                        (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) &&
                                !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                                        || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))
                ).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(reportedList)) {
                    MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                    fillMedPromiseFloorConfig(viaFloorConfig, reportedMedPromise, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                    viaFloorConfigList.add(viaFloorConfig);
                } else {
                    for (MedicalPromiseDTO medicalPromiseDTO : medPromiseList) {
                        // 冻结或者作废不展示
                        if (JdhFreezeEnum.FREEZE.getStatus().equals(medicalPromiseDTO.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDTO.getStatus())) {
                            continue;
                        }
                        fillMedPromiseFloorConfig(viaFloorConfig, medicalPromiseDTO, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                        viaFloorConfigList.add(viaFloorConfig);
                    }
                }
            }
            viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        }
        log.info("HomeAngelCareOrderDetailHandlerV2 handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.HOME_ORDER_DETAIL.getScene() + "_" + BusinessModeEnum.ANGEL_CARE.getCode() + "_" + ServiceTypeEnum.CARE.getServiceType() + "_" + "V2";
    }


    /**
     * 构建联系客服的按钮
     *
     * @param btnInfo
     * @param ctx
     * @param jdOrder
     * @param action
     */
    private void buildContactCustomerBtn(ViaBtnInfo btnInfo, FillViaConfigDataContext ctx, JdOrderDTO jdOrder, ViaActionInfo action) {
        //联系客服
        if (ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())) {
            String envCode = EnvTypeEnum.get(ctx.getEnvType()).getCode();
            Map<String, String> urlMap = JSON.parseObject(btnInfo.getJumpUrlRule(), new TypeReference<Map<String, String>>() {
            });
            String jumpUrl = urlMap.get(envCode);
            if (EnvTypeEnum.JD_APP.getCode().equals(ctx.getEnvType())) {
                //openapp.jdmobile://virtual?params={"category":"jump","des":"jd_dongdong_chat","entry":"jd_sdk_kjzydxy","orderId":"{0}"}
                action.setUrl(jumpUrl.replace("{0}", jdOrder.getVenderId()).replace("{1}", jdOrder.getOrderId().toString()));
            } else {
                //https://jdcs.m.jd.com/chat/index.action?entry=jd_sdk_kjzydxy&orderId={0}
                action.setUrl(MessageFormat.format(jumpUrl, jdOrder.getVenderId(), jdOrder.getOrderId().toString()));
            }
        }
    }
}
