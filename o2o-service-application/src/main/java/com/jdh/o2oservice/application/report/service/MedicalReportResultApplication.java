package com.jdh.o2oservice.application.report.service;

import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorES;
import com.jdh.o2oservice.export.report.cmd.*;
import com.jdh.o2oservice.export.report.dto.IndicatorResultAndAggDTO;
import com.jdh.o2oservice.export.report.dto.MedPromisePositiveRateDTO;
import com.jdh.o2oservice.export.report.query.MedicalReportIndicatorRateFromEsRequest;

import java.util.List;

/**
 *  MedicalReportResultApplication
 * <AUTHOR>
 * @date 2024-11-13 13:21
 */
public interface MedicalReportResultApplication {



    /**
     * 分析指定医疗承诺的报告指标。
     * @param medicalPromiseId 医疗承诺的ID。
     * @return 分析结果，true表示分析成功，false表示分析失败。
     */
    Boolean analyseReportIndicators(Long medicalPromiseId);


    /**
     * 保存报告指标。
     * @param reportIndicatorCmds 报告指标命令列表。
     * @return 保存操作是否成功。
     */
    Boolean saveReportIndicators(List<ReportIndicatorCmd> reportIndicatorCmds);


    /**
     * 刷新医疗报告指标的标识符。
     * @param medicalReportIndicatorFlushCmd 包含要刷新的医疗报告指标的命令对象。
     * @return 如果成功刷新指标标识符，则返回 true；否则返回 false。
     */
    Boolean flushReportIndicatorId(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);

    /**
     * 刷新医疗报告指标。
     * @param medicalReportIndicatorFlushCmd 包含刷新指令的对象。
     * @return 是否成功刷新指标。
     */
    Boolean flushReportIndicator(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);

    /**
     * 刷新医疗报告指标的检查时间。
     * @param medicalReportIndicatorFlushCmd 包含要刷新的医疗报告指标信息的命令对象。
     * @return 刷新操作是否成功。
     */
    Boolean flushCheckTime(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);

    /**
     * 阳性率
     * @param medPromisePosRateCmd 查询条件对象
     * @return 医保承诺履行率DTO对象
     */
    MedPromisePositiveRateDTO queryMedPromisePositiveRate(MedPromisePosRateCmd medPromisePosRateCmd);

    /**
     *
     * @param fixStructReportCmd
     * @return
     */
    Boolean fixStructReport(FixStructReportCmd fixStructReportCmd);

    /**
     * 模拟完成患者 SKU 订单
     * @param promisePatientSkuFinishCmd 包含完成患者 SKU 订单所需信息的命令对象
     * @return 是否成功模拟完成患者 SKU 订单
     */
    Boolean mockPromisePatientSkuFinish(PromisePatientSkuFinishCmd promisePatientSkuFinishCmd);


    /**
     * 刷新报告中心ID。
     * @param fixStructReportCmd 包含报告中心ID的FixStructReportCmd对象。
     * @return 如果操作成功，则返回true；否则返回false。
     */
    Boolean flushReportCenterId(FixStructReportCmd fixStructReportCmd);

    /**
     * 通过检测单同步报告中心的数据。
     * @param fixStructReportCmd 报告中心数据的固定结构体命令。
     * @return 同步操作是否成功。
     */
    Boolean syncReportCenterByMp(FixStructReportCmd fixStructReportCmd);

    /**
     * 修复空白报告
     * @param fixStructReportCmd 报告中心数据的固定结构体命令。
     * @return 同步操作是否成功。
     */
    Boolean fixEmptyJpg(FixStructReportCmd fixStructReportCmd);


    /**
     * 刷新报告ct值
     * @param fixStructReportCmd 报表命令结构体，包含报表的相关信息。
     * @return 是否成功刷新报表计数器值。
     */
    Boolean flushReportCtValue(FixStructReportCmd fixStructReportCmd);

    /**
     * 阳性率
     * @param medPromisePosRateCmd 查询条件对象
     * @return 医保承诺履行率DTO对象
     */
    MedPromisePositiveRateDTO queryMedPromisePositiveRateNew(MedPromisePosRateCmd medPromisePosRateCmd);

    Boolean flushReportIndicatorIdOnly(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);


    /**
     * 保存报告指标到ES。
     * @param medicalReportIndicatorFlushToEsCmd 报告指标命令列表。
     * @return 保存操作是否成功。
     */
    Boolean flushReportIndicatorsToEs(MedicalReportIndicatorFlushToEsCmd medicalReportIndicatorFlushToEsCmd);

    /**
     * 删除报告指标ES。
     * @param medicalReportIndicatorDeleteFromEsCmd 报告指标命令列表。
     * @return 保存操作是否成功。
     */
    Boolean deleteReportIndicatorsFromEs(MedicalReportIndicatorDeleteFromEsCmd medicalReportIndicatorDeleteFromEsCmd);

    /**
     * 查询单条数据
     * @param id
     * @return
     */
    MedicalReportIndicator getMedicalReportIndicatorById(Long id);

    /**
     * 实验室指标阳性率
     * @param req
     * @return
     */
    IndicatorResultAndAggDTO getRateIndicator(MedicalReportIndicatorRateFromEsRequest req);

}
