package com.jdh.o2oservice.application.product.service;

import com.jdh.o2oservice.core.domain.product.bo.UserAddressDetailBO;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.ProductServiceGoodsListRequest;
import com.jdh.o2oservice.export.product.query.ServiceGoodsContrastRequest;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName ProductPoolApplication
 * @Description
 * <AUTHOR>
 * @Date 2024/3/25 22:39
 **/
public interface ProductPoolApplication {

    /**
     * 检查项数量是否符合
     * @param skuId
     * @return
     */
    Boolean goodsCheckItemAccord(String skuId);

    /**
     * 套餐对比加入套餐池
     * @return
     */
    Boolean addServiceGoodsToPool(String userPin,String skuId);

    /**
     * 从套餐池删除服务套餐
     * @return
     */
    Boolean removeServiceGoodsFromPool(String userPin,List<String> skuIdList);

    /**
     * 查看用户套餐池列表
     * @param request
     * @return
     */
    List<ServiceGoodsPoolDto> queryServiceGoodsPoolListOfPin(ProductServiceGoodsListRequest request);

    /**
     * 查看套餐对比列表
     * @param request
     * @return
     */
    ServiceBizItemContrastlDto showGoodsContrast(ServiceGoodsContrastRequest request);
    /**
     * 套餐之适用人群对比列表
     * @param request
     * @return
     */
    ServiceBizItemContrastlDto programSuitableContrast(ServiceGoodsContrastRequest request);
    /**
     * 查看检查项目详情
     * @param request
     * @return
     */
    List<BizIndicatorItemCategoryDto> getIndicatorDetail(ProductServiceGoodsListRequest request);

    /**
     * 查看套餐对比差异项
     * @param request
     * @return
     */
    ServiceBizItemContrastlDto showGoodsContrastDiff(ServiceGoodsContrastRequest request);

    /**
     * 查询到手价
     * @param skuId
     * @param addressId
     * @param userPin
     * @return
     */
    String getPurchasePrice(String skuId, Long addressId, String userPin);


    /**
     * 查询到手价
     * @param skuIds
     * @param userAddressDetail
     * @param userPin
     * @return
     */
    Map<String, ProductPurchasePriceDto> getProductPurchasePriceMap(Set<String> skuIds, UserAddressDetailBO userAddressDetail, String userPin);

}