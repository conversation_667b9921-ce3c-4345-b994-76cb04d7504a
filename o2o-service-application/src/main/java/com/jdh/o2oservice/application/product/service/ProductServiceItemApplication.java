package com.jdh.o2oservice.application.product.service;

import com.jdh.o2oservice.common.result.response.ImportResult;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.bo.JdhContentBO;
import com.jdh.o2oservice.export.product.cmd.*;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.*;
import com.jdh.o2oservice.export.report.cmd.SyncServiceItemIndicatorCmd;

import java.io.InputStream;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/16 5:32 下午
 * @Description:
 */
public interface ProductServiceItemApplication {


    /**
     * 查询内容百科id是否存在
     *
     * @param query
     * @return
     */
    JdhContentBO queryContentById(JdhContentQuery query);

    /**
     * 批量保存项目
     *
     * @param serviceItemCmdList
     * @return
     */

    Boolean batchSaveServiceItem(List<SaveServiceItemCmd> serviceItemCmdList);


    /**
     * 分页查询项目列表
     *
     * @param serviceItemQuery
     * @return
     */
    PageDto<ServiceItemDto> queryServiceItemPage(ServiceItemQuery serviceItemQuery);


    /**
     * 查询项目详细
     *
     * @param serviceItemQuery
     * @return
     */
    ServiceItemDto queryServiceItemDetail(ServiceItemQuery serviceItemQuery);


    /**
     * 查询项目列表
     *
     * @param serviceItemQuery
     * @return
     */
    List<ServiceItemDto> queryServiceItemList(ServiceItemQuery serviceItemQuery);

    /**
     * 查询指标分类
     *
     * @param categoryQuery
     * @return
     */
    List<IndicatorCategoryDto> queryIndicatorCategory(IndicatorCategoryQuery categoryQuery);


    /**
     * 导入项目
     *
     * @param inputStream
     * @param pin
     * @param imptResult
     */
    @Deprecated
    void importServiceItem(InputStream inputStream, String pin, ImportResult imptResult);


    /**
     * 导入指标
     *
     * @param inputStream
     * @param pin
     * @param imptResult
     */
    @Deprecated
    void importIndicator(InputStream inputStream, String pin, ImportResult imptResult);

    /**
     * 导入指标
     *
     * @param inputStream
     * @param pin
     * @param imptResult
     */
    @Deprecated
    void importIndicatorCate(InputStream inputStream, String pin, ImportResult imptResult);

    /**
     * 异步导入项目列表
     *
     * @param cmd cmd
     * @return true
     */
    Boolean importProductItem(ServiceItemImportCmd cmd);

    /**
     * 查询标准项目
     *
     * @param request
     * @return
     */
    List<StandardItemDTO> queryStandardItemList(StandardItemRequest request);

    /**
     * 查询标准指标
     *
     * @param request
     * @return
     */
    List<StandardIndicatorDTO> queryStandardIndicatorList(StandardIndicatorRequest request);

    /**
     * 查询适用人群
     *
     * @return
     */
    List<ItemSuitableDTO> queryItemSuitableList();

    /**
     * 分页查询业务项目
     *
     * @param request
     * @return
     */
    PageDto<BizItemDTO> pageBizItem(PageBizItemRequest request);

    /**
     * 创建发品业务项目
     *
     * @pam productBizItemCmd
     * @return
     */
    Boolean createPublishProductBizItem(SaveProductBizItemCmd productBizItemCmd);

    /**
     * 查询业务项目对应的指标
     * @param bizItemId
     * @return
     */
    List<StandardIndicatorDTO> queryIndicatorListById(Long bizItemId);

    /**
     * 保存业务项目
     * @param productBizItemAddCmd
     * @return
     */
    Boolean saveProductBizItem(ProductBizItemAddCmd productBizItemAddCmd);

    /**
     * 发布商家商品服务项目
     *
     * @param productBizItemCmd
     * @return
     */
    Boolean publishMerchantProductItem(SaveProductBizItemCmd productBizItemCmd);

    /**
     * 删除业务项目
     * @param productBizItemDeleteCmd
     * @return
     */
    Boolean deleteBizItem(ProductBizItemDeleteCmd productBizItemDeleteCmd);

    /**
     * 分页查询业务项目
     *
     * @param request
     * @return
     */
    List<BizItemDTO> queryBizItemList(PageBizItemRequest request);

    /**
     * 查询业务项目对应的指标
     * @param bizItemId
     * @return
     */
    List<StandardIndicatorDTO> queryIndicatorListByBizItemList(List<Long> bizItemId);

    /**
     * 根据条件查询项目列表
     *
     * @param serviceItemConditionQuery
     * @return
     */
    List<ServiceItemDto> queryServiceItemListCondition(ServiceItemConditionQuery serviceItemConditionQuery);

    /**
     *
     * @param syncServiceItemIndicatorCmd
     * @return
     */
    Boolean syncServiceItemIndicator(SyncServiceItemIndicatorCmd syncServiceItemIndicatorCmd);

    /**
     * 查询服务项目包含问题
     *
     * @param serviceItemQuery
     * @return
     */
    List<ServiceItemDto> queryServiceItemListWithQuestion(ServiceItemQuery serviceItemQuery);
}
