package com.jdh.o2oservice.application.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.trade2.base.export.common.util.CollectionUtils;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.report.convert.ReportAuditConvert;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.report.service.ReportAuditApplication;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.SubStatusAlarmConfig;
import com.jdh.o2oservice.base.ducc.model.report.VerifyReportAnomalyIndicatorConflictConfig;
import com.jdh.o2oservice.base.ducc.model.report.VerifyReportAnomalyIndicatorNumConfig;
import com.jdh.o2oservice.base.ducc.model.report.VerifyReportBaseRootConfig;
import com.jdh.o2oservice.base.ducc.model.report.VerifyReportRootConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ReportVerifyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.TextUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.provider.rpc.QuickCheckThirdExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.FlowCodeListBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.FreeFlowCodeBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.QuickCheckResultReviewBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.QuickCheckResultTaskBO;
import com.jdh.o2oservice.core.domain.report.ReportErrorCode;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportDataQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.ReportAuditPageQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.ReportAuditQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportData;
import com.jdh.o2oservice.core.domain.report.model.ReportAudit;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportDataRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.ReportAuditRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseLogCmd;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.report.cmd.ReportAuditCmd;
import com.jdh.o2oservice.export.report.cmd.ReportAuditResultCmd;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.enums.SampleAnomalyOperateReasonTypeEnum;
import com.jdh.o2oservice.export.report.enums.SampleAnomalyOperateTypeEnum;
import com.jdh.o2oservice.export.report.query.ReportAuditPageRequest;
import com.jdh.o2oservice.export.report.query.ReportAuditRequest;
import com.jdh.o2oservice.export.report.query.ReportAuditTestRequest;
import com.jdh.o2oservice.export.support.command.OperationLogCmd;
import com.jdh.o2oservice.export.support.enums.OperationLogOperateTypeEnum;
import com.jdh.o2oservice.export.support.enums.OperationLogResultTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;


import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/22
 */
@Component
@Slf4j
public class ReportAuditApplicationImpl implements ReportAuditApplication {


    /**
     * 报告审核仓储的实例，用于执行与报告审核相关的数据库操作。
     */
    @Autowired
    private ReportAuditRepository reportAuditRepository;



    @Resource
    private RedisLockUtil redisLockUtil;


    @Autowired
    private MedicalReportDataRepository medicalReportDataRepository;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Autowired
    private EventCoordinator eventCoordinator;

    @Autowired
    private MedicalReportRepository medicalReportRepository;

    @Autowired
    private PromiseRepository promiseRepository;


    @Autowired
    private QuickCheckThirdExportServiceRpc quickCheckThirdExportServiceRpc;

    @Autowired
    private FileManageService fileManageService;

    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    @Autowired
    private DuccConfig duccConfig;

    @Autowired
    private DongDongRobotRpc dongDongRobotRpc;

    @Autowired
    private OperationLogApplication operationLogJsfExport;

    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    private MedicalReportApplication medicalReportApplication;

    @Autowired
    private PromiseApplication promiseApplication;

   /**
     * 保存报告审核记录
     *
     * @param reportAuditCmd 报告审计命令对象
     * @return 保存操作是否成功
     */
    @Override
    public Boolean saveReportAudit(ReportAuditCmd reportAuditCmd) {
        reportAuditRepository.saveReportAudit(null);
        return null;
    }

    /**
     * 分页查询报告审核记录。
     *
     * @param request 报告审计分页查询请求对象。
     * @return 包含报告审计记录的分页数据。
     */
    @Override
    public PageDto<ReportAuditBaseDTO> queryReportAuditPage(ReportAuditPageRequest request) {

        ReportAuditPageQueryBO queryBO = ReportAuditConvert.INSTANCE.convert(request);

        Page<ReportAudit> reportAuditPage = reportAuditRepository.queryReportAuditPage(queryBO);
        PageDto<ReportAuditBaseDTO> auditBaseDTOPageDto = ReportAuditConvert.INSTANCE.convert(reportAuditPage);

        if (Objects.nonNull(auditBaseDTOPageDto) && CollectionUtil.isNotEmpty(auditBaseDTOPageDto.getList())){
            packDTO(auditBaseDTOPageDto.getList());
        }

        return auditBaseDTOPageDto;
    }

    /**
     * 保存审核结果。
     *
     * @param reportAuditResultCmd 审计结果命令对象。
     * @return 保存操作是否成功。
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean saveAuditResult(ReportAuditResultCmd reportAuditResultCmd) {


        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SAVE_REPORT_AUDIT_LOCK,reportAuditResultCmd.getExperimentalId());
        String exceptValue = UUID.randomUUID().toString();
        log.info("ReportAuditApplicationImpl saveAuditResult lockKey:{} , exceptValue:{}", lockKey, exceptValue);
        try {
            boolean lock = redisLockUtil.tryLock(lockKey, exceptValue, RedisKeyEnum.SAVE_REPORT_AUDIT_LOCK.getExpireTime(),RedisKeyEnum.SAVE_REPORT_AUDIT_LOCK.getExpireTimeUnit());
            if (!lock) {
                //操作频繁
                throw new BusinessException(ReportErrorCode.OPERATE_TOO_FAST);
            }


            //搜索孔位数据
            List<MedicalReportData> medicalReportDatas = medicalReportDataRepository.queryMedicalReportDataList(
                    MedicalReportDataQueryBO.builder()
                            .holes(reportAuditResultCmd.getHoles())
                            .experimentalId(reportAuditResultCmd.getExperimentalId())
                            .build()
            );
            log.info("ReportAuditApplicationImpl saveAuditResult medicalReportDatas:{}", JsonUtil.toJSONString(medicalReportDatas));

            MedicalReportData errorData = medicalReportDatas.stream().filter(p -> Objects.equals(ReportAuditStatusEnum.DATA_ERROR.getAuditStatus(), p.getAuditStatus())).findFirst().orElse(null);
            if (Objects.nonNull(errorData)) {
                throw new BusinessException(ReportErrorCode.REPORT_DATA_ERROR);
            }


            //将孔位数据置为已审核
            for (MedicalReportData medicalReportData : medicalReportDatas) {

                if (
                        Objects.equals(ReportAuditStatusEnum.PASS.getAuditStatus(),reportAuditResultCmd.getAuditStatus())
                        && StringUtils.isNotBlank(medicalReportData.getValue())
                        && isNumber(medicalReportData.getValue())
                        && isValidNumber(medicalReportData.getValue())
                ){
                    throw new BusinessException(ReportErrorCode.CT_ERROR);
                }

                if (!Objects.equals(ReportAuditStatusEnum.INIT.getAuditStatus(),medicalReportData.getAuditStatus())){
                    continue;
                }
                medicalReportData.setAuditStatus(reportAuditResultCmd.getAuditStatus());
                medicalReportData.setAuditTime(new Date());
                medicalReportData.setAuditUser(reportAuditResultCmd.getAuditUser());
            }
            medicalReportDataRepository.updateMedicalReportDataAudit(medicalReportDatas);

            //调用三方提交审核记录
//            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
//                syncToStation(reportAuditResultCmd, medicalReportDatas);
//            });


            //查询实验对应检测单的所有数据
            List<MedicalReportData> medicalReportDataAll = medicalReportDataRepository.queryMedicalReportDataList(
                    MedicalReportDataQueryBO.builder()
                            .experimentalId(reportAuditResultCmd.getExperimentalId())
                            .build()
            );
            log.info("ReportAuditApplicationImpl saveAuditResult medicalReportDataAll:{}", JsonUtil.toJSONString(medicalReportDataAll));


            Map<Long, List<MedicalReportData>> medicalPromiseToData = medicalReportDataAll.stream().collect(Collectors.groupingBy(MedicalReportData::getMedicalPromiseId));


            //查询本次修改的孔位对应的审批单
            List<Long> medicalPromiseIds = medicalReportDatas.stream().map(MedicalReportData::getMedicalPromiseId).collect(Collectors.toList());
            List<ReportAudit> reportAudits = reportAuditRepository.queryReportAuditList(
                    ReportAuditQueryBO.builder()
                            .experimentalId(reportAuditResultCmd.getExperimentalId())
                            .medicalPromiseIds(medicalPromiseIds)
                            .build()
            );
            log.info("ReportAuditApplicationImpl saveAuditResult reportAudits:{}", JsonUtil.toJSONString(reportAudits));

            List<Long> mpIds = medicalReportDatas.stream().map(MedicalReportData::getMedicalPromiseId).distinct().collect(Collectors.toList());
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(mpIds).build());
            Map<Long, String> mpIdToStation = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, MedicalPromise::getStationId));
            log.info("ReportAuditApplicationImpl saveAuditResult mpIdToStation:{}", JsonUtil.toJSONString(mpIdToStation));
            List<MedicalReport> medicalReports = medicalReportRepository.getByMedicalPromiseIdList(mpIds);
            Map<Long, String> mpToReportOss = medicalReports.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, MedicalReport::getReportOss));
            Map<Long, MedicalReport> mpToReport = medicalReports.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, p -> p));
            log.info("ReportAuditApplicationImpl saveAuditResult mpToReportOss:{}", JsonUtil.toJSONString(mpIdToStation));


            //如果是审批驳回，则有一个驳回就是驳回
            if (Objects.equals(ReportAuditStatusEnum.REFUSE.getAuditStatus(),reportAuditResultCmd.getAuditStatus())){
                //将未评审过的置为驳回，其他不变
                refuseDeal(reportAuditResultCmd, medicalReportDataAll, reportAudits,mpIdToStation,mpToReportOss);
                return Boolean.TRUE;
            }
            dealPass(reportAuditResultCmd, reportAudits, medicalPromiseToData,mpIdToStation,mpToReportOss,mpToReport);
        } catch (Exception e) {
            log.error("ReportAuditApplicationImpl saveAuditResult exception", e);
            throw e;
        } finally {
            redisLockUtil.unLock(lockKey, exceptValue);
        }

        return Boolean.TRUE;
    }




    /**
     * @param request
     * @return
     */
    @Override
    public ReportAuditTestDTO queryReportAuditTestInfo(ReportAuditTestRequest request) {
        List<ReportAudit> reportAudits = reportAuditRepository.queryReportAuditList(ReportAuditQueryBO.builder().medicalPromiseId(request.getMedicalPromiseId()).experimentalId(request.getExperimentalId()).build());
        if (CollectionUtil.isEmpty(reportAudits)){
            return null;
        }
        ReportAudit reportAudit = reportAudits.get(0);
        ReportAuditTestDTO reportAuditTestDTO = new ReportAuditTestDTO();
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(reportAudit.getMedicalPromiseId()).build());
        reportAuditTestDTO.setAuditStatus(reportAudits.get(0).getAuditStatus());
        reportAuditTestDTO.setExperimentalId(request.getExperimentalId());
        reportAuditTestDTO.setTestTime(medicalPromise.getTestTime());
        reportAuditTestDTO.setTestFinishTime(medicalPromise.getTestFinishTime());

        MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(reportAudit.getMedicalPromiseId());
        if (Objects.nonNull(medicalReport)){
            reportAuditTestDTO.setEquipmentModel(medicalReport.getEquipmentModel());
            reportAuditTestDTO.setEquipmentSn(medicalReport.getSnCode());
        }

        return reportAuditTestDTO;
    }

    /**
     * 查询报表审计列表
     *
     * @param reportAuditRequest 报表审计请求对象
     * @return 报表审计基础DTO列表
     */
    @Override
    public List<ReportAuditBaseDTO> queryReportAuditList(ReportAuditRequest reportAuditRequest) {
        List<ReportAudit> reportAudits = reportAuditRepository.queryReportAuditList(ReportAuditConvert.INSTANCE.convert(reportAuditRequest));
        List<ReportAuditBaseDTO> reportAuditBaseDTOS = ReportAuditConvert.INSTANCE.convert(reportAudits);
        if (CollectionUtil.isNotEmpty(reportAuditBaseDTOS)){
            packDTO(reportAuditBaseDTOS);
        }
        return reportAuditBaseDTOS;
    }


    /**
     * 报告审核驳回
     * @param reportAuditResultCmd 审核结果命令对象，包含审核状态、审核时间和审核用户等信息。
     * @param medicalReportDataAll 医疗报告数据列表。
     * @param reportAudits 审核记录列表。
     */
    private void refuseDeal(ReportAuditResultCmd reportAuditResultCmd, List<MedicalReportData> medicalReportDataAll, List<ReportAudit> reportAudits,Map<Long, String> mpIdToStation,Map<Long, String> mpToReportOss) {
        //本次涉及到的检测单ID
        Set<Long> medicalPromiseIds = reportAudits.stream().map(ReportAudit::getMedicalPromiseId).collect(Collectors.toSet());
        List<MedicalReportData> initData = medicalReportDataAll.stream().filter(p -> Objects.equals(ReportAuditStatusEnum.INIT.getAuditStatus(), p.getAuditStatus()) && medicalPromiseIds.contains(p.getMedicalPromiseId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(initData)){
            for (MedicalReportData medicalReportData : initData) {
                medicalReportData.setAuditStatus(reportAuditResultCmd.getAuditStatus());
                medicalReportData.setAuditTime(new Date());
                medicalReportData.setAuditUser(reportAuditResultCmd.getAuditUser());
            }
            medicalReportDataRepository.updateMedicalReportDataAudit(initData);
        }
        Map<Long, List<MedicalReportData>> medicalPromiseToData = medicalReportDataAll.stream().filter(p->medicalPromiseIds.contains(p.getMedicalPromiseId())).collect(Collectors.groupingBy(MedicalReportData::getMedicalPromiseId));

        if (CollUtil.isNotEmpty(medicalPromiseToData)) {
            List<Long> medicalIds = new ArrayList<>(medicalPromiseToData.keySet());
            log.info("ReportAuditApplicationImpl refuseDeal 释放流转码 medicalIds:{}", JsonUtil.toJSONString(medicalIds));
            List<MedicalPromise> list = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(medicalIds).build());
            for (MedicalPromise medicalPromise : list) {
                log.info("ReportAuditApplicationImpl refuseDeal medicalPromise medicalPromise:{}", JsonUtil.toJSONString(medicalPromise));
                if (StringUtils.isNotBlank(medicalPromise.getFlowCode())) {
                    FlowCodeListBO flowCodeListBO = new FlowCodeListBO();
                    flowCodeListBO.setTransCode(medicalPromise.getFlowCode());
                    flowCodeListBO.setSampleBarcode(medicalPromise.getSpecimenCode());
                    quickCheckThirdExportServiceRpc.freeFlowCode(FreeFlowCodeBO.builder().medicalPromiseId(String.valueOf(medicalPromise.getMedicalPromiseId())).flowCodeList(Lists.newArrayList(flowCodeListBO)).stationId(medicalPromise.getStationId()).build());
                }
            }
        }

        //将未评审过的置为驳回，其他不变
        List<ReportAudit> initReportAudit = reportAudits.stream().filter(p -> Objects.equals(ReportAuditStatusEnum.INIT.getAuditStatus(), p.getAuditStatus())).collect(Collectors.toList());
        for (ReportAudit reportAudit : initReportAudit) {
            reportAudit.setAuditStatus(reportAuditResultCmd.getAuditStatus());
            reportAudit.setAuditTime(new Date());
            reportAudit.setAuditUser(reportAuditResultCmd.getAuditUser());
            if (Objects.nonNull(reportAudit.getAuditTimeOutDeadline())){
                if (DateUtil.compare(new Date(), reportAudit.getAuditTimeOutDeadline()) > 0){
                    reportAudit.setAuditTimeOutStatus(CommonConstant.ONE);
                }else {
                    reportAudit.setAuditTimeOutStatus(CommonConstant.ZERO);
                }
            }


            reportAuditRepository.updateReportAuditStatus(Lists.newArrayList(reportAudit));


            //通知实验室驳回

            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                String taskId = reportAuditResultCmd.getExperimentalId().split("_")[1];
                syncToStation(medicalPromiseToData.get(reportAudit.getMedicalPromiseId()),CommonConstant.ZERO_STR,mpIdToStation,mpToReportOss,taskId,reportAudit.getMedicalPromiseId());
            });


            MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(reportAudit.getMedicalPromiseId()).build());
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_REPORT_AUDIT_FINISH,
                    MedicalPromiseEventBody.builder()
                            .operateSystem(reportAuditResultCmd.getOperateSystem())
                            .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                            .reportAuditId(reportAudit.getReportAuditId())
                            .auditStatus(reportAudit.getAuditStatus())
                            .promiseId(medicalPromise.getPromiseId())
                            .sampleAnomalyOperateReasonType(reportAuditResultCmd.getSampleAnomalyOperateReasonType())
                            .sampleAnomalyOperateReason(reportAuditResultCmd.getSampleAnomalyOperateReason())
                            .sampleAnomalyOperateType(reportAuditResultCmd.getSampleAnomalyOperateType())
                            .experimentalId(reportAuditResultCmd.getExperimentalId())
                            .build()));

            //记录日志
            saveOperateRecord(reportAuditResultCmd, medicalPromise,BizSceneActionKeyEnum.AUDIT_REFUSE);


            //告警
            //判断是否需要告警
            List<SubStatusAlarmConfig> subStatusAlarmConfigs = duccConfig.getSubStatusAlarmConfig();

            SubStatusAlarmConfig subStatusAlarmConfig = subStatusAlarmConfigs.stream().filter(p -> Objects.equals(p.getSubStatus(), MedicalPromiseSubStatusEnum.REPORT_CHECK_ERROR.getSubStatus())).findFirst().orElse(null);
            if (Objects.nonNull(subStatusAlarmConfig)){
                //告警
                Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                JSONObject jsonObject = robotAlarmMap.get(subStatusAlarmConfig.getAlarmConfig());
                if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                    log.info("medicalPromiseCallBack -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                    return;
                }

                String stageDesc = MedPromiseMainStatusSyncEnum.REPORT.getCallBackMainStatusDesc();
                String reason = SampleAnomalyOperateReasonTypeEnum.packReasonDesc(reportAuditResultCmd.getSampleAnomalyOperateReasonType());
                if (CollectionUtils.isNotEmpty(reportAuditResultCmd.getSampleAnomalyOperateReasonType()) && reportAuditResultCmd.getSampleAnomalyOperateReasonType().contains(999)){
                    reason = reason+reportAuditResultCmd.getSampleAnomalyOperateReason();
                }
                String dealDesc = SampleAnomalyOperateTypeEnum.getDescByType(reportAuditResultCmd.getSampleAnomalyOperateType());
                //订单号
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromise.getPromiseId()).build());
                //样本编码
                //检测项目
                //实验室

                //如果是质控单
                String message = "";
                if (StringUtil.equals(medicalPromise.getVerticalCode(),"xfylMerchantQualityTest")){
                    message = medicalPromise.getStationName()+"实验室的质控样本检测结果不通过，报告审核不通过,日期："+DateUtil.format(new Date(),CommonConstant.YMDHMS)+",检测项目:"+medicalPromise.getServiceItemName()+",样本编码："+medicalPromise.getSpecimenCode()+",请及时关注";
                    dongDongRobotRpc.sendDongDongRobotMessage(message,jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
                }else {
                    Map<String,String> replaceMap = Maps.newHashMap();
                    replaceMap.put("stageDesc",stageDesc);
                    replaceMap.put("reason",reason);
                    replaceMap.put("dealDesc",dealDesc);
                    replaceMap.put("orderId",promise.getSourceVoucherId());
                    replaceMap.put("specimenCode",medicalPromise.getSpecimenCode());
                    replaceMap.put("serviceItemName",medicalPromise.getServiceItemName());
                    replaceMap.put("stationName",medicalPromise.getStationName());
                    replaceMap.put("promiseId", String.valueOf(medicalPromise.getPromiseId()));
                    replaceMap.put("medicalPromiseId", String.valueOf(medicalPromise.getMedicalPromiseId()));

                    String alarmTemple = null;
                    if (Objects.equals(SampleAnomalyOperateTypeEnum.RETEST.getType(),reportAuditResultCmd.getSampleAnomalyOperateType())){
                        alarmTemple = "实验室在{stageDesc}阶段反馈由于{reason}，需要{dealDesc}，订单号：{orderId},履约单号{promiseId},样本编码{specimenCode}，检测项目{serviceItemName}，实验室{stationName}";
                    }else {
                        alarmTemple = "实验室在{stageDesc}阶段反馈由于{reason}，需联系用户是否{dealDesc}，订单号：{orderId},履约单号{promiseId},样本编码{specimenCode}，检测项目{serviceItemName}，实验室{stationName}";
                    }

                    message = TextUtil.replacePlaceholders(alarmTemple,replaceMap);
                }

                dongDongRobotRpc.sendDongDongRobotMessage(message,jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));

            }
        }
    }




    /**
     * 处理审核结果并更新相关状态和发送事件
     * @param reportAuditResultCmd 审核结果命令对象
     * @param reportAudits 审核列表
     * @param medicalPromiseToData 医疗报告数据映射表
     */
    private void dealPass(ReportAuditResultCmd reportAuditResultCmd, List<ReportAudit> reportAudits, Map<Long, List<MedicalReportData>> medicalPromiseToData,Map<Long, String> mpIdToStation,Map<Long, String> mpToReportOss,Map<Long, MedicalReport> mpToReport) {
        //如果是通过，遍历审批单，判断同medicalPromise是否都已审核
        //如果都已审核 并且审核通过，则为审核通过
        //如果都已审核 并且审核不通过，则为审核不通过
        for (ReportAudit reportAudit : reportAudits) {

            List<MedicalReportData> medicalReportData = medicalPromiseToData.get(reportAudit.getMedicalPromiseId());

            MedicalReportData initMedicalReportData = medicalReportData.stream().filter(p -> Objects.equals(ReportAuditStatusEnum.INIT.getAuditStatus(), p.getAuditStatus())
                    || Objects.equals(ReportAuditStatusEnum.DATA_ERROR.getAuditStatus(), p.getAuditStatus())
            ).findFirst().orElse(null);

            //如果全部审核了，则修改审批单状态
            if (Objects.isNull(initMedicalReportData)){
                //判断是否有审核驳回的，如果有审核驳回的，则审批单状态为驳回
                MedicalReportData refuseMedicalReportData = medicalReportData.stream().filter(p -> Objects.equals(ReportAuditStatusEnum.REFUSE.getAuditStatus(), p.getAuditStatus())).findFirst().orElse(null);
                Integer auditStatus = Objects.isNull(refuseMedicalReportData) ? ReportAuditStatusEnum.PASS.getAuditStatus() : ReportAuditStatusEnum.REFUSE.getAuditStatus();
                reportAudit.setAuditStatus(auditStatus);
                reportAudit.setAuditTime(new Date());
                reportAudit.setAuditUser(reportAuditResultCmd.getAuditUser());
                if (Objects.nonNull(reportAudit.getAuditTimeOutDeadline())){
                    if (DateUtil.compare(new Date(), reportAudit.getAuditTimeOutDeadline()) > 0){
                        reportAudit.setAuditTimeOutStatus(CommonConstant.ONE);
                    }else {
                        reportAudit.setAuditTimeOutStatus(CommonConstant.ZERO);
                    }
                }
                reportAuditRepository.updateReportAuditStatus(Lists.newArrayList(reportAudit));

                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                    String taskId = reportAuditResultCmd.getExperimentalId().split("_")[1];
                    syncToStation(medicalReportData,CommonConstant.ONE_STR,mpIdToStation,mpToReportOss,taskId,reportAudit.getMedicalPromiseId());
                });


                MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(reportAudit.getMedicalPromiseId()).build());

                //TODO 如果是通过，则基础校验一下(阳性数量是否过多)
                List<VerifyReportRootConfig> list = JSON.parseArray(JSON.toJSONString(duccConfig.getVerifyReportRootConfig()), VerifyReportRootConfig.class);
                // 未配置项目数据,直接返回成功
                if (CollUtil.isNotEmpty(list)) {
                    executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                        Map<String, VerifyReportBaseRootConfig> verifyReportRootConfigMapping = new HashMap<>();
                        for (VerifyReportRootConfig verifyReportRootConfig: list) {
                            for (String serviceItemId : verifyReportRootConfig.getServiceItemId()) {
                                verifyReportRootConfigMapping.put(serviceItemId, verifyReportRootConfig);
                            }
                        }
                        VerifyReportBaseRootConfig verifyReportRootConfig = JSONUtil.toBean(JSONUtil.toJsonStr(verifyReportRootConfigMapping.get(medicalPromise.getServiceItemId())), VerifyReportBaseRootConfig.class);

                        if (Objects.nonNull(verifyReportRootConfig)){
                            VerifyReportAnomalyIndicatorNumConfig verifyReportAnomalyIndicatorNumConfig = verifyReportRootConfig.getAnomalyIndicatorNum();

                            if (Objects.nonNull(verifyReportAnomalyIndicatorNumConfig)){
                                List<MedicalReportData> abnormalList = medicalReportData.stream().filter(p -> StringUtils.isNotBlank(p.getIndicatorName()) && !Objects.equals(p.getStatus(), CommonConstant.ZERO)).collect(Collectors.toList());

                                if (CollectionUtil.isNotEmpty(abnormalList)){
                                    List<String> collect = abnormalList.stream().map(MedicalReportData::getIndicatorName).distinct().collect(Collectors.toList());
                                    if (collect.size()>verifyReportAnomalyIndicatorNumConfig.getAnomalyIndicatorWarnNum()){
                                        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                                        JSONObject jsonObject = robotAlarmMap.get("阳性报告数量超阈值");
                                        dongDongRobotRpc.sendDongDongRobotMessage(String.format("报告阳性指标过多，检测单ID：%s，检测项目名称：%s，样本编码：%s，实验室名称：%s，请关注",
                                                        medicalPromise.getMedicalPromiseId(), medicalPromise.getServiceItemName(), medicalPromise.getSpecimenCode(), medicalPromise.getStationName()),
                                                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
                                    }
                                }
                            }
                        }
                    });

                }

                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                    //同步报告中心
                    MedicalReportSaveCmd medicalReportSaveCmd = getMedicalReportSaveCmd(mpToReport, medicalPromise);
                    medicalPromiseApplication.syncMedicalReportToReportCenter(medicalReportSaveCmd);

                    MedicalReport medicalReport = new MedicalReport();
                    medicalReport.setMedicalPromiseId(medicalReportSaveCmd.getMedicalPromiseId());
                    medicalReport.setReportCenterId(medicalReportSaveCmd.getReportCenterId());
                    medicalReportRepository.updateByCondition(medicalReport);
                });




                //发送审核单审核完成事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_REPORT_AUDIT_FINISH,
                        MedicalPromiseEventBody.builder().experimentalId(reportAuditResultCmd.getExperimentalId()).operateSystem(reportAuditResultCmd.getOperateSystem()).medicalPromiseId(medicalPromise.getMedicalPromiseId()).reportAuditId(reportAudit.getReportAuditId()).auditStatus(reportAudit.getAuditStatus()).promiseId(medicalPromise.getPromiseId()).build()));

                saveOperateRecord(reportAuditResultCmd, medicalPromise,BizSceneActionKeyEnum.AUDIT_PASS);

            }

        }
    }

    /**
     * 根据提供的医疗报告和医疗承诺信息，生成并返回一个 MedicalReportSaveCmd 对象。
     * @param mpToReport 医疗报告的 Map 对象，key 为患者 ID，value 为对应的 MedicalReport 对象。
     * @param medicalPromise 医疗承诺对象，包含相关的承诺信息。
     * @return 生成的 MedicalReportSaveCmd 对象。
     */
    private  MedicalReportSaveCmd getMedicalReportSaveCmd(Map<Long, MedicalReport> mpToReport, MedicalPromise medicalPromise) {
        MedicalReport medicalReport = mpToReport.get(medicalPromise.getMedicalPromiseId());
        MedicalReportSaveCmd medicalReportSaveCmd = new MedicalReportSaveCmd();
        medicalReportSaveCmd.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medicalReportSaveCmd.setUserPin(medicalPromise.getUserPin());
        medicalReportSaveCmd.setChannelNo(medicalPromise.getProviderId());
        medicalReportSaveCmd.setSourceOss(medicalReport.getSourceOss());
        medicalReportSaveCmd.setStructReportOss(medicalReport.getStructReportOss());
        medicalReportSaveCmd.setReportOss(medicalReport.getReportOss());
        medicalReportSaveCmd.setPromiseId(medicalPromise.getPromiseId());

        Date examinationTime = Objects.nonNull(medicalPromise.getCheckTime()) ? medicalPromise.getCheckTime() : new Date();
        medicalReportSaveCmd.setExaminationTime(DateUtil.format(examinationTime, CommonConstant.YMD));
        Date reportTime = Objects.nonNull(medicalPromise.getReportTime()) ? medicalPromise.getReportTime() : new Date();
        medicalReportSaveCmd.setReportTime(reportTime);
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        MedicalReportDTO byMedicalPromiseId = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        if (Objects.isNull(byMedicalPromiseId)){

            PromiseDto byPromiseId = getPromiseDto(medicalPromise);
            List<PromisePatientDto> patients = byPromiseId.getPatients();
            PromisePatientDto promisePatientDto = patients.stream().filter(p -> Objects.equals(medicalPromise.getPromisePatientId(), p.getPromisePatientId())).findFirst().orElse(null);
            if (Objects.nonNull(promisePatientDto)){
                medicalReportSaveCmd.setPatientId(promisePatientDto.getPatientId());
                medicalReportSaveCmd.setPatientName(Objects.nonNull(promisePatientDto.getUserName())?promisePatientDto.getUserName().getName():null);
            }
        }else {
            medicalReportSaveCmd.setReportCenterId(byMedicalPromiseId.getReportCenterId());
        }
        medicalReportSaveCmd.setMedicalType( CommonConstant.ONE);
        medicalReportSaveCmd.setReportType(CommonConstant.ONE);
        medicalReportSaveCmd.setReportStatus(Objects.nonNull(medicalPromise.getReportStatus()) ? medicalPromise.getReportStatus() : CommonConstant.ONE);


        //设置FileMd5
        ObjectMetadata objectMetadata = fileManageService.getObjectMetadata(medicalReportSaveCmd.getReportOss());


        medicalReportSaveCmd.setFileMd5(objectMetadata.getETag());
        medicalReportSaveCmd.setServiceItemName(medicalPromise.getServiceItemName());
        medicalReportSaveCmd.setServiceItemId(medicalPromise.getServiceItemId());
        return medicalReportSaveCmd;
    }

    /**
     *
     * @param medicalPromise
     * @return
     */
    PromiseDto getPromiseDto(MedicalPromise medicalPromise) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(medicalPromise.getPromiseId());
        return promiseApplication.findByPromiseId(promiseIdRequest);
    }

    private String getOssHeepUrl(String url) {

        LocalDateTime expireTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Date expire = TimeUtils.localDateTimeToDate(expireTime);
        String urlResult = fileManageService.getPublicUrl(url, true, expire);
        log.info("ReportAuditApplicationImpl.getOssHeepUrl urlResult= {} url={} ", urlResult, url);
        return urlResult;
    }


    private void syncToStation(List<MedicalReportData> medicalReportDatas,String isValid,Map<Long, String> mpIdToStation,Map<Long, String> mpToReportOss,String taskId,Long mpId){
        //通知实验室通过
        Set<String> holeSet = Sets.newHashSet();
        QuickCheckResultReviewBO quickCheckResultReviewBO = new QuickCheckResultReviewBO();
        quickCheckResultReviewBO.setIsValid(isValid);
        if (StringUtils.isNotBlank(mpIdToStation.get(mpId))){
            quickCheckResultReviewBO.setStoreId(mpIdToStation.get(mpId));
        }
        if (StringUtils.isNotBlank(mpToReportOss.get(mpId))){
            quickCheckResultReviewBO.setReportUrl(getOssHeepUrl(mpToReportOss.get(mpId)));
        }
        List<QuickCheckResultTaskBO> taskList = Lists.newArrayList();

        for (MedicalReportData mpd : medicalReportDatas) {
            if (holeSet.add(mpd.getHole())){
                QuickCheckResultTaskBO taskBO = new QuickCheckResultTaskBO();
                taskBO.setTaskId(taskId);
                taskBO.setHole(mpd.getHole());
                taskList.add(taskBO);
            }
        }

        quickCheckResultReviewBO.setTaskList(taskList);

        quickCheckThirdExportServiceRpc.checkResultReview(quickCheckResultReviewBO);

    }


    /**
     * 将报告数据同步到站点。
     * @param reportAuditResultCmd 审核结果命令对象，包含实验ID和审核状态。
     * @param medicalReportDatas 医疗报告数据列表。
     */
    private void syncToStation(ReportAuditResultCmd reportAuditResultCmd, List<MedicalReportData> medicalReportDatas) {
        List<Long> mpIds = medicalReportDatas.stream().map(MedicalReportData::getMedicalPromiseId).distinct().collect(Collectors.toList());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(mpIds).build());
        Map<Long, String> mpIdToStation = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, MedicalPromise::getStationId));
        log.info("ReportAuditApplicationImpl saveAuditResult mpIdToStation:{}", JsonUtil.toJSONString(mpIdToStation));
        List<MedicalReport> medicalReports = medicalReportRepository.getByMedicalPromiseIdList(mpIds);
        Map<Long, String> mpToReportOss = medicalReports.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, MedicalReport::getReportOss));
        log.info("ReportAuditApplicationImpl saveAuditResult mpToReportOss:{}", JsonUtil.toJSONString(mpIdToStation));

        Map<Long, List<MedicalReportData>> mpToData = medicalReportDatas.stream().collect(Collectors.groupingBy(MedicalReportData::getMedicalPromiseId));
        Set<String> holeSet = Sets.newHashSet();
        String invalid = Objects.equals(ReportAuditStatusEnum.PASS.getAuditStatus(), reportAuditResultCmd.getAuditStatus()) ? CommonConstant.ONE_STR : CommonConstant.ZERO_STR;

        String taskId = reportAuditResultCmd.getExperimentalId().split("_")[1];

        mpToData.forEach((mpId,datas)->{
            log.info("ReportAuditApplicationImpl saveAuditResult datas:{}", JsonUtil.toJSONString(datas));

            QuickCheckResultReviewBO quickCheckResultReviewBO = new QuickCheckResultReviewBO();
            quickCheckResultReviewBO.setIsValid(invalid);
            if (StringUtils.isNotBlank(mpIdToStation.get(mpId))){
                quickCheckResultReviewBO.setStoreId(mpIdToStation.get(mpId));
            }
            if (StringUtils.isNotBlank(mpToReportOss.get(mpId))){
                quickCheckResultReviewBO.setReportUrl(getOssHeepUrl(mpToReportOss.get(mpId)));
            }
            List<QuickCheckResultTaskBO> taskList = Lists.newArrayList();

            for (MedicalReportData medicalReportData : datas) {
                if (holeSet.add(medicalReportData.getHole())){
                    QuickCheckResultTaskBO taskBO = new QuickCheckResultTaskBO();
                    taskBO.setTaskId(taskId);
                    taskBO.setHole(medicalReportData.getHole());
                    taskList.add(taskBO);
                }
            }

            quickCheckResultReviewBO.setTaskList(taskList);

            quickCheckThirdExportServiceRpc.checkResultReview(quickCheckResultReviewBO);

        });
    }

    /**
     * 保存操作记录和日志信息。
     * @param reportAuditResultCmd 审核结果命令对象，包含审核用户等信息。
     * @param medicalPromise 医疗承诺对象，包含医疗承诺的ID、流程代码、样本代码、站点ID等信息。
     * @param bizSceneActionKeyEnum 业务场景操作键枚举，表示当前操作的类型。
     */
    private void saveOperateRecord(ReportAuditResultCmd reportAuditResultCmd, MedicalPromise medicalPromise,BizSceneActionKeyEnum bizSceneActionKeyEnum) {
        OperationLogCmd operationLogCmd = new OperationLogCmd();
//            operationLogCmd.setSystemKey();
        operationLogCmd.setBizSceneKey(BizSceneKeyEnum.MEDICAL_PROMISE_STATION_INTERACTION.getBizSceneKey());
        operationLogCmd.setBizSceneDesc(BizSceneKeyEnum.MEDICAL_PROMISE_STATION_INTERACTION.getBizSceneDesc());
        operationLogCmd.setBizSceneActionKey(bizSceneActionKeyEnum.getBizSceneActionKey());
        operationLogCmd.setBizSceneActionDesc(bizSceneActionKeyEnum.getBizSceneActionDesc());
        operationLogCmd.setOperateType(OperationLogOperateTypeEnum.UPDATE.getType());
        operationLogCmd.setBizUnionId(String.valueOf(medicalPromise.getMedicalPromiseId()));
        operationLogCmd.setResultType(OperationLogResultTypeEnum.SUCCESS.getType());
        operationLogCmd.setOperateTime(new Date());
        operationLogCmd.setOperator(reportAuditResultCmd.getAuditUser());


        MedicalPromiseLogCmd medicalPromiseLogCmd = new MedicalPromiseLogCmd();
        medicalPromiseLogCmd.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medicalPromiseLogCmd.setParam(JsonUtil.toJSONString(reportAuditResultCmd));
        medicalPromiseLogCmd.setFlowCode(medicalPromise.getFlowCode());
        medicalPromiseLogCmd.setSpecimenCode(medicalPromise.getSpecimenCode());
        medicalPromiseLogCmd.setStationId(medicalPromise.getStationId());
        medicalPromiseLogCmd.setOperateTime(new Date());
        medicalPromiseLogCmd.setOperatePin(reportAuditResultCmd.getAuditUser());
        medicalPromiseLogCmd.setOperateType(bizSceneActionKeyEnum.getBizSceneActionKey());
        medicalPromiseLogCmd.setExceptionMsg(reportAuditResultCmd.getSampleAnomalyOperateReason());
        medicalPromiseLogCmd.setDealType(reportAuditResultCmd.getSampleAnomalyOperateType());
        medicalPromiseLogCmd.setDealTypeDesc(SampleAnomalyOperateTypeEnum.getDescByType(reportAuditResultCmd.getSampleAnomalyOperateType()));

        operationLogCmd.setParam(JsonUtil.toJSONString(medicalPromiseLogCmd));

        operationLogJsfExport.batchInsertAsyncToLocalDB(Lists.newArrayList(operationLogCmd));
    }

    public boolean isNumber(String str) {
        if (str == null) return false;
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public boolean isValidNumber(String str) {
        if (str == null || str.trim().isEmpty()) {return false;};
        try {
            double value = Double.parseDouble(str);
            return value > 0 && value < 10;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 指标数量配置
     */
    private void contrastServiceItemIndicatorNum(VerifyReportBaseRootConfig verifyReportRootConfig, MedicalPromise medicalPromise, QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO, StructQuickReportResultDTO structQuickReportResultDTO, List<String> errorMsg, Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup) {
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], 校验结果与项目配置指标数量是否一致start");
        ServiceItemDto serviceItemDto = productServiceItemApplication.queryServiceItemDetail(ServiceItemQuery.builder().itemId(Long.parseLong(medicalPromise.getServiceItemId())).build());
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], reportSize={} serviceItemSize={}",  structQuickReportResultDTO.getIndicators().size(), serviceItemDto.getServiceIndicatorDtoList().size());
        if (structQuickReportResultDTO.getIndicators().size() != serviceItemDto.getServiceIndicatorDtoList().size()){
            quickStructReportVerifyResultDTO.setVerifyResult(false);
            // 如果已经明确不可忽略，优先返回不可忽略
            if (!Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyFailCanSkip())) {
                quickStructReportVerifyResultDTO.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
            }
            String errorMsgString = structQuickReportResultDTO.getSampleBarcode() + "报告中的指标数量不对";
            errorMsg.add(errorMsgString);
            // 错误明细按照类型记录
            QuickStructReportVerifyResultDTO errorDetail = new QuickStructReportVerifyResultDTO();
            errorDetail.setVerifyResult(false);
            errorDetail.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
            errorDetail.setVerifyFailMsg(errorMsgString);
            if (errorGroup.containsKey(ReportVerifyEnum.CONTRAST_SERVICE_ITEM_INDICATOR_NUM.getCode())) {
                errorGroup.get(ReportVerifyEnum.CONTRAST_SERVICE_ITEM_INDICATOR_NUM.getCode()).add(errorDetail);
            } else {
                errorGroup.put(ReportVerifyEnum.CONTRAST_SERVICE_ITEM_INDICATOR_NUM.getCode(), org.assertj.core.util.Lists.newArrayList(errorDetail));
            }
        }
    }

    /**
     * 指标冲突配置
     */
    private void indicatorConflict(VerifyReportBaseRootConfig verifyReportRootConfig, Map<String, String> indicatorNameTypeMap, QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO, StructQuickReportResultDTO  structQuickReportResultDTO, List<String> errorMsg, Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup) {
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], 阳性指标冲突校验start");
        verifyReportRootConfig.getIndicatorConflict().sort(Comparator.comparing(VerifyReportAnomalyIndicatorConflictConfig::getSort));
        for (VerifyReportAnomalyIndicatorConflictConfig verifyReportAnomalyIndicatorConflictConfig : verifyReportRootConfig.getIndicatorConflict()) {
            if (CollUtil.isEmpty(verifyReportAnomalyIndicatorConflictConfig.getAnomalyIndicatorNameConflict())) {
                continue;
            }
            for (Map<String, String>  nameTypeMap : verifyReportAnomalyIndicatorConflictConfig.getAnomalyIndicatorNameConflict()) {
                List<String> conflictName = new ArrayList<>();
                for (Map.Entry<String, String> entry : nameTypeMap.entrySet()) {
                    String indicatorName = entry.getKey();
                    if (!indicatorNameTypeMap.containsKey(indicatorName)) {
                        continue;
                    }
                    Map<String, Object> param = new HashMap<>();
                    param.put("value", indicatorNameTypeMap.get(indicatorName));
                    if ((boolean) AviatorEvaluator.compile(entry.getValue(), Boolean.TRUE).execute(param)) {
                        conflictName.add(indicatorName);
                    }
                }
                if (conflictName.size() > 1) {
                    quickStructReportVerifyResultDTO.setVerifyResult(false);
                    // 如果已经明确不可忽略，优先返回不可忽略
                    if (!Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyFailCanSkip())) {
                        quickStructReportVerifyResultDTO.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportAnomalyIndicatorConflictConfig.getVerifyFailCanSkip()));
                    }
                    String errorMsgString = structQuickReportResultDTO.getSampleBarcode() + Joiner.on("和").join(conflictName) + "检测结果冲突";
                    errorMsg.add(errorMsgString);
                    // 错误明细按照类型记录
                    QuickStructReportVerifyResultDTO errorDetail = new QuickStructReportVerifyResultDTO();
                    errorDetail.setVerifyResult(false);
                    errorDetail.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
                    errorDetail.setVerifyFailMsg(errorMsgString);
                    if (errorGroup.containsKey(ReportVerifyEnum.INDICATOR_CONFLICT.getCode())) {
                        errorGroup.get(ReportVerifyEnum.INDICATOR_CONFLICT.getCode()).add(errorDetail);
                    } else {
                        errorGroup.put(ReportVerifyEnum.INDICATOR_CONFLICT.getCode(), org.assertj.core.util.Lists.newArrayList(errorDetail));
                    }
                }
            }
        }
    }

    /**
     * 根据报告审核基础DTO列表，填充相关的患者信息和超时状态。
     * @param reportAuditBaseDTOS 报告审核基础DTO列表
     */
    private void packDTO(List<ReportAuditBaseDTO> reportAuditBaseDTOS) {
        List<Long> medicalPromiseIds = reportAuditBaseDTOS.stream().map(ReportAuditBaseDTO::getMedicalPromiseId).collect(Collectors.toList());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(medicalPromiseIds).build());
        Map<Long, JdhPromisePatient> ppidToObj = Maps.newHashMap();
        Map<Long,MedicalPromise> ppidToMedicalPromise = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(medicalPromises)){
            ppidToMedicalPromise = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, p -> p));
            Set<Long> promisePatientIds = medicalPromises.stream().map(MedicalPromise::getPromisePatientId).collect(Collectors.toSet());
            List<JdhPromisePatient> jdhPromisePatients = promiseRepository.listPatient(promisePatientIds);
            if (CollectionUtil.isNotEmpty(jdhPromisePatients)){
                ppidToObj = jdhPromisePatients.stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, p -> p));
            }
        }

        for (ReportAuditBaseDTO reportAuditBaseDTO : reportAuditBaseDTOS) {
            if (Objects.equals(CommonConstant.ZERO,reportAuditBaseDTO.getAuditStatus())
                    && Objects.equals(CommonConstant.ZERO,reportAuditBaseDTO.getAuditTimeOutStatus())
                    && Objects.nonNull(reportAuditBaseDTO.getAuditTimeOutDeadline())
            ){
                if (DateUtil.compare(new Date(),reportAuditBaseDTO.getAuditTimeOutDeadline()) > 0){
                    reportAuditBaseDTO.setAuditTimeOutStatus(CommonConstant.ONE);
                }else {
                    reportAuditBaseDTO.setAuditTimeOutStatus(CommonConstant.ZERO);
                }
            }


            MedicalPromise medicalPromise = ppidToMedicalPromise.get(reportAuditBaseDTO.getMedicalPromiseId());
            if (Objects.nonNull(medicalPromise)){
                JdhPromisePatient patient = ppidToObj.get(medicalPromise.getPromisePatientId());
                if (Objects.nonNull(patient)) {
                    reportAuditBaseDTO.setAge(patient.getBirthday().getAge());
                    reportAuditBaseDTO.setGender(patient.getGender());
                    reportAuditBaseDTO.setPatientName(patient.getUserName().getName());
                }

            }
        }
    }
}
