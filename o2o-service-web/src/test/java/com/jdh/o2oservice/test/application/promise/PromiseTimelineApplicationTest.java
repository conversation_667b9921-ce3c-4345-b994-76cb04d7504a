package com.jdh.o2oservice.test.application.promise;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.promise.service.PromiseTimelineApplication;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * PromiseTimelineApplication 测试类
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PromiseTimelineApplicationTest {

    @Autowired
    private PromiseTimelineApplication promiseTimelineApplication;

    /**
     * 测试查询履约单时间轴
     */
    @Test
    public void testQueryPromiseTimeline() {
        // 构建测试请求
        QueryPromiseTimelineRequest request = QueryPromiseTimelineRequest.builder()
                .promiseId(123456L) // 使用实际存在的履约单ID进行测试
                .build();

        // 调用方法
        PromiseTimelineDto result = promiseTimelineApplication.queryPromiseTimeline(request);

        // 输出结果
        log.info("PromiseTimelineApplicationTest -> testQueryPromiseTimeline result={}", JSON.toJSONString(result));

        // 验证结果
        assert result != null;
        log.info("测试完成，时间轴事件数量: {}", 
            result.getPromiseTimelineDetailDtoList() != null ? result.getPromiseTimelineDetailDtoList().size() : 0);
    }

    /**
     * 测试空参数情况
     */
    @Test
    public void testQueryPromiseTimelineWithNullRequest() {
        // 测试空请求
        PromiseTimelineDto result = promiseTimelineApplication.queryPromiseTimeline(null);
        
        log.info("PromiseTimelineApplicationTest -> testQueryPromiseTimelineWithNullRequest result={}", JSON.toJSONString(result));
        
        // 验证结果
        assert result != null;
        assert result.getPromiseTimelineDetailDtoList() == null || result.getPromiseTimelineDetailDtoList().isEmpty();
    }

    /**
     * 测试不存在的履约单ID
     */
    @Test
    public void testQueryPromiseTimelineWithInvalidPromiseId() {
        // 构建测试请求
        QueryPromiseTimelineRequest request = QueryPromiseTimelineRequest.builder()
                .promiseId(-1L) // 使用不存在的履约单ID
                .build();

        // 调用方法
        PromiseTimelineDto result = promiseTimelineApplication.queryPromiseTimeline(request);

        // 输出结果
        log.info("PromiseTimelineApplicationTest -> testQueryPromiseTimelineWithInvalidPromiseId result={}", JSON.toJSONString(result));

        // 验证结果
        assert result != null;
        assert result.getPromiseTimelineDetailDtoList() == null || result.getPromiseTimelineDetailDtoList().isEmpty();
    }
}
