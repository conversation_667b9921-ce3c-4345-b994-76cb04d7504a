package com.jdh.o2oservice.web.controller.admin.angelPromise;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssReturnVisitCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssessmentResultCmd;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentDetailManDTO;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentPageDTO;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentDetailRequest;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 风险评估单controller
 * @date 2025/6/26
 */
@Slf4j
@RestController
@RequestMapping("/riskAssessment")
public class RiskAssessmentController {


    /**
     * 自动注入的风险评估单应用层对象，用于处理与风险评估单相关的业务逻辑。
     */
    @Autowired
    private RiskAssessmentApplication riskAssessmentApplication;


    /**
     * 查询风险评估单分页数据。
     * @param request 查询请求参数，包括分页信息和过滤条件等。
     * @return 分页数据的DTO对象。
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryRiskAssessmentPage")
    @LogAndAlarm(jKey = "RiskAssessmentController.queryRiskAssessmentPage")
    public Response<PageDto<RiskAssessmentPageDTO>> queryRiskAssessmentPage(@RequestBody RiskAssessmentPageRequest request) {
        request.setRiskLevel(CommonConstant.THREE);
        PageDto<RiskAssessmentPageDTO> res = riskAssessmentApplication.queryRiskAssessmentPage(request);
        return ResponseUtil.buildSuccResponse(res);
    }



    //查询详情
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryRiskAssDetailForMan")
    @LogAndAlarm(jKey = "RiskAssessmentController.queryRiskAssessmentDetail")
    public Response<RiskAssessmentDetailManDTO> queryRiskAssDetailForMan(@RequestBody RiskAssessmentDetailRequest request){
        RiskAssessmentDetailManDTO res = riskAssessmentApplication.queryRiskAssDetailForMan(request);
        return ResponseUtil.buildSuccResponse(res);
    }



    /**
     * 保存风险评估结果。
     * @param riskAssessmentResultCmd 风险评估结果命令对象。
     * @return 保存结果。
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/saveRiskAssessmentResult")
    @LogAndAlarm(jKey = "RiskAssessmentController.saveRiskAssessmentResult")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "保存风险评估结果")
    public Response<Boolean> saveRiskAssessmentResult(@RequestBody RiskAssessmentResultCmd riskAssessmentResultCmd){
        String erp = LoginContext.getLoginContext().getPin();
        riskAssessmentResultCmd.setOperator(erp);
        Boolean res = riskAssessmentApplication.saveRiskAssResult(riskAssessmentResultCmd);
        return ResponseUtil.buildSuccResponse(res);
    }


    /**
     * 保存回访信息
     * @param riskAssReturnVisitCmd 回访信息命令对象
     * @return 保存结果
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/saveReturnVisit")
    @LogAndAlarm(jKey = "RiskAssessmentController.saveReturnVisit")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "保存风险评估单回访记录")
    public Response<Boolean> saveReturnVisit(@RequestBody RiskAssReturnVisitCmd riskAssReturnVisitCmd){
        Boolean res = riskAssessmentApplication.saveReturnVisit(riskAssReturnVisitCmd);
        return ResponseUtil.buildSuccResponse(res);
    }


}
