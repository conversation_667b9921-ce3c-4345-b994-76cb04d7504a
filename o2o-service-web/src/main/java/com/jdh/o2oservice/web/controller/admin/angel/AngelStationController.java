package com.jdh.o2oservice.web.controller.admin.angel;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelStationSaveApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angel.cmd.*;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.*;
import com.jdh.o2oservice.job.support.PopOrderExpireScanJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @ClassName: AngelStationController
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/27 22:37
 * @Vserion: 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/station")
public class AngelStationController {

    @Resource
    private StationApplication stationApplication;

    @Resource
    private AngelStationSaveApplication angelStationSaveApplication;

    @Resource
    private AngelApplication angelApplication;

    @Resource
    private PopOrderExpireScanJob popOrderExpireScanJob;

    /**
     * B端-服务站新增/编辑
     * @param angelStationSaveCmd
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/saveStationElement")
    @LogAndAlarm(jKey = "AngelStationController.saveStationElement")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.PROVIDER, operationDesc = "保存服务者", recordParamBizIdExpress = {"args[0].angelStationId"}, paramJudgeOperationTypeExpress = {"args[0].angelStationId"})
    public Response<Boolean> saveStationElement(@RequestBody AngelStationSaveCmd angelStationSaveCmd) {
        angelStationSaveCmd.setOperator(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.saveStationElement(angelStationSaveCmd));
    }

    /**
     * B端-服务站详情
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelStationList")
    @LogAndAlarm(jKey = "AngelStationController.queryAngelStationList")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务站详情")
    public Response<AngelStationDto> queryAngelStationList(@RequestBody AngelStationDetailRequest request) {
        return ResponseUtil.buildSuccResponse(stationApplication.queryAngelStationDetail(request));
    }

    /**
     * B端-服务站分页数据
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelStationPage")
    @LogAndAlarm(jKey = "AngelStationController.queryAngelStationPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务站列表")
    public Response<PageDto<AngelStationDto>> queryAngelStationPage(@RequestBody AngelStationPageRequest request) {
        return ResponseUtil.buildSuccResponse(stationApplication.queryAngelStationPage(request));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryAddressLatAndLng")
    @LogAndAlarm(jKey = "AngelStationController.queryAddressLatAndLng")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.BASE, operationDesc = "查询地址经纬度")
    public Response<AngelStationLatAndLngDto> queryAddressLatAndLng(@RequestBody AngelStationAddressRequest request) {
        return ResponseUtil.buildSuccResponse(stationApplication.queryAddressLatAndLng(request));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/bindStationMaster")
    @LogAndAlarm(jKey = "AngelStationController.bindStationMaster")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.PROVIDER, operationDesc = "绑定服务站站长", recordParamBizIdExpress = {"args[0].stationMaster"})
    public Response<Boolean> bindStationMaster(@RequestBody BindAngelMasterCmd bindAngelMasterCmd) {
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.bindStationMaster(bindAngelMasterCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/startOrStop")
    @LogAndAlarm(jKey = "AngelStationController.startOrStop")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.PROVIDER, operationDesc = "启停服务站", recordParamBizIdExpress = {"args[0].angelStationId"})
    public Response<Boolean> startOrStop(@RequestBody StartStopAngelStationCmd startStopAngelStationCmd) {
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.startOrStop(startStopAngelStationCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/angel/queryStationAngelPage")
    @LogAndAlarm(jKey = "AngelStationController.queryStationAngelPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务站资源管理列表")
    public Response<PageDto<StationAngelManDto>> queryStationAngelPage(@RequestBody StationAngelPageRequest stationAngelPageRequest) {
        return ResponseUtil.buildSuccResponse(stationApplication.queryStationAngelPage(stationAngelPageRequest));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/angel/queryAngelPage")
    @LogAndAlarm(jKey = "AngelStationController.queryAngelPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务者列表")
    public Response<PageDto<JdhAngelDto>> queryAngelPage(@RequestBody AngelPageRequest request) {
        return ResponseUtil.buildSuccResponse(angelApplication.queryAngelByPage(request));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/angel/queryStationAngelDetail")
    @LogAndAlarm(jKey = "AngelStationController.queryStationAngelDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务者护士详情")
    public Response<StationAngelManDto> queryStationAngelDetail(@RequestBody StationAngelDetailRequest detailRequest) {
        return ResponseUtil.buildSuccResponse(stationApplication.queryStationAngelDetail(detailRequest));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/angel/unBind")
    @LogAndAlarm(jKey = "AngelStationController.unBind")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.PROVIDER, operationDesc = "解绑服务站服务者", recordParamBizIdExpress = {"args[0].angelId"})
    public Response<Boolean> unBind(@RequestBody AngelStationUnbindCmd angelStationUnbindCmd) {
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.unBind(angelStationUnbindCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/angel/saveStationAngel")
    @LogAndAlarm(jKey = "AngelStationController.saveStationAngel")
    @OperationLog(operationType = OpTypeEnum.ADD, domainCode = DomainEnum.PROVIDER, operationDesc = "批量绑定服务站服务者", recordParamBizIdExpress = {"args[0].angelStationId"})
    public Response<Boolean> saveStationAngel(@RequestBody StationAngelSaveCmd stationAngelSaveCmd) {
        stationAngelSaveCmd.setOperator(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.saveStationAngel(stationAngelSaveCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/sku/queryStationSkuPage")
    @LogAndAlarm(jKey = "AngelStationController.queryStationSkuPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务站商品列表")
    public Response<PageDto<StationSkuManDto>> queryStationSkuPage(@RequestBody StationSkuPageRequest stationSkuPageRequest) {
        if (stationSkuPageRequest != null && stationSkuPageRequest.getOnOffShelf() == null) {
            stationSkuPageRequest.setQueryIgnoreOnOffShelf(true);
        }
        return ResponseUtil.buildSuccResponse(stationApplication.queryStationSkuPage(stationSkuPageRequest));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/sku/queryStationSkuDetail")
    @LogAndAlarm(jKey = "AngelStationController.queryStationSkuDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务站商品详情")
    public Response<StationSkuManDto> queryStationSkuDetail(@RequestBody StationSkuDetailRequest stationSkuDetailRequest) {
        return ResponseUtil.buildSuccResponse(stationApplication.queryStationSkuDetail(stationSkuDetailRequest));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/sku/saveStationSku")
    @LogAndAlarm(jKey = "AngelStationController.saveStationSku")
    @OperationLog(operationType = OpTypeEnum.ADD, domainCode = DomainEnum.PROVIDER, operationDesc = "批量绑定服务站商品", recordParamBizIdExpress = {"args[0].angelStationId"})
    public Response<Boolean> saveStationSku(@RequestBody StationSkuSaveCmd stationSkuSaveCmd) {
        stationSkuSaveCmd.setOperator(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.saveStationSku(stationSkuSaveCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/sku/updateStationSkuOnOffShelf")
    @LogAndAlarm(jKey = "AngelStationController.updateStationSkuOnOffShelf")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.PROVIDER, operationDesc = "更新服务站商品上下架", recordParamBizIdExpress = {"args[0].angelStationId"})
    public Response<Boolean> updateStationSkuOnOffShelf(@RequestBody StationSkuUpdateCmd stationSkuUpdateCmd) {
        stationSkuUpdateCmd.setOperator(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.updateOnOffShelf(stationSkuUpdateCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/sku/skuUnbind")
    @LogAndAlarm(jKey = "AngelStationController.skuUnbind")
    @OperationLog(operationType = OpTypeEnum.DEL, domainCode = DomainEnum.PROVIDER, operationDesc = "批量解绑服务站商品", recordParamBizIdExpress = {"args[0].angelStationId"})
    public Response<Boolean> skuUnbind(@RequestBody AngelStationUnbindSkuCmd angelStationUnbindSkuCmd) {
        return ResponseUtil.buildSuccResponse(angelStationSaveApplication.skuUnbind(angelStationUnbindSkuCmd));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/sku/querySkuDetail")
    @LogAndAlarm(jKey = "AngelStationController.querySkuDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROVIDER, operationDesc = "查询服务站商品详情")
    public Response<AngelStationSkuManDto> querySkuDetail(@RequestBody AngelStationSkuQuery angelStationSkuQuery) {
        return ResponseUtil.buildSuccResponse(stationApplication.querySkuDetail(angelStationSkuQuery));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/testJob")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.BASE, operationDesc = "测试JOB")
    public Response<Boolean> testJob() {
        popOrderExpireScanJob.voucherRemindBeforeExpireSendSms(new ShardingContext("","",1,"",0,""));
        return ResponseUtil.buildSuccResponse(true);
    }
}
