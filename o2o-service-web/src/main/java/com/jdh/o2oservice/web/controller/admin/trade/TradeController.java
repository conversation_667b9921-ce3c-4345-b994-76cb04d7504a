package com.jdh.o2oservice.web.controller.admin.trade;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderRefundApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.export.trade.dto.ManRefundTipsInfoDto;
import com.jdh.o2oservice.export.trade.dto.OrderRefundTaskDto;
import com.jdh.o2oservice.export.trade.query.JdOrderFullPageParam;
import com.jdh.o2oservice.export.trade.query.ManRefundTipsParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * TradeController
 * <AUTHOR>
 * @date 2024-05-02 11:04
 */
@Slf4j
@RestController
@RequestMapping("/trade")
public class TradeController {

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * jdOrderRefundApplication
     */
    @Autowired
    private JdOrderRefundApplication jdOrderRefundApplication;

    /**
     * jdOrderFullApplication
     */
    @Autowired
    private JdOrderFullApplication jdOrderFullApplication;

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 退款
     *
     * @param param param
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/refund")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.trade.TradeController.refund")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.TRADE, operationDesc = "退款", recordParamBizIdExpress = {"args[0].orderId", "args[0].promiseId"})
    public Response<Boolean> refund(@RequestBody RefundOrderParam param){
        String pin = LoginContext.getLoginContext().getPin();
        param.setOperator(pin);
        if (StringUtils.isBlank(param.getRefundReasonCode())){
            param.setRefundReasonCode("99999901");
        }
        return ResponseUtil.buildSuccResponse(tradeApplication.xfylOrderRefund(param));
    }

    /**
     * 查询订单退款记录
     *
     * @param orderId 订单id
     * @return {@link Response}<{@link List}<{@link OrderRefundTaskDto}>>
     */
    @RequestMapping("/queryRefundRecord")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.trade.TradeController.queryRefundRecord")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.TRADE, operationDesc = "查询订单退款记录")
    public Response<List<OrderRefundTaskDto>> queryRefundRecord(Long orderId){
        return ResponseUtil.buildSuccResponse(jdOrderRefundApplication.queryOrderRefundRecord(orderId));
    }

    /**
     * 运营端服务单列表
     * @param param
     * @return
     */
    @RequestMapping("/queryFullOrderPage")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.trade.TradeController.queryFullOrderPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.TRADE, operationDesc = "运营端服务单列表")
    public Response<PageDto<JdOrderFullDTO>> queryFullOrderPage(@RequestBody JdOrderFullPageParam param){
        return ResponseUtil.buildSuccResponse(jdOrderFullApplication.queryPage(param));
    }

    /**
     * 查询运营端退款提示信息
     *
     * @param param param
     * @return {@link Response}<{@link List}<{@link ManRefundTipsInfoDto}>>
     */
    @RequestMapping("/queryManRefundTipsInfo")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.trade.TradeController.queryManRefundTipsInfo")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.TRADE, operationDesc = "查询运营端退款提示信息")
    public Response<ManRefundTipsInfoDto> queryManRefundTipsInfo(@RequestBody ManRefundTipsParam param){
        return ResponseUtil.buildSuccResponse(tradeApplication.queryManRefundTipsInfo(param));
    }

    /**
     * 运营端服务单列表
     * @param param
     * @return
     */
    @RequestMapping("/export")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.trade.TradeController.export")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROMISE, operationDesc = "导出运营端服务单列表")
    public Response<Boolean> export(@RequestBody JdOrderFullPageParam param){
        Long count = jdOrderFullApplication.queryCount(param);
        if (count == null || count <= 0) {
            throw new BusinessException(new DynamicErrorCode(SupportErrorCode.SUPPORT_FILE_DOWN_LIMIT.getCode(), "无符合导出条件数据"));
        }
        if (count > duccConfig.getExportMedicalPromiseInfoLimit()) {
            throw new BusinessException(SupportErrorCode.SUPPORT_FILE_DOWN_LIMIT.formatDescription(duccConfig.getExportMedicalPromiseInfoLimit()));
        }
        return ResponseUtil.buildSuccResponse(jdOrderFullApplication.export(param));
    }

}
