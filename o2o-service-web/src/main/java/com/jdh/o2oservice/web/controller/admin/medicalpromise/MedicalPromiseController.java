package com.jdh.o2oservice.web.controller.admin.medicalpromise;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.application.via.ManViaApplication;
import com.jdh.o2oservice.application.ztools.AngelWorkToolsApplication;
import com.jdh.o2oservice.application.ztools.MedicalPromiseToolsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationModeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationStatusEnum;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportChangeLog;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.oplog.bo.OpLogResult;
import com.jdh.o2oservice.core.domain.support.via.model.ManViaFloorConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ManViaFloorElementConfig;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.query.AngelStationQuery;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedPromiseSubStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.ResetReportStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.ResetSpecimenCodeCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromisePageRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.provider.cmd.ListLaboratoryByStoreNameCmd;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.report.dto.MedicalReportDTO;
import com.jdh.o2oservice.export.support.dto.MedicalCenterOperationLogDTO;
import com.jdh.o2oservice.export.support.query.MedicalCenterOperationLogRequest;
import com.jdh.o2oservice.export.ztools.cmd.ManBindStationCmd;
import com.jdh.o2oservice.export.ztools.cmd.ManSyncStationCmd;
import com.jdh.o2oservice.export.ztools.cmd.TargetStationCmd;
import com.jdh.o2oservice.export.ztools.query.QueryAngelStationRequest;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * DispatchController
 *
 * <AUTHOR>
 * @date 2024/05/03
 */
@Log4j
@RestController
@RequestMapping("/medicalPromise")
public class MedicalPromiseController {

    /**
     * 派单 application
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 实验室
     */
    @Resource
    private ProviderStoreApplication providerStoreApplication;

    /**
     * 服务站
     */
    @Resource
    private StationApplication stationApplication;

    /**
     * 检验单工具类
     */
    @Resource
    private MedicalPromiseToolsApplication medicalPromiseToolsApplication;

    /**
     * 报告
     */
    @Resource
    MedicalReportApplication medicalReportApplication;

    /**
     * 操作日志
     */
    @Resource
    OperationLogApplication opLogApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * angelWorkToolsApplication
     */
    @Resource
    private AngelWorkToolsApplication angelWorkToolsApplication;

    /**
     * 文件管理领域服务
     */
    @Resource
    private FileManageService fileManageService;

    /**
     * 商家端
     */
    @Value("${opLog.merchantAppName}")
    private String merchantAppName;

    /**
     * 运营后台视图
     */
    @Resource
    ManViaApplication manViaApplication;


    /**
     * 重置检测单报告状态，供应商可以重推报告
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/reset/reportStatus")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "重置报告", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> resetReportStatus(@RequestBody ResetReportStatusCmd cmd){
        cmd.setOperator(LoginContext.getLoginContext().getPin());
        cmd.setRoleType(NumConstant.NUM_4);
        medicalPromiseApplication.resetReportStatus(cmd);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 让步检测，重置检测单子状态
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/reset/exeConcessionCheck")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "让步检测", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> exeConcessionCheck(@RequestBody MedPromiseSubStatusCmd cmd){
        cmd.setOperator(LoginContext.getLoginContext().getPin());
        cmd.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_AGREE.getSubStatus());
        medicalPromiseApplication.updateMedPromiseSubStatus(cmd);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }


    /**
     * 换绑条码
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/reset/specimenCode")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "换绑条码", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> resetSpecimenCode(@RequestBody ResetSpecimenCodeCmd cmd){
        cmd.setOperator(LoginContext.getLoginContext().getPin());
        cmd.setRoleType(NumConstant.NUM_4);
        medicalPromiseApplication.resetSpecimenCode(cmd);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 查询实验室列表
     *
     * @param storeNameCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/station/queryStation")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询实验室列表")
    public Response<List<StoreInfoDto>> queryStation(@RequestBody ListLaboratoryByStoreNameCmd storeNameCmd) {
        storeNameCmd.setBusinessType(16);
        storeNameCmd.setPageSize(CommonConstant.TWENTY);
        storeNameCmd.setPageNum(CommonConstant.ONE);
        storeNameCmd.setStoreStatus(CommonConstant.ONE);
        List<StoreInfoDto> result = providerStoreApplication.listLaboratoryByStoreName(storeNameCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询服务站列表
     *
     * @param angelStationQuery angelStationQuery
     * @return
     */
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/station/queryAngelStation")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询服务站列表")
    public Response<List<AngelStationDto>> queryAngelStation(@RequestBody AngelStationQuery angelStationQuery) {
        QueryAngelStationRequest angelStationRequest = new QueryAngelStationRequest();
        // 底层字段代表实验室
        angelStationRequest.setStationId2(angelStationQuery.getStationId());
        angelStationRequest.setAngelStationName(angelStationQuery.getAngelStationName());
        angelStationRequest.setProvinceCode(angelStationQuery.getProvinceCode());
        angelStationRequest.setCityCode(angelStationQuery.getCityCode());
        angelStationRequest.setDistrictCode(angelStationQuery.getDistrictCode());
        angelStationRequest.setPageNum(CommonConstant.ONE);
        angelStationRequest.setPageSize(CommonConstant.TWENTY);
        angelStationRequest.setStationModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        angelStationRequest.setAngelStationStatus(AngelStationStatusEnum.ALIVE.getCode());
        List<AngelStationDto> angelStationDtoList = stationApplication.queryAngelStationList(angelStationRequest);
        return ResponseUtil.buildSuccResponse(angelStationDtoList);
    }

    /**
     * 指派实验室
     *
     * @param manBindStationCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/station/dispatchStation")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "指派实验室", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> manBindStation(@RequestBody ManBindStationCmd manBindStationCmd) {
        TargetStationCmd targetStationCmd = new TargetStationCmd();
        targetStationCmd.setStationId(manBindStationCmd.getStationId());
        targetStationCmd.setAngelStationId(StringUtils.isNotBlank(manBindStationCmd.getAngelStationId()) ? Long.valueOf(manBindStationCmd.getAngelStationId()) : null);
        targetStationCmd.setMedicalPromiseId(StringUtils.isNotBlank(manBindStationCmd.getMedicalPromiseId()) ? Long.valueOf(manBindStationCmd.getMedicalPromiseId()) : null);
        return ResponseUtil.buildSuccResponse(medicalPromiseToolsApplication.targetStation(targetStationCmd));
    }

    /**
     * 同步检验单至实验室
     * @param manSyncStationCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/station/syncMedicalPromise")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "同步检验单至实验室", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> syncMedicalStation(@RequestBody ManSyncStationCmd manSyncStationCmd) {
        AssertUtils.nonNull(manSyncStationCmd, "入参不允许为空");
        AssertUtils.nonNull(manSyncStationCmd.getMedicalPromiseId(), "medicalPromiseId不允许为空");
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.syncMedicalStation(manSyncStationCmd));
    }

    /**
     * 同步全部检验单至实验室
     * @param manSyncStationCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/station/syncPromise")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "同步履约单至实验室", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> syncPromiseStation(@RequestBody ManSyncStationCmd manSyncStationCmd) {
        AssertUtils.nonNull(manSyncStationCmd, "入参不允许为空");
        AssertUtils.nonNull(manSyncStationCmd.getMedicalPromiseId(), "medicalPromiseId不允许为空");
        if (manSyncStationCmd.getPromiseId() == null) {
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder().medicalPromiseId(manSyncStationCmd.getMedicalPromiseId()).build());
            manSyncStationCmd.setPromiseId(medicalPromiseDTO.getPromiseId());
        }
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.syncPromiseStation(manSyncStationCmd));
    }

    /**
     * 查询实验室操作信息
     *
     * @param medicalPromiseRequest medicalPromiseRequest
     * @return
     */
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/station/opLog")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询实验室操作信息")
    public Response<List<MedicalCenterOperationLogDTO>> queryStationOpLog(@RequestBody MedicalPromisePageRequest medicalPromiseRequest) throws ExecutionException, InterruptedException {
        AssertUtils.nonNull(medicalPromiseRequest, "入参不允许为空");
        AssertUtils.nonNull(medicalPromiseRequest.getMedicalPromiseId(), "medicalPromiseId不允许为空");
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(MedicalPromiseRequest.builder().medicalPromiseId(medicalPromiseRequest.getMedicalPromiseId()).build());
        MedicalCenterOperationLogRequest operationLogRequest = new MedicalCenterOperationLogRequest();
        operationLogRequest.setAppName(merchantAppName);
        operationLogRequest.setOpObjectId(String.valueOf(medicalPromiseRequest.getMedicalPromiseId()));
        operationLogRequest.setPageSize(1);
        operationLogRequest.setPageSize(50);
        List<MedicalCenterOperationLogDTO> ret = new ArrayList<>();
        CompletableFuture<PageDto<MedicalCenterOperationLogDTO>> medicalPromiseIdPageFuture = CompletableFuture.supplyAsync(() -> opLogApplication.getOpLogByQueryPageFromMedicalLogCenter(operationLogRequest), executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_QUERY_THREAD_POOL));

        if (StringUtils.isNotBlank(medicalPromiseDTO.getSpecimenCode())) {
            MedicalCenterOperationLogRequest codeRequest = new MedicalCenterOperationLogRequest();
            codeRequest.setAppName(merchantAppName);
            codeRequest.setOpObjectId(medicalPromiseDTO.getSpecimenCode());
            codeRequest.setPageSize(1);
            codeRequest.setPageSize(50);
            CompletableFuture<PageDto<MedicalCenterOperationLogDTO>> codeFuture = CompletableFuture.supplyAsync(() -> opLogApplication.getOpLogByQueryPageFromMedicalLogCenter(codeRequest), executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_QUERY_THREAD_POOL));
            PageDto<MedicalCenterOperationLogDTO> codePage = codeFuture.get();
            if (codePage != null && CollUtil.isNotEmpty(codePage.getList())) {
                ret.addAll(codePage.getList());
            }
        }
        PageDto<MedicalCenterOperationLogDTO> medicalPromiseIdPage = medicalPromiseIdPageFuture.get();
        if (medicalPromiseIdPage != null && CollUtil.isNotEmpty(medicalPromiseIdPage.getList())) {
            ret.addAll(medicalPromiseIdPage.getList());
        }

        MedicalReportChangeLog medicalReportChangeLog = new MedicalReportChangeLog();
        medicalReportChangeLog.setMedicalPromiseId(medicalPromiseRequest.getMedicalPromiseId());
        CompletableFuture<List<MedicalReportChangeLog>> medicalReportLogFuture = CompletableFuture.supplyAsync(() -> medicalReportApplication.queryMedicalReportChangeLogList(medicalReportChangeLog), executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_QUERY_THREAD_POOL));
        List<MedicalReportChangeLog> medicalReportChangeLogs = medicalReportLogFuture.get();
        if (CollUtil.isNotEmpty(medicalReportChangeLogs)) {
            for (MedicalReportChangeLog medicalReportChange : medicalReportChangeLogs) {
                MedicalCenterOperationLogDTO medicalCenterOperationLogDTO = new MedicalCenterOperationLogDTO();
                medicalCenterOperationLogDTO.setOpDesc("系统存储报告文件");
                medicalCenterOperationLogDTO.setOpTime(medicalReportChange.getUpdateTime());
                medicalCenterOperationLogDTO.setCreated(medicalReportChange.getUpdateTime());
                medicalCenterOperationLogDTO.setModified(medicalReportChange.getUpdateTime());
                medicalCenterOperationLogDTO.setIsSuccess(true);
                if (StringUtils.isNotBlank(medicalReportChange.getReportOss())) {
                    medicalReportChange.setReportOss(fileManageService.getPublicUrl(medicalReportChange.getReportOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30)));
                }
                if (StringUtils.isNotBlank(medicalReportChange.getSourceOss())) {
                    medicalReportChange.setSourceOss(fileManageService.getPublicUrl(medicalReportChange.getSourceOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30)));
                }
                medicalCenterOperationLogDTO.setNewValue(JSON.toJSONString(medicalReportChange));
                ret.add(medicalCenterOperationLogDTO);
            }
        }

        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseReq = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseReq.setMedicalPromiseId(medicalPromiseRequest.getMedicalPromiseId());
        CompletableFuture<MedicalReportDTO> medicalReportFuture = CompletableFuture.supplyAsync(() -> medicalReportApplication.getByMedicalPromiseId(medicalPromiseReq), executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_QUERY_THREAD_POOL));
        MedicalReportDTO medicalReportDTO = medicalReportFuture.get();
        if (medicalReportDTO != null) {
            MedicalCenterOperationLogDTO medicalCenterOperationLogDTO = new MedicalCenterOperationLogDTO();
            medicalCenterOperationLogDTO.setOpDesc("【最新】系统存储报告文件");
            medicalCenterOperationLogDTO.setOpTime(medicalReportDTO.getUpdateTime());
            medicalCenterOperationLogDTO.setCreated(medicalReportDTO.getUpdateTime());
            medicalCenterOperationLogDTO.setModified(medicalReportDTO.getUpdateTime());
            medicalCenterOperationLogDTO.setIsSuccess(true);
            if (StringUtils.isNotBlank(medicalReportDTO.getReportOss())) {
                medicalReportDTO.setReportOss(fileManageService.getPublicUrl(medicalReportDTO.getReportOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30)));
            }
            if (StringUtils.isNotBlank(medicalReportDTO.getSourceOss())) {
                medicalReportDTO.setSourceOss(fileManageService.getPublicUrl(medicalReportDTO.getSourceOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30)));
            }
            medicalCenterOperationLogDTO.setNewValue(JSON.toJSONString(medicalReportDTO));
            ret.add(medicalCenterOperationLogDTO);
        }

        List<MedicalCenterOperationLogDTO> collect = ret.stream().sorted(Comparator.comparing(MedicalCenterOperationLogDTO::getCreated).reversed()).collect(Collectors.toList());
        collect.forEach(this::buildRet);

        return ResponseUtil.buildSuccResponse(collect);
    }


    /**
     * 重置检测单报告状态，供应商可以重推报告
     *
     * @param request request
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/report/query")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "运营后台查看报告", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<String> resetReportStatus(@RequestBody MedicalPromisePageRequest request){
        AssertUtils.nonNull(request.getMedicalPromiseId(), "medicalPromiseId不允许为空");
        // 有权限过滤
        ManViaFloorConfig manViaFloorConfig = manViaApplication.findPageFloor(CommonConstant.MAN_PAGE_MEDICAL_PROMISE_DETAIL, CommonConstant.MAN_PAGE_MEDICAL_PROMISE_DETAIL_STATION_FLOOR);
        // 权限过滤后无查询权限，抛异常
        if (manViaFloorConfig == null || CollUtil.isEmpty(manViaFloorConfig.getFloorElementConfigList()) || !manViaFloorConfig.getFloorElementConfigList().stream().map(ManViaFloorElementConfig::getKey).collect(Collectors.toList()).contains(CommonConstant.MAN_PAGE_MEDICAL_PROMISE_DETAIL_STATION_FLOOR_ELE_QUERY_REPORT)) {
            throw new BusinessException(new DynamicErrorCode("-1","账号无权限"));
        }
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(request.getMedicalPromiseId());
        MedicalReportDTO medicalReportDTO = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        String url = fileManageService.getPublicUrl(medicalReportDTO.getReportOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30));
        return ResponseUtil.buildSuccResponse(url);
    }


    /**
     *
     * @param operationLogDTO
     */
    private void buildRet(MedicalCenterOperationLogDTO operationLogDTO) {
        try {
            OpLogResult opLogResult = JSON.parseObject(operationLogDTO.getOpExtraData(), OpLogResult.class);
            operationLogDTO.setIsSuccess(opLogResult.getIsSuccess());
            operationLogDTO.setMsg(opLogResult.getMsg());
            operationLogDTO.setCode(opLogResult.getCode());
        } catch (Exception e) {
            log.info("MedicalPromiseController buildRet error", e);
            OpLogResult opLogResult = new OpLogResult();
            opLogResult.setCode("");
            opLogResult.setIsSuccess(true);
            opLogResult.setMsg("");
        }
    }

}
