package com.jdh.o2oservice.web.controller.c;

import com.alibaba.fastjson.JSON;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.SkuDetailRouteJumpConfig;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.product.repository.db.ProductDetailPropertiesRepository;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/4/30 09:38
 **/
@Slf4j
@Controller
@RequestMapping("/product/route")
public class ProductController {

    @Resource
    private ProductApplication productApplication;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private ProductDetailPropertiesRepository productDetailPropertiesRepository;

    /**
     * 商详页面路由
     *
     * @return response
     */
    @RequestMapping(method = RequestMethod.GET, value = "/page")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PRODUCT, operationDesc = "商详路由")
    public void routeSkuRedirect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String skuId = request.getParameter("skuId");
        if(StringUtils.isBlank(skuId)){
            skuId = request.getParameter("sku");
        }
        log.info("ProductController.routeSkuRedirect skuId:{}", skuId);
        /**sku关系映射逻辑*/
        Map<String, String> skuOld2NewMap = duccConfig.getSkuOldToNewMap();
        skuId = StringUtils.isNotBlank(skuOld2NewMap.get(skuId)) ? skuOld2NewMap.get(skuId) : skuId;
        //校验 sku 是否是新的地址
        JdhSkuRequest jdhSkuRequest = new JdhSkuRequest();
        jdhSkuRequest.setSkuId(Long.valueOf(skuId));
        jdhSkuRequest.setQueryServiceItem(false);
        jdhSkuRequest.setQuerySkuCoreData(false);
        JdhSkuDto jdhSkuDto = productApplication.queryAggregationJdhSkuInfo(jdhSkuRequest);
        Map<Long,String> productRouteControl = duccConfig.getProductRouteControl();
        if(productRouteControl != null && productRouteControl.size() > 0 && productRouteControl.containsKey(Long.valueOf(skuId))){
            log.info("ProductController.routeSkuRedirect 命中ducc干预 skuId:{}, 干预方向:{}", skuId, productRouteControl.get(Long.valueOf(skuId)));
            redirect(request, response, "new".equalsIgnoreCase(productRouteControl.get(Long.valueOf(skuId))),jdhSkuDto,skuOld2NewMap, skuId);
            return;
        }
        redirect(request, response, Objects.nonNull(jdhSkuDto),jdhSkuDto,skuOld2NewMap, skuId);
    }

    /**
     * 跳转
     * @param request request
     * @param response response
     * @param isNewPage isNewPage
     * @param jdhSkuDto jdhSkuDto
     * @param skuOld2NewMap skuOld2NewMap
     * @throws IOException e
     */
    private void redirect(HttpServletRequest request, HttpServletResponse response, boolean isNewPage,JdhSkuDto jdhSkuDto,Map<String, String> skuOld2NewMap, String jumpSkuId) throws IOException {
        String queryString = request.getQueryString();
        String redirectUrlResult = "";
        // 1、sku 配置的跳转规则列表，优先级按照配置的顺序自上而下匹配
        // 2、如果命中了，则跳转到对应的url
        Map<String, Object> param = new HashMap<>(1);
        param.put("isNewPage", isNewPage);
        param.put("jdhSkuDto", jdhSkuDto);
        List<SkuDetailRouteJumpConfig> skuDetailJumpConfigList = JSON.parseArray(JSON.toJSONString(duccConfig.getSkuDetailRouteJumpConfig()), SkuDetailRouteJumpConfig.class);;
        for (SkuDetailRouteJumpConfig skuDetailJumpConfig : skuDetailJumpConfigList) {
            if ((boolean) AviatorEvaluator.compile(skuDetailJumpConfig.getExpression(), Boolean.TRUE).execute(param)) {
                redirectUrlResult = skuDetailJumpConfig.getUrl();
                log.info("ProductController.routeSkuRedirect 命中规则名称={} 命中url={}", skuDetailJumpConfig.getExpressionDesc(), redirectUrlResult);
                break;
            }
        }
        String uri = replaceVariable(redirectUrlResult, jumpSkuId) + queryString;
        log.info("ProductController.routeSkuRedirect 跳转地址:{}", uri);
        /**新老sku映射*/
        for (String skuNeedMapping : skuOld2NewMap.keySet()) {
            if (uri.contains(skuNeedMapping)){
                uri = uri.replace(skuNeedMapping, skuOld2NewMap.get(skuNeedMapping));
                break;
            }
        }
        log.info("ProductController.routeSkuRedirect 跳转地址new:{}", uri);
        response.sendRedirect(uri);
    }

    /**
     * 变量替换，主站商品id在uri中
     * @param url url
     * @param skuId skuId
     * @return url
     */
    private String replaceVariable(String url, String skuId) {
        // 具体的业务数据，需要替换占位符
        Map<String, String> dataMap = new HashMap<>(1);
        dataMap.put("skuId", skuId);
        // 创建StringSubstitutor，入参是要替换的业务数据map
        StringSubstitutor sub = new StringSubstitutor(dataMap);
        // 占位符字段替换为具体业务数据，入参为模板字符串
        return sub.replace(url);
    }
}
