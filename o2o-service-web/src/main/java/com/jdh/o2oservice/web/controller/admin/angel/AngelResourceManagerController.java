package com.jdh.o2oservice.web.controller.admin.angel;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelSkillDictApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angel.cmd.*;
import com.jdh.o2oservice.export.angel.dto.AngelSkillDictDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelProfessionTitleDictDto;
import com.jdh.o2oservice.export.angel.query.AngelDetailRequest;
import com.jdh.o2oservice.export.angel.query.AngelPageRequest;
import com.jdh.o2oservice.export.angel.query.AngelSkillDictPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 服务者资源管理controller
 * <AUTHOR>
 * @Date 2024/4/26
 * @Version V1.0
 **/
@Slf4j
@RestController
@RequestMapping("/angel/resourceManager")
public class AngelResourceManagerController {

    @Resource
    AngelApplication angelApplication;

    @Resource
    AngelSkillDictApplication angelSkillDictApplication;

    @Resource
    FileManageApplication fileManageApplication;

    /**
     * 查询护士入驻列表
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryPageList")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.queryPageList")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.ANGEL, operationDesc = "查询入驻护士列表")
    public Response<PageDto<JdhAngelDto>> queryPageList(@RequestBody AngelPageRequest request) {
        PageDto<JdhAngelDto> pageDto = angelApplication.queryAngelByPage(request);
        return ResponseUtil.buildSuccResponse(pageDto);
    }

    /**
     * 获取护士职称列表
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/getAngelProfessionTitleDictList")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.ANGEL, operationDesc = "查询护士职称列表")
    public Response<List<JdhAngelProfessionTitleDictDto>> getAngelProfessionTitleDictList(){
        return ResponseUtil.buildSuccResponse(angelApplication.getAngelProfessionTitleDictList());
    }

    /**
     * 查询护士详情
     * @param angelDetailRequest
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelDetail")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.queryAngelDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.ANGEL, operationDesc = "查询护士详情")
    public Response<JdhAngelDetailDto> queryAngelDetail(@RequestBody AngelDetailRequest angelDetailRequest) {
        JdhAngelDetailDto jdhAngelDetailDto = angelApplication.queryAngelDetail(angelDetailRequest);
        return ResponseUtil.buildSuccResponse(jdhAngelDetailDto);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/batchBindStationMaster")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.batchBindStationMaster")
    @OperationLog(operationType = OpTypeEnum.ADD, domainCode = DomainEnum.ANGEL, operationDesc = "绑定站长", recordParamBizIdExpress = {"args[0].stationMaster"})
    public Response<Boolean> batchBindStationMaster(@RequestBody AngelBindStationMasterCmd angelBindStationMasterCmd) {
        String pin = LoginContext.getLoginContext().getPin();
        angelBindStationMasterCmd.setOperator(pin);
        Boolean bindResult = angelApplication.batchBindStationMaster(angelBindStationMasterCmd);
        return ResponseUtil.buildSuccResponse(bindResult);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/batchUnBindStationMaster")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.batchUnBindStationMaster")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.ANGEL, operationDesc = "解绑站长", recordParamBizIdExpress = {"args[0].stationMaster"})
    public Response<Boolean> batchUnBindStationMaster(@RequestBody AngelBindStationMasterCmd angelBindStationMasterCmd) {
        String pin = LoginContext.getLoginContext().getPin();
        angelBindStationMasterCmd.setOperator(pin);
        Boolean unbindResult = angelApplication.batchUnBindStationMaster(angelBindStationMasterCmd);
        return ResponseUtil.buildSuccResponse(unbindResult);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/updateAngelJobNature")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.updateAngelJobNature")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.ANGEL, operationDesc = "修改服务者标签", recordParamBizIdExpress = {"args[0].angelId"})
    public Response<Boolean> updateAngelJobNature(@RequestBody SubmitAngelCmd submitAngelCmd) {
        String pin = LoginContext.getLoginContext().getPin();
        submitAngelCmd.setOperator(pin);
        Boolean updateResult = angelApplication.updateAngelJobNature(submitAngelCmd);
        return ResponseUtil.buildSuccResponse(updateResult);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/batchBindAngelSkill")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.batchBindAngelSkill")
    @OperationLog(operationType = OpTypeEnum.ADD, domainCode = DomainEnum.ANGEL, operationDesc = "批量绑定服务者技能")
    public Response<Boolean> batchBindAngelSkill(@RequestBody AngelBindSkillCmd angelBindSkillCmd) {
        String pin = LoginContext.getLoginContext().getPin();
        angelBindSkillCmd.setOperator(pin);
        Boolean bindResult = angelApplication.batchBindAngelSkill(angelBindSkillCmd);
        return ResponseUtil.buildSuccResponse(bindResult);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/batchUnBindAngelSkill")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.batchUnBindAngelSkill")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.ANGEL, operationDesc = "批量解绑服务者技能")
    public Response<Boolean> batchUnBindAngelSkill(@RequestBody AngelBindSkillCmd angelBindSkillCmd) {
        String pin = LoginContext.getLoginContext().getPin();
        angelBindSkillCmd.setOperator(pin);
        Boolean bindResult = angelApplication.batchUnBindAngelSkill(angelBindSkillCmd);
        return ResponseUtil.buildSuccResponse(bindResult);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryPageSkill")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.queryPageSkill")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.BASE, operationDesc = "查询入技能列表")
    public Response<PageDto<AngelSkillDictDto>> queryPageSkill(@RequestBody AngelSkillDictPageRequest request) {
        PageDto<AngelSkillDictDto> pageDto = angelSkillDictApplication.queryByPage(request);
        return ResponseUtil.buildSuccResponse(pageDto);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelSkillByPage")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.queryAngelSkillByPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.BASE, operationDesc = "查询入技能列表")
    public Response<PageDto<AngelSkillDictDto>> queryAngelSkillByPage(@RequestBody AngelSkillDictPageRequest request) {
        PageDto<AngelSkillDictDto> pageDto = angelSkillDictApplication.queryAngelSkillByPage(request);
        return ResponseUtil.buildSuccResponse(pageDto);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/updateAngelWorkIdentity")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.angel.AngelResourceManagerController.updateAngelWorkIdentity")
    @OperationLog(operationType = OpTypeEnum.UPDATE, domainCode = DomainEnum.ANGEL, operationDesc = "修改工作身份", recordParamBizIdExpress = {"args[0].angelId"})
    public Response<Boolean> updateAngelWorkIdentity(@RequestBody UpdateAngelWorkIdentityCmd cmd) {
        String pin = LoginContext.getLoginContext().getPin();
        cmd.setOperator(pin);
        Boolean updateResult = angelApplication.updateWorkIdentity(cmd);
        return ResponseUtil.buildSuccResponse(updateResult);
    }

    /**
     * 批量开关护士技能
     * @param angelBatchSkillCmd
     * @return
     */
    @RequestMapping("/batchAngelSkillUpload")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.admin.angel.AngelResourceManagerController.batchAngelSkillUpload")
    @OperationLog(operationType = OpTypeEnum.ADD, domainCode = DomainEnum.ANGEL, operationDesc = "批量开关护士技能")
    Response<Boolean> batchAngelSkillUpload(@RequestBody AngelBatchSkillCmd angelBatchSkillCmd){
        String erp = LoginContext.getLoginContext().getPin();
        angelBatchSkillCmd.setUserPin(erp);
        //1、构建上下文
        Map<String, Object> ctx = new HashMap<>();
        ctx.put("fileId", angelBatchSkillCmd.getFileId());
        ctx.put("scene", angelBatchSkillCmd.getScene());
        ctx.put("userPin", angelBatchSkillCmd.getUserPin());
        ctx.put("operationType", angelBatchSkillCmd.getScene());
        fileManageApplication.importFile(ctx);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }
}
