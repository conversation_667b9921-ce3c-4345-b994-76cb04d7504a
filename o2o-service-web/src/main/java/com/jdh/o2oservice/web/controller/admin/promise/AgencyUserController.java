package com.jdh.o2oservice.web.controller.admin.promise;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.common.web.LoginContext;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.support.service.PatientApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.CredentialEnum;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseModifyEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.model.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.vo.JdSkuRelationInfoVo;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angel.query.StationGeoForManQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.ResetPromiseCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.AgencyMedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.AgencyMedicalPromiseRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.product.dto.GroupUserAddressDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.UserAddressDetailDTO;
import com.jdh.o2oservice.export.product.query.AgencyQueryDateRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.product.query.OrderUserAddressQuery;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.dto.ModifyRecordDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.ModifyPromiseQuery;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.VoucherIdRequest;
import com.jdh.o2oservice.export.support.command.PatientAddCommand;
import com.jdh.o2oservice.export.support.dto.PatientDto;
import com.jdh.o2oservice.export.support.query.PatientListRequest;
import com.jdh.o2oservice.export.trade.dto.AvaiableAppointmentTimeDTO;
import com.jdh.o2oservice.export.trade.query.AvaiableAppointmentTimeParam;
import com.jdh.o2oservice.export.user.cmd.AgencyPatientCmd;
import com.jdh.o2oservice.export.user.cmd.AgencyUserAddressCmd;
import com.jdh.o2oservice.export.user.dto.AgencyPatientDTO;
import com.jdh.o2oservice.export.user.dto.AgencyUserAddressDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AgencyUserController
 *
 * <AUTHOR>
 * @date 2024/05/03
 */
@RestController
@RequestMapping("/admin/agency/user")
@Slf4j
public class AgencyUserController {

    /**
     * 履约单
     */
    @Resource
    MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 患者application
     */
    @Resource
    private PatientApplication patientApplication;

    /**
     * jdOrderApplication
     */
    @Resource
    private JdOrderApplication jdOrderApplication;

    /**
     * 健康地址rpc
     */
    @Resource
    private JdhAddressRpc jdhAddressRpc;

    /**
     * 商品
     */
    @Resource
    private ProductApplication productApplication;

    /**
     * 履约单
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * 交易
     */
    @Resource
    private TradeApplication tradeApplication;

    /**
     * 站点
     */
    @Resource
    private StationApplication stationApplication;

    /**
     * 服务单
     */
    @Resource
    private VoucherApplication voucherApplication;

    /**
     * 行政地址
     */
    @Resource
    private AddressRpc addressRpc;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private PromiseHistoryRepository promiseHistoryRepository;

    @Resource
    private AngelApplication angelApplication;

    @Resource
    private VerticalBusinessRepository businessRepository;

    /**
     * 聚合页面
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/aggregationView")
    @LogAndAlarm(jKey = "AgencyUserController.aggregationView")
    @OperationLog(operationType = OpTypeEnum.QUERY, domainCode = DomainEnum.PROMISE, operationDesc = "代预约聚合页")
    public Response<AgencyMedicalPromiseDTO> aggregationView(@Validated @RequestBody AgencyMedicalPromiseRequest request) {
        MedicalPromiseDTO medicalPromiseDTO = checkPinAndMedicalPromise(request.getUserPin(), request.getMedicalPromiseId());
        Long skuId = medicalPromiseDTO.getServiceId();
        AgencyMedicalPromiseDTO agencyMedicalPromiseDTO = new AgencyMedicalPromiseDTO();
        // 查询商品基础信息，是否需要实名
        JdhSkuRequest skuRequest = new JdhSkuRequest();
        skuRequest.setSkuId(skuId);
        JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(skuRequest);
        if (jdhSkuDto != null) {
            agencyMedicalPromiseDTO.setNeedRealName(NumConstant.NUM_1.equals(jdhSkuDto.getRequiredRealName()));
        }
        // 查询过滤商品可服务地址
        OrderUserAddressQuery orderUserAddressQuery = new OrderUserAddressQuery();
        orderUserAddressQuery.setUserPin(request.getUserPin());
        orderUserAddressQuery.setSkuNoList(Lists.newArrayList(String.valueOf(skuId)));
        Map<String, GroupUserAddressDTO> userAddressDTOMap = productApplication.listGroupAddress(orderUserAddressQuery);
        if (CollUtil.isNotEmpty(userAddressDTOMap)) {
            List<AgencyUserAddressDTO> userAddressList = Lists.newArrayList();
            if (userAddressDTOMap.containsKey("canSelect") && userAddressDTOMap.get("canSelect") != null && CollUtil.isNotEmpty(userAddressDTOMap.get("canSelect").getList())) {
                userAddressList.addAll(userAddressDTOMap.get("canSelect").getList().stream().map(s -> transferAddress(s, false)).collect(Collectors.toList()));
            }
            if (userAddressDTOMap.containsKey("disSelect") && userAddressDTOMap.get("disSelect") != null && CollUtil.isNotEmpty(userAddressDTOMap.get("disSelect").getList())) {
                userAddressList.addAll(userAddressDTOMap.get("disSelect").getList().stream().map(s -> transferAddress(s, true)).collect(Collectors.toList()));
            }
            agencyMedicalPromiseDTO.setUserAddressList(userAddressList.stream().sorted(Comparator.comparing(AgencyUserAddressDTO::isSelected).reversed()).collect(Collectors.toList()));
        }
        // 查询过滤商品可预约档案列表
        PatientListRequest patientListRequest = new PatientListRequest();
        patientListRequest.setUserPin(request.getUserPin());
        patientListRequest.setSkuIdList(Sets.newHashSet(skuId));
        List<PatientDto> patientList = patientApplication.getPatientList(patientListRequest);
        if (CollUtil.isNotEmpty(patientList)) {
            if (Boolean.TRUE.equals(agencyMedicalPromiseDTO.getNeedRealName())) {
                agencyMedicalPromiseDTO.setPatientList(patientList.stream().map(this::transferPatient).sorted(Comparator.comparing(AgencyPatientDTO::getIsRealName).reversed()).collect(Collectors.toList()));
            } else {
                agencyMedicalPromiseDTO.setPatientList(patientList.stream().map(this::transferPatient).collect(Collectors.toList()));
            }
        }
        // 查询收货人信息
        VoucherDto jdhVoucher = voucherApplication.findByVoucherId(VoucherIdRequest.builder().voucherId(medicalPromiseDTO.getVoucherId()).build());
        JdOrderExt jdOrderExt = jdOrderApplication.findJdOrderExtDetail(Long.parseLong(jdhVoucher.getSourceVoucherId()), JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType());
        if (jdOrderExt != null && StringUtils.isNotBlank(jdOrderExt.getExtContext())) {
            OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExt.getExtContext(),OrderAppointmentInfoValueObject.class);
            if (orderAppointmentInfo != null && orderAppointmentInfo.getAddressInfo() != null && StringUtils.isNotBlank(orderAppointmentInfo.getAddressInfo().getDecryptMobile())) {
                PhoneNumber phoneNumber = new PhoneNumber(orderAppointmentInfo.getAddressInfo().getDecryptMobile());
                agencyMedicalPromiseDTO.setOrderMobile(phoneNumber.encrypt());
            }
        }
        // 查询主品信息
        JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(Long.parseLong(jdhVoucher.getSourceVoucherId()));
        List<JdSkuRelationInfoVo> main = jdOrder.findAllMainSku();
        if (CollUtil.isNotEmpty(main)) {
            agencyMedicalPromiseDTO.setMainSkuName("【主品信息】" + main.get(0).getMainSkuName());
        }

        return ResponseUtil.buildSuccResponse(agencyMedicalPromiseDTO);
    }

    /**
     * 预约时间
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/availableTime")
    @LogAndAlarm(jKey = "AgencyUserController.availableTime")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "代预约查询可预约时间")
    public Response<List<AgencyAppointDateDto>> availableTime(@Validated @RequestBody AgencyQueryDateRequest request) {
        MedicalPromiseDTO medicalPromiseDTO = checkPinAndMedicalPromise(request.getUserPin(), request.getMedicalPromiseId());
        AvaiableAppointmentTimeParam param = new AvaiableAppointmentTimeParam();
        param.setUserPin(request.getUserPin());
        param.setSkuIds(Lists.newArrayList(medicalPromiseDTO.getServiceId()));
        if (request.getAddressId() != null) {
            param.setAddressId(String.valueOf(request.getAddressId()));
            List<AddressDetailBO> addressDetailBOList = jdhAddressRpc.queryAddressList(request.getUserPin());
            AddressDetailBO addressDetailBO = addressDetailBOList.stream().filter(s -> request.getAddressId().equals(s.getAddressId())).findFirst().orElse(null);
            param.setFullAddress(addressDetailBO != null ? addressDetailBO.getFullAddress() : null);
        } else {
            PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromiseDTO.getPromiseId()).build());
            if (promiseDto == null) {
                throw new BusinessException(PromiseErrorCode.PROMISE_NOT_EXISTS);
            }if (promiseDto.getStore() == null) {
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_MISS_ERROR);
            }
            if (StringUtils.isBlank(promiseDto.getStore().getStoreId())) {
                // 买约一体无storeId,底层需要addressId,用于缓存逻辑,不影响实际业务,默认给个-1
                promiseDto.getStore().setStoreId("-1");
            }
            param.setAddressId(promiseDto.getStore().getStoreId());
            param.setFullAddress(promiseDto.getStore().getStoreAddr());
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(promiseDto.getVerticalCode());
            if(Objects.nonNull(jdhVerticalBusiness)){
                param.setSkuServiceType(BusinessModeUtil.getSkuServiceType(BusinessModeEnum.getEnumByCode(jdhVerticalBusiness.getBusinessModeCode())));
            }
        }
        AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = tradeApplication.queryAvaiableAppointmentTime(param);
        return ResponseUtil.buildSuccResponse(promiseApplication.changeAndFilterDate(request.getParentDateId(), avaiableAppointmentTimeDTO));
    }

    /**
     * 保存地址
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/address/save")
    @LogAndAlarm(jKey = "AgencyUserController.addressSave")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "代预约保存地址", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<AgencyUserAddressDTO> addressSave(@Validated @RequestBody AgencyUserAddressCmd cmd) {
        MedicalPromiseDTO medicalPromiseDTO = checkPinAndMedicalPromise(cmd.getUserPin(), cmd.getMedicalPromiseId());
        AddressDetailBO addressDetailBO = new AddressDetailBO();
        try {
            if (StringUtils.isNotBlank(cmd.getName())) {
                UserName userName = new UserName(cmd.getName());
                userName.decrypt();
                if (!userName.verifyNotCheckSensitiveWord()){
                    throw new BusinessException(BaseDomainErrorCode.USER_NAME_ILLEGAL);
                }
                addressDetailBO.setName(userName.getName());
            }
        } catch (BusinessException e) {
            log.error("AgencyUserController.addressSave name error", e);
            throw e;
        } catch (Exception e) {
            log.error("AgencyUserController.addressSave name error", e);
            throw new BusinessException(SystemErrorCode.DECRYPT_DATA_ERROR.formatDescription("姓名"));
        }
        try {
            if (StringUtils.isNotBlank(cmd.getMobile())) {
                PhoneNumber phoneNumber = new PhoneNumber(cmd.getMobile());
                phoneNumber.decrypt();
                phoneNumber.verify();
                if (!phoneNumber.verify()){
                    throw new BusinessException(BaseDomainErrorCode.USER_PHONE_ILLEGAL);
                }
                addressDetailBO.setMobile(phoneNumber.getPhone());
            }
        } catch (BusinessException e) {
            log.error("AgencyUserController.addressSave mobile error", e);
            throw e;
        } catch (Exception e) {
            log.error("AgencyUserController.addressSave mobile error", e);
            throw new BusinessException(SystemErrorCode.DECRYPT_DATA_ERROR.formatDescription("手机号"));
        }
        try {
            if (StringUtils.isNotBlank(cmd.getAddressDetail())) {
                addressDetailBO.setAddressDetail(RSACode.sectionDecryptByPrivateKey(cmd.getAddressDetail()).replaceAll(" ", ""));
            }
            if (addressDetailBO.getAddressDetail().length() > 50) {
                throw new BusinessException(new DynamicErrorCode(SystemErrorCode.SYSTEM_ERROR.getCode(),"详细地址不允许超过50个字符"));
            }
        } catch (BusinessException e) {
            log.error("AgencyUserController.addressSave addressDetail error", e);
            throw e;
        } catch (Exception e) {
            log.error("AgencyUserController.addressSave addressDetail error", e);
            throw new BusinessException(SystemErrorCode.DECRYPT_DATA_ERROR.formatDescription("详细地址"));
        }
        addressDetailBO.setProvinceId(cmd.getProvinceId());
        String provinceName = "";
        if (cmd.getProvinceId() != null) {
            provinceName = addressRpc.getDistrictInfo(cmd.getProvinceId()).getDistrictName();
        }
        addressDetailBO.setCityId(cmd.getCityId());
        String cityName = "";
        if (cmd.getCityId() != null) {
            cityName = addressRpc.getDistrictInfo(cmd.getCityId()).getDistrictName();
        }
        addressDetailBO.setCountyId(cmd.getCountyId());
        String countyName = "";
        if (cmd.getCountyId() != null) {
            countyName = addressRpc.getDistrictInfo(cmd.getCountyId()).getDistrictName();
        }
        addressDetailBO.setTownId(cmd.getTownId());
        String townName = "";
        if (cmd.getTownId() != null) {
            townName = addressRpc.getDistrictInfo(cmd.getTownId()).getDistrictName();
        }

        // 校验地址是否可用
        //返回的数据表示地址是高亮
        List<JdhStationDto> jdhStationDtos = new ArrayList<>();
        try {
            List<com.jdh.o2oservice.export.angel.query.AddressDetail> addressDetailList = new ArrayList<>();
            com.jdh.o2oservice.export.angel.query.AddressDetail addressDetail = new com.jdh.o2oservice.export.angel.query.AddressDetail();
            // 底层需要addressId，新建没有addressId，这里使用随机数字，避免底层缓存key重复
            addressDetail.setAddressId(RandomUtil.randomNumbers(10));
            addressDetail.setFullAddress(provinceName + cityName + countyName + townName + addressDetailBO.getAddressDetail());
            addressDetailList.add(addressDetail);
            StationGeoForManQuery build = new StationGeoForManQuery();
            build.setSkuNos(Sets.newHashSet(medicalPromiseDTO.getServiceId()));
            build.setAddressDetailList(addressDetailList);
            log.info("AgencyUserController.addressSave build={}", JSON.toJSONString(build));
            jdhStationDtos = stationApplication.queryJdhStationGeoForMan(build);
        } catch (BusinessException e) {
            log.error("AgencyUserController.addressSave checkAddress error", e);
            throw e;
        } catch (Exception e) {
            log.error("AgencyUserController.addressSave checkAddress error", e);
            throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
        }

        if (CollUtil.isEmpty(jdhStationDtos)) {
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_NO_SERVICE);
        }

        AddressDetailBO ret = jdhAddressRpc.insertAddress(cmd.getUserPin(), addressDetailBO);
        return ResponseUtil.buildSuccResponse(transferUserAddress(ret));
    }

    /**
     * 保存档案
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/promise/submit")
    @LogAndAlarm(jKey = "AgencyUserController.promiseSubmit")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "代预约提交", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> promiseSubmit(@Validated @RequestBody AgentSubmitPromiseCmd cmd) {
        MedicalPromiseDTO medicalPromiseDTO = checkPinAndMedicalPromise(cmd.getUserPin(), cmd.getMedicalPromiseId());
        // 前置校验地址数是否已经发生变更
        checkAddress(cmd.getUserPin(), cmd);
        // 前置校验预约人信息
        checkPatient(cmd);
        SubmitPromiseCmd submitPromiseCmd = new SubmitPromiseCmd();
        submitPromiseCmd.setUserPin(cmd.getUserPin());
        // 业务身份
        submitPromiseCmd.setServiceType(medicalPromiseDTO.getServiceType());
        submitPromiseCmd.setVerticalCode(medicalPromiseDTO.getVerticalCode());
        // 履约单
        submitPromiseCmd.setPromiseId(String.valueOf(medicalPromiseDTO.getPromiseId()));
        // 服务信息
        List<PromiseServiceItem> services = new ArrayList<>();
        PromiseServiceItem promiseServiceItem = new PromiseServiceItem();
        promiseServiceItem.setPromiseId(medicalPromiseDTO.getPromiseId());
        promiseServiceItem.setServiceId(medicalPromiseDTO.getServiceId());
        services.add(promiseServiceItem);
        submitPromiseCmd.setServices(services);
        // 服务地址id
        submitPromiseCmd.setStoreId(String.valueOf(cmd.getAddressId()));
        // 预约人信息
        List<SubmitUser> submitUsers = new ArrayList<>();
        SubmitUser submitUser = new SubmitUser();
        submitUser.setPatientId(cmd.getPatientId());
        submitUsers.add(submitUser);
        submitPromiseCmd.setUsers(submitUsers);
        // 预约日期
        AppointmentTime appointmentTime = new AppointmentTime();
        appointmentTime.setDateType(cmd.getDateType());
        appointmentTime.setAppointmentStartTime(cmd.getAppointmentStartTime());
        appointmentTime.setAppointmentEndTime(cmd.getAppointmentEndTime());
        appointmentTime.setIsImmediately(cmd.getIsImmediately() != null ? cmd.getIsImmediately() : false);
        submitPromiseCmd.setAppointmentTime(appointmentTime);

        submitPromiseCmd.setOperator(LoginContext.getLoginContext().getPin());
        submitPromiseCmd.setOperatorRoleType(OperatorRoleTypeEnum.CUSTOMER_SERVICE.getType());
        if (StringUtils.isNotBlank(cmd.getRemark())) {
            submitPromiseCmd.setRemark(cmd.getRemark());
        }
        Boolean ret = promiseApplication.submit(submitPromiseCmd);
        return ResponseUtil.buildSuccResponse(ret);
    }

    /**
     * 保存档案
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/promise/modify")
    @LogAndAlarm(jKey = "AgencyUserController.modify")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "修改预约时间", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> modify(@Validated @RequestBody AgentModifyPromiseCmd cmd) {
        MedicalPromiseDTO medicalPromiseDTO = checkPinAndMedicalPromise(cmd.getUserPin(), cmd.getMedicalPromiseId());
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromiseDTO.getPromiseId()).build());
        if (promiseDto == null) {
            throw new BusinessException(PromiseErrorCode.PROMISE_NOT_EXISTS);
        }

        ModifyAppointCmd modifyAppointCmd = new ModifyAppointCmd();
        modifyAppointCmd.setPromiseId(String.valueOf(promiseDto.getPromiseId()));
        AppointmentTime appointmentTime = new AppointmentTime();
        appointmentTime.setDateType(2);
        appointmentTime.setAppointmentStartTime(cmd.getAppointmentStartTime());
        appointmentTime.setAppointmentEndTime(cmd.getAppointmentEndTime());
        appointmentTime.setIsImmediately(cmd.getIsImmediately());
        modifyAppointCmd.setAppointmentTime(appointmentTime);
        modifyAppointCmd.setOperator(LoginContext.getLoginContext().getPin());
        modifyAppointCmd.setOperatorRoleType(OperatorRoleTypeEnum.CUSTOMER_SERVICE.getType());
        modifyAppointCmd.setReasonContent(cmd.getReasonContent());
        modifyAppointCmd.setReasonType(cmd.getReasonType());
        modifyAppointCmd.setIsImmediately(cmd.getIsImmediately());
        Boolean ret = promiseApplication.modify(modifyAppointCmd);
        return ResponseUtil.buildSuccResponse(ret);
    }

    /**
     * 保存档案
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/patient/save")
    @LogAndAlarm(jKey = "AgencyUserController.patientSave")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "代预约保存档案", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<AgencyPatientDTO> patientSave(@Validated @RequestBody AgencyPatientCmd cmd) {
        checkPinAndMedicalPromise(cmd.getUserPin(), cmd.getMedicalPromiseId());
        PatientAddCommand addCommand = new PatientAddCommand();
        addCommand.setName(cmd.getName());
        if (StringUtils.isNotBlank(cmd.getCredentialNo())) {
            addCommand.setCredentialNo(cmd.getCredentialNo());
            try {
                CredentialNumber credentialNumber = new CredentialNumber(CredentialEnum.CID.getPatientType(), cmd.getCredentialNo());
                credentialNumber.decrypt();
                credentialNumber.verifyException();
                String crNo = credentialNumber.getCredentialNo();
                addCommand.setGender(IdCardUtils.getGenderByIdCard(crNo));
                addCommand.setBirthday(IdCardUtils.getStandarBirthByIdCard(crNo));
                addCommand.setAge(String.valueOf(IdCardUtils.getAgeByIdCard(crNo)));
            } catch (BusinessException e) {
                log.error("AgencyUserController.patientSave error", e);
                throw e;
            } catch (Exception e) {
                log.error("AgencyUserController.patientSave error", e);
                throw new BusinessException(SystemErrorCode.DECRYPT_DATA_ERROR.formatDescription("证件号"));
            }
        } else {
            addCommand.setGender(cmd.getGender());
            if (StringUtils.isNotBlank(cmd.getBirthday())) {
                addCommand.setBirthday(cmd.getBirthday());
            } else {
                if (StringUtils.isNotBlank(cmd.getAge())) {
                    int age = Integer.parseInt(cmd.getAge());
                    DateTime dateTime = DateUtil.offset(new Date(), DateField.YEAR, -age);
                    addCommand.setBirthday(TimeUtils.dateTimeToStr(dateTime, TimeFormat.DATE_PATTERN_YEAR) + "-01-01");
                }
            }
            addCommand.setAge(cmd.getAge());
        }
        addCommand.setPhone(cmd.getMobile());
        addCommand.setUserPin(cmd.getUserPin());
        Long patientId = patientApplication.addPatient(addCommand);
        PatientListRequest request = new PatientListRequest();
        request.setUserPin(cmd.getUserPin());
        request.setPatientId(patientId);
        PatientDto ret = patientApplication.getPatient(request);
        return ResponseUtil.buildSuccResponse(transferPatient(ret));
    }

    /**
     * 保存档案
     *
     * @param query query
     * @return response
     */
    @PostMapping(value = "/promise/modifyRecords")
    @LogAndAlarm(jKey = "AgencyUserController.modifyRecords")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "修改预约记录", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<List<ModifyRecordDto>> modifyRecords(@Validated @RequestBody ModifyPromiseQuery query) {
        MedicalPromiseDTO medicalPromiseDTO = checkPinAndMedicalPromise(query.getUserPin(), query.getMedicalPromiseId());
        PromiseHistoryRepQuery promiseHistoryRepQuery = PromiseHistoryRepQuery.builder()
                .promiseId(medicalPromiseDTO.getPromiseId())
                .eventCode(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY.getCode())
                .createTimeOrderByDesc(true)
                .build();
        List<JdhPromiseHistory> list = promiseHistoryRepository.findList(promiseHistoryRepQuery);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseUtil.buildSuccResponse(Collections.emptyList());
        }
        List<ModifyRecordDto> modifyRecordDtoList = new ArrayList<>();
        for (JdhPromiseHistory jdhPromiseHistory : list) {
            ModifyRecordDto modifyRecordDto = new ModifyRecordDto();
            modifyRecordDto.setOperator(jdhPromiseHistory.getOperator());
            modifyRecordDto.setReason(jdhPromiseHistory.getReason());

            if (StringUtils.isNotBlank(jdhPromiseHistory.getExtend())) {
                PromiseModifyEventBody promiseModifyEventBody = JSON.parseObject(jdhPromiseHistory.getExtend(), PromiseModifyEventBody.class);
                if (promiseModifyEventBody != null) {
                    modifyRecordDto.setBeforeAppointTime(promiseModifyEventBody.getBeforeTime() != null ? promiseModifyEventBody.getBeforeTime().formatAppointmentStartTime() + "-" + promiseModifyEventBody.getBeforeTime().getAppointmentEndTime().format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter): null);
                    modifyRecordDto.setAfterAppointTime(promiseModifyEventBody.getAfterTime() != null ? promiseModifyEventBody.getAfterTime().formatAppointmentStartTime() + "-" + promiseModifyEventBody.getAfterTime().getAppointmentEndTime().format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter): null);
                    modifyRecordDto.setOperateTime(promiseModifyEventBody.getOperatorTime() == null ? null : TimeUtils.dateTimeToStr(promiseModifyEventBody.getOperatorTime()));
                    modifyRecordDto.setOperatorRoleType(promiseModifyEventBody.getOperatorRoleType());
                    if (OperatorRoleTypeEnum.DOCTOR.getType().equals(promiseModifyEventBody.getOperatorRoleType()) && StringUtils.isNotBlank(promiseModifyEventBody.getOperator())) {
                        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelPin(promiseModifyEventBody.getOperator()).build());
                        if (jdhAngelDto != null) {
                            modifyRecordDto.setOperator(jdhAngelDto.getAngelName());
                        }
                    }
                }
            }
            if (StringUtils.isBlank(modifyRecordDto.getOperateTime())) {
                modifyRecordDto.setOperateTime(jdhPromiseHistory.getCreateTime() == null ? null : TimeUtils.dateTimeToStr(jdhPromiseHistory.getCreateTime()));
            }
            modifyRecordDtoList.add(modifyRecordDto);
        }
        return ResponseUtil.buildSuccResponse(modifyRecordDtoList);
    }

    /**
     * 非快检模式: 重置订单状态-重新采样信息
     * @return
     */
    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkController.resetPromise")
    @RequestMapping(method = RequestMethod.POST, value = "/resetPromise")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "非快检-重置采样信息", recordParamBizIdExpress = {"args[0].promiseId"})
    public Response<Boolean> resetPromise(@RequestBody ResetPromiseCmd resetPromiseCmd){
        promiseApplication.delPromise(resetPromiseCmd);
        return Response.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 拼装所属区域
     *
     * @param provinceName provinceName
     * @param cityName     cityName
     * @param countyName   countyName
     * @return 区域
     */
    private String buildDistrict(String provinceName, String cityName, String countyName) {
        provinceName = StringUtils.isNotBlank(provinceName) ? provinceName: "";
        cityName = StringUtils.isNotBlank(cityName) ? cityName : "";
        countyName = StringUtils.isNotBlank(countyName) ? countyName : "";
        return provinceName + cityName + countyName;
    }

    /**
     * 转换地址
     *
     * @param bo bo
     * @return dto
     */
    private AgencyUserAddressDTO transferAddress(UserAddressDetailDTO bo, Boolean disable) {
        if (bo == null) {
            return null;
        }
        AgencyUserAddressDTO agencyUserAddressDTO = new AgencyUserAddressDTO();
        UserName userName = new UserName(bo.getName());
        agencyUserAddressDTO.setName(userName.encrypt());
        agencyUserAddressDTO.setNameMask(userName.mask());
        agencyUserAddressDTO.setMobileMask(bo.getMobile());
        AddressDetail addressDetail = new AddressDetail(bo.getAddressDetail());
        agencyUserAddressDTO.setAddressDetail(addressDetail.encrypt());
        agencyUserAddressDTO.setAddressDetailMask(addressDetail.mask());
        agencyUserAddressDTO.setAddressId(bo.getAddressId());
        agencyUserAddressDTO.setDisabled(disable);
        agencyUserAddressDTO.setAddressDistrict(buildDistrict(bo.getProvinceName(), bo.getCityName(), bo.getCountyName()));
        // 不可选默认不选中
        if (Boolean.TRUE.equals(disable)) {
            agencyUserAddressDTO.setSelected(false);
        } else {
            agencyUserAddressDTO.setSelected(bo.isAddressDefault());
        }
        return agencyUserAddressDTO;
    }

    /**
     * 转换地址
     *
     * @param bo bo
     * @return dto
     */
    private AgencyUserAddressDTO transferUserAddress(AddressDetailBO bo) {
        if (bo == null) {
            return null;
        }
        AgencyUserAddressDTO agencyUserAddressDTO = new AgencyUserAddressDTO();
        UserName userName = new UserName(bo.getName());
        agencyUserAddressDTO.setName(userName.encrypt());
        agencyUserAddressDTO.setNameMask(userName.mask());
        agencyUserAddressDTO.setMobileMask(bo.getMobile());
        String provinceName = StringUtils.isNotBlank(bo.getProvinceName()) ? bo.getProvinceName() : "";
        String cityName = StringUtils.isNotBlank(bo.getCityName()) ? bo.getCityName() : "";
        String countyName = StringUtils.isNotBlank(bo.getCountyName()) ? bo.getCountyName() : "";
        agencyUserAddressDTO.setAddressDistrict(provinceName + cityName + countyName);
        AddressDetail addressDetail = new AddressDetail(bo.getAddressDetail());
        agencyUserAddressDTO.setAddressDetail(addressDetail.encrypt());
        agencyUserAddressDTO.setAddressDetailMask(addressDetail.mask());
        agencyUserAddressDTO.setFullAddress(bo.getFullAddress());
        agencyUserAddressDTO.setFullAddressMask(bo.getFullAddress());
        agencyUserAddressDTO.setAddressId(bo.getAddressId());
        // 新增默认选中
        agencyUserAddressDTO.setSelected(true);
        // 添加之前有校验,能添加说明可以选择
        agencyUserAddressDTO.setDisabled(false);
        return agencyUserAddressDTO;
    }

    /**
     * 转换患者信息
     *
     * @param dto dto
     * @return dto
     */
    private AgencyPatientDTO transferPatient(PatientDto dto) {
        if (dto == null) {
            return null;
        }
        AgencyPatientDTO agencyPatientDTO = new AgencyPatientDTO();
        agencyPatientDTO.setName(dto.getName());
        agencyPatientDTO.setNameMask(UserName.mask(dto.getNameMask()));
        agencyPatientDTO.setCredentialNo(dto.getCredentialNo());
        agencyPatientDTO.setCredentialNoMask(dto.getCredentialNoMask());
        agencyPatientDTO.setGender(dto.getGender());
        agencyPatientDTO.setGenderStr(GenderEnum.getDescOfType(dto.getGender()));
        agencyPatientDTO.setAge(dto.getAge());
        agencyPatientDTO.setAgeStr(dto.getAgeStr());
        agencyPatientDTO.setMobile(dto.getPhone());
        agencyPatientDTO.setMobileMask(dto.getPhoneMask());
        boolean isReal = NumConstant.NUM_3.equals(dto.getVerifyStatus());
        agencyPatientDTO.setIsRealName(isReal);
        agencyPatientDTO.setIsRealNameStr(isReal ? "已实名" : "未实名");
        agencyPatientDTO.setPatientId(dto.getPatientId());
        agencyPatientDTO.setDisabled(dto.isDisabled());
        agencyPatientDTO.setBirthday(dto.getBirthday());
        return agencyPatientDTO;
    }


    /**
     * 校验pin与预约单对应关系，方式越权操作
     *
     * @param pin              pin
     * @param medicalPromiseId id
     */
    private MedicalPromiseDTO checkPinAndMedicalPromise(String pin, Long medicalPromiseId) {
        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromiseId);
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(medicalPromiseRequest);
        if (medicalPromiseDTO == null || !pin.equalsIgnoreCase(medicalPromiseDTO.getUserPin())) {
            throw new SystemException(SystemErrorCode.ILLEGAL_OPERATION);
        }
        return medicalPromiseDTO;
    }

    /**
     * 校验地址
     * @param pin pin
     * @param cmd cmd
     */
    private void checkAddress(String pin, AgentSubmitPromiseCmd cmd) {
        if (StringUtils.isBlank(pin) || cmd == null) {
            return;
        }
        try {
            Long addressId = cmd.getAddressId();
            List<AddressDetailBO> addressDetailBOList = jdhAddressRpc.queryAddressList(pin);
            if (CollUtil.isEmpty(addressDetailBOList)) {
                throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR.getCode(), "地址已被删除，请重新选择"));
            }
            AddressDetailBO addressDetailBO = addressDetailBOList.stream().filter(s -> addressId.equals(s.getAddressId())).findFirst().orElse(null);
            if (addressDetailBO == null) {
                throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR.getCode(), "地址已被删除，请重新选择"));
            }
            // 解密地址
            AddressDetail addressDetail = new AddressDetail(cmd.getAddressDetail());
            String queryDistrict = buildDistrict(addressDetailBO.getProvinceName(), addressDetailBO.getCityName(), addressDetailBO.getCountyName());
            if (!(queryDistrict.equalsIgnoreCase(cmd.getAddressDistrict()) && addressDetailBO.getAddressDetail().equalsIgnoreCase(addressDetail.decrypt()))) {
                throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR.getCode(), "地址已被修改，请重新选择"));
            }
        } catch (Exception e) {
            throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR.getCode(), "地址已发生变更，请重新选择"));
        }
    }

    /**
     * 校验地址
     * @param cmd cmd
     */
    private void checkPatient(AgentSubmitPromiseCmd cmd) {
        try {
            UserName originName = new UserName(cmd.getPatientName());
            originName.decrypt();
            PatientListRequest request = new PatientListRequest();
            request.setUserPin(cmd.getUserPin());
            request.setPatientId(cmd.getPatientId());
            PatientDto patientDto = patientApplication.getPatient(request);
            if (patientDto == null) {
                throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_USER_CHANGE_ERROR.getCode(), "预约人已被删除，请重新选择"));
            }
            UserName queryUserName = new UserName(patientDto.getName());
            queryUserName.decrypt();
            if (!originName.getName().equalsIgnoreCase(queryUserName.getName())) {
                throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_USER_CHANGE_ERROR.getCode(), "预约人已被修改，请重新选择"));
            }
        } catch (Exception e) {
            throw new BusinessException(new DynamicErrorCode(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_USER_CHANGE_ERROR.getCode(), "预约人已变更，请重新选择"));
        }
    }

}
