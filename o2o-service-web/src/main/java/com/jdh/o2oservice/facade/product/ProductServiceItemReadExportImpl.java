package com.jdh.o2oservice.facade.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angel.convert.StationApplicationConverter;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.support.service.BizCategoryApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.enums.BizCategorySceneEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationSkuRel;
import com.jdh.o2oservice.core.domain.product.bo.UserAddressDetailBO;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.service.ProductDomainService;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.StationGeoForManQuery;
import com.jdh.o2oservice.export.product.ProductServiceItemReadExport;
import com.jdh.o2oservice.export.product.dto.BizCategoryDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.JdhSkuItemRelDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("productServiceItemReadExport")
@Slf4j
public class ProductServiceItemReadExportImpl implements ProductServiceItemReadExport {

    /**
     * 商品接口信息
     */
    @Resource
    ProductApplication productApplication;

    @Resource
    private ProductDomainService productDomainService;

    @Resource
    private StationApplication stationApplication;

    /**
     * 业务分类接口信息
     */
    @Resource
    private BizCategoryApplication bizCategoryApplication;

    @Resource
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * 根据标准项目集合查询多个商品信息
     *
     * @param jdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest
     * @return maps
     */
    @Override
    @LogAndAlarm(jKey = "ProductServiceItemReadExportImpl.queryMultiSkuByServiceItem")
    public Response<Map<Long, List<JdhSkuDto>>> queryMultiSkuByServiceItem(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest) {
        return this.queryMultiSkuByServiceItemAndAddress(jdhSkuServiceItemRelRequest);
    }

    @LogAndAlarm(jKey =  "com.jdh.o2oservice.facade.product.ProductServiceItemReadExportImpl.queryMultiSkuByServiceItemAndAddress")
    @Override
    public Response<Map<Long, List<JdhSkuDto>>> queryMultiSkuByServiceItemAndAddress(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest) {
        Map<Long, List<JdhSkuDto>> multiSku = productApplication.queryMultiSkuByServiceItem(jdhSkuServiceItemRelRequest);
        if(multiSku == null || multiSku.size() == 0 ){
            return ResponseUtil.buildSuccResponse(multiSku);
        }
        if((jdhSkuServiceItemRelRequest.getAddressId() == null || jdhSkuServiceItemRelRequest.getUserPin() == null) && jdhSkuServiceItemRelRequest.getFullAddress() == null ){
            return ResponseUtil.buildSuccResponse(multiSku);
        }
        // addressId场景下补全fullAddress
        if(jdhSkuServiceItemRelRequest.getAddressId() != null && jdhSkuServiceItemRelRequest.getUserPin() != null && StringUtils.isBlank(jdhSkuServiceItemRelRequest.getFullAddress())){
            UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(jdhSkuServiceItemRelRequest.getUserPin(), jdhSkuServiceItemRelRequest.getAddressId());
            if(userAddressDetail != null){
                jdhSkuServiceItemRelRequest.setFullAddress(userAddressDetail.getFullAddress());
            }
        }

        StationGeoForManQuery stationGeoForManQuery = new StationGeoForManQuery();
        stationGeoForManQuery.setSkuNos(multiSku.values().stream().filter(s -> s.size() > 0).map(item -> item.get(0)).map(JdhSkuDto::getSkuId).collect(Collectors.toSet()));
        if(stationGeoForManQuery.getSkuNos().size() == 0){
            return ResponseUtil.buildSuccResponse(multiSku);
        }
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setAddressId(String.valueOf(jdhSkuServiceItemRelRequest.getAddressId() == null ? stationGeoForManQuery.getSkuNos().stream().findFirst().get() : jdhSkuServiceItemRelRequest.getAddressId()));
        addressDetail.setFullAddress(jdhSkuServiceItemRelRequest.getFullAddress());
        List<AddressDetail> addressDetails = Arrays.asList(addressDetail);
        stationGeoForManQuery.setAddressDetailList(addressDetails);
        List<JdhStationDto> jdhStationDtos = new ArrayList<>();
        try {
            jdhStationDtos = stationApplication.queryJdhStationGeoForMan(stationGeoForManQuery);
            log.info("ProductServiceItemReadExportImpl.queryMultiSkuByServiceItemAndAddress, param:{},获取服务站地址={}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(jdhStationDtos));
        } catch (Exception e) {
            log.error("ProductServiceItemReadExportImpl.queryMultiSkuByServiceItemAndAddress, 获取服务站错误={}", JSON.toJSONString(stationGeoForManQuery), e);
        }
        if(CollUtil.isEmpty(jdhStationDtos)){
            multiSku.forEach((k1,k2) -> {
                k2.forEach(s -> {
                    s.setServiceType(null);
                    s.setServiceTypeName(null);
                });
            });
        }
        return ResponseUtil.buildSuccResponse(multiSku);
    }

    /**
     * 分页查询商品项目关联关系
     *
     * @param request request
     * @return page
     */
    @Override
    @LogAndAlarm(jKey = "ProductServiceItemReadExportImpl.queryPageSkuServiceItemRel")
    public Response<PageDto<JdhSkuItemRelDto>> queryPageSkuServiceItemRel(JdhSkuServiceItemRelPageRequest request) {
        return ResponseUtil.buildSuccResponse(productApplication.queryPageJdhSkuItemRel(request));
    }

    /**
     * 查询指标一级类目
     * 查询表：jdh_common_biz_category
     * @param bizCategoryRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "ProductServiceItemReadExportImpl.queryCommonIndicatorCategory")
    public Response<List<BizCategoryDTO>> queryCommonIndicatorCategory(BizCategoryRequest bizCategoryRequest) {
        if(bizCategoryRequest == null) {
            bizCategoryRequest = new BizCategoryRequest();
        }
        bizCategoryRequest.setBizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode());
        if(bizCategoryRequest.getCategoryLevel() == null && bizCategoryRequest.getParentCategoryId() == null) {
            bizCategoryRequest.setCategoryLevel(1);
        }
        return ResponseUtil.buildSuccResponse(bizCategoryApplication.queryCategoryList(bizCategoryRequest));
    }

    /**
     * 根据查询条件分页获取服务项列表。
     *
     * @param serviceItemQuery 查询条件对象，包含查询参数。
     * @return 分页后的服务项列表。
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<ServiceItemDto>> queryServiceItemPage(ServiceItemQuery serviceItemQuery) {
        return Response.buildSuccessResult(null);
    }

    /**
     * 批量查询指定地址+项目的SKU详细信息
     *      1、批量单次最多20个项目
     *      2、通过地址拿到可用的服务站列表
     *      3、通过2中的可用服务站列表+项目拿到可用sku列表
     * @param jdhSkuServiceItemRelRequest
     * @return
     */
    @LogAndAlarm
    @Override
    public Response<Map<Long, List<JdhSkuDto>>> batchQueryMultiSkuByServiceItemAndAddress(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest) {
        if(Objects.isNull(jdhSkuServiceItemRelRequest)){
            return ResponseUtil.buildErrResponse(ProductErrorCode.PARAM_NULL_ERROR);
        }
        if(CollectionUtil.isNotEmpty(jdhSkuServiceItemRelRequest.getServiceItemIds())){
            if(jdhSkuServiceItemRelRequest.getServiceItemIds().size() > 50){
                return ResponseUtil.buildErrResponse(ProductErrorCode.BATCH_QUERY_SERVICE_ITEM_MAX_LIMITED);
            }
        }
//        if(jdhSkuServiceItemRelRequest.getAddressId() == null && jdhSkuServiceItemRelRequest.getFullAddress() == null && jdhSkuServiceItemRelRequest.getUserPin() == null){
//            return ResponseUtil.buildErrResponse(ProductErrorCode.BATCH_QUERY_SERVICE_ITEM_ADDREDD_NULL);
//        }
        jdhSkuServiceItemRelRequest.setMergeSkuByServiceType(false);
        Map<Long, List<JdhSkuDto>> multiSku = productApplication.queryMultiSkuByServiceItem(jdhSkuServiceItemRelRequest);
        if(multiSku == null || multiSku.size() == 0 ){
            return ResponseUtil.buildSuccResponse(multiSku);
        }
        if(jdhSkuServiceItemRelRequest.getAddressId() == null && jdhSkuServiceItemRelRequest.getFullAddress() == null && jdhSkuServiceItemRelRequest.getUserPin() == null){
            return ResponseUtil.buildSuccResponse(multiSku);
        }

        StationGeoForManQuery stationGeoForManQuery = new StationGeoForManQuery();
        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setAddressId(String.valueOf(jdhSkuServiceItemRelRequest.getAddressId()));
        addressDetail.setFullAddress(jdhSkuServiceItemRelRequest.getFullAddress());
        List<AddressDetail> addressDetails = Arrays.asList(addressDetail);
        stationGeoForManQuery.setAddressDetailList(addressDetails);
        List<JdhStationDto> jdhStationDtos = stationApplication.queryJdhStationGeoForMan(stationGeoForManQuery);
        log.info("ProductApplicationImpl.batchQueryMultiSkuByServiceItemAndAddress 通过地址信息获取地址信息 param:{}，result:{}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(jdhStationDtos));
        if (CollectionUtils.isEmpty(jdhStationDtos)) {
            return ResponseUtil.buildSuccResponse(multiSku);
        }
        List<JdhStation> jdhStations = StationApplicationConverter.ins.convertToJdhStationList(jdhStationDtos.get(0).getStationDtoList());
        Map<Long, List<JdhStationSkuRel>> skuGroup = stationApplication.filterSkuByStation(jdhStations, multiSku.values().stream().flatMap(Collection::stream).map(JdhSkuDto::getSkuId).collect(Collectors.toSet()));
        Set<Long> avaiableSkuList = skuGroup.keySet();

        //按照规则进行过滤并按serviceType排序
        for (Map.Entry<Long, List<JdhSkuDto>> entry : multiSku.entrySet()) {
            List<JdhSkuDto> filterSkuList = Optional.ofNullable(entry.getValue())
                    .map(List::stream)
                    .orElseGet(Stream::empty)
                    // .filter(skuItem -> avaiableSkuList.contains(skuItem.getSkuId()))
                    // 按serviceType排序，如果serviceType相同则按skuId排序
                    .sorted(Comparator.comparing(JdhSkuDto::getServiceType, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(JdhSkuDto::getSkuId, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            filterSkuList.forEach(s -> {
                if(!avaiableSkuList.contains(s.getSkuId())){
                    s.setServiceType(null);
                    s.setServiceTypeName(null);
                }
            });
            multiSku.put(entry.getKey(), filterSkuList);
        }

        return ResponseUtil.buildSuccResponse(multiSku);
    }

    /**
     * 查询项目信息包含项目组
     *
     * @param serviceItemQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<List<ServiceItemDto>> queryServiceItemListWithQuestion(ServiceItemQuery serviceItemQuery) {
        AssertUtils.nonNull(serviceItemQuery, "参数不能为空");
        AssertUtils.isNotEmpty(serviceItemQuery.getItemIds(), "项目集合不能为空");
        return Response.buildSuccessResult(productServiceItemApplication.queryServiceItemListWithQuestion(serviceItemQuery));
    }


}
