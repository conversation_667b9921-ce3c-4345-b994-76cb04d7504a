package com.jdh.o2oservice.facade.promise;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseTimelineApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.StationGeoForManQuery;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.product.dto.GroupUserAddressDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.AgencyQueryDateRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.OrderUserAddressQuery;
import com.jdh.o2oservice.export.promise.PromiseJsfExport;
import com.jdh.o2oservice.export.promise.cmd.AgentModifyPromiseCmd;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.SubmitPromiseCmd;
import com.jdh.o2oservice.export.promise.dto.CompletePromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.query.CompletePromiseRequest;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import com.jdh.o2oservice.export.trade.dto.AvaiableAppointmentTimeDTO;
import com.jdh.o2oservice.export.trade.query.AvaiableAppointmentTimeParam;
import com.jdh.o2oservice.export.via.dto.ViaCompletePromiseDto;
import com.jdh.o2oservice.export.via.query.ViaCompletePromiseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2024/9/18
 */
@Slf4j
@Service
public class PromiseJsfExportImpl implements PromiseJsfExport {

    @Resource
    private ProductApplication productApplication;
    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    @Resource
    private VoucherApplication voucherApplication;

    @Resource
    private TradeApplication tradeApplication;

    @Resource
    StationApplication stationApplication;

    @Resource
    private PromiseTimelineApplication promiseTimelineApplication;

    @Override
    @LogAndAlarm
    public Response<List<PromiseDto>> createVoucher(CreateVoucherCmd cmd) {
        AssertUtils.hasText(cmd.getUserPin(), BusinessErrorCode.UNKNOWN_USER_LOGIN);
        log.info("PromiseJsfExportImpl.createVoucher before voucherId:{} cmd:{}",
                cmd.getUserPin(), JSON.toJSONString(cmd));
        List<PromiseDto> voucher = voucherApplication.createVoucher(cmd);
        return ResponseUtil.buildSuccResponse(voucher);
    }


    @Override
    @LogAndAlarm
    public Response<Boolean> invalidVoucher(InvalidVoucherCmd cmd) {
        log.info("PromiseJsfExportImpl.invalidVoucher before voucherId:{} cmd:{}",
                cmd.getVoucherId(), JSON.toJSONString(cmd));
        Boolean invalidVoucher = voucherApplication.invalidVoucher(cmd);
        return ResponseUtil.buildSuccResponse(invalidVoucher);
    }

    @Override
    public Response<AvaiableAppointmentTimeDTO> queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param) {
        String userPin = "";
        try {
            if (param == null) {
                return Response.buildErrorResult(BusinessErrorCode.ILLEGAL_ARG_ERROR.getCode(), BusinessErrorCode.ILLEGAL_ARG_ERROR.getDescription());
            }
            userPin = param.getUserPin();
            AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = tradeApplication.queryAvaiableAppointmentTime(param);
            log.info("PromiseJsfExportImpl queryAvaiableAppointmentTime 查询排期列表 userPin:{} param:{} avaiableAppointmentTimeDTO:{}",
                    userPin, JSONObject.toJSONString(param), JSONObject.toJSONString(avaiableAppointmentTimeDTO));
            return Response.buildSuccessResult(avaiableAppointmentTimeDTO);
        } catch (BusinessException e) {
            log.error("PromiseJsfExportImpl queryAvaiableAppointmentTime 查询排期列表 business error userPin:{} param:{} ", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("PromiseJsfExportImpl queryAvaiableAppointmentTime 查询排期列表 unknown error userPin:{} param:{} ", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @Override
    @LogAndAlarm
    public Response<Map<String, GroupUserAddressDTO>> queryListGroupAddress(OrderUserAddressQuery request) {
        GwMapUtil.checkPin(request.getUserPin());
        AssertUtils.isNotEmpty(request.getSkuNoList(), "请选择正确的商品");
        Map<String, GroupUserAddressDTO> result = productApplication.listGroupAddress(request);
        log.info("PromiseJsfExportImpl.queryListGroupAddress 获取地址分堆 pin:{} param:{} result:{}",
                request.getUserPin(), JSON.toJSONString(request), JSON.toJSONString(result));
        return ResponseUtil.buildSuccResponse(result);
    }

    @Override
    @LogAndAlarm
    public Response<Boolean> submit(SubmitPromiseCmd cmd) {
        log.info("PromiseJsfExportImpl.submit before pin:{} cmd:{}",
                cmd.getUserPin(), JSON.toJSONString(cmd));
        AssertUtils.hasText(cmd.getUserPin(), BusinessErrorCode.UNKNOWN_USER_LOGIN);
        Boolean res = promiseApplication.submit(cmd);
        log.info("PromiseJsfExportImpl.submit after pin:{} cmd:{} result:{}",
                cmd.getUserPin(), JSON.toJSONString(cmd), res);
        return ResponseUtil.buildSuccResponse(res);
    }

    @Override
    @LogAndAlarm
    public Response<CompletePromiseDto> queryCompletePromise(CompletePromiseRequest request) {
        CompletePromiseDto completePromiseDto = promiseApplication.queryCompletePromise(request);
        return ResponseUtil.buildSuccResponse(completePromiseDto);
    }

    @Override
    @LogAndAlarm
    public Response<List<CompletePromiseDto>> queryCompletePromiseList(CompletePromiseRequest request) {
        return ResponseUtil.buildSuccResponse(promiseApplication.queryCompletePromiseList(request));
    }

    @Override
    @LogAndAlarm
    public Response<PromiseDto> findPromise(PromiseIdRequest request){
        PromiseDto dto = promiseApplication.findByPromiseId(request);
        return ResponseUtil.buildSuccResponse(dto);

    }


    /**
     * queryPromise
     *
     * @param request 请求
     * @return {@link Response }<{@link PromiseDto }>
     */
    @Override
    @LogAndAlarm
    public Response<PromiseDto> queryPromise(PromiseIdRequest request) {
        PromiseDto dto = promiseApplication.findByPromiseId(request);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 查询预约时间
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.promise.PromiseJsfExportImpl.queryAvailableTime")
    public Response<List<AgencyAppointDateDto>> queryAvailableTime(AgencyQueryDateRequest request) {
        return ResponseUtil.buildSuccResponse(promiseApplication.queryAvailableTime(request));
    }

    @Override
    public Response<List<JdhStationDto>> queryStationDtoList(AgencyQueryDateRequest request) {
        List<AddressDetail> addressDetailList = new ArrayList<>();
        AddressDetail addressDetail = new AddressDetail();
        if(Objects.isNull(request.getAddressId())){
            addressDetail.setAddressId(String.valueOf(System.currentTimeMillis()));
        } else{
            addressDetail.setAddressId(String.valueOf(request.getAddressId()));
        }
        addressDetail.setFullAddress(request.getFullAddress());
        addressDetailList.add(addressDetail);

        StationGeoForManQuery stationGeoForManQuery = new StationGeoForManQuery();
        stationGeoForManQuery.setSkuNos(new HashSet<>(request.getSkuIds()));
        stationGeoForManQuery.setAddressDetailList(addressDetailList);

        Map<Long, JdhSkuDto> skuInfoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(request.getSkuIds().stream().collect(Collectors.toSet())).build());
        Integer serviceType = skuInfoMap.values().stream().findFirst().get().getServiceType();
        if(Integer.valueOf("1").equals(serviceType)){
            stationGeoForManQuery.setAngelType(AngelTypeEnum.DELIVERY.getType());
        } else if(Integer.valueOf("2").equals(serviceType)){
            stationGeoForManQuery.setAngelType(AngelTypeEnum.NURSE.getType());
        } else if(Integer.valueOf("3").equals(serviceType)){
            stationGeoForManQuery.setAngelType(AngelTypeEnum.NURSE.getType());
        } else if(Integer.valueOf("5").equals(serviceType)){
            stationGeoForManQuery.setAngelType(AngelTypeEnum.NURSE.getType());
        } else if(Integer.valueOf("6").equals(serviceType)){
            stationGeoForManQuery.setAngelType(AngelTypeEnum.NURSE.getType());
        }
        //返回的数据表示地址是高亮
        List<JdhStationDto> stationDtoList = stationApplication.queryJdhStationGeoForMan(stationGeoForManQuery);
        log.info("PromiseJsfExportImpl queryStationDtoList stationGeoForManQuery={}, stationDtoList={}", JSON.toJSONString(stationGeoForManQuery), JSON.toJSONString(stationDtoList));
        return ResponseUtil.buildSuccResponse(stationDtoList);
    }

    /**
     * 修改预约时间
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.promise.PromiseJsfExportImpl.modifyAppointmentTime")
    public Response<Boolean> modifyAppointmentTime(AgentModifyPromiseCmd cmd) {
        return ResponseUtil.buildSuccResponse(promiseApplication.modifyAppointmentTime(cmd));
    }

    /**
     * 查询履约单列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.promise.PromiseJsfExportImpl.findByPromiseList")
    public Response<List<PromiseDto>> findByPromiseList(PromiseListRequest request) {
        return ResponseUtil.buildSuccResponse(promiseApplication.findByPromiseList(request));
    }

    @Override
    @LogAndAlarm
    public Response<PromiseTimelineDto> queryPromiseTimeline(QueryPromiseTimelineRequest request) {
        return ResponseUtil.buildSuccResponse(promiseTimelineApplication.queryPromiseTimeline(request));
    }

}
