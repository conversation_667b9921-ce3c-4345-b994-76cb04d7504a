package com.jdh.o2oservice.facade.trade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.i18n.order.dict.FieldKeyEnum;
import com.jd.order.sdk.domain.param.ClientInfo;
import com.jd.order.sdk.domain.param.OrderInfoQueryVoParam;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.convert.VoucherApplicationConverter;
import com.jdh.o2oservice.application.trade.util.OrderEntityBoUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.context.MedicalPromiseCreateContext;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseAppointmentPatient;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseServiceItem;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseSku;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseSkuServiceItem;
import com.jdh.o2oservice.core.domain.promise.context.CreatePromiseContext;
import com.jdh.o2oservice.core.domain.promise.enums.JdhVoucherSourceTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.ServiceTagEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import com.jdh.o2oservice.core.domain.trade.enums.OrderListServiceStatusEnum;
import com.jdh.o2oservice.core.domain.trade.enums.OrderListTabEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.factory.JdOrderFactory;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedPromiseCmdAppointmentPatient;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedPromiseCmdServiceItem;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCreateCmd;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.VoucherExtend;
import com.jdh.o2oservice.export.promise.cmd.VoucherItem;
import com.jdh.o2oservice.export.trade.TradeDevJsfExport;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreateCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreatePromiseCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreatePromisePatientCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockModifyPromiseCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("tradeDevJsfExport")
public class TradeDevJsfExportImpl implements TradeDevJsfExport {

    /**
     * 订单rpc
     */
    @Resource
    OrderInfoRpc orderInfoRpc;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;

    /**
     * jdhVoucherRepository
     */
    @Resource
    private VoucherRepository jdhVoucherRepository;

    /**
     * 商品
     */
    @Resource
    private ProductApplication productApplication;
    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;

    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;
    /** */
    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     * 生成订单数据
     *
     * @return
     */
    @Override
    public Response<Boolean> mockGeneralOrder(OrderMockCreateCmd cmd) {
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 生成订单数据
     *
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> mockGeneralPromise(OrderMockCreatePromiseCmd cmd) {
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 生成订单数据
     *
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> mockModifyPromise(OrderMockModifyPromiseCmd cmd) {
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 作废订单下数据
     *
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> mockInvalidOrderService(OrderMockModifyPromiseCmd cmd) {
        return null;
    }

}
