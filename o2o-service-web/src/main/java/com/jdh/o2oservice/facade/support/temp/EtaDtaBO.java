package com.jdh.o2oservice.facade.support.temp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/25 13:46
 */
@Data
public class EtaDtaBO {
    /**
     * 护士ID
     */
    @ExcelProperty(value = "检测项目ID", index = 0)
    private String service_item_id;

    /**
     * 服务者技能code(全局唯一)
     */
    @ExcelProperty(value = "实验室ID", index = 1)
    private String station_id;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "出报告时间", index = 2)
    private String report_time;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "是否有采样盒", index = 3)
    private String pre_sample_flag;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "预约开始时间", index = 4)
    private String appointment_start_time;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "预约结束时间", index = 5)
    private String appointment_end_time;


    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "实验室经度", index = 6)
    private String station_lat;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "实验室维度", index = 7)
    private String station_lng;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "运单规划距离", index = 8)
    private String total_distance;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "预约类型（立即预约）", index = 9)
    private String immediately;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "用户地址", index = 10)
    private String store_addr;

    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "用户地址经度", index = 11)
    private String user_lat;


    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "用户地址维度", index = 12)
    private String user_lng;
    /**
     * 服务者技能关联标识 0未开启 1已开启
     */
    @ExcelProperty(value = "用户地址到实验室路径规划", index = 13)
    private String distance;
}
