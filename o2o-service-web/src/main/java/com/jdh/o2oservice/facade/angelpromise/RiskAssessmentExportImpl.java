package com.jdh.o2oservice.facade.angelpromise;

import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.riskassessment.RiskAssessmentExport;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssReturnVisitCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssessmentCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssessmentResultCmd;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentDetailManDTO;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentPageDTO;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentDetailRequest;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/3
 */
@Service
@Slf4j
public class RiskAssessmentExportImpl implements RiskAssessmentExport {

    /**
     * 自动注入的RiskAssessmentApplication对象，用于在RiskAssessmentExportImpl中调用相关方法。
     */
    @Autowired
    private RiskAssessmentApplication riskAssessmentApplication;


    /**
     * 创建风险评估。
     *
     * @param riskAssessmentCmd 风险评估命令对象，包含创建风险评估所需的信息。
     * @return 操作结果，true表示成功，false表示失败。
     */
    @Override
    public Response<Boolean> createRiskAssessment(RiskAssessmentCmd riskAssessmentCmd) {
        Boolean res = riskAssessmentApplication.createRiskAssessment(riskAssessmentCmd);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 保存风险评估结果
     *
     * @param resultCmd 需要更新的风险评估细节命令对象列表。
     * @return 更新操作是否成功。
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> saveRiskAssResult(RiskAssessmentResultCmd resultCmd) {
        Boolean res = riskAssessmentApplication.saveRiskAssResult(resultCmd);
        return ResponseUtil.buildSuccResponse(res);

    }

    /**
     * 分页查询风险评估信息。
     *
     * @param request 分页查询请求参数，包括页码、每页记录数等。
     * @return 分页查询结果，包含总记录数、当前页码、每页记录数和数据列表。
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<RiskAssessmentPageDTO>> queryRiskAssessmentPage(RiskAssessmentPageRequest request) {
        PageDto<RiskAssessmentPageDTO> res = riskAssessmentApplication.queryRiskAssessmentPage(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 保存风险评估回访信息
     *
     * @param riskAssReturnVisitCmd 风险评估回访命令对象
     * @return 是否保存成功
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> saveReturnVisit(RiskAssReturnVisitCmd riskAssReturnVisitCmd) {
        Boolean res = riskAssessmentApplication.saveReturnVisit(riskAssReturnVisitCmd);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 查询风险评估详情
     *
     * @param request 风险评估详情请求对象
     * @return 风险评估详情DTO对象
     */
    @Override
    @LogAndAlarm
    public Response<RiskAssessmentDetailManDTO> queryRiskAssDetailForMan(RiskAssessmentDetailRequest request) {
        RiskAssessmentDetailManDTO res = riskAssessmentApplication.queryRiskAssDetailForMan(request);
        return ResponseUtil.buildSuccResponse(res);
    }
}
