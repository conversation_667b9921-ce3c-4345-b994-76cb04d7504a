package com.jdh.o2oservice.facade.settlement;

import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.settlement.AngelSettleReadExport;
import com.jdh.o2oservice.export.settlement.cmd.AngelCashOutCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelBankDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementMoneyDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:48 下午
 * @Description:
 */
@Service("angelSettleReadExport")
@Slf4j
public class AngelSettleReadExportImpl implements AngelSettleReadExport {

    @Autowired
    private JdServiceSettleReadApplication serviceSettleReadApplication;

    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.settle.AngelSettleReadExportImpl.querySettlementPage")
    public Response<PageDto<AngelSettlementDto>> querySettlementPage(Map<String, String> param) {
        AngelSettleQuery queryContext = GwMapUtil.convertToParam(param, AngelSettleQuery.class);
        PageDto<AngelSettlementDto> res = serviceSettleReadApplication.querySettlementPage(queryContext);
        return Response.buildSuccessResult(res);
    }

    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.settle.AngelSettleReadExportImpl.querySettlementDetailList")
    public Response<AngelSettlementDto> querySettlementDetailList(Map<String, String> param) {
        AngelSettleQuery queryContext = GwMapUtil.convertToParam(param, AngelSettleQuery.class);
        AngelSettlementDto res = serviceSettleReadApplication.querySettlementDetailList(queryContext);
        return Response.buildSuccessResult(res);
    }

    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.settle.AngelSettleReadExportImpl.queryAngelSettlementMoneyDto")
    public Response<AngelSettlementMoneyDto> queryAngelSettlementMoneyDto(Map<String, String> param) {
        AngelSettleQuery queryContext = GwMapUtil.convertToParam(param, AngelSettleQuery.class);
        AngelSettlementMoneyDto res = serviceSettleReadApplication.queryAngelSettlementMoneyDto(queryContext);
        return Response.buildSuccessResult(res);
    }


    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.settle.AngelSettleReadExportImpl.querySettleTotal")
    public Response<Map<String, BigDecimal>> querySettleTotal(Map<String, String> param) {
        AngelSettleQuery queryContext = GwMapUtil.convertToParam(param, AngelSettleQuery.class);
        Map<String, BigDecimal> res = serviceSettleReadApplication.querySettleTotal(queryContext);
        return Response.buildSuccessResult(res);
    }

    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.settle.AngelSettleReadExportImpl.queryBindBank")
    public Response<AngelBankDto> queryBindBank(Map<String, String> param) {
        AngelSettleQuery queryContext = GwMapUtil.convertToParam(param, AngelSettleQuery.class);
        AngelBankDto res = serviceSettleReadApplication.queryBindBank(queryContext);
        return Response.buildSuccessResult(res);
    }


    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.settle.AngelSettleReadExportImpl.submitCashOut")
    public Response<Long> submitCashOut(Map<String, String> param) {
        AngelCashOutCmd angelCashOutCmd = GwMapUtil.convertToParam(param, AngelCashOutCmd.class);
        Long res = serviceSettleReadApplication.submitCashOut(angelCashOutCmd);
        return Response.buildSuccessResult(res);
    }
}
