package com.jdh.o2oservice.facade.provider;

import com.jdh.o2oservice.application.provider.service.ProviderQueryApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.provider.service.ProviderStoreDomainService;
import com.jdh.o2oservice.export.provider.ProviderStoreJsfExport;
import com.jdh.o2oservice.export.provider.cmd.JdhStoreTransferStationAddCmd;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StationCompositeMedicalPromiseDTO;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.PageCompositeMedicalPromiseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/20
 */
@Component
@Slf4j
public class ProviderStoreJsfExportImpl implements ProviderStoreJsfExport {

    /**
     * 自动注入的ProviderStoreApplication实例，用于在ProviderStoreJsfExportImpl中调用相关方法。
     */
    @Resource
    private ProviderStoreApplication providerStoreApplication;
    @Resource
    private ProviderQueryApplication providerQueryApplication;

    /**
     * 查询门店服务
     */
    @Resource
    ProviderStoreDomainService providerStoreDomainService;

    /**
     * 分页查询站点服务项目关联信息。
     *
     * @param request 分页查询请求对象，包含查询条件和分页信息。
     * @return 分页查询结果，包含当前页数据和分页信息。
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<JdhStationServiceItemRelDto>> pageStationServiceItemRel(JdhStationServiceItemRelRequest request) {
        PageDto<JdhStationServiceItemRelDto> res = providerStoreApplication.queryStationServiceItemRelPage(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 实验室分页查询检测单聚合数据
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<StationCompositeMedicalPromiseDTO>> pageCompositeMedicalPromise(PageCompositeMedicalPromiseRequest request) {
        PageDto<StationCompositeMedicalPromiseDTO> page = providerQueryApplication.pageCompositeMedicalPromise(request);
        return ResponseUtil.buildSuccResponse(page);
    }

    /**
     * 添加默认接驳点
     *
     * @param jdhStoreTransferStationAddCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> addDefaultTransformStation(JdhStoreTransferStationAddCmd jdhStoreTransferStationAddCmd) {
        Boolean ret = providerStoreDomainService.addDefaultTransformStation(jdhStoreTransferStationAddCmd);
        return ResponseUtil.buildSuccResponse(ret);
    }
}
