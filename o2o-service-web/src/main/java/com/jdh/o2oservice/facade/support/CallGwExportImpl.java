package com.jdh.o2oservice.facade.support;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.CallGwExport;
import com.jdh.o2oservice.export.support.command.SecurityNumberBindAxbCmd;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.dto.CallRecordingRetrievalDto;
import com.jdh.o2oservice.export.support.dto.SecurityNumberBindAxbResultDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import com.jdh.o2oservice.export.support.query.SyncAngelPhoneRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description 外呼服务
 * @Date 2024/12/20 下午2:44
 * <AUTHOR>
 **/
@Service
@Slf4j
public class CallGwExportImpl implements CallGwExport {

    @Resource
    private CallRecordApplication callRecordApplication;

    /**
     * 虚拟号绑定
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallGwExportImpl.bindAxb")
    public Response<SecurityNumberBindAxbResultDto> bindAxb(Map<String, String> param) {
        SecurityNumberBindAxbCmd cmd = GwMapUtil.convertToParam(param, SecurityNumberBindAxbCmd.class);
        return ResponseUtil.buildSuccResponse(callRecordApplication.bindAxb(cmd));
    }

    /**
     * 录音调取
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallGwExportImpl.callRecordingRetrieval")
    public Response<Boolean> callRecordingRetrieval(Map<String, String> param) {
        CallRecordingRetrievalDto callRecordingRetrievalDto = GwMapUtil.convertToParam(param, CallRecordingRetrievalDto.class);
        return ResponseUtil.buildSuccResponse(callRecordApplication.callRecordingRetrieval(callRecordingRetrievalDto));
    }

    /**
     * 外呼记录列表
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallGwExportImpl.queryCallRecordList")
    public Response<List<CallRecordDto>> queryCallRecordList(Map<String, String> param) {
        QueryCallRecordRequest queryCallRecordRequest = GwMapUtil.convertToParam(param, QueryCallRecordRequest.class);
        return ResponseUtil.buildSuccResponse(callRecordApplication.queryCallRecordList(queryCallRecordRequest));
    }

    /**
     * 查询外呼url
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallGwExportImpl.queryCallRecordUrl")
    public Response<String> queryCallRecordUrl(Map<String, String> param) {
        QueryCallRecordRequest queryCallRecordRequest = GwMapUtil.convertToParam(param, QueryCallRecordRequest.class);
        return ResponseUtil.buildSuccResponse(callRecordApplication.queryCallRecordUrl(queryCallRecordRequest));
    }


    /**
     * 同步护士手机号
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.CallGwExportImpl.syncAngelPhone")
    public Response<Boolean> syncAngelPhone(Map<String, String> param) {
        SyncAngelPhoneRequest syncAngelPhoneRequest = GwMapUtil.convertToParam(param, SyncAngelPhoneRequest.class);
        return ResponseUtil.buildSuccResponse(callRecordApplication.syncAngelPhone(syncAngelPhoneRequest));
    }
}
