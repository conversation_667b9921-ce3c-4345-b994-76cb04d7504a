package com.jdh.o2oservice.facade.support;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.enums.BizSceneActionKeyEnum;
import com.jdh.o2oservice.common.enums.BizSceneKeyEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseLogCmd;
import com.jdh.o2oservice.export.support.OperationLogJsfExport;
import com.jdh.o2oservice.export.support.command.OperationLogCmd;
import com.jdh.o2oservice.export.support.dto.MedicalPromiseOperateLogDTO;
import com.jdh.o2oservice.export.support.dto.OperationLogDto;
import com.jdh.o2oservice.export.support.query.MedicalPromiseOpeLogRequest;
import com.jdh.o2oservice.export.support.query.OperationLogPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 费项配置 网关color接口
 *
 * @author: wangyu1387
 * @date: 2024/8/14 4:46 下午
 * @version: 1.0
 */
@Service
@Slf4j
public class OperationLogJsfExportImpl implements OperationLogJsfExport {

    @Resource
    private OperationLogApplication operationLogApplication;

    /**
     * 保存操作日志
     *
     * @param operationLogCmd
     * @return
     */
    @Override
    @UserPinCheck
    public Response<Boolean> saveLogs(List<OperationLogCmd> operationLogCmd) {
        Boolean ret = operationLogApplication.batchInsertAsyncToLocalDB(operationLogCmd);
        return ResponseUtil.buildSuccResponse(ret);
    }

    /**
     * 查询医疗承诺操作日志。
     *
     * @param request 医疗承诺操作日志请求对象。
     * @return 包含 MedicalPromiseOperateLogDTO 列表的 Response 对象。
     */
    @Override
    @LogAndAlarm
    public Response<List<MedicalPromiseOperateLogDTO>> queryMedicalPromiseLog(MedicalPromiseOpeLogRequest request) {
        OperationLogPageRequest operationLogPageRequest = new OperationLogPageRequest();
        operationLogPageRequest.setBizUnionId(String.valueOf(request.getMedicalPromiseId()));
        operationLogPageRequest.setBizSceneKey(BizSceneKeyEnum.MEDICAL_PROMISE_STATION_INTERACTION.getBizSceneKey());
        operationLogPageRequest.setPageNum(request.getPageNum());
        operationLogPageRequest.setPageSize(request.getPageSize());
        List<OperationLogDto> operationLogDtos = operationLogApplication.queryOperationLogList(operationLogPageRequest);
        log.info("OperationLogJsfExportImpl->queryMedicalPromiseLog->operationLogDtos:{}", JsonUtil.toJSONString(operationLogDtos));
        if (CollectionUtil.isNotEmpty(operationLogDtos)){
            List<MedicalPromiseOperateLogDTO> res = Lists.newArrayList();

            for (OperationLogDto operationLogDto : operationLogDtos) {
                MedicalPromiseLogCmd medicalPromiseLogCmd = JsonUtil.parseObject(operationLogDto.getParam(), MedicalPromiseLogCmd.class);
                MedicalPromiseOperateLogDTO medicalPromiseOperateLogDTO = new MedicalPromiseOperateLogDTO();
                medicalPromiseOperateLogDTO.setMedicalPromiseId(medicalPromiseLogCmd.getMedicalPromiseId());
                medicalPromiseOperateLogDTO.setSpecimenCode(medicalPromiseLogCmd.getSpecimenCode());
                medicalPromiseOperateLogDTO.setFlowCode(medicalPromiseLogCmd.getFlowCode());
                medicalPromiseOperateLogDTO.setOperateType(medicalPromiseLogCmd.getOperateType());
                medicalPromiseOperateLogDTO.setExceptionMsg(medicalPromiseLogCmd.getExceptionMsg());
                medicalPromiseOperateLogDTO.setDealType(medicalPromiseLogCmd.getDealType());
                medicalPromiseOperateLogDTO.setDealTypeDesc(medicalPromiseLogCmd.getDealTypeDesc());
                medicalPromiseOperateLogDTO.setOperatePin(medicalPromiseLogCmd.getOperatePin());
                Date operateTime = Objects.nonNull(medicalPromiseLogCmd.getOperateTime()) ? medicalPromiseLogCmd.getOperateTime() : operationLogDto.getOperateTime();
                medicalPromiseOperateLogDTO.setOperateTime(operateTime);
                medicalPromiseOperateLogDTO.setStationId(medicalPromiseLogCmd.getStationId());
                medicalPromiseOperateLogDTO.setOperateTypeDesc(BizSceneActionKeyEnum.getDescByKey(medicalPromiseLogCmd.getOperateType()));
                res.add(medicalPromiseOperateLogDTO);
            }

            if (StringUtils.isNotBlank(request.getStationId()) && CollectionUtil.isNotEmpty(res)){
                res = res.stream().filter(p -> StringUtils.equals(request.getStationId(), p.getStationId())).collect(Collectors.toList());
            }

            if (StringUtils.isNotBlank(request.getOperateType()) && CollectionUtil.isNotEmpty(res)){
                res = res.stream().filter(p -> StringUtils.equals(request.getOperateType(), p.getOperateType())).collect(Collectors.toList());
            }

            if (StringUtils.isNotBlank(request.getFlowCode()) && CollectionUtil.isNotEmpty(res)){
                res = res.stream().filter(p -> StringUtils.equals(request.getFlowCode(), p.getFlowCode())).collect(Collectors.toList());
            }


            if (StringUtils.isNotBlank(request.getSpecimenCode()) && CollectionUtil.isNotEmpty(res)){
                res = res.stream().filter(p -> StringUtils.equals(request.getSpecimenCode(), p.getSpecimenCode())).collect(Collectors.toList());
            }


            if (CollectionUtil.isNotEmpty(res)){
                return ResponseUtil.buildSuccResponse(res.stream().sorted(Comparator.comparing(MedicalPromiseOperateLogDTO::getOperateTime).reversed()).collect(Collectors.toList()));
            }

        }

        return null;
    }

}
