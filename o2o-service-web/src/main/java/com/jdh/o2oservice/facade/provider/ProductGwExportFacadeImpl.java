package com.jdh.o2oservice.facade.provider;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.support.service.AddressApplication;
import com.jdh.o2oservice.base.annotation.AbTest;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.enums.ProductDetailBottomBannerEnum;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.ProductGwExport;
import com.jdh.o2oservice.export.product.cmd.ApplyOpenServiceRecordCmd;
import com.jdh.o2oservice.export.product.cmd.BatchGetCouponCmd;
import com.jdh.o2oservice.export.product.cmd.GetCouponCmd;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.*;
import com.jdh.o2oservice.export.support.query.JdhUserAddressHistoryDetailRequest;
import com.jdh.o2oservice.export.support.query.JdhUserAddressHistoryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName ProductGwExportFacadeImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 11:33
 **/
@Service
@Slf4j
@EnableAsync
public class ProductGwExportFacadeImpl implements ProductGwExport {

    /**
     * 商品
     */
    @Autowired
    private ProductApplication productApplication;
    @Autowired
    private AddressApplication addressApplication;


    /**
     * 查询服务套餐列表
     *
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.queryServiceGoodsList")
    public Response<List<ProviderServiceGoodsDto>> queryServiceGoodsList(Map<String, String> param) {
        ProductServiceGoodsListRequest request = GwMapUtil.convertToParam(param, ProductServiceGoodsListRequest.class);
        List<ProviderServiceGoodsDto> res = productApplication.queryServiceGoodsList(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.queryProductDetailFloorBySku")
    @AbTest
    public Response<ProductDetailDTO> queryProductDetailFloorBySku(Map<String, String> param) {
        ProductDetailFloorRequest request = GwMapUtil.convertToParam(param, ProductDetailFloorRequest.class);
//        GwMapUtil.checkPin(request.getUserPin());
        if(Objects.nonNull(request) && StringUtils.isEmpty(request.getEnvType())){
            request.setEnvType(EnvTypeEnum.H5.getCode());
        }
        ProductDetailDTO result = productApplication.queryProductDetailFloorBySku(request);
        log.info("ProductGwExportFacadeImpl.queryProductDetailFloorBySku 商详首页数据 pin:{} param:{} result:{}",
                request.getUserPin(), JSON.toJSONString(request), JSON.toJSONString(result));
        asyncSaveUserAddressHistoryByProudctDetail(request.getUserPin(), result, request.getSkuNo());
        return ResponseUtil.buildSuccResponse(result);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.listGroupAddress")
    public Response<Map<String, GroupUserAddressDTO>> listGroupAddress(Map<String, String> param) {
        OrderUserAddressQuery request = GwMapUtil.convertToParam(param, OrderUserAddressQuery.class);
        GwMapUtil.checkPin(request.getUserPin());
        AssertUtils.isNotEmpty(request.getSkuNoList(), "请选择正确的商品");
        Map<String, GroupUserAddressDTO> result = productApplication.listGroupAddress(request);
        log.info("ProductGwExportFacadeImpl.listGroupAddress 获取地址分堆 pin:{} param:{} result:{}",
                request.getUserPin(), JSON.toJSONString(request), JSON.toJSONString(result));
        asyncSaveUserAddressHistoryByAddressList(request.getUserPin(), result, request.getSkuNoList().stream().collect(Collectors.joining(",")));
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 在地址列表场景异步存储用户地址历史
     * @param paramMap
     * @param skuIds
     */
    @Async
    private void asyncSaveUserAddressHistoryByAddressList(String userPin, Map<String, GroupUserAddressDTO> paramMap, String skuIds){
        try {
            if(MapUtils.isEmpty(paramMap) || Objects.isNull(paramMap.get("disSelect")) || CollectionUtils.isEmpty(paramMap.get("disSelect").getList())){
                return;
            }
            JdhUserAddressHistoryRequest request = JdhUserAddressHistoryRequest.builder().build();
            if(Objects.nonNull(paramMap.get("canSelect")) && CollectionUtils.isNotEmpty(paramMap.get("canSelect").getList())){
                request.setScene(1);
            } else {
                request.setScene(2);
            }
            request.setSkuIds(skuIds);
            request.setUserPin(userPin);
            request.setAddressHistoryDetailRequestList(JSON.parseArray(JSON.toJSONString(paramMap.get("disSelect").getList()), JdhUserAddressHistoryDetailRequest.class));
            addressApplication.saveUserAddressHistory(request);
        }catch (Exception e){
            log.error("ProductGwExportFacadeImpl.asyncSaveUserAddressHistory 异步保存用户历史地址失败，paramMap:{}，skuIds:{} ", JSON.toJSONString(paramMap), skuIds, e);
        }
    }

    /**
     * 在商详场景异步存储用户地址历史
     * @param productDetailDTO
     * @param skuIds
     */
    @Async
    private void asyncSaveUserAddressHistoryByProudctDetail(String userPin, ProductDetailDTO productDetailDTO, String skuIds){
        try {
            if(Objects.isNull(productDetailDTO) || Objects.isNull(productDetailDTO.getProductInfo())){
                return;
            }
            if(Objects.isNull(productDetailDTO.getProductInfo().getBuyLimitType()) || !ProductDetailBottomBannerEnum.NO_TESTING_SERVICE.getBuyLimitType().equals(productDetailDTO.getProductInfo().getBuyLimitType())){
                return;
            }
            if(Objects.isNull(productDetailDTO.getProductInfo().getUserAddress())){
                return;
            }
            JdhUserAddressHistoryRequest request = JdhUserAddressHistoryRequest.builder().build();
            request.setScene(0);
            request.setSkuIds(skuIds);
            request.setUserPin(userPin);
            request.setAddressHistoryDetailRequestList(Arrays.asList(JSON.parseObject(JSON.toJSONString(productDetailDTO.getProductInfo().getUserAddress()), JdhUserAddressHistoryDetailRequest.class)));
            addressApplication.saveUserAddressHistory(request);
        }catch (Exception e){
            log.error("ProductGwExportFacadeImpl.asyncSaveUserAddressHistory 异步保存用户历史地址失败，productDetailDTO:{}，skuIds:{} ", JSON.toJSONString(productDetailDTO), skuIds, e);
        }
    }

    /**
     * 查询商品优惠券列表
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.queryProductCouponList")
    public Response<List<ProductCouponDTO>> queryProductCouponList(Map<String, String> param) {
        ProductCouponRequest request = GwMapUtil.convertToParam(param, ProductCouponRequest.class);
        GwMapUtil.checkPin(request.getUserPin());
        AssertUtils.nonNull(request.getSkuNo(),"请选择正确的商品");
        List<ProductCouponDTO> result = productApplication.queryProductCouponList(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 领取优惠券
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.getCoupon")
    public Response<CouponGetResultDTO> getCoupon(Map<String, String> param) {
        GetCouponCmd cmd = GwMapUtil.convertToParam(param, GetCouponCmd.class);
        GwMapUtil.checkPin(cmd.getUserPin());
        AssertUtils.nonNull(cmd.getRoleId(),"请选择正确的活动id");
        CouponGetResultDTO result = productApplication.getCoupon(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 批量-领取优惠券
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.batchGetCoupon")
    public Response<CouponGetResultDTO> batchGetCoupon(Map<String, String> param) {
        BatchGetCouponCmd cmd = GwMapUtil.convertToParam(param, BatchGetCouponCmd.class);
        GwMapUtil.checkPin(cmd.getUserPin());
        AssertUtils.nonNull(cmd.getSkuNo(),"请选择正确的商品");
        CouponGetResultDTO result = productApplication.batchGetCoupon(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 申请开通服务记录
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.createApplyOpenServiceRecord")
    public Response<ApplyOpenServiceRecordDto> createApplyOpenServiceRecord(Map<String, String> param) {
        ApplyOpenServiceRecordCmd cmd = GwMapUtil.convertToParam(param, ApplyOpenServiceRecordCmd.class);
        return ResponseUtil.buildSuccResponse(productApplication.createApplyOpenServiceRecord(cmd));
    }

    /**
     * 查询可服务区域
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.queryServiceArea")
    public Response<ServiceAreaDto> queryServiceArea(Map<String, String> param) {
        ServiceAreaRequest request = GwMapUtil.convertToParam(param, ServiceAreaRequest.class);
        return ResponseUtil.buildSuccResponse(productApplication.queryServiceArea(request));
    }

    /**
     * 商品评价列表
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.getCommentPageList")
    public Response<EvaluateInfoDTO> getCommentPageList(Map<String, String> param) {
        CommentPageRequest request = GwMapUtil.convertToParam(param, CommentPageRequest.class);
        return ResponseUtil.buildSuccResponse(productApplication.getCommentPageList(request));
    }

    /**
     * 查询商品限购
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.provider.ProductGwExportFacadeImpl.queryProductLimitBuy")
    public Response<List<ProductLimitBuyDTO>> queryProductLimitBuy(Map<String, String> param) {
        ProductLimitBuyRequest request = GwMapUtil.convertToParam(param, ProductLimitBuyRequest.class);
        GwMapUtil.checkPin(request.getUserPin());
        AssertUtils.nonNull(request.getSkuIdList(),"请选择正确的商品");
        List<ProductLimitBuyDTO> result = productApplication.queryProductLimitBuy(request);
        return ResponseUtil.buildSuccResponse(result);
    }
}