package com.jdh.o2oservice.facade.angel;

import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelMainReadApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angel.AngelGwExport;
import com.jdh.o2oservice.export.angel.cmd.AngelUnionIdCmd;
import com.jdh.o2oservice.export.angel.cmd.ImportDepartmentCmd;
import com.jdh.o2oservice.export.angel.cmd.SubmitAngelCmd;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.AngelMainRequest;
import com.jdh.o2oservice.export.angel.dto.JdUnionPageDto;
import com.jdh.o2oservice.export.angel.dto.JdhDepartmentDto;
import com.jdh.o2oservice.export.angel.query.AngelCertificatesRequest;
import com.jdh.o2oservice.export.angel.query.GenerateClickUrlQuery;
import com.jdh.o2oservice.export.angel.query.GetDepartmentRequest;
import com.jdh.o2oservice.export.angel.query.JdUnionGoodsListQuery;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: AngelGwExportFacadeImpl
 * <AUTHOR>
 * @Date 2024/4/18
 * @Version V1.0
 **/
@Service("angelGwExportFacadeImpl")
public class AngelGwExportFacadeImpl implements AngelGwExport {

    @Autowired
    private AngelApplication angelApplication;

    @Resource
    private AngelMainReadApplication angelMainReadApplication;

    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angel.AngelGwExportFacadeImpl.submit")
    public Response<Boolean> submit(Map<String, String> param) {
        SubmitAngelCmd submitAngelCmd = GwMapUtil.convertToParam(param, SubmitAngelCmd.class);
        Boolean res = angelApplication.submitAngel(submitAngelCmd);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * excel导入护士科室信息
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> importDepartmentData(Map<String, String> param) {
        ImportDepartmentCmd importDepartmentCmd = GwMapUtil.convertToParam(param, ImportDepartmentCmd.class);
        angelApplication.importDepartmentData(importDepartmentCmd);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 查询科室信息
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<List<JdhDepartmentDto>> getDepartments(Map<String, String> param) {
        GetDepartmentRequest getDepartmentRequest = GwMapUtil.convertToParam(param, GetDepartmentRequest.class);
        List<JdhDepartmentDto> departments = angelApplication.getDepartments(getDepartmentRequest);
        return ResponseUtil.buildSuccResponse(departments);
    }

    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<JdUnionPageDto> getCpsPage(Map<String, String> param) {
        JdUnionGoodsListQuery request = GwMapUtil.convertToParam(param, JdUnionGoodsListQuery.class);
        JdUnionPageDto unionPageDto = angelApplication.queryGoodsListByCpsPage(request);
        return ResponseUtil.buildSuccResponse(unionPageDto);
    }

    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<String> generateClickUrl(Map<String, String> param) {
        GenerateClickUrlQuery request = GwMapUtil.convertToParam(param, GenerateClickUrlQuery.class);
        String clickUrl = angelApplication.generateClickUrl(request);
        return ResponseUtil.buildSuccResponse(clickUrl);
    }

    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<Boolean> submitAngelUnionId(Map<String, String> param) {
        AngelUnionIdCmd cmd = GwMapUtil.convertToParam(param, AngelUnionIdCmd.class);
        return ResponseUtil.buildSuccResponse(angelApplication.saveAngelUnionId(cmd));
    }

    /**
     * 查询服务者主页
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<AngelMainInfoDto> getAngelMain(Map<String, String> param) {
        AngelMainRequest angelMainRequest = GwMapUtil.convertToParam(param, AngelMainRequest.class);
        return ResponseUtil.buildSuccResponse(angelMainReadApplication.queryAngelMain(angelMainRequest));
    }

    /**
     * 查询护士评价分页数据
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<AngelMainValuationPageDto> getAngelValuationPage(Map<String, String> param) {
        AngelMainRequest angelMainRequest = GwMapUtil.convertToParam(param, AngelMainRequest.class);
        return ResponseUtil.buildSuccResponse(angelMainReadApplication.getAngelValuationPage(angelMainRequest));
    }

    /**
     * 查询护士认证信息
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<AngelAuthenticationDto> getAngelAuthentication(Map<String, String> param) {
        AngelMainRequest angelMainRequest = GwMapUtil.convertToParam(param, AngelMainRequest.class);
        return ResponseUtil.buildSuccResponse(angelMainReadApplication.getAngelAuthentication(angelMainRequest));
    }

    /**
     * 查询护士证书
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<List<AngelCertificateDto>> getAngelCertificates(Map<String, String> param) {
        AngelCertificatesRequest request = GwMapUtil.convertToParam(param, AngelCertificatesRequest.class);
        return ResponseUtil.buildSuccResponse(angelApplication.getAngelCertificates(request));
    }
}
