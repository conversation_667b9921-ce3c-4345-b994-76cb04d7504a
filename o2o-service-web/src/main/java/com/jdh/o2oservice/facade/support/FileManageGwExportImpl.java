package com.jdh.o2oservice.facade.support;

import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseFileBizTypeEnum;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordFlowQuery;
import com.jdh.o2oservice.export.support.FileManageGwExport;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.command.PdfSignatureCmd;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.PdfSignatureResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 文件管理 网关color接口
 * @author: yangxiyu
 * @date: 2024/4/28 4:46 下午
 * @version: 1.0
 */
@Component("fileManageGwExport")
public class FileManageGwExportImpl implements FileManageGwExport {

    /**
     *
     */
    @Resource
    private FileManageApplication fileManageApplication;

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    /**
     *
     * @param params
     * @return
     */
    @Override
    public Response<FilePreSignedUrlDto> generatePutUrl(Map<String, String> params) {
        GeneratePutUrlCommand cmd = GwMapUtil.convertToParam(params, GeneratePutUrlCommand.class);

        FilePreSignedUrlDto dto = fileManageApplication.generatePutUrl(cmd);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 获取文件访问预签名
     * @param params
     * @return
     */
    @Override
    @UserPinCheck
    public Response<List<FilePreSignedUrlDto>> generateGetUrl(Map<String, String> params) {

        GenerateGetUrlCommand cmd = GwMapUtil.convertToParam(params, GenerateGetUrlCommand.class);
        List<FilePreSignedUrlDto> list = fileManageApplication.generateGetUrl(cmd);
        return ResponseUtil.buildSuccResponse(list);
    }

    /**
     * 获取护理单签署文件链接
     * @param params
     * @return
     */
    @Override
    @UserPinCheck
    public Response<List<FilePreSignedUrlDto>> generateGetUrlsByTaskId(Map<String, String> params) {
        List<FilePreSignedUrlDto> list = new ArrayList<>();
        GenerateGetUrlCommand cmd = GwMapUtil.convertToParam(params, GenerateGetUrlCommand.class);
        AngelServiceRecordFlowQuery query = new AngelServiceRecordFlowQuery();
        query.setTaskId(cmd.getTaskId());
        query.setUserPin(cmd.getUserPin());
        Boolean authority = angelServiceRecordApplication.queryAngelTaskAuthorityByTaskId(query);
        if(CollectionUtils.isNotEmpty(cmd.getFileIds()) && authority){
            Date endTime = cmd.getExpireTime();
            if (Objects.isNull(cmd.getExpireTime())) {
                endTime = TimeUtils.localDateTimeToDate(LocalDateTime.now().plusDays(CommonConstant.SEVEN));
            }
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(cmd.getFileIds());
            command.setDomainCode(cmd.getDomainCode());
            command.setIsPublic(cmd.getIsPublic());
            command.setExpireTime(endTime);
            list = fileManageApplication.generateGetUrl(command);
        }
        return ResponseUtil.buildSuccResponse(list);
    }

    /**
     * 运营端：获取护理单签署文件链接
     * @param params
     * @return
     */
    @Override
    public Response<List<FilePreSignedUrlDto>> generateGetUrlsManByTaskId(Map<String, String> params) {
        List<FilePreSignedUrlDto> list = new ArrayList<>();
        GenerateGetUrlCommand cmd = GwMapUtil.convertToParam(params, GenerateGetUrlCommand.class);
        AngelServiceRecordFlowQuery query = new AngelServiceRecordFlowQuery();
        query.setTaskId(cmd.getTaskId());
        query.setUserPin(cmd.getUserPin());
        Boolean authority = angelServiceRecordApplication.queryBindAccountInfoManByPin(query);
        if(CollectionUtils.isNotEmpty(cmd.getFileIds()) && authority){
            Date endTime = cmd.getExpireTime();
            if (Objects.isNull(cmd.getExpireTime())) {
                endTime = TimeUtils.localDateTimeToDate(LocalDateTime.now().plusDays(CommonConstant.SEVEN));
            }
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(cmd.getFileIds());
            command.setDomainCode(cmd.getDomainCode());
            command.setIsPublic(cmd.getIsPublic());
            command.setExpireTime(endTime);
            list = fileManageApplication.generateGetUrl(command);
        }
        return ResponseUtil.buildSuccResponse(list);
    }

    /**
     * PDF追加签名信息
     * @param params
     * @return
     */
    @Override
    @UserPinCheck
    public Response<PdfSignatureResult> pdfSignature(Map<String, String> params) {
        PdfSignatureCmd cmd = GwMapUtil.convertToParam(params, PdfSignatureCmd.class);
        PdfSignatureResult result = fileManageApplication.pdfSignature(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 护理单PDF签名
     * @param params
     * @return
     */
    @Override
    @UserPinCheck
    public Response<List<PdfSignatureResult>> pdfSignatureAndSave(Map<String, String> params) {
        PdfSignatureCmd cmd = GwMapUtil.convertToParam(params, PdfSignatureCmd.class);
        if(StringUtil.isBlank(cmd.getDomainCode())){
            cmd.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
        }
        if(StringUtil.isBlank(cmd.getFileBizType())){
            cmd.setFileBizType(AngelPromiseFileBizTypeEnum.SIGN_SYNTHESIS.getBizType());
        }
        if(cmd.getScale() == null){
            Float[] scale = new Float[2];
            scale[0] = 80f;
            scale[1] = 80f;
            cmd.setScale(scale);
        }
        List<PdfSignatureResult> result = fileManageApplication.pdfSignatureAndSave(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

}
