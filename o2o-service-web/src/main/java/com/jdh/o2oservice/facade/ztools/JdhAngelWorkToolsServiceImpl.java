package com.jdh.o2oservice.facade.ztools;

import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.support.service.AutoBotsApplication;
import com.jdh.o2oservice.application.ztools.AngelWorkToolsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.AlarmLevelEnum;
import com.jdh.o2oservice.base.enums.AlarmPolicyTypeEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsResultBO;
import com.jdh.o2oservice.export.support.query.AutoBotsO2ORequest;
import com.jdh.o2oservice.export.ztools.JdhAngelWorkToolsService;
import com.jdh.o2oservice.export.ztools.cmd.AngelWorkExportFileCmd;
import com.jdh.o2oservice.export.ztools.cmd.FlushShipCmd;
import com.jdh.o2oservice.export.ztools.query.AngelWorkPageRequest;
import com.jdh.o2oservice.export.ztools.dto.FindJimParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @ClassName JdhAngelWorkToolsServiceImpl
 * @Description 工单工具类实现
 * <AUTHOR>
 * @Date 2024/9/7 14:52
 */
@Service("jdhAngelWorkToolsService")
@Slf4j
public class JdhAngelWorkToolsServiceImpl implements JdhAngelWorkToolsService {

    @Resource
    private AngelWorkToolsApplication angelWorkToolsApplication;
    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private Cluster jimClient;

    @Autowired
    private AutoBotsApplication autoBotsApplication;

    /**
     * 工单导出文件
     *
     * @param angelWorkExportFileCmd
     * @return
     */
    @Override
    public Response<Boolean> exportFile(AngelWorkExportFileCmd angelWorkExportFileCmd) {
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.exportFile(angelWorkExportFileCmd));
    }

    /**
     * 执行groovy脚本语言
     *
     * @param scriptName
     * @return
     */
    @Override
    public Response<Boolean> executeGroovyScript(String scriptName) {
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.executeGroovyScript(scriptName));
    }

    /**
     * @param flushShipCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> flushShipAddress(FlushShipCmd flushShipCmd) {
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.flushShipAddress(flushShipCmd));
    }

    /**
     * 工单导出文件
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> exportServiceImg(AngelWorkPageRequest request) {
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.exportServiceImg(request));
    }

    /**
     * 查询redis
     *
     * @param findJimParam
     * @return
     */
    @Override
    public Response<String> findFromJim(FindJimParam findJimParam) {
        if(findJimParam.getType() == 1) {
            if(StringUtils.isNotBlank(findJimParam.getKey()) && StringUtils.isNotBlank(findJimParam.getField())) {
                return ResponseUtil.buildSuccResponse(jimClient.hGet(findJimParam.getKey(), findJimParam.getField()));
            }else if(StringUtils.isNotBlank(findJimParam.getKey())) {
                Map<String, String> stringStringMap = jimClient.hGetAll(findJimParam.getKey());
                return ResponseUtil.buildSuccResponse(JSON.toJSONString(stringStringMap));
            }
        }else if(findJimParam.getType() == 2) {
            if(StringUtils.isNotBlank(findJimParam.getKey()) && Objects.nonNull(findJimParam.getLRange()) && Objects.nonNull(findJimParam.getRRange())) {
                return ResponseUtil.buildSuccResponse(JSON.toJSONString(jimClient.lRange(findJimParam.getKey(), findJimParam.getLRange(), findJimParam.getRRange())));
            }
        }else if(findJimParam.getType() == 3) {
            if(StringUtils.isNotBlank(findJimParam.getKey())) {
                return ResponseUtil.buildSuccResponse(JSON.toJSONString(jimClient.sMembers(findJimParam.getKey())));
            }
        }else if(findJimParam.getType() == 4) {
            if(StringUtils.isNotBlank(findJimParam.getKey())) {
                return ResponseUtil.buildSuccResponse(JSON.toJSONString(JSON.toJSONString(jimClient.zRange(findJimParam.getKey(), findJimParam.getLRange(), findJimParam.getRRange()))));
            }
        }else if(findJimParam.getType() == 5) {
            if(StringUtils.isNotBlank(findJimParam.getKey())) {
                return ResponseUtil.buildSuccResponse(jimClient.get(findJimParam.getKey()));
            }
        }
        return ResponseUtil.buildSuccResponse("");
    }

    /**
     * @param autoBotsRequestBO
     * @return
     */
    @Override
    public Response<Boolean> searchAiRequest(AutoBotsO2ORequest autoBotsRequestBO) {
        AutoBotsRequestBO bo = new AutoBotsRequestBO();
        bo.setErp(autoBotsRequestBO.getErp());
        bo.setKeyword(autoBotsRequestBO.getKeyword());
        bo.setExtParams(autoBotsRequestBO.getExtParams());
        bo.setReqId(String.valueOf(System.currentTimeMillis()));
        bo.setTraceId(UUID.randomUUID().toString());
        bo.setCallbackUrl(autoBotsRequestBO.getCallbackUrl());
        bo.setExtendId(autoBotsRequestBO.getExtendId());
        bo.setExtendIdType(autoBotsRequestBO.getExtendIdType());
        bo.setScene(autoBotsRequestBO.getScene());

        AutoBotsResultBO autoBotsResultBO = autoBotsApplication.searchAiRequest(
                autoBotsRequestBO.getAgentId(),autoBotsRequestBO.getToken(),bo
                );
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    @Override
    @LogAndAlarm
    public Response<Boolean> xfylAlarm(String level) {
        if(AlarmLevelEnum.CRITICAL.getLevel().equalsIgnoreCase(level)) {
            throw new BusinessException(AngelPromiseBizErrorCode.CRITICAL);
        }
        if(AlarmLevelEnum.ERROR.getLevel().equalsIgnoreCase(level)) {
            throw new BusinessException(AngelPromiseBizErrorCode.ERROR);
        }
        if(AlarmLevelEnum.WARING.getLevel().equalsIgnoreCase(level)) {
            throw new BusinessException(AngelPromiseBizErrorCode.WARING);
        }
        if(AlarmLevelEnum.INFO.getLevel().equalsIgnoreCase(level)) {
            throw new BusinessException(AngelPromiseBizErrorCode.INFO);
        }

        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 工单导出文件
     *
     * @param workId
     * @return
     */
    @Override
    public Response<Boolean> mockAngelWorkServiceImgUpload(Long workId) {
        AngelWorkEventBody angelWorkEventBody = new AngelWorkEventBody();
        angelWorkEventBody.setWorkId(workId);
        AngelWork work = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(workId).build());
        if (work != null) {
            eventCoordinator.publish(EventFactory.newDefaultEvent(work, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_SUBMIT_SERVICE_RECORDS, angelWorkEventBody));
            return ResponseUtil.buildSuccResponse(true);
        }
        return ResponseUtil.buildSuccResponse(false);
    }
}
