package com.jdh.o2oservice.listener.dts;

import com.jd.binlog.client.WaveEntry;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

/**
 * BinlakeBaseEventListener
 *
 * <AUTHOR>
 * @version 2024/07/05 11:51
 **/
public abstract class BinlakeBaseEventListener {

    protected static final Integer SYNC_VERSION = 3000;
    protected static final String SYNC_USER = "SYNC_USER";
    protected static final String BRANCH_PRODUCTION = "production";

    /**
     * mvp护士的sku清单
     */
    protected static final List<Long> MVP_NURSE_SKUID_LIST = Arrays.asList(1100098754544L, 100089607467L, 100098754562L, 100098754586L, 100098754588L, 100098754556L, 100098754554L, 100089607417L, 100089607419L, 100098754552L, 100089817093L, 100099068202L, 100089817107L, 100089607445L, 100102462804L, 100102462832L, 100102462812L, 100103260588L, 100092614285L, 100092614265L, 100092614267L, 100092614287L, 100103260590L, 100103260584L, 100103260592L, 100103260604L, 100092614261L, 100092614283L, 100103260586L, 100092614263L, 100103260602L);

    protected static ServiceHomeTypeEnum getVertical(Long skuId) {

        if (MVP_NURSE_SKUID_LIST.contains(skuId)) {
            // MVP护士上门
            return ServiceHomeTypeEnum.getServiceHomeTypeEnum("20");
        } else {
            // 骑手上门
            return ServiceHomeTypeEnum.getServiceHomeTypeEnum("10");
        }
    }

    /**
     * 过滤除要丢弃的行数据
     *
     * @param columns
     * @return
     */
    protected boolean filterDiscardRow(List<WaveEntry.Column> columns) {
        if (columns == null) {
            return true;
        }
        List<Predicate<WaveEntry.Column>> predicateList = filterNeedCondition();
        // 过滤条件为空，意味着全需要
        if(CollectionUtil.isEmpty(predicateList)){
            return false;
        }
        return columns.stream().filter(s -> predicateList.stream().anyMatch(f -> f.test(s))).limit(10).count() != predicateList.size();
    }

    /**
     * 过滤数据必要条件
     *
     * @return
     */
    protected abstract List<Predicate<WaveEntry.Column>> filterNeedCondition();
}
