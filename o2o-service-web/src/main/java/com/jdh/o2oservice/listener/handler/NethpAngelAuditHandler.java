package com.jdh.o2oservice.listener.handler;

import com.jd.fastjson.JSON;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.angel.enums.NethpAngelEventAuditTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.NethpAngelEventProfessionTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.rpc.NewNethpDispatchRpc;
import com.jdh.o2oservice.export.angel.cmd.AngelSyncCmd;
import com.jdh.o2oservice.export.angel.dto.NethpAngelAuditEventBody;
import com.jdh.o2oservice.export.dispatch.cmd.RecoverDispatchCmd;
import com.jdh.o2oservice.export.dispatch.dto.NethpTriageDiagRecycleEventBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName NethpAngelAuditHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/6/9 16:46
 **/
@Slf4j
@Service
public class NethpAngelAuditHandler extends AbstractHandler<NethpAngelAuditEventBody> implements MapAutowiredKey {

    /**
     * angelApplication
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    /**
     * handlerTopic
     */
    @Value("${topics.jdhReachStoreMq2Consumer.nethpDoctorAuditEventTopic}")
    private String handlerTopic;

    /**
     * handlerTopicYf
     */
    @Value("${topics.jdhReachStoreMq2Consumer.xfylNethpDoctorAuditEventForwardYfbTopic}")
    private String handlerTopicYf;

    /**
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    /**
     *
     * @param message
     * @return
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //消息检查
        if(Objects.isNull(message) || StringUtils.isBlank(message.getText())){
            return true;
        }
        return false;
    }

    /**
     *
     * @param message
     * @return
     */
    @Override
    public NethpAngelAuditEventBody analysisMessage(Message message) {
        NethpAngelAuditEventBody eventBody = JSON.parseObject(message.getText(), NethpAngelAuditEventBody.class);
        log.info("NethpAngelAuditHandler -> eventBody:{}", JSON.toJSONString(eventBody));
        return eventBody;
    }

    /**
     *
     * @param eventBody
     * @return
     */
    @Override
    public boolean filterOfDiscardMessage(NethpAngelAuditEventBody eventBody) {
        //非消费医疗护士审核消息
        if (Objects.isNull(eventBody) || !NethpAngelEventProfessionTypeEnum.filterHomeProfession(eventBody.getProfessionType())) {
            log.info("NethpAngelAuditHandler -> 非消费医疗护士审核消息:{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));
            return true;
        }
        //只处理审核类型 = 资质或信息变更,其余消息不做处理
        if (!NethpAngelEventAuditTypeEnum.ZZ.getCode().equals(eventBody.getAuditType())
                && !NethpAngelEventAuditTypeEnum.XXBG.getCode().equals(eventBody.getAuditType())
                && !NethpAngelEventAuditTypeEnum.YSSFZSH.getCode().equals(eventBody.getAuditType())) {
            log.info("NethpAngelAuditListener -> 非消费医疗护士审核消息,AuditType不在处理范围内 :{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));
            return true;
        }
        return false;
    }

    /**
     *
     * @param message
     * @param eventBody
     * @return
     */
    @Override
    public boolean transferToYf(Message message, NethpAngelAuditEventBody eventBody) {
        // 判断当前pin是否在转投预发白名单
        if (duccConfig.checkBlankPin(eventBody.getDoctorPin())) {
            log.info("NethpAngelAuditHandler -> transferToYf, eventBody转投到预发环境. doctorPin={}", eventBody.getDoctorPin());
            try {
                message.setTopic(handlerTopicYf);
                log.info("NethpAngelAuditHandler -> transferToYf, eventBody转投到预发环境. message={}", JSON.toJSONString(message));
                reachStoreProducer.send(message);
            } catch (JMQException e) {
                throw new RuntimeException(e);
            }
            return true;
        }
        return false;
    }

    /**
     * 业务处理消息
     *
     * @param eventBody
     * @return
     */
    @Override
    public void dealMessage(NethpAngelAuditEventBody eventBody) {
        try {
            AngelSyncCmd angelSyncCmd = new AngelSyncCmd();
            angelSyncCmd.setPlatformId(eventBody.getPlatformId());
            angelSyncCmd.setDoctorPin(eventBody.getDoctorPin());
            angelSyncCmd.setAuditReason(eventBody.getAuditReason());
            angelSyncCmd.setAuditType(eventBody.getAuditType());
            angelSyncCmd.setAuditStatus(eventBody.getAuditStatus());
            angelApplication.syncAngelAuditInfoFromNethp(angelSyncCmd);
        } catch (Exception e) {
            log.error("NethpAngelAuditHandler->processMessage error", e);
        }
    }
}