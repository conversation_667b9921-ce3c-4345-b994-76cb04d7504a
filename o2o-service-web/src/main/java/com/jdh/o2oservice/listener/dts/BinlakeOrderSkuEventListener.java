package com.jdh.o2oservice.listener.dts;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.shorturl.exceptions.BussinessException;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.vo.AddressInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentTimeValueObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * BinlakeOrderSkuEventListener examination_order_sku_info表binlake
 *
 * <AUTHOR>
 * @version 2024/7/2 18:49
 **/
@Slf4j
@Component
public class BinlakeOrderSkuEventListener extends BinlakeBaseEventListener implements MessageListener {

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();
    /**
     * 表名称
     */
    private static final String TABLE_NAME = "examination_order_sku_info";

    @Resource
    JdOrderItemRepository jdOrderItemRepository;
    @Resource
    JdOrderExtRepository jdOrderExtRepository;
    @Resource
    GenerateIdFactory generateIdFactory;

    @JmqListener(id = "dtsConsumer", topics = {"${topics.dts.order-sku}"})
    @LogAndAlarm(jKey = "com.jdh.o2oservice.listener.dts.BinlakeOrderSkuEventListener.onMessage")
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            if (!TABLE_NAME.equals(entryMessage.getHeader().getTableName())) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            log.info("BinlakeOrderSkuEventListener -> onMessage 消息数量, rowDatasListSize={}", entryMessage.getRowChange().getRowDatasList().size());
            WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
            log.info("BinlakeOrderSkuEventListener -> onMessage eventType={}", eventType);
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    // 不符合同步要求的行数据
                    if (filterDiscardRow(rowData.getAfterColumnsList())) {
                        continue;
                    }
                    processRowData(rowData, eventType);
                } catch (BusinessException e) {
                    log.error("BinlakeOrderSkuEventListener -> onMessage business error msg={}", e.getMessage());
                    throw e;
                } catch (Exception e) {
                    log.error("BinlakeOrderSkuEventListener -> onMessage Exception ", e);
                    throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
                } catch (Throwable e) {
                    log.error("BinlakeOrderSkuEventListener -> onMessage Throwable ", e);
                    throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
                }
            }
        }
    }

    /**
     * 处理单行数据
     *
     * @param rowData
     * @param eventType
     * @throws BussinessException
     */
    private void processRowData(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        List<JdOrderItem> jdOrderItemList = buildJdOrderItem(rowData, eventType);
        if (CollectionUtil.isNotEmpty(jdOrderItemList)) {
            // JdOrderItem不做更新操作，无数据则insert，有数据即放弃
            if (CollUtil.isEmpty(jdOrderItemRepository.listByOrderId(jdOrderItemList.stream().findFirst().get().getOrderId()))) {
                jdOrderItemRepository.batchSave(jdOrderItemList);
            }
        }
        JdOrderExt jdOrderExt = buildJdOrderExt(rowData, eventType);
        if (Objects.nonNull(jdOrderExt)) {
            if (Objects.nonNull(jdOrderExtRepository.findJdOrderExtDetail(jdOrderExt.getOrderId(), jdOrderExt.getExtType()))) {
                jdOrderExtRepository.updateJdOrderExt(jdOrderExt);
            } else {
                jdOrderExtRepository.save(jdOrderExt);
            }
        }
    }

    /**
     * 构建订单明细列表
     *
     * @param rowData
     * @param eventType
     * @return
     * @throws ParseException
     */
    private List<JdOrderItem> buildJdOrderItem(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        List<JdOrderItem> jdOrderItemList = new ArrayList<>();
        JdOrderItem jdOrderItem = new JdOrderItem();
        jdOrderItemList.add(jdOrderItem);
        jdOrderItem.setOrderItemId(generateIdFactory.getId());
        jdOrderItem.setItemDiscount(BigDecimal.ZERO);
        jdOrderItem.setVersion(SYNC_VERSION);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("order_id".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setOrderId(Long.valueOf(column.getValue()));
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setUserPin(column.getValue());
            } else if ("sku_no".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setSkuId(Long.valueOf(column.getValue()));
            } else if ("sku_name".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setSkuName(column.getValue());
            } else if ("sku_num".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setSkuNum(Integer.valueOf(column.getValue()));
            } else if ("sku_image".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setSkuImage(column.getValue());
            } else if ("sku_amount".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setItemAmount(new BigDecimal(column.getValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                jdOrderItem.setItemTotalAmount(new BigDecimal(column.getValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            } else if ("sku_expire_date".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setSkuExpireDate(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setYn(Integer.valueOf(column.getValue()));
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                jdOrderItem.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        // 补充垂直身份
        ServiceHomeTypeEnum verticalEnum = super.getVertical(jdOrderItem.getSkuId());
        jdOrderItem.setVerticalCode(verticalEnum.getVerticalCode());
        jdOrderItem.setServiceType(verticalEnum.getServiceType());
        return jdOrderItemList;
    }

    /**
     * 构建订单扩展数据
     *
     * @param rowData
     * @param eventType
     * @return
     */
    private JdOrderExt buildJdOrderExt(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExt.setExtId(generateIdFactory.getId());
        jdOrderExt.setExtType("appointmentInfo");
        jdOrderExt.setVersion(SYNC_VERSION);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("order_id".equalsIgnoreCase(column.getName())) {
                jdOrderExt.setOrderId(Long.valueOf(column.getValue()));
            } else if ("sku_info".equalsIgnoreCase(column.getName())) {
                jdOrderExt.setExtContext(convertExtContent(column.getValue()));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                jdOrderExt.setYn(Integer.valueOf(column.getValue()));
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                jdOrderExt.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                jdOrderExt.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        return jdOrderExt;
    }

    /**
     * skuinfo转extContent
     *
     * @param skuInfo
     * @return
     */
    private String convertExtContent(String skuInfo) {
        JSONObject skuInfoObj = JSON.parseObject(skuInfo);
        if (skuInfoObj == null) {
            return null;
        }
        OrderAppointmentInfoValueObject orderAppointmentInfoValueObject = new OrderAppointmentInfoValueObject();

        OrderAppointmentTimeValueObject orderAppointmentTimeValueObject = new OrderAppointmentTimeValueObject();
        orderAppointmentInfoValueObject.setAppointmentTime(orderAppointmentTimeValueObject);
        orderAppointmentTimeValueObject.setAppointmentStartTime(TimeUtils.dateTimeToStr(skuInfoObj.getDate("doorTimeStart"), TimeFormat.LONG_PATTERN_LINE_NO_S));
        orderAppointmentTimeValueObject.setAppointmentEndTime(TimeUtils.dateTimeToStr(skuInfoObj.getDate("doorTimeEnd"), TimeFormat.LONG_PATTERN_LINE_NO_S));
        orderAppointmentTimeValueObject.setDateType(2);
        orderAppointmentTimeValueObject.setIsImmediately(false);

        AddressInfoValueObject addressInfoValueObject = new AddressInfoValueObject();
        orderAppointmentInfoValueObject.setAddressInfo(addressInfoValueObject);
        addressInfoValueObject.setMobile(skuInfoObj.getString("userPhone"));
        addressInfoValueObject.setName(skuInfoObj.getString("userName"));
        addressInfoValueObject.setId(skuInfoObj.getLongValue("addressId"));
        addressInfoValueObject.setFullAddress(skuInfoObj.getString("addressDetail"));
        return JSON.toJSONString(orderAppointmentInfoValueObject);
    }

    /**
     * 过滤数据必要条件  order_appoint_type=16 and payment_time is not null
     *
     * @return
     */
    @Override
    protected List<Predicate<WaveEntry.Column>> filterNeedCondition() {
        return Arrays.asList(
                s -> "order_appoint_type".equalsIgnoreCase(s.getName()) && "16".equals(s.getValue()),
                s -> "sku_info".equalsIgnoreCase(s.getName()) && StringUtil.isNotEmpty(s.getValue()),
                s -> "store_id".equalsIgnoreCase(s.getName()) && StringUtil.isNotEmpty(s.getValue())
        );
    }
}