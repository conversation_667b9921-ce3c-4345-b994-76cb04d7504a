package com.jdh.o2oservice.listener.dts;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.shorturl.exceptions.BussinessException;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderMoney;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderMoneyRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.function.Predicate;

/**
 * BinlakeOrderEventListener xfyl_appointment_order_info表binlake
 *
 * <AUTHOR>
 * @version 2024/7/2 18:49
 **/
@Slf4j
@Component
public class BinlakeOrderEventListener extends BinlakeBaseEventListener implements MessageListener {

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();
    /**
     * 表名称
     */
    private static final String TABLE_NAME = "xfyl_appointment_order_info";

    @Resource
    JdOrderRepository jdOrderRepository;
    @Resource
    JdOrderMoneyRepository jdOrderMoneyRepository;

    @JmqListener(id = "dtsConsumer", topics = {"${topics.dts.order}"})
    @LogAndAlarm(jKey = "com.jdh.o2oservice.listener.dts.BinlakeOrderEventListener.onMessage")
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            if (!TABLE_NAME.equals(entryMessage.getHeader().getTableName())) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            log.info("BinlakeOrderEventListener -> onMessage 消息数量, rowDatasListSize={}", entryMessage.getRowChange().getRowDatasList().size());
            WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
            log.info("BinlakeOrderEventListener -> onMessage eventType={}", eventType);
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    // 不符合同步要求的行数据
                    if (filterDiscardRow(rowData.getAfterColumnsList())) {
                        log.info("BinlakeOrderEventListener -> onMessage 不符合同步要求的行数据 rowData AfterColumnsList={}", JsonUtil.toJSONString(rowData.getAfterColumnsList()));
                        continue;
                    }
                    processRowData(rowData, eventType);
                } catch (BusinessException e) {
                    log.error("BinlakeOrderEventListener -> onMessage business error msg={}", e.getMessage());
                    throw e;
                } catch (Exception e) {
                    log.error("BinlakeOrderEventListener -> onMessage Exception ", e);
                    throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
                } catch (Throwable e) {
                    log.error("BinlakeOrderEventListener -> onMessage Throwable ", e);
                    throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
                }
            }
        }
    }

    /**
     * 处理单行数据
     *
     * @param rowData
     * @param eventType
     * @throws BussinessException
     */
    private void processRowData(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        JdOrder jdOrder = buildJdOrder(rowData, eventType);
        log.info("BinlakeOrderEventListener -> onMessage processRowData jdOrder={}", JsonUtil.toJSONString(jdOrder));
        if (Objects.nonNull(jdOrder)) {
            // 存在即更新，不存在即新增
            if (Objects.nonNull(jdOrderRepository.find(jdOrder.getIdentifier()))) {
                jdOrderRepository.updateOrderByOrderId(jdOrder);
            } else {
                jdOrderRepository.save(jdOrder);
            }
        }
        List<JdOrderMoney> jdOrderMoneyList = buildJdOrderMoney(rowData, eventType);
        log.info("BinlakeOrderEventListener -> onMessage processRowData jdOrderMoneyList={}", JsonUtil.toJSONString(jdOrderMoneyList));
        if (CollectionUtil.isNotEmpty(jdOrderMoneyList)) {
            // JdOrderMoney不做更新操作，无数据则insert，有数据即放弃
            if (CollUtil.isEmpty(jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId()))) {
                jdOrderMoneyRepository.batchSave(jdOrderMoneyList);
            }
        }
    }

    /**
     * 构建订单
     *
     * @param rowData
     * @param eventType
     * @return
     * @throws ParseException
     */
    private JdOrder buildJdOrder(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        Long skuId = null;
        JdOrder jdOrder = new JdOrder();
        jdOrder.setParentId(0L);
        jdOrder.setServiceFee(BigDecimal.ZERO);
        jdOrder.setPaymentWay("0");
        jdOrder.setPartnerSource(0);
        jdOrder.setOrgId("6");
        jdOrder.setVersion(SYNC_VERSION);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("order_id".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderId(Long.valueOf(column.getValue()));
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                jdOrder.setUserPin(column.getValue());
            } else if ("order_type".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderType(Integer.valueOf(column.getValue()));
            } else if ("send_pay".equalsIgnoreCase(column.getName())) {
                jdOrder.setSendPay(column.getValue());
            } else if ("order_user_phone".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderUserPhone(column.getValue());
            } else if ("order_user_name".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderUserName(column.getValue());
            } else if ("vender_id".equalsIgnoreCase(column.getName())) {
                jdOrder.setVenderId(column.getValue());
            } else if ("vender_name".equalsIgnoreCase(column.getName())) {
                jdOrder.setVenderName(column.getValue());
            } else if ("payment_time".equalsIgnoreCase(column.getName())) {
                jdOrder.setPaymentTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("pay_type".equalsIgnoreCase(column.getName())) {
                jdOrder.setPayType(Integer.valueOf(column.getValue()));
            } else if ("order_total_amount".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderAmount(new BigDecimal(column.getValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                jdOrder.setOrderTotalAmount(new BigDecimal(column.getValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            } else if ("order_discount".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderDiscount(new BigDecimal(column.getValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                jdOrder.setYn(Integer.valueOf(column.getValue()));
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                jdOrder.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                jdOrder.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("order_status".equalsIgnoreCase(column.getName())) {
                jdOrder.setOrderStatus(convertOrderStatus(Integer.valueOf(column.getValue())));
            } else if ("sku_no".equalsIgnoreCase(column.getName())) {
                skuId = Long.valueOf(column.getValue());
            }
        }
        // 补充垂直身份
        ServiceHomeTypeEnum verticalEnum = super.getVertical(skuId);
        jdOrder.setVerticalCode(verticalEnum.getVerticalCode());
        jdOrder.setServiceType(verticalEnum.getServiceType());
        return jdOrder;
    }

    /**
     * 构建订单金额列表
     *
     * @param rowData
     * @param eventType
     * @return
     */
    private List<JdOrderMoney> buildJdOrderMoney(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        // 初始化4个公用字段的定义
        Long skuId = null;
        Long orderId = null;
        Integer yn = null;
        Date createTime = null;
        Date updateTime = null;
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("pay_amount_detail".equalsIgnoreCase(column.getName())) {
                JSONArray amountJsonArray = JSON.parseArray(column.getValue());
                if (Objects.nonNull(amountJsonArray) && amountJsonArray.size() > 0) {
                    int amountJsonArraySize = amountJsonArray.size();
                    for (int i = 0; i < amountJsonArraySize; i++) {
                        JSONObject amountJsonObj = amountJsonArray.getJSONObject(i);
                        if (Objects.isNull(amountJsonObj)) {
                            continue;
                        }
                        JdOrderMoney jdOrderMoney = new JdOrderMoney();
                        jdOrderMoneyList.add(jdOrderMoney);
                        jdOrderMoney.setMoneyType(amountJsonObj.getInteger("amountType"));
                        jdOrderMoney.setAmount(amountJsonObj.getBigDecimal("amount").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                        jdOrderMoney.setVersion(SYNC_VERSION);
                    }
                }
            } else if ("sku_no".equalsIgnoreCase(column.getName())) {
                skuId = Long.valueOf(column.getValue());
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                yn = Integer.valueOf(column.getValue());
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                createTime = DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss");
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                updateTime = DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss");
            } else if ("order_id".equalsIgnoreCase(column.getName())) {
                orderId = Long.valueOf(column.getValue());
            }
        }
        // 将4个共有字段补充返回
        for (JdOrderMoney item : jdOrderMoneyList) {
            item.setSkuId(skuId);
            item.setYn(yn);
            item.setCreateTime(createTime);
            item.setUpdateTime(updateTime);
            item.setOrderId(orderId);
        }
        return jdOrderMoneyList;
    }

    /**
     * 将老订单状态映射成新状态
     *
     * @param oldOrderStatus
     * @return
     */
    private Integer convertOrderStatus(Integer oldOrderStatus) {
        switch (oldOrderStatus.intValue()) {
            // 退款中
            case 6:
                return 8;
            // 已退款
            case 8:
                return 9;
            // 已取消
            case 11:
                return 7;
            // 已服务
            case 15:
                return 6;
            // 待付款
            case 9:
                return 1;
            // 已付款、待预约、待使用
            default:
                return 2;
        }
    }

    /**
     * 过滤数据必要条件  order_appoint_type=16 and payment_time is not null
     *
     * @return
     */
    @Override
    protected List<Predicate<WaveEntry.Column>> filterNeedCondition() {
        return Arrays.asList(
                s -> "order_appoint_type".equalsIgnoreCase(s.getName()) && "16".equals(s.getValue()),
                s -> "payment_time".equalsIgnoreCase(s.getName()) && StringUtil.isNotEmpty(s.getValue())
        );
    }
}