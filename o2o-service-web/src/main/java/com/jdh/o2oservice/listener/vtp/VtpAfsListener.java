package com.jdh.o2oservice.listener.vtp;


import cn.hutool.core.collection.CollUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.core.domain.trade.bo.VtpAfsMessageBO;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundTask;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRefundTaskRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.trade.query.RefundOrderParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderSku;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * VtpAfsListener 售后全流程跟踪消息
 *
 * <AUTHOR>
 * @version 2024/9/3 11:01
 **/
@Slf4j
@Component
public class VtpAfsListener implements MessageListener {


    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    @Lazy
    private JdOrderRepository jdOrderRepository;

    @Resource
    @Lazy
    private TradeApplication tradeApplication;
    /**
     * jdOrderRefundTaskRepository
     */
    @Resource
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;
    /**
     * promiseExtApplication
     */
    @Resource
    @Lazy
    private PromiseExtApplication promiseExtApplication;
    /**
     * medicalPromiseApplication
     */
    @Resource
    @Lazy
    private MedicalPromiseApplication medicalPromiseApplication;


    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.vtpafs}"}, delayedStart = 60)
    @Override
    @LogAndAlarm
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            String text = message.getText();
            VtpAfsMessageBO vtpAfsMessageBO = null;
            String lockKey = "";

            try {
                vtpAfsMessageBO = JsonUtil.parseObject(text, VtpAfsMessageBO.class);
                //参数校验
                if (null == vtpAfsMessageBO) {
                    log.info("VtpAfsListener -> processMessage, vtpAfsMessageBO is null, message = {}", message);
                    return;
                }
                if (!checkMessage(vtpAfsMessageBO)) {
                    log.info("VtpAfsListener -> processMessage, param error, vtpAfsMessageBO = {}", vtpAfsMessageBO);
                    return;
                }
                log.info("VtpAfsListener -> processMessage, start vtpAfsMessageBO = {}", vtpAfsMessageBO);
                Long orderId = vtpAfsMessageBO.getOrderId();
                lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REFUND_SUCC_TRANSACTION_KEY, orderId);
                // 防重
                boolean lockResult = redisLockUtil.tryLock(lockKey, CommonConstant.REDIS_DEFAULT_VALUE, CommonConstant.TEN, TimeUnit.SECONDS);
                if (!lockResult) {
                    log.info("VtpAfsListener -> processMessage, orderId is exists.");
                    return;
                }
                //服务单退款
                Boolean result = vtpAfsOrderRefund(orderId);
                log.info("VtpAfsListener -> processMessage, result={}", result);
                if(result){
                    //订单状态更新
                    JdOrder jdOrder = JdOrder.builder().orderId(orderId).orderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus()).build();
                    jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
                    tradeApplication.reviseOrderFinishState(orderId);
                }
            } catch (BusinessException e) {
                log.error("VtpAfsListener vtpAfsMessageBO = {}", JsonUtil.toJSONString(vtpAfsMessageBO), e);
                throw e;
            } catch (Exception e) {
                log.error("VtpAfsListener -> processMessage exception, message={}", JsonUtil.toJSONString(message), e);
                throw new RuntimeException("VtpAfsListener ERROR:" + e.getMessage());
            } finally {
                if (StringUtils.isNotBlank(lockKey)) {
                    redisLockUtil.unLock(lockKey);
                }
            }
        }
    }

    /**
     * vtp 赠随主退
     * @param orderId
     */
    private Boolean vtpAfsOrderRefund(Long orderId){
        JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
        jdOrderRefundTask.setOrderId(orderId);
        List<JdOrderRefundTask> taskTemp = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        if(CollUtil.isNotEmpty(taskTemp)){
            return false;
        }
        RefundOrderParam refundOrderParam = new RefundOrderParam();
        refundOrderParam.setOrderId(orderId);
        refundOrderParam.setRefundSource(CommonConstant.THREE_STR);
        refundOrderParam.setRefundType(RefundTypeEnum.AMOUNT_REFUND.getType());
        refundOrderParam.setRefundAmount(BigDecimal.ZERO);
        refundOrderParam.setNotSavePromiseId(Boolean.TRUE);
        refundOrderParam.setOperator("赠随主退");
        refundOrderParam.setRefundReason("赠随主退");
        refundOrderParam.setRefundReasonCode("99999902");
        List<PromiseDto> promiseList = promiseExtApplication.getPromiseByOrderItemId(String.valueOf(orderId));
        if(CollUtil.isNotEmpty(promiseList)){
            List<Long> promiseIdList = promiseList.stream().map(PromiseDto::getPromiseId).collect(Collectors.toList());
            MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
            medicalPromiseListRequest.setPromiseIdList(promiseIdList);
            List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
            Map<Long,List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromiseId));
            for(PromiseDto promiseDto : promiseList){
                List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseMap.get(promiseDto.getPromiseId());
                refundOrderParam.setRefundOrderSkuList(getRefundOrderSkus(medicalPromiseList));
                refundOrderParam.setPromiseId(promiseDto.getPromiseId());
                tradeApplication.xfylOrderRefund(refundOrderParam);
            }
        }else{
            log.error("VtpAfsListener -> vtpAfsOrderRefund, PromiseDto is null.");
        }
        return true;
    }
    /**
     *
     * @param medicalPromiseList
     * @return
     */
    private List<RefundOrderSku> getRefundOrderSkus(List<MedicalPromiseDTO> medicalPromiseList){
        List<RefundOrderSku> refundOrderSkuList = new ArrayList<>();
        for(MedicalPromiseDTO medicalPromiseDTO : medicalPromiseList){
            RefundOrderSku refundOrderSku = new RefundOrderSku();
            refundOrderSku.setServiceId(String.valueOf(medicalPromiseDTO.getServiceId()));
            refundOrderSku.setPromisePatientId(medicalPromiseDTO.getPromisePatientId());
            refundOrderSkuList.add(refundOrderSku);
        }
        return refundOrderSkuList;
    }

    /**
     *
     * @param medicalPromiseListRequest
     * @return
     */
    private List<MedicalPromiseDTO> getMedicalPromises(MedicalPromiseListRequest medicalPromiseListRequest){
        List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        return medicalPromises;
    }

    /**
     * @param vtpAfsMessageBO
     * @return
     */
    private boolean checkMessage(VtpAfsMessageBO vtpAfsMessageBO) {
        if (null == vtpAfsMessageBO.getOrderId()) {
            log.info("VtpAfsListener -> processMessage, orderId is null");
            return false;
        }
        if (null == vtpAfsMessageBO.getStepType() || !"CONFIRM".equalsIgnoreCase(vtpAfsMessageBO.getStepType())){
            log.info("VtpAfsListener -> processMessage, stepType is null or not CONFIRM");
            return false;
        }
        if (null == vtpAfsMessageBO.getRuleId() || vtpAfsMessageBO.getRuleId().longValue() != 344L){
            log.info("VtpAfsListener -> processMessage, ruleId is null or not 344");
            return false;
        }
        return true;
    }
}
