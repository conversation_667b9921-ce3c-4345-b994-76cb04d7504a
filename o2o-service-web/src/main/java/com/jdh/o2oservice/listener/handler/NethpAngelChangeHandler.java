package com.jdh.o2oservice.listener.handler;

import com.jd.fastjson.JSON;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.angel.enums.NethpAngelEventAuditTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.NethpAngelEventProfessionTypeEnum;
import com.jdh.o2oservice.export.angel.cmd.AngelNethpChangeCmd;
import com.jdh.o2oservice.export.angel.cmd.AngelSyncCmd;
import com.jdh.o2oservice.export.angel.dto.NethpAngelAuditEventBody;
import com.jdh.o2oservice.export.angel.dto.NethpAngelChangeEventBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName NethpAngelChangeHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/6/9 16:54
 **/
@Slf4j
@Service
public class NethpAngelChangeHandler extends AbstractHandler<NethpAngelChangeEventBody> implements MapAutowiredKey {

    /**
     * angelApplication
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    /**
     * handlerTopic
     */
    @Value("${topics.jdhReachStoreMq2Consumer.nethpDoctorChangeEventTopic}")
    private String handlerTopic;

    /**
     * handlerTopicYf
     */
    @Value("${topics.jdhReachStoreMq2Consumer.xfylNethpDoctorChangeEventForwardYfbTopic}")
    private String handlerTopicYf;

    /**
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    /**
     *
     * @param message
     * @return
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //消息检查
        if(Objects.isNull(message) || StringUtils.isBlank(message.getText())){
            return true;
        }
        return false;
    }

    /**
     *
     * @param message
     * @return
     */
    @Override
    public NethpAngelChangeEventBody analysisMessage(Message message) {
        NethpAngelChangeEventBody eventBody = JSON.parseObject(message.getText(), NethpAngelChangeEventBody.class);
        log.info("NethpAngelChangeHandler -> processMessage eventBody:{}", JSON.toJSONString(eventBody));
        return eventBody;
    }

    /**
     *
     * @param eventBody
     * @return
     */
    @Override
    public boolean filterOfDiscardMessage(NethpAngelChangeEventBody eventBody) {
        //非消费医疗护士审核消息
        if (Objects.isNull(eventBody) || !NethpAngelEventProfessionTypeEnum.filterHomeProfession(eventBody.getProfessionType())) {
            log.info("NethpAngelChangeHandler ->  processMessage 非消费医疗护士数据变更消息:{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));
            return true;
        }

        if (CollectionUtils.isEmpty(eventBody.getRemark())) {
            log.info("NethpAngelChangeHandler -> processMessage 变更消息中remark为空消息过滤 :{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));
            return true;
        }
        return false;
    }

    /**
     *
     * @param message
     * @param eventBody
     * @return
     */
    @Override
    public boolean transferToYf(Message message, NethpAngelChangeEventBody eventBody) {
        // 判断当前pin是否在转投预发白名单
        if (duccConfig.checkBlankPin(eventBody.getDoctorPin())) {
            log.info("NethpAngelChangeHandler -> transferToYf, eventBody转投到预发环境. doctorPin={}", eventBody.getDoctorPin());
            try {
                message.setTopic(handlerTopicYf);
                log.info("NethpAngelChangeHandler -> transferToYf, eventBody转投到预发环境. message={}", JSON.toJSONString(message));
                reachStoreProducer.send(message);
            } catch (JMQException e) {
                throw new RuntimeException(e);
            }
            return true;
        }
        return false;
    }

    /**
     * 业务处理消息
     *
     * @param eventBody
     * @return
     */
    @Override
    public void dealMessage(NethpAngelChangeEventBody eventBody) {
        try {
            AngelNethpChangeCmd angelNethpChangeCmd = new AngelNethpChangeCmd();
            angelNethpChangeCmd.setBusinessId(eventBody.getBusinessId());
            angelNethpChangeCmd.setDoctorId(eventBody.getDoctorId());
            angelNethpChangeCmd.setDoctorPin(eventBody.getDoctorPin());
            angelNethpChangeCmd.setChangeTime(eventBody.getChangeTime());
            angelNethpChangeCmd.setRemark(eventBody.getRemark());
            angelNethpChangeCmd.setChangeValue(eventBody.getChangeValue());
            angelNethpChangeCmd.setTestDoctor(eventBody.getTestDoctor());
            angelNethpChangeCmd.setTenantId(eventBody.getTenantId());
            angelNethpChangeCmd.setProfessionType(eventBody.getProfessionType());

            angelApplication.syncAngelChangeFromNethp(angelNethpChangeCmd);
        } catch (Exception e) {
            log.error("NethpAngelChangeHandler->processMessage error", e);
        }
    }
}