package com.jdh.o2oservice.listener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.export.support.dto.CallBillingNotificationDto;
import com.jdh.o2oservice.export.support.dto.CallBillingUserDataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 通话话单、通话事件、短信事件JMQ消息
 * @Date 2024/12/19 下午2:20
 * <AUTHOR>
 **/
@Slf4j
@Service("yunDingPrivacyNotificationListener")
public class YunDingPrivacyNotificationListener implements MessageListener {

    @Resource
    private CallRecordApplication callRecordApplication;

    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.yunDingPrivacyNotificationTopic}"})
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            try {
                String text = message.getText();
                //log.info("YunDingPrivacyNotificationListener onMessage text={}", text);
                JSONObject obj = JSON.parseObject(text);
                // 消息类型: call_billing（话单，双发打通电话发送消息，没打通也会有这个消息）、call_event（通话事件，没接通有消息）、sms_event（短信事件）
                if (!"call_billing".equals(obj.getString("messageType"))){
                    continue;
                }
                CallBillingNotificationDto content = JSON.parseObject(obj.getString("content"), CallBillingNotificationDto.class);
                //log.info("YunDingPrivacyNotificationListener onMessage content={}", JSON.toJSONString(content));
                if (StringUtils.isBlank(content.getUserData())){
                    continue;
                }
                JSONObject userDataObj = JSON.parseObject(content.getUserData());
                if (!userDataObj.containsKey("promiseId")){
                    continue;
                }
                CallBillingUserDataDto userDataDto = JSON.parseObject(content.getUserData(), CallBillingUserDataDto.class);
                content.setUserDataDto(userDataDto);
                callRecordApplication.receiveCallBilling(content);
            } catch (Exception e) {
                //log.error("YunDingPrivacyNotificationListener onMessage error e", e);
            }
        }
    }
}
