package com.jdh.o2oservice.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.report.service.MedicalReportResultApplication;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.application.support.convert.OpLogApplicationConverter;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.support.operationlog.model.OperationLogEs;
import com.jdh.o2oservice.core.domain.support.operationlog.repository.OperationLogEsRepository;
import com.jdh.o2oservice.export.report.cmd.MedicalReportIndicatorFlushToEsCmd;
import com.jdh.o2oservice.export.support.command.OperationLogCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 21:36
 * @Desc :
 */
@Slf4j
@Service("jdhOperationLogListener")
public class JdhOperationLogListener implements MessageListener {

    @Resource
    OperationLogEsRepository operationLogEsRepository;

    /**
     * onMessage
     *
     * @param messages 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.support.logs}"})
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            try {
                String text = message.getText();
                List<OperationLogCmd> logs = JSON.parseArray(text, OperationLogCmd.class);
                operationLogEsRepository.save(OpLogApplicationConverter.instance.toOperationLogEsList(logs));
            } catch (Exception e) {
                log.error("[JdhOperationLogListener->onMessage],jmq exception!", e);
                throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
            }
        }
    }
}

