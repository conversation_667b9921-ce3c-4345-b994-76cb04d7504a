package com.jdh.o2oservice.listener.settlement;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.settlement.enums.CashStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.WithdrawMsgChangeTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawDetailVo;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.dto.WithdrawChangeEventBody;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/30 9:19 上午
 * @Description:护士提现结果监听
 */
@Slf4j
@Service
public class AngelWithdrawMessageListener implements MessageListener {

    /**
     * jdServiceSettleReadApplication
     */
    @Autowired
    private JdServiceSettleReadApplication serviceSettleReadApplication;

    /**
     * handlerTopicYf
     */
    @Value("${topics.jdhReachStoreConsumer.withdrawChangeEventEventForwardYfbTopic}")
    private String handlerTopicYf;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;
    /**
     * hySettleRpc
     */
    @Autowired
    private HySettleRpc hySettleRpc;


    /**
     * @param list
     * @throws Exception
     */
    @Override
    @JmqListener(id = "jdhReachStoreConsumer",
            topics = {"${topics.jdhReachStoreConsumer.withdrawChangeEventEventTopic}"})
    public void onMessage(List<Message> list) throws Exception {
        log.info("AngelWithdrawMessageListener -> onMessage start");
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (Message message : list) {
            this.processMessage(message);
        }
    }

    /**
     * processMessage
     *
     * @param message 讯息
     */
    private void processMessage(Message message) {
        log.info("AngelWithdrawMessageListener -> processMessage message:{}", JSON.toJSONString(message));
        try {
            WithdrawChangeEventBody eventBody = JSON.parseObject(message.getText(), WithdrawChangeEventBody.class);
            log.info("AngelWithdrawMessageListener -> eventBody:{}", JSON.toJSONString(eventBody));
            //非主账户提现结果处理
            if (Objects.isNull(eventBody) || !WithdrawMsgChangeTypeEnum.WITHDRAW_ACCOUNT_PAY_RESULT.getCode().equals(eventBody.getChangeType())) {
                log.info("AngelWithdrawMessageListener -> 非主账户提现结果处理消息:{}", JSON.toJSONString(eventBody));
                return;
            }
            if(transferToYf(message,eventBody.getAccountId())){
                return;
            }
            //查询提现记录
            AngelSettleQuery settleQuery = new AngelSettleQuery();
            settleQuery.setSettlementNo(eventBody.getDetailId());
            settleQuery.setSettlementType(SettleTypeEnum.EXPEND.getType());
            settleQuery.setQuerySettleDetail(Boolean.FALSE);
            AngelSettlementDto record = serviceSettleReadApplication.querySettlement(settleQuery);
            if (Objects.isNull(record)) {
                log.info("AngelWithdrawMessageListener -> 提现记录不存在:{}", JSON.toJSONString(eventBody));
                return;
            }
            WithdrawDetailVo withdrawDetailVo = hySettleRpc.queryWithdrawRecordDetail(eventBody.getDetailId(),null,eventBody.getAccountId());
            if(Objects.isNull(withdrawDetailVo)){
                return;
            }
            Integer withdrawStatus = withdrawDetailVo.getWithdrawStatus();
            if(withdrawStatus == 3){
                record.setCashStatus(CashStatusEnum.CASH_SUCCESS.getType());
            }else if(withdrawStatus == 4){
                record.setCashStatus(CashStatusEnum.CASH_FAIL.getType());
            }else{
                return;
            }
            record.setReceivedTime(new Date());
            Boolean res = serviceSettleReadApplication.updateCashOutResult(record);
            if (!res) {
                log.info("AngelWithdrawMessageListener -> 更新提现结果:{}", JSON.toJSONString(eventBody));
                return;
            }

        } catch (Exception e) {
            log.error("AngelWithdrawMessageListener -> processMessage error", e);
            throw e;
        }
    }

    /**
     *
     * @param message
     * @param accountId
     * @return
     */
    private boolean transferToYf(Message message,Long accountId) {
        // 判断当前pin是否在转投预发白名单
        if (duccConfig.checkBlankAccountId(String.valueOf(accountId))) {
            log.info("AngelWithdrawMessageListener -> transferToYf, eventBody转投到预发环境. accountId={}",accountId);
            try {
                message.setTopic(handlerTopicYf);
                log.info("AngelWithdrawMessageListener -> transferToYf, eventBody转投到预发环境. message={}", com.jd.fastjson.JSON.toJSONString(message));
                reachStoreProducer.send(message);
            } catch (JMQException e) {
                throw new RuntimeException(e);
            }
            return true;
        }
        return false;
    }


}
