<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd" default-autowire="byName">

    <jsf:server id="jsf" protocol="jsf" threads="1000" threadpool="cached" queuetype="normal" queues="0" port="22000"/>
    <jsf:server id="label_jsf" protocol="jsf" threads="200" threadpool="fixed" queuetype="normal" queues="256" port="22001"/>

    <!--promise 履约gw接口-->
    <jsf:provider id="promiseGwExportJsf"
                  interface="com.jdh.o2oservice.export.promise.PromiseGwExport"
                  alias="${common.provider.alias}"
                  ref="promiseGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--页面视图 gw接口-->
    <jsf:provider id="viaConfigGwExportJsf"
                  interface="com.jdh.o2oservice.export.via.ViaConfigGwExport"
                  alias="${common.provider.alias}"
                  ref="viaConfigGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--B端页面视图 gw接口-->
    <jsf:provider id="bViaConfigGwExport"
                  interface="com.jdh.o2oservice.export.via.BViaConfigGwExport"
                  alias="${common.provider.alias}"
                  ref="bViaConfigGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商家 gw接口-->
    <jsf:provider id="productGwExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductGwExport"
                  alias="${common.provider.alias}"
                  ref="productGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商家 gw接口-->
    <jsf:provider id="providerStoreGwExportJsf"
                  interface="com.jdh.o2oservice.export.provider.ProviderStoreGwExport"
                  alias="${common.provider.alias}"
                  ref="providerStoreGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商家预约 gw接口-->
    <jsf:provider id="providerPromiseGwExportJsf"
                  interface="com.jdh.o2oservice.export.provider.ProviderPromiseGwExport"
                  alias="${common.provider.alias}"
                  ref="providerPromiseGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--患者 gw接口-->
    <jsf:provider id="patientGwExportJsf"
                  interface="com.jdh.o2oservice.export.support.PatientGwExport"
                  alias="${common.provider.alias}"
                  ref="patientGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--触达 gw接口-->
    <jsf:provider id="communicationGwExportJsf"
                  interface="com.jdh.o2oservice.export.support.CommunicationGwExport"
                  alias="${common.provider.alias}"
                  ref="communicationGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 交易 gw接口-->
    <jsf:provider id="tradeGwExportJsf"
                  interface="com.jdh.o2oservice.export.trade.TradeGwExport"
                  alias="${common.provider.alias}"
                  ref="tradeGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 交易 gw接口-->
    <jsf:provider id="tradeJsfExportJsf"
                  interface="com.jdh.o2oservice.export.trade.TradeJsfExport"
                  alias="${common.provider.alias}"
                  ref="tradeJsfExportFacade"
                  server="jsf" cache="false" delay="10000" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 标准指标读接口 -->
    <jsf:provider id="productServiceIndicatorReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceIndicatorReadExport"
                  alias="${common.provider.alias}"
                  ref="productServiceIndicatorReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 标准指标分类读接口 -->
    <jsf:provider id="productIndicatorCategoryReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductIndicatorCategoryReadExport"
                  alias="${common.provider.alias}"
                  ref="productIndicatorCategoryReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 标准指标写接口 -->
    <jsf:provider id="productServiceIndicatorCmdExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceIndicatorCmdExport"
                  alias="${common.provider.alias}"
                  ref="productServiceIndicatorCmdExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 健康服务写接口 -->
    <jsf:provider id="productServiceCmdExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceCmdExport"
                  alias="${common.provider.alias}"
                  ref="productServiceCmdExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>
    <!-- 健康服务读接口 -->
    <jsf:provider id="productServiceReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceReadExport"
                  alias="${common.provider.alias}"
                  ref="productServiceReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- dev 接口-->
    <jsf:provider id="supportDevExportJsf"
                  interface="com.jdh.o2oservice.export.support.SupportDevExport"
                  alias="${common.provider.alias}"
                  ref="supportDevExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 大屏监控数据 接口-->
    <jsf:provider id="omeLargeScreenMonitorJsfExportJsf"
                  interface="com.jdh.o2oservice.export.support.HomeLargeScreenMonitorJsfExport"
                  alias="${common.provider.alias}"
                  ref="homeLargeScreenMonitorJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 用户反馈 接口-->
    <jsf:provider id="userFeedbackGwExportJsf" interface="com.jdh.o2oservice.export.support.UserFeedbackGwExport"
                  alias="${common.provider.alias}" ref="userFeedbackGwExport" server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 文件管理 接口-->
    <jsf:provider id="jmFileGwExportJsf"
                  interface="com.jdh.o2oservice.export.provider.JmFileGwExport"
                  alias="${common.provider.alias}"
                  ref="jmFileGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 派单 gw接口-->
    <jsf:provider id="dispatchGwExportJsf"
                  interface="com.jdh.o2oservice.export.dispatch.DispatchGwExport"
                  alias="${common.provider.alias}"
                  ref="dispatchGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 派单 jsf接口-->
    <jsf:provider id="dispatchJsfExportImplJsf"
                  interface="com.jdh.o2oservice.export.dispatch.DispatchJsfExport"
                  alias="${common.provider.alias}"
                  ref="dispatchJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 派单因子 jsf接口-->
    <jsf:provider id="dispatchCalculationFactorJsfExportJsf"
                  interface="com.jdh.o2oservice.export.dispatch.DispatchCalculationFactorJsfExport"
                  alias="${common.provider.alias}"
                  ref="dispatchCalculationFactorJsfExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 套餐对比服务读接口 -->
    <jsf:provider id="productServiceGoodsReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceGoodsReadExport"
                  alias="${common.provider.alias}"
                  ref="productServiceGoodsReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>
    <!-- 套餐对比服务写接口 -->
    <jsf:provider id="productServiceGoodsCmdExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceGoodsCmdExport"
                  alias="${common.provider.alias}"
                  ref="productServiceGoodsCmdExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者位置服务接口 -->
    <jsf:provider id="angelLocationExportJsf"
                  interface="com.jdh.o2oservice.export.angel.AngelLocationExport"
                  alias="${common.provider.alias}"
                  ref="angelLocationExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者位置 gw接口 -->
    <jsf:provider id="angelLocationGwExportJsf"
                  interface="com.jdh.o2oservice.export.angel.AngelLocationGwExport"
                  alias="${common.provider.alias}"
                  ref="angelLocationGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者 gw接口 -->
    <jsf:provider id="angelGwExport"
                  interface="com.jdh.o2oservice.export.angel.AngelGwExport"
                  alias="${common.provider.alias}"
                  ref="angelGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者 gw接口 -->
    <jsf:provider id="angelActivityGwExport"
                  interface="com.jdh.o2oservice.export.angel.AngelActivityGwExport"
                  alias="${common.provider.alias}"
                  ref="angelActivityGwExportFacadeImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 地图接口 -->
    <jsf:provider id="gisMapExportJsf"
                  interface="com.jdh.o2oservice.export.angel.GisMapExport"
                  alias="${common.provider.alias}"
                  ref="gisMapExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 履约检测单 jsf接口 -->
    <jsf:provider id="medicalPromiseJsfExportJsf"
                  interface="com.jdh.o2oservice.export.medicalpromise.MedicalPromiseJsfExport"
                  alias="${common.provider.alias}"
                  ref="medicalPromiseJsfExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!-- 文件管理color jsf接口 -->
    <jsf:provider id="fileManageGwExportJsf"
                  interface="com.jdh.o2oservice.export.support.FileManageGwExport"
                  alias="${common.provider.alias}"
                  ref="fileManageGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务站查询接口 -->
    <jsf:provider id="stationReadExportServiceJsf"
                  interface="com.jdh.o2oservice.export.angel.StationReadExportService"
                  alias="${common.provider.alias}"
                  ref="stationReadExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!-- 服务者排班日历接口 -->
    <jsf:provider id="angelScheduleGwExportJsf"
                  interface="com.jdh.o2oservice.export.angel.AngelScheduleGwExport"
                  alias="${common.provider.alias}"
                  ref="angelScheduleGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者工单网关接口 -->
    <jsf:provider id="angelPromiseGwExportJsf"
                  interface="com.jdh.o2oservice.export.angelpromise.AngelPromiseGwExport"
                  alias="${common.provider.alias}"
                  ref="angelPromiseGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--报告页 gw接口-->
    <jsf:provider id="medicalReportGwExportJsf"
                  interface="com.jdh.o2oservice.export.report.MedicalReportGwExport"
                  alias="${common.provider.alias}"
                  ref="medicalReportGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--报告页 jsf接口-->
    <jsf:provider id="medicalReportExportJsf"
                  interface="com.jdh.o2oservice.export.report.MedicalReportExport"
                  alias="${common.provider.alias}"
                  ref="medicalReportExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商品项目 jsf接口-->
    <jsf:provider id="productServiceItemReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceItemReadExport"
                  alias="${common.provider.alias}"
                  ref="productServiceItemReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者 C端 我的相关接口 -->
    <jsf:provider id="angelMineGwExportJsf"
                  interface="com.jdh.o2oservice.export.angel.AngelMineGwExport"
                  alias="${common.provider.alias}"
                  ref="angelMineGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 后门接口 -->
    <jsf:provider id="medicalPromiseDevExportJsf"
                  interface="com.jdh.o2oservice.export.medicalpromise.MedicalPromiseDevExport"
                  alias="${common.provider.alias}"
                  ref="medicalPromiseDevExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!-- 地址服务color jsf接口 -->
    <jsf:provider id="addressGwExportJsf"
                  interface="com.jdh.o2oservice.export.support.AddressGwExport"
                  alias="${common.provider.alias}"
                  ref="addressGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 护士结算color jsf接口 -->
    <jsf:provider id="angelSettleReadExportJsf"
                  interface="com.jdh.o2oservice.export.settlement.AngelSettleReadExport"
                  alias="${common.provider.alias}"
                  ref="angelSettleReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>
    <!-- 护士结算color jsf接口 -->
    <jsf:provider id="angelSettleJsfExportJsf"
                  interface="com.jdh.o2oservice.export.settlement.AngelSettleJsfExport"
                  alias="${common.provider.alias}"
                  ref="angelSettleJsfExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>
    <!-- 互医接口 jsf接口 -->
    <jsf:provider id="inspectionSheetJsfExportImplJsf"
                  interface="com.jdh.o2oservice.export.trade.InspectionSheetJsfExport"
                  alias="${common.provider.alias}"
                  ref="inspectionSheetJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 工单读服务 jsf接口 -->
    <jsf:provider id="angelWorkReadGwExportJsf"
                  interface="com.jdh.o2oservice.export.angelpromise.AngelWorkReadGwExport"
                  alias="${common.provider.alias}"
                  ref="angelWorkReadGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!-- 消息盒子 jsf接口 -->
    <jsf:provider id="reachGwExportJsf"
                  interface="com.jdh.o2oservice.export.support.ReachGwExport"
                  alias="${common.provider.alias}"
                  ref="reachGwExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--服务站库存服务-->
    <jsf:provider id="angelStationInventoryExportService"
                  interface="com.jdh.o2oservice.export.angel.AngelStationInventoryExportService"
                  alias="${common.provider.alias}"
                  ref="angelStationInventoryExportServiceImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--账单服务-->
    <jsf:provider id="providerBillExport"
                  interface="com.jdh.o2oservice.export.provider.ProviderBillExport"
                  alias="${common.provider.alias}"
                  ref="providerBillExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 商家域JSF服务-->
    <jsf:provider id="providerPromiseJsfExport"
                  interface="com.jdh.o2oservice.export.provider.ProviderPromiseJsfExport"
                  alias="${common.provider.alias}"
                  ref="providerPromiseJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 履约单工具服务 -->
    <jsf:provider id="jdhPromiseToolServiceJsf"
                  interface="com.jdh.o2oservice.export.ztools.JdhPromiseToolExport"
                  alias="${common.provider.alias}"
                  ref="jdhPromiseToolService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!--费项配置服务-->
    <jsf:provider id="feeConfigGwExport"
                  interface="com.jdh.o2oservice.export.support.FeeConfigGwExport"
                  alias="${common.provider.alias}"
                  ref="feeConfigGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--操作日志服务-->
    <jsf:provider id="operationLogJsfExport"
                  interface="com.jdh.o2oservice.export.support.OperationLogJsfExport"
                  alias="${common.provider.alias}"
                  ref="operationLogJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 商品域 - 解析套餐项目jsf -->
    <jsf:provider id="xfylProductItemServiceJsfExportJsf"
                  interface="com.jdh.o2oservice.export.product.XfylProductItemServiceJsfExport"
                  alias="${common.provider.alias}"
                  ref="xfylProductItemServiceJsfExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>
    <!-- 京麦发品服务接口 -->
    <jsf:provider id="productServiceItemReadForJMExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceItemReadForJMExport"
                  alias="${xfyl.merchant.jm.export.alias}"
                  ref="productServiceItemReadForJMExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${xfyl.merchant.jm.token}" hide="true"/>
    </jsf:provider>

    <!-- pop发品服务接口 -->
    <jsf:provider id="productServiceItemForPopExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductServiceItemForPopExport"
                  alias="${xfyl.merchant.pop.export.alias}"
                  ref="productServiceItemForPopExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${xfyl.merchant.pop.token}" hide="true"/>
    </jsf:provider>

    <!-- 商品域 - 标准项目jsf -->
    <jsf:provider id="productStandardItemJsf"
                  interface="com.jdh.o2oservice.export.product.ProductStandardItemJsfExport"
                  alias="${common.provider.alias}"
                  ref="productStandardItemJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 商品域 - 标准指标jsf -->
    <jsf:provider id="productStandardIndicatorJsf"
                  interface="com.jdh.o2oservice.export.product.ProductStandardIndicatorJsfExport"
                  alias="${common.provider.alias}"
                  ref="productStandardIndicatorJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!-- 支撑域 - 文件相关jsf -->
    <jsf:provider id="fileManageJsfExport"
                  interface="com.jdh.o2oservice.export.support.FileManageJsfExport"
                  alias="${common.provider.alias}"
                  ref="fileManageJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 套餐对比服务读接口 -->
    <jsf:provider id="productBizItemReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductBizItemReadExport"
                  alias="${common.provider.alias}"
                  ref="productBizItemReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 套餐对比服务读接口 -->
    <jsf:provider id="productBizItemCmdExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductBizItemCmdExport"
                  alias="${common.provider.alias}"
                  ref="productBizItemCmdExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 支撑域 - 词典jsf -->
    <jsf:provider id="dictJsfExportJsf"
                  interface="com.jdh.o2oservice.export.support.DictJsfExport"
                  alias="${common.provider.alias}"
                  ref="dictJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 商家商品域，套餐接口 -->
    <jsf:provider id="productProgramReadExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductProgramReadExport"
                  alias="${common.provider.alias}"
                  ref="productProgramReadExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 商家商品域，套餐接口 -->
    <jsf:provider id="productProgramCmdExportJsf"
                  interface="com.jdh.o2oservice.export.product.ProductProgramCmdExport"
                  alias="${common.provider.alias}"
                  ref="productProgramCmdExport"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 派单工具接口服务 -->
    <jsf:provider id="dispatchToolsExportServiceJsf"
                  interface="com.jdh.o2oservice.export.ztools.DispatchToolsExportService"
                  alias="${common.provider.alias}"
                  ref="dispatchToolsExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 工单接口服务 -->
    <jsf:provider id="jdhAngelWorkToolsServiceJsf"
                  interface="com.jdh.o2oservice.export.ztools.JdhAngelWorkToolsService"
                  alias="${common.provider.alias}"
                  ref="jdhAngelWorkToolsService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 支撑域 - 患者相关jsf -->
    <jsf:provider id="patientJsfExport"
                  interface="com.jdh.o2oservice.export.support.PatientJsfExport"
                  alias="${common.provider.alias}"
                  ref="patientJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- JDH Promise -->
    <jsf:provider id="promiseJsfExport"
                  interface="com.jdh.o2oservice.export.promise.PromiseJsfExport"
                  alias="${common.provider.alias}"
                  ref="promiseJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 内容接口服务 -->
    <jsf:provider id="contentGwExport"
                  interface="com.jdh.o2oservice.export.content.ContentGwExport"
                  alias="${common.provider.alias}"
                  ref="contentGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--promise 报告后门接口-->
    <jsf:provider id="medicalReportDevExport"
                  interface="com.jdh.o2oservice.export.report.MedicalReportDevExport"
                  alias="${common.provider.alias}"
                  ref="medicalReportDevExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--promise 报告指标jsf接口-->
    <jsf:provider id="medicalReportResultExport"
                  interface="com.jdh.o2oservice.export.report.MedicalReportResultExport"
                  alias="${common.provider.alias}"
                  ref="medicalReportResultExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--promise 商家门店接口-->
    <jsf:provider id="providerStoreJsfExport"
                  interface="com.jdh.o2oservice.export.provider.ProviderStoreJsfExport"
                  alias="${common.provider.alias}"
                  ref="providerStoreJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--promise 服务站写相关接口-->
    <jsf:provider id="angelStationSaveExportJsfService"
                  interface="com.jdh.o2oservice.export.angel.AngelStationSaveExportService"
                  alias="${common.provider.alias}"
                  ref="angelStationSaveExportServiceImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!--顺丰接口-->
    <jsf:provider id="shunFengExport"
                  interface="com.jdh.o2oservice.export.angel.ShunFengExport"
                  alias="${common.provider.alias}"
                  ref="shunFengExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>


    <!--外呼接口-->
    <jsf:provider id="callGwExport"
                  interface="com.jdh.o2oservice.export.support.CallGwExport"
                  alias="${common.provider.alias}"
                  ref="callGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--外呼jsf接口-->
    <jsf:provider id="callJsfExport"
                  interface="com.jdh.o2oservice.export.support.CallJsfExport"
                  alias="${common.provider.alias}"
                  ref="callJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--运营端视图jsf接口-->
    <jsf:provider id="manViaJsfExport"
                  interface="com.jdh.o2oservice.export.support.ManViaJsfExport"
                  alias="${common.provider.alias}"
                  ref="manViaJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--运营端视图jsf接口-->
    <jsf:provider id="productJsfExport"
                  interface="com.jdh.o2oservice.export.product.ProductJsfExport"
                  alias="${common.provider.alias}"
                  ref="productJsfExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--商品标签-->
    <jsf:provider id="productLabelServiceReadExport"
                  interface="com.jdh.o2oservice.export.product.ProductLabelServiceReadExport"
                  alias="${common.provider.alias}"
                  ref="productLabelServiceReadExportImpl"
                  server="label_jsf" cache="false" delay="10000" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <jsf:provider id="dataMigrationExport"
                  interface="com.jdh.o2oservice.export.datamigration.DataMigrationExport"
                  alias="${common.provider.alias}"
                  ref="dataMigrationExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 触达jsf接口-->
    <jsf:provider id="medPromiseOpenJsf"
                  interface="com.jdh.o2oservice.export.medicalpromise.MedPromiseOpenJsfExport"
                  alias="${common.provider.alias}"
                  ref="medPromiseOpenJsfExport"
                  server="jsf" cache="false" delay="10000" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 触达jsf接口-->
    <jsf:provider id="reachJsfExport"
                  interface="com.jdh.o2oservice.export.support.ReachJsfExport"
                  alias="${common.provider.alias}"
                  ref="reachJsfExportImpl"
                  server="jsf" cache="false" delay="10000" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 订单工具类-->
    <jsf:provider id="tradeDevJsf"
                  interface="com.jdh.o2oservice.export.trade.TradeDevJsfExport"
                  alias="${common.provider.alias}"
                  ref="tradeDevJsfExport"
                  server="jsf" cache="false" delay="10000" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 护士生态接口 -->
    <jsf:provider id="angelEcologyExportService"
                  interface="com.jdh.o2oservice.export.angel.AngelEcologyExportService"
                  alias="${common.provider.alias}"
                  ref="angelEcologyExportServiceImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 护士生态网关接口 -->
    <jsf:provider id="angelEcologyGwService"
                  interface="com.jdh.o2oservice.export.angel.AngelEcologyGwService"
                  alias="${common.provider.alias}"
                  ref="angelEcologyExportServiceImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者任务接口 -->
    <jsf:provider id="angelJobExportService"
                  interface="com.jdh.o2oservice.export.angel.AngelJobExportService"
                  alias="${common.provider.alias}"
                  ref="angelJobExportServiceImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!--导出工具-->
    <jsf:provider id="jdhExportToolsExportServiceJsf"
                  interface="com.jdh.o2oservice.export.support.JdhExportToolsExportService"
                  alias="${common.provider.alias}"
                  ref="jdhExportToolsExportService"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 服务者服务记录接口 -->
    <jsf:provider id="angelServiceRecordGwExport"
                  interface="com.jdh.o2oservice.export.angelpromise.AngelServiceRecordGwExport"
                  alias="${common.provider.alias}"
                  ref="angelServiceRecordGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 查询工单及项目信息 -->
    <jsf:provider id="angelWorkReadExport"
                  interface="com.jdh.o2oservice.export.angelpromise.AngelWorkReadExport"
                  alias="${common.provider.alias}"
                  ref="angelWorkReadExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <!-- 查询工单及项目信息 -->
    <jsf:provider id="dictGwExport"
                  interface="com.jdh.o2oservice.export.support.DictGwExport"
                  alias="${common.provider.alias}"
                  ref="dictGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <jsf:provider id="soundExtGwExport"
                  interface="com.jdh.o2oservice.export.support.SoundExtGwExport"
                  alias="${common.provider.alias}"
                  ref="soundExtGwExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <jsf:provider id="riskAssessmentExport"
                  interface="com.jdh.o2oservice.export.riskassessment.RiskAssessmentExport"
                  alias="${common.provider.alias}"
                  ref="riskAssessmentExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <jsf:provider id="medicalReportAuditExport"
                  interface="com.jdh.o2oservice.export.report.MedicalReportAuditExport"
                  alias="${common.provider.alias}"
                  ref="medicalReportAuditExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

    <jsf:provider id="medicalReportDataExport"
                  interface="com.jdh.o2oservice.export.report.MedicalReportDataExport"
                  alias="${common.provider.alias}"
                  ref="medicalReportDataExportImpl"
                  server="jsf" cache="false" serialization="hessian">
        <jsf:parameter key="token" value="${common.provider.token}" hide="true"/>
    </jsf:provider>

</beans>