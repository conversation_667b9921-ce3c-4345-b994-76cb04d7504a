package com.jdh.o2oservice.application.medicalpromise;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.*;
import com.jdh.o2oservice.export.medicalpromise.query.*;
import com.jdh.o2oservice.export.provider.dto.ExportDataDTO;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.report.cmd.UpdateMedPromiseStationCmd;
import com.jdh.o2oservice.export.report.dto.QuickStructReportVerifyResultDTO;
import com.jdh.o2oservice.export.report.dto.StructQuickReportContentDTO;

import java.util.List;
import java.util.Set;

/**
 * @Description: 履约检测单应用层
 * @Interface: MedicalPromiseApplication
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
public interface MedicalPromiseApplication {

    /**
     * 批量创建检测单
     * @param batchCreateCmd
     * @return
     */
    Boolean createMedicalPromise(MedicalPromiseCreateCmd batchCreateCmd);

    /**
     * 创建质控检测单
     * @param createCmd
     * @return
     */
    Boolean createQualityMedicalPromise(MedicalPromiseQualityCreateCmd createCmd);

    /**
     * 实验室派发
     * @param medicalPromiseDispatchCmd
     * @return
     */
    Boolean storeDisPatch(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd);

    /**
     * 根据条件分页查询检测单
     * @param medicalPromiseListRequest
     * @return
     */
    PageDto<MedicalPromiseDTO> queryMedicalPromisePage(MedicalPromiseListRequest medicalPromiseListRequest);

    PageDto<MedicalPromiseDTO> queryQualityMedicalPromisePage(MedicalPromiseListRequest medicalPromiseListRequest);

    /**
     * 检测单状态回传
     * @param medicalPromiseCallbackCmd
     * @return
     */
    MedicalPromiseCallbackResultDTO medicalPromiseCallBack(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd);

    /**
     * 根据条件查询检测单
     * @param medicalPromiseListRequest
     * @return
     */
    List<MedicalPromiseDTO> queryMedicalPromiseList(MedicalPromiseListRequest medicalPromiseListRequest);

    /**
     * 查询履约检测单绑定情况
     * @return
     */
    MedicalPromiseBindDTO queryMedicalPromiseBindCondition(MedicalPromiseBindRequest medicalPromiseBindRequest);

    /**
     * 批量绑定样本条码
     * @param cmd
     * @return
     */
    Boolean batchBindSpecimenCode(MedicalPromiseBindSpecimenCodeCmd cmd);
    /**
     * 推送检测单结构化报告
     * @param medicalPromiseReportCmd
     * @return
     */
    MedicalPromisePushReportDTO pushMedicalPromiseReport(MedicalPromiseReportCmd medicalPromiseReportCmd);

    /**
     * 检测单状态更新
     * @param medicalPromiseStatusCmd
     * @return
     */
    List<MedicalPromiseDTO> freezeMedicalPromiseStatusBatch(MedicalPromiseStatusCmd medicalPromiseStatusCmd);

    /**
     * 检测单批量作废，返回promiseId下还生效的检测单
     * @param medicalPromiseBatchInvalidCmd
     * @return
     */
    List<MedicalPromiseDTO> invalidMedicalPromiseBatch(MedicalPromiseBatchInvalidCmd medicalPromiseBatchInvalidCmd);

    /**
     * 提交检测单
     * @param medicalPromiseSubmitCmd m
     * @return r
     */
    Boolean submitMedicalPromiseToStation(MedicalPromiseSubmitCmd medicalPromiseSubmitCmd);

    /**
     * 直接调三方API,提交预约信息
     * @param directCallMedicalPromiseSubmitCmd
     * @return
     */
    Boolean directCallMedicalPromiseToStation(DirectCallMedicalPromiseSubmitCmd directCallMedicalPromiseSubmitCmd);

    /**
     * 提交检测单
     * @param batchMedicalPromiseSubmitCmd
     * @return r
     */
    Boolean batchSubmitMedicalPromiseToStation(BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd);

    /**
     * 手动创建检测单
     * @param medicalPromiseHandCmd
     * @return
     */
    Boolean handCreateMedicalPromise(MedicalPromiseHandCmd medicalPromiseHandCmd);

    /**
     * 查询检测单信息
     * @param medicalPromiseRequest
     * @return
     */
    MedicalPromiseDTO queryMedicalPromise(MedicalPromiseRequest medicalPromiseRequest);

    /**
     * 查询检测单信息
     * @param medicalPromiseRequest
     * @return
     */
    List<MedicalPromiseDTO> queryMedicalPromiseList(MedicalPromiseRequest medicalPromiseRequest);
    /**
     * 查询履约单下报告情况
     * @param promiseId
     * @return
     */
    MedicalPromiseReportCheckDTO queryReportStatus(Long promiseId);

    /**
     * 推送检测单结构化报告
     * @param medicalPromiseReportCmd
     * @return
     */
    MedicalPromisePushReportDTO pushMedicalPromiseReportInfo(MedicalPromiseReportCmd medicalPromiseReportCmd);

    /**
     * 到检
     */
    Boolean medicalPromiseCheck(MedPromiseCallbackStatusCmd medPromiseCallbackStatusCmd);

    /**
     *
     * @return
     */
    Boolean sendSettlementMessage(MedPromiseSettlementCmd medPromiseSettlementCmd);

    /**
     * 履约单报告全部已出
     * @return
     */
    Boolean medicalPromiseAllGenerate(MedicalPromiseAllGenerateCmd medicalPromiseAllGenerateCmd);

    /**
     * 实验室结算推ebs
     * @return
     */
    Boolean settlementToEbs(MedPromiseToEbsCmd medPromiseToEbsCmd);

    /**
     * 服务者检测单完成
     * @param medicalPromiseStatusCmd
     * @return
     */
    Boolean angelMedicalPromiseFinish(MedicalPromiseStatusCmd medicalPromiseStatusCmd);

    /**
     * 分页查询检测单综合数据
     * @param labQueryMedPromisePageRequest
     * @return
     */
    PageDto<MedicalPromiseFullDTO> labQueryMedicalPromisePage(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);

    /**
     * 更新结算状态
     * @param medicalPromiseSettleStatusCmd
     * @return
     */
    Integer updateSettleSatus(MedicalPromiseSettleStatusCmd medicalPromiseSettleStatusCmd);

    /**
     * 导出门店下检测单
     * @param labQueryMedPromisePageRequest
     * @return
     */
    ExportDataDTO exportMedicalPromiseList(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);

    /**
     * 重置报告上传状态
     * @param
     */
    void resetReportStatus(ResetReportStatusCmd cmd);

    /**
     * 更新检测单子状态
     * @param
     */
    void updateMedPromiseSubStatus(MedPromiseSubStatusCmd cmd);


    /**
     * 重置条码
     * @param cmd
     */
    void resetSpecimenCode(ResetSpecimenCodeCmd cmd);

    /**
     * 生成序号
     * @param medicalPromiseSerialCmd
     * @return
     */
    String generalSerialNum(MedicalPromiseSerialCmd medicalPromiseSerialCmd);

    /**
     * 预约单人纬度完成事件->触发实验室结算
     *
     * @param cmd
     * @return
     */
    Boolean sendPatientSettlementMessage(MedPromiseSettlementCmd cmd);

    /**
     * 同步报告中心，将报告传给报告中心侧，同时返回报告reportid写入数据库
     * @param medicalReportSaveCmd
     */
    Boolean syncMedicalReportToReportCenter(MedicalReportSaveCmd medicalReportSaveCmd);

    /**
     * 检查是否存在进行中的检测单
     *
     * @param receiverId
     * @param patientIdSet
     * @return
     */
    Boolean checkProgressMedPromise(String receiverId, Set<String> patientIdSet);

    /**
     * 校验结构化报告
     *
     * @param reportResult reportResult
     * @return dto dto
     */
    QuickStructReportVerifyResultDTO verifyStructQuickReport(List<MedicalPromiseReportVerifyRequest> reportResult);

    /**
     * 从原始oss解析获取结构化报告
     * @return
     */
    StructQuickReportContentDTO getStructQuickReportFromSourceOss(Long medicalPromiseId);

    /**
     * 从原始oss解析并重新生成结构化报告
     * @return
     */
    String anewStructQuickReportFromSourceOss(Long medicalPromiseId);

    /**
     * 更新检测单时效信息
     *
     * @param medicalPromiseEtaCmd medicalPromiseEtaCmd
     * @return {@link Boolean }
     */
    Boolean updateMedicalPromiseEta(UpdateMedicalPromiseEtaCmd medicalPromiseEtaCmd);

    /**
     * 更新检测单实验室信息
     * @param updateMedPromiseStationCmd
     * @return
     */
    Boolean updateMedicalPromiseStation(UpdateMedPromiseStationCmd updateMedPromiseStationCmd);


    Integer listMedicalPromiseBySpecimenCode(List<String> specimenCodes);

    /**
     * 分页查询检测单综合数据
     * @param labQueryMedPromisePageRequest
     * @return
     */
    MedicalPromiseFullDTO labQueryMedicalPromiseDetail(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest);


    /**
     * AI体检报告解读
     * @param medicalPromiseId
     */
    void aiRead(Long medicalPromiseId);

    /**
     * 异常操作
     * @return
     */
    Boolean sampleAnomalyOperate(AnomalyOperateCmd anomalyOperateCmd);
}
