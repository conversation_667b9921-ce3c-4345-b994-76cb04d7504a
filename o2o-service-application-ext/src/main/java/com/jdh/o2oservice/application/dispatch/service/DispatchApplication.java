package com.jdh.o2oservice.application.dispatch.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.*;
import com.jdh.o2oservice.export.dispatch.query.*;

import java.util.List;

/**
 * @ClassName DispatchApplication
 * @Description
 * <AUTHOR>
 * @Date 2024/4/17 18:37
 **/
public interface DispatchApplication {

    /**
     * 提交派单
     * @param cmd
     * @return
     */
    Boolean submitDispatch(SubmitDispatchCmd cmd);

    /**
     * 修改预约时间派单
     * @param cmd
     * @return
     */
    Boolean modifyServiceDateDispatch(SubmitDispatchCmd cmd);

    /**
     * 发起服务者派单
     * @param cmd
     * @return
     */
    Boolean angelDispatch(AngelDispatchCmd cmd);

    /**
     * 冻结派单任务
     * @param cmd
     * @return
     */
    DispatchFreezeStatusDto freezeDispatch(FreezeDispatchCmd cmd);

    /**
     * 取消派单
     * @return
     */
    DispatchInvalidStatusDto cancelDispatch(CancelDispatchCmd cmd);

    /**
     * 作废派单
     * @return
     */
    DispatchInvalidStatusDto invalidDispatch(CancelDispatchCmd cmd);

    /**
     * 重新派单
     * @return
     */
    Boolean reDispatch(ReDispatchCmd cmd);

    /**
     * 指定派单
     * @param cmd
     * @return
     */
    DispatchTargetStatusDto targetDispatch(TargetDispatchCmd cmd);

    /**
     * 接单护士转单
     * @param cmd
     * @return
     */
    DispatchTargetStatusDto receiveTransferDispatch(TargetDispatchCmd cmd);

    /**
     * 派单信息回调
     * @param cmd
     * @return
     */
    DispatchCallbackDto callBack(DispatchCallbackCmd cmd);

    /**
     * 互医派单信息回调
     * @param cmd
     * @return
     */
    DispatchCallbackDto nethpTriageDispatchCallBack(DispatchCallbackCmd cmd);

    /**
     * 服务者接单
     * @param cmd
     * @return
     */
    DispatchCallbackDto angelReceive(DispatchCallbackCmd cmd);

    /**
     * 派单回收
     * @param cmd
     * @return
     */
    Boolean recoverDispatch(RecoverDispatchCmd cmd);

    /**
     * 查询待接单总览
     * @param request
     * @return
     */
    JdhDispatchWaitingReceiveOverview queryDispatchWaitingReceiveOverview(DispatchWaitingReceiveOverviewRequest request);

    /**
     * 查询派单明细列表
     * @param request
     * @return
     */
    List<JdhDispatchDetailDto> queryDispatchWaitingReceiveList(DispatchWaitingReceiveListRequest request);

    /**
     * 查询派单明细
     * @param request
     * @return
     */
    AngelWorkDto queryDispatchWaitingReceiveDetail(DispatchWaitingReceiveDetailRequest request);

    /**
     * 服务者派单操作
     * @param cmd
     * @return
     */
    AngelDispatchOperationDto angelDispatchOperate(AngelDispatchOperationCmd cmd);

    /**
     * 服务者操作接单按钮
     * @param cmd
     * @return
     */
    AngelDispatchSwitchDto angelDispatchSwitchOperate(AngelDispatchSwitchCmd cmd);

    /**
     * 查询围栏命中的护士ID列表
     * @param request
     * @return
     */
    LocationSelectedDTO getSelectedByLocation(LocationSelectedRequest request);

    /**
     * 查询派单历史记录列表
     * @param request
     * @return
     */
    List<JdhDispatchHistoryDto> queryDispatchHistoryListByPromiseId(DispatchHistoryListRequest request);


    /**
     * 查询派单任务详情
     * @param request
     * @return
     */
    JdhDispatchDto queryDispatchInfo(DispatchQueryRequest request);

    /**
     * 派单拉完成
     * @return
     */
    Boolean dispatchComplete(DispatchCompleteCmd cmd);

    /**
     * 执行派单流程
     * @param cmd
     * @return
     */
    Boolean dispatchFlowExecuteHandle(AngelDispatchCmd cmd);

    /**
     * 处理互医侧派单
     * @param cmd
     * @return
     */
    Boolean handleNewNethpDispatch(DispatchNewNethpHandleCmd cmd);

    /**
     * 查询派单明细列表
     * @param request
     * @return
     */
    PageDto<JdhDispatchForManDTO> queryDispatchListForMan(DispatchDetailForManRequest request);


    /**
     * 护士接单前提醒
     *
     * @param cmd
     * @return
     */
    AngelDispatchOperationDto angelDispatchTips(AngelDispatchOperationCmd cmd);
}