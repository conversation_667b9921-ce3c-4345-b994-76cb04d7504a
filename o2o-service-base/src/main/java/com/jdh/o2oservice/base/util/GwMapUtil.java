package com.jdh.o2oservice.base.util;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.GwConstants;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.gw.ColorGwRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 网关map转换工具类
 *
 * <AUTHOR>
 * @date 2019-11-13 14:19
 */
@Slf4j
public class GwMapUtil {

    /**
     * 参数转换
     *
     * @param gwMap
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T extends Object> T convertToParam(Map<?, ?> gwMap, Class<T> clazz) {
        ColorGwRequest colorGwRequest = new ColorGwRequest(gwMap);
        String body = colorGwRequest.getBody();
        T domain = null;
        if (StringUtil.isBlank(body)) {
            log.info("GwMapUtil -> convertToParam param error body is null, gwMap={}, clazz={}", JSON.toJSONString(gwMap), clazz);
            domain = ReflectUtil.newInstance(clazz);
        }else{
            domain = colorGwRequest.toDomain(clazz);
        }
        String pin = GwMapUtil.getPin(gwMap);
        String clientIp = GwMapUtil.getIP(gwMap);
        String uuid = GwMapUtil.getUuid(gwMap);
        String agent = GwMapUtil.getAgent(gwMap);
        String appKey = GwMapUtil.getAgent(gwMap);
        String location = GwMapUtil.getLocation(gwMap);
        String eid = GwMapUtil.getEid(gwMap);

        if (StringUtils.isNotBlank(agent)){
            // 获取京东外卖配置
            JSONObject jdWmObj = JSON.parseObject(SpringUtil.getBean(DuccConfig.class).getJdWmConfig());
            List<String> jdWmAppNameList = JSON.parseArray(jdWmObj.getString("appNameList"), String.class);
            for (String jdWmAppName : jdWmAppNameList) {
                if (agent.contains(jdWmAppName)) {
                    appKey = jdWmObj.getString("jdWmAppKey");
                    if(Objects.nonNull(domain)){
                        putEnvTypeField(domain, EnvTypeEnum.JD_WM_APP.getCode());
                    }
                    break;
                }
            }
        }

        if(StrUtil.isNotEmpty(pin) && Objects.nonNull(domain)){
            putUserPinField(domain,pin);
        }
        if(StrUtil.isNotEmpty(clientIp) && Objects.nonNull(domain)){
            putClientIpField(domain,clientIp);
        }
        if(StrUtil.isNotEmpty(uuid) && Objects.nonNull(domain)){
            putUuidField(domain,uuid);
        }
        if(StrUtil.isNotEmpty(agent) && Objects.nonNull(domain)){
            putAgentField(domain,agent);
        }
        if(StrUtil.isNotEmpty(appKey) && Objects.nonNull(domain)){
            putAppKeyField(domain,appKey);
        }
        if(StrUtil.isNotEmpty(location) && Objects.nonNull(domain)){
            putLocationField(domain,location);
        }
        if(StrUtil.isNotEmpty(eid) && Objects.nonNull(domain)){
            putEidField(domain,eid);
        }
        return domain;
    }

    /**
     * 参数转换
     *
     * @return
     */
    public static Map<String, Object> parseBody(Map<?, ?> gwMap) {
        ColorGwRequest colorGwRequest = new ColorGwRequest(gwMap);
        String body = colorGwRequest.getBody();
        Map<String, Object> params;
        if (StringUtils.isNoneBlank(body)) {
            params = JSON.parseObject(body);
            log.info("GwMapUtil -> parseBody");
        }else{
            params = new HashMap<>();
        }
        String pin = GwMapUtil.getPin(gwMap);
        if(StrUtil.isNotEmpty(pin)){
            params.put("userPin", pin);
        }
        return params;
    }

    /**
     * 默认塞入userPin，字段名必须为userPin
     *
     * @param domain domain
     * @param pin    pin
     */
    private static <T> void putUserPinField(T domain,String pin){
        try {
            ReflectUtil.invoke(domain,"setUserPin",pin);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putUserPinField error",throwable);
        }
    }

    private static <T> void putClientIpField(T domain, String clientIp){
        try {
            ReflectUtil.invoke(domain,"setClientIp",clientIp);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putClientIpField error",throwable);
        }
    }

    private static <T> void putUuidField(T domain, String uuid){
        try {
            ReflectUtil.invoke(domain,"setUuid",uuid);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putUuidField error",throwable);
        }
    }

    private static <T> void putAppKeyField(T domain, String appKey){
        try {
            ReflectUtil.invoke(domain,"setAppKey",appKey);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putAppKeyField error",throwable);
        }
    }

    private static <T> void putAgentField(T domain, String agent){
        try {
            ReflectUtil.invoke(domain,"setAgent",agent);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putAgentField error",throwable);
        }
    }

    private static <T> void putEnvTypeField(T domain, String envType){
        try {
            ReflectUtil.invoke(domain,"setEnvType",envType);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putEnvTypeField error",throwable);
        }
    }

    private static <T> void putEidField(T domain, String eid){
        try {
            ReflectUtil.invoke(domain,"setEid",eid);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putEidField error",throwable);
        }
    }

    private static <T> void putLocationField(T domain, String location){
        try {
            ReflectUtil.invoke(domain,"setLocation",location);
        }catch (Throwable throwable){
            //log.error("GwMapUtil putLocationField error",throwable);
        }
    }


    /**
     * @param request
     * @return
     */
    public static String getPin(Map<?, ?> request) {
        if (null == request) {
            return null;
        }
        Object val = request.get(GwConstants.PIN);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getClientVersion(Map<?, ?> request) {
        if (null == request) {
            return null;
        }
        Object val = request.get(GwConstants.CLIENT_VERSION);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * 校验pin是否登录
     *
     * @param map
     * @return
     */
    public static void checkGwPin(Map<?, ?> map) {
        //获取pin
        String pin = getPin(map);
        //校验pin
        checkPin(pin);
    }

    /**
     * 校验pin是否登录
     *
     * @param userPin
     * @return
     */
    public static void checkPin(String userPin) {
        if (StringUtil.isBlank(userPin)) {
            log.info("GwMapUtil -> checkPin 用户未登录，请先登录, userPin={}", userPin);
            throw new BusinessException(BusinessErrorCode.UNKNOWN_USER_LOGIN);
        }
    }


    /**
     * @param request
     * @return
     */
    public static String getIP(Map<?, ?> request) {
        Object val = request.get(GwConstants.IP);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getUuid(Map<?, ?> request) {
        Object val = request.get(GwConstants.Uuid);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getAppKey(Map<?, ?> request) {
        Object val = request.get(GwConstants.X_APP_KEY);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getAgent(Map<?, ?> request) {
        Object val = request.get(GwConstants.AGENT);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getLocation(Map<?, ?> request) {
        Object val = request.get(GwConstants.LOCATION);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

    /**
     * @param request
     * @return
     */
    public static String getEid(Map<?, ?> request) {
        Object val = request.get(GwConstants.EID);
        if (val == null) {
            return null;
        } else {
            return String.valueOf(val);
        }
    }

}
