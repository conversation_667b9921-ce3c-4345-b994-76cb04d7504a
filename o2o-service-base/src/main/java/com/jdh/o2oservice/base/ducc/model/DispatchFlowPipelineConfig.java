package com.jdh.o2oservice.base.ducc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName DispatchFlowPipeline
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 14:35
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchFlowPipelineConfig {

    /**
     * 派单流程名称
     */
    private String flowPipelineName;

    /**
     * 派单轮次配置列表
     */
    List<DispatchRoundConfig> dispatchRoundConfigList;
}