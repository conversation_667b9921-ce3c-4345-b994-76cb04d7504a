package com.jdh.o2oservice.base.ducc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.Configuration;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.model.*;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.ducc.model.datamigration.BigTableConfig;
import com.jdh.o2oservice.base.ducc.model.datamigration.ExecutorSqlBo;
import com.jdh.o2oservice.base.ducc.model.dispatch.DispatchMultiRoundsConfig;
import com.jdh.o2oservice.base.ducc.model.dispatch.DispatchReceiveReStationDisConfig;
import com.jdh.o2oservice.base.ducc.model.fee.*;
import com.jdh.o2oservice.base.ducc.model.jdlogistics.JdLogisticsConfig;
import com.jdh.o2oservice.base.ducc.model.price.PricingServiceFeeConfig;
import com.jdh.o2oservice.base.ducc.model.price.PricingServiceFormula;
import com.jdh.o2oservice.base.ducc.model.report.VerifyReportRootConfig;
import com.jdh.o2oservice.base.ducc.model.settlement.JdhSettlementEbsConfig;
import com.jdh.o2oservice.base.ducc.model.shunfeng.ShunFengConfig;
import com.jdh.o2oservice.base.event.EventConsumerFailBackConfig;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * DuccConfig 配置中心
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Data
@Slf4j
@Component
public class DuccConfig {

    /**
     * 服务时间配置
     */
    @LafValue("promiseServiceStartTimeDescConfig")
    private String promiseServiceStartTimeDescConfig;

    /**
     * 采样扩增曲线数据过滤的指标名称列表
     */
    @Value("${filter_indicator_name:内标}")
    private String filterIndicatorName;

    /**
     * 采样楼层报告出具按钮文案配置
     */
    @Value("${material_floor_report_issue_button_content_config}")
    private String materialFloorReportIssueButtonContentConfig;

    /**
     * 实验室主图默认值
     */
    @Value("${default_store_icon_image:https://img13.360buyimg.com/imagetools/jfs/t1/315069/21/25007/76484/68995f81F4c205d2c/a9c65a5b43419814.png}")
    private String defaultStoreIconImage;

    /**
     * 样本检测配置
     */
    @JsonConverter
    @LafValue("materialInspectionConfigMapping")
    private Map<String, MaterialInspectionConfig> materialInspectionConfigMapping;

    /**
     * 通用样本检测进度条配置
     */
    @JsonConverter
    @LafValue("customMaterialProgressBarList")
    private List<CustomMaterialProgressBarMapping> customMaterialProgressBarList;

    /**
     * 预警的 code和ump集合
     */
    @JsonConverter
    @LafValue("excludeAlarmCode")
    private Map<String, List<String>> excludeAlarmCode;

    /**
     * 触达事件规则映射
     */
    @JsonConverter
    @LafValue("reachEventRuleMapping")
    private Map<String, ReachRuleEventDuccMapping> reachEventRuleMapping = Maps.newHashMap();

    /**
     * 触达规则
     */
    @JsonConverter
    @LafValue("reachRuleSetMapping")
    private Map<String, List<ReachRuleDuccItem>> reachRuleSetMapping = Maps.newHashMap();

    /**
     * 触达模板
     */
    @JsonConverter
    @LafValue("reachTemplateMapping")
    private Map<String, ReachTemplateDuccMapping> reachTemplateDuccMapping = Maps.newHashMap();

    /**
     * 订单MQ每秒限流开关
     */
    @LafValue("order.no.split.onOff")
    private Boolean orderNoSplitOnOff;

    /**
     * 每秒限流次数
     */
    @LafValue("order.limit.num")
    private Integer orderLimitNum;

    /**
     * blankPin
     */
    @JsonConverter
    @LafValue("blankPin")
    private String blankPin;

    /**
     * blankAccountId
     */
    @JsonConverter
    @LafValue("blankAccountId")
    private String blankAccountId;
    /**
     * 状态 0 未启用 1 启用
     */
    @LafValue("blankStatus")
    private String blankStatus;

    /**
     * 订单信息推送shopId
     */
    @LafValue("pushOrderShopId")
    private String pushOrderShopId;

    /**
     * ducc resource区分
     */
    @LafValue("common")
    private Configuration commonConfiguration;

    /**
     * ducc resource区分
     */
    @LafValue("reach")
    private Configuration reachConfiguration;

    /**
     * ducc resource区分
     */
    @LafValue("fee_config")
    private Configuration feeConfigConfiguration;

    /**
     * 垂直身份编码
     */
    @LafValue("businessVerticalCodeConfig")
    private String businessVerticalCodeConfig;


    @JsonConverter
    @LafValue("order.listener.sku.cid.filter")
    private Map<String, List<Integer>> orderListenerSkuCidFilter;

    @JsonConverter
    @LafValue("privacyNumberRuleMapping")
    private List<PrivacyNumberDuccConfig> privacyNumberRuleMapping;

    /**
     * 商品属性配置
     */
    @JsonConverter
    @LafValue("skuServiceAtrrConfig")
    private Map<String, SkuServiceIndicatorMapping> skuServiceAtrrConfig = Maps.newHashMap();

    /**
     * 商品属性过期时间
     */
    @LafValue("skuServiceExpireTime")
    private Integer skuServiceExpireTime;

    /**
     * 垂直业务方在交易域中的用户行为
     * identity_domain_map 和  identity_user_action_map 可以合并成一个，但是经过考虑合成一个嵌套map结构过于复杂，暂不考虑
     *  key： 业务身份（这个之后可能回扩展）
     *  value： 在交易域中可执行的用户行为
     */
    @LafValue("identity_user_action_map")
    @JsonConverter
    private Map<String, List<String>> identityUserActionMap;

    /**
     * 垂直业务方在交易域中需要打的sendPay
     */
    @LafValue("identity_sendPay")
    @JsonConverter
    private Map<String, Map<String, String>> identitySendPayMap;

    /**
     * 骑手上门检测派发实验室规则顺序
     */
    @LafValue("knightHomeCheckStoreDispatchRuleKey")
    @JsonConverter
    private List<String> knightHomeCheckStoreDispatchRuleKey;

    /**
     * 时段费 配置
     */
    @LafValue("time_period_fee_config")
    @JsonConverter
    private String timePeriodFeeConfig;

    /**
     * 时段费配置
     */
    private Map<String, TimePeriodFeeConfig> timePeriodFeeConfigMap;

    /**
     * 上门费用 配置
     */
    @LafValue("home_visit_fee_config")
    @JsonConverter
    private String homeVisitFeeConfig;

    /**
     * 上门费用 配置
     */
    private Map<String, Map<String,HomeVisitFeeConfig>> homeVisitFeeConfigMap;

    /**
     * 动态 - 费项 配置
     */
    @LafValue("dynamic_fee_config")
    private String dynamicFeeConfigStr;

    private List<DynamicFee> dynamicFeeConfig;

    /**
     * 减免服务费 sku白名单 配置
     */
    @LafValue("sku_free_fee_config")
    @JsonConverter
    private Set<String> skuFreeFeeList;
    /**
     * 费项配置字段配置
     */
    @LafValue("fee_config_dict_config")
    @JsonConverter
    private String feeConfigDictConfig;
       /**
     * 费项配置数据源切换开关
     */
    @LafValue("fee_config_data_source_switch")
    private Boolean feeConfigDataSourceSwitch;

    /**
     * 算费接口预热参数
     */
    @LafValue("warmup_param")
    private String calcFeeWarmUpParam;

    @LafValue("discount_fee_config")
    @JsonConverter
    private DiscountFeeConfig discountFeeConfig;

    /**
     * 护士到家的 SKU列表
     */
    @Getter
    @JsonConverter
    @LafValue("nurses.home.sku.list")
    private List<String> nursesHomeSkuList = new ArrayList<>();

    /**
     * 根据地址查询围栏最大地址数量
     */
    @LafValue("gisAddressLimitNum")
    private Integer gisAddressLimitNum;


    /**
     * 假期 配置
     */
    private Map<String,HolidayConfig> holidayConfigMap;

    /**
     * 假期 配置
     */
    @LafValue("holiday_config")
    private String holidayConfig;

    /**
     * 服务者 入驻地区
     */
    @LafValue("angel_open_area_map")
    @JsonConverter(isSupportGeneric=true)
    private Map<Integer, OpenAreaDuccConfig> angelOpenAreaMap;

    /**
     *  工单详情页虚拟状态栏配置
     *  key： workTypeEnum
     *  value： AngelBizExtStatusEnum
     */
    @LafValue("angel_virtual_status_navigation")
    @JsonConverter
    private Map<Integer, List<Integer>> angelVirtualStatusNavigationMap;

    /**
     * 服务者工单页面 - 保单状态展示配置
     */
    @LafValue("insure_status_desc")
    @JsonConverter
    private Map<Integer, String> insureStatusDescMap;

    /**
     * 服务者履约用到的跳转地址配置
     */
    @LafValue("angel_promise_jump_url")
    @JsonConverter
    private Map<String, String> angelPromiseJumpUrlMap;

    /**
     * 服务者履约用到的跳转地址配置
     */
    @LafValue("work_type_desc")
    @JsonConverter
    private Map<Integer, String> workTypeDescMap;

    @LafValue("dada_callback_url")
    private String dadaCallbackUrl;

    /**
     * 服务者履约用到的跳转地址配置
     */
    @LafValue("work_detail_template")
    private String workDetailTemplateMap;

    /**
     * 服务者 '我的'页面 兜底信息
     */
    @LafValue("angel_mine_default_msg")
    @JsonConverter
    private Map<String,String> angelMineDefaultMsgMap;

    /**
     * 服务者配置提醒
     */
    @LafValue("angel_setting_tips_map")
    @JsonConverter
    private JSONObject angelSettingTipsMap;

    /**
     * 服务者黑名单
     * @return
            */
    @LafValue("angel_black_list")
    @JsonConverter
    private Set<Long> angelBlackListSet;

    /**
     * 服务者黑名单
     * @return
     */
    @LafValue("cps_recommend_product_list")
    @JsonConverter
    private List<CpsSkuConfig> cpsRecommendProductList;

    /**
     * 无订单作废结算场景
     * @return
     */
    @LafValue("no_order_invalid_settle_config")
    @JsonConverter
    private String noOrderInvalidSettleConfig;


    public Map<Integer, OpenAreaDuccConfig> getAngelOpenAreaMap() {
        return angelOpenAreaMap;
    }

    public void setAngelOpenAreaMap(Map<Integer, OpenAreaDuccConfig> angelOpenAreaMap) {
        this.angelOpenAreaMap = angelOpenAreaMap;
    }

    @JsonConverter
    @LafValue("homeServiceProductDetailProperties")
    private Map<String, Map<String, String>> homeServiceProductDetailProperties;

    @LafValue("quick.check.customer.service.link")
    private String customerServiceLink;

    /**
     * 服务者履约用到的跳转地址配置
     */
    @LafValue("work_enum_map")
    @JsonConverter
    private Map<String, List<AngelWorkEnumConfig>> workEnumMap;

    /**
     * 费用算价因子
     */
    @LafValue("fee_calculation_factor")
    @JsonConverter
    private Map<String, Map<String, String>> feeCalculationFactor;

    /**
     * 状态退款商品金额比例
     */
    @LafValue("refund_status_amout_ratio")
    @JsonConverter
    private Map<String, RefundStatusRatioMapping> refundStatusAmoutRatio = Maps.newHashMap();

    /**
     * 状态退款费项金额比例
     */
    @LafValue("refund_status_fee_amout_ratio")
    @JsonConverter
    private Map<String, Map<String,String>> refundStatusFeeAmoutRatio = Maps.newHashMap();

    /**
     * 退款冻结配置
     */
    @JsonConverter
    @LafValue("refundFreezeConfig")
    private Map<String, RefundFreezeMapping> refundFreezeConfig = Maps.newHashMap();

    /**
     * 实验室派发是否圈选二级地址
     */
    @LafValue("dispatchStationLimitCity")
    private Boolean dispatchStationLimitCity;

    /**
     * 费用算价因子
     */
    @LafValue("devSpecimenCode")
    @JsonConverter
    private Set<String> devSpecimenCode;

    /**
     * 状态退款 + 结算 金额比例
     */
    @LafValue("server_settle_amout")
    @JsonConverter
    private Map<String, JSONObject> serverSettleAmoutMap = Maps.newHashMap();

    /**
     * 派单服务者预计收入配置
     */
    @LafValue("angel_dispatch_price_config")
    @JsonConverter
    private Map<String, AngelDispatchPriceConfig> angelDispatchPriceConfigMap;

    /**
     * 新派单流程开关
     */
    @LafValue("dispatch_new_pipeline_switch")
    private Boolean dispatchNewPipelineSwitch;

    /**
     * 城市级别类型
     */
    @LafValue("city_level_type")
    @JsonConverter
    private Map<String, String> cityLevelTypeMap;

    /**
     * 护士结算比例
     */
    @LafValue("angel_settle_ratio")
    @JsonConverter
    private Map<String, JSONObject> angelSettleRatioMap = Maps.newHashMap();

    /**
     *
     */
    @LafValue("reachMessageType")
    private String reachMessageType;

    /**
     * 结算场景code
     */
    @LafValue("settle_vertical_code")
    @JsonConverter
    private String settleVerticalCode;

    /**
     * 查询地址围栏的最大地址数据量
     */
    @LafValue("address_max_size")
    private Integer addressMaxSize;

    /**
     * 配送完成需要距离服务站的最小距离
     */
    @LafValue("max_geo_distance")
    private Double maxGeoDistance;

    /**
     * 派单互医问诊单SKU
     */
    @LafValue("new_nethp_product_id")
    private Long newNethpProductId;

    /**
     * 派单白名单
     */
    @LafValue("dispatch_id_whitelist")
    @JsonConverter
    private List<Long> dispatchIdWhitelist;

    /**
     * 派单详情页跳转链接
     */
    @LafValue("dispatch_detail_url")
    private String dispatchDetailUrl;

    /**
     * 派单执行路由
     */
    @LafValue("dispatch_execute_route")
    private String dispatchExecuteRoute;

    /**
     * 城市派单技能过滤
     */
    @LafValue("dispatch_city_filter")
    @JsonConverter
    private Map<String, JSONObject> dispatchCityFilterMap;

    /**
     * 派单持续时间配置
     */
    @LafValue("dispatch_detail_duration")
    @JsonConverter
    private Map<String, Map<String, JSONObject>> dispatchDetailDurationMap;

    /**
     * 直辖市一级地址ID,京标
     */
    @LafValue("municipalFirstLevelSet")
    @JsonConverter
    private Set<Integer> municipalFirstLevelSet = Sets.newHashSet();

    /**
     * 护士结算类型
     */
    @LafValue("angel_settle_type")
    @JsonConverter
    private Map<String, String> angelSettleTypeMap;

    /**
     * 算价服务公式配置
     */
    @LafValue("pricing_service_formula_config")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, PricingServiceFormula> pricingServiceFormulaConfigMap;

    /**
     * 算价服务费项配置
     */
    @LafValue("pricing_service_fee_config")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, PricingServiceFeeConfig> pricingServiceFeeConfigMap;

    /**
     * 护士结算价开启算价服务开关
     */
    @LafValue("angel_settlement_pricing_service_switch")
    private Boolean angelSettlementPricingServiceSwitch;

    /**
     * 城市等级结算系数配置
     */
    @LafValue("city_level_settlement_coefficient")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, CityLevelSettlementCoefficientConfig> cityLevelSettlementCoefficientConfigMap;

    /**
     * 护士服务ump时间配置
     */
    @LafValue("angel_finish_ump_time")
    @JsonConverter
    private Map<String, Integer> angelFinishUmpTimeConfig;

    /**
     * 护士服务ump开始时间
     */
    @LafValue("angel_ump_start_time")
    @JsonConverter
    private String angelFinishUmpTime;

    /**
     * 结费结算EBS场景配置
     */
    @LafValue("settlement_ebs_scene_param_config")
    @JsonConverter(isSupportGeneric=true)
    private Map<String, JdhSettlementEbsConfig> settlementEbsSceneParamConfig;

    /**
     * 咚咚报警配置
     */
    @LafValue("robotAlarm")
    @JsonConverter
    private Map<String, JSONObject> robotAlarmMap;

    /**
     * 报告详情页-医生楼层配置
     */
    @LafValue("reportDoctorConfig")
    private String reportDoctorConfig;
    /**
     * 是否推送天算测试金额(0.00)
     */
    @LafValue("testTianSuanMoney")
    private Boolean testTianSuanMoney;

    /**
     * 服务站和实验室关联关系
     */
    @LafValue("angelStationToStationMap")
    @JsonConverter
    private Map<String,String> angelStationToStationMap = Maps.newHashMap();

    /**
     * 护士基本工资
     */
    @LafValue("angelBasicSalary")
    private String angelBasicSalary;

    /**
     * 护士日保底工资上限*/
    @LafValue("angelDailyBaseSalaryUpper")
    private String angelDailyBaseSalaryUpper;
    /**
     * 护士每日每小时基本工资
     */
    @LafValue("angelDailyIntervalBasicSalary")
    private String angelDailyIntervalBasicSalary;

    /**
     * 护士每日每小时基本工资 key 职级*地区码 value对应单价 demo:36*51226 : 50.0
     */
    @LafValue("angelDailyIntervalBasicSalaryMap")
    @JsonConverter
    private Map<String,String> angelDailyIntervalBasicSalaryMap = Maps.newHashMap();

    /**
     * 商详路由控制清单
     */
    @LafValue("productRouteControl")
    @JsonConverter
    private Map<Long,String> productRouteControl;

    /**
     * 指标名称映射关系
     */
    @LafValue("indicatorNameMap")
    @JsonConverter
    private Map<String,String> indicatorNameMap = Maps.newHashMap();

    /**
     * 中文字符替换英文字符
     */
    @LafValue("cnCharToEnChar")
    @JsonConverter
    private Map<String,String> cnCharToEnChar;

    /**
     * 是否开启验签
     */
    @LafValue("verify_signature_switch")
    private Boolean verifySignatureSwitch;

    /**
     * 报告页温馨提示
     */
    @LafValue("reportTipsMap")
    @JsonConverter
    private Map<String,String> reportTipsMap;

    /**
     * sku 新老切换map
     * key： 老sku, value 新sku
     */
    @LafValue("sku_old_to_new_map")
    @JsonConverter
    private Map<String, String> skuOldToNewMap;

    /**
     * 开启socket超时限制的与图方法
     */
    @LafValue("limit_socket_timeout_path")
    @JsonConverter
    private List<String> limitSocketTimeoutPath;

    /**
     * http超时配置
     */
    @LafValue("http_timeout_config")
    private String httpTimeoutConfig;

    /**
     * ItemType和feeName的map转换
     */
    @LafValue("item_type_to_fee_name_map")
    @JsonConverter
    private Map<String,String> itemTypeToFeeNameMap ;

    /**
     *
     * 服务者排期模板过期时间编辑开关
     */
    @LafValue("angel_schedule_expire_operate_switch")
    private Boolean angelScheduleExpireOperateSwitch;

    /**
     * 数据清洗时间节点
     */
    @LafValue("data_cleansing_timestamp")
    @JsonConverter
    private  String dataCleansingTimestamp ;



    /**
     * 垂直业务身份编码与sku信息的映射关系 新老切换
     * key： sku上的saleChannel与serviceType拼接 value：verticalCode
     */
    @LafValue("vertical_code_by_sku_info_map")
    @JsonConverter
    private Map<String, String> verticalCodeBySkuInfoMap;

    /**
     * 垂直业务身份编码 新老切换
     * key： Channel与serviceType拼接 value：新Channel与serviceType
     */
    @LafValue("channel_type_to_channel_type_map")
    @JsonConverter
    private Map<String,Map<String,String>> channelServiceTypeConvertMap = Maps.newHashMap();
    /**
     * 服务单详情地址
     */
    @Value("${work_id_detail_url:https://laputa-yf.jd.com/nurse-on-site-service/service/orderDetail?workId=%s}")
    private String workIdDetailUrl;

    /**
     * 护士日保底工资调整开关
     */
    @LafValue("angel_settlement_daily_adjust_switch")
    private Boolean angelSettlementDailyAdjustSwitch;



    /**
     * 快检门店查询状态
     */
    @JsonConverter
    @LafValue("quick.check.store.query.status")
    private Map<String, Object> quickCheckQueryStoreStatus;

    /**
     * 快检评价标签开关 0-关 1-开
     */
    @LafValue("quickCheckCommentTag")
    private Integer quickCheckCommentTag;

    /**
     * 快检评价：guid集合
     */
    @LafValue("commentGuidList")
    private String commentGuidList;

    /**
     * 渠道新项目ID兼容骑手上门
     */
    @JsonConverter
    @LafValue("serviceItemIdToKnight")
    private Map<String,Map<String,String>> serviceItemIdToKnight = Maps.newHashMap();


    /**
     * 达达期望配送时间间隔配置
     */
    @LafValue("dada.delay.publish.time.gap")
    private Integer dadaDelayPublishTime;

    /**
     * 实验室迁移,<stationId,<hours,stationObject>>
     */
    @JsonConverter
    @LafValue("labMigration")
    private Map<String,Map<String,String>> labMigration = Maps.newHashMap();

    /**
     * 快检配置
     */
    @LafValue("quick.check.status.config")
    @JsonConverter
    private Map<String,String> quickCheckStatusConfig = Maps.newHashMap();

    /**
     * 需要生成日账单的businessMode
     */
    @JsonConverter
    @LafValue("needCreateBillBusinessMode")
    private Set<String> needCreateBillBusinessMode = Sets.newHashSet();

    /**
     * 需要生成结构化报告PDF的businessMode
     */
    @JsonConverter
    @LafValue("needCreatePdfBusinessMode")
    private Set<String> needCreatePdfBusinessMode = Sets.newHashSet();

    /**
     * 生成服务者库存的履约时效配置
     *
     */
    @JsonConverter
    @LafValue("schedule_time_slice")
    private InventoryInitConfig scheduleTimeSlice;

    /**
     * 快检条码格式
     */
    @LafValue("jdRandomFormat")
    private String jdRandomFormat;

    /**
     * 检验单条形码最低位数
     */
    @LafValue("randomCodeMinLength")
    private Integer randomCodeMinLength;

    /**
     * 服务站库存配置,可配置服务站不走库存逻辑,直接放行
     */
    @Value("${angel.station.inventory.global.config:{\"angelStationInventoryGlobalOpen\":true,\"whiteAngelStationIds\":[],\"serviceType\":[1,2]}}")
    private String angelStationInventoryConfig;

    /**
     * 服务站-库存初始化配置
     */
    @JsonConverter
    @LafValue("angel_station_inventory_init_config")
    private Map<String,Map<String,Integer>> angelStationInventoryInitConfig;

    /**
     * 服务站映射
     */
    @JsonConverter
    @LafValue("angel_station_map")
    private Map<String, String> angelStationMap;

    /**
     * 服务站映射
     */
    @JsonConverter
    @LafValue("oldKnightSkuIndicatorVaule")
    private Set<String> oldKnightSkuIndicatorValue = Sets.newHashSet();

    /**
     * 服务站映射
     */
    @JsonConverter
    @LafValue("indicatorAbnoramlDesc")
    private Map<String, String> indicatorAbnoramlDesc = Maps.newHashMap();

    /**
     * 合并条码
     */
    @JsonConverter
    @LafValue("mergeMedicalPromiseConfig")
    private Map<String, List<String>> mergeMedicalPromiseConfig = Maps.newHashMap();
    /**
     * 批量上传配置费项不为空的参数校验
     */
    @LafValue("upload_fee_config_check_not_null_collection")
    private String uploadFeeConfigCheckNotNullCollection;
    /**
     * 批量上传配置费项为正数的参数校验
     */
    @LafValue("upload_fee_config_check_positive_collection")
    private String uploadFeeConfigCheckPositiveCollection;

    /**
     * 批量上传配置地址不为空的参数校验
     */
    @LafValue("upload_address_config_check_not_null_collection")
    private String uploadAddressConfigCheckNotNullCollection;
    /**
     * 服务者履约用到的跳转地址配置
     */
    @LafValue("skuShortNameTempMap")
    @JsonConverter
    private Map<String, String> skuShortNameTempMap;


    /**
     * 给护士投保险种id
     */
    @Value("${insurance_code:**********}")
    private String insuranceCode;

    /**
     * 适用人群配置
     */
    @LafValue("item_suitable_config")
    private String itemSuitableConfig;

    /**
     * 标准项目导入文件模板
     */
    @LafValue("standard_item_import_template")
    @JsonConverter
    private Map<String,JSONObject> standardItemImportTemplate;

    /**
     * 商品二级类目id配置
     */
    @LafValue("sku_secondary_category_id_config")
    @JsonConverter
    private List<String> skuSecondaryCategoryIdConfig;


    /**
     * 商品类目id与定制属性id关系
     */
    @LafValue("sku_category_attr_id_map")
    @JsonConverter
    private Map<String, String> skuCategoryAttrIdMap;


    /**
     * 商品类目id与定制属性id关系
     */
    @LafValue("sku_category_attr_suitable_map")
    @JsonConverter
    private Map<String, Object> skuCategoryAttrSuitableMap;

    @LafValue("sx_specification_config")
    private String SxSpecificationConfig;

    /**
     * 商品主数据刷项目数据开关
     */
    @LafValue("product_item_update_switch")
    private Boolean productItemUpdateSwitch;

    @JsonConverter
    @LafValue("specimenCodeLength")
    private List<Integer> specimenCodeLength = Lists.newArrayList(12,13);

    /**
     * 卡临期job执行参数配置,重写set方法
     */
    private List<VoucherRemindBeforeExpireConfig> voucherRemindBeforeExpireConfigList;

    /**
     * 自定义订单信息配置
     */
    @LafValue("custom_order_info_config")
    private String customOrderInfoConfig;


    @LafValue("voucher_remind_before_expire_config")
    public void setVoucherRemindBeforeExpireConfigList(String duccValue) {
        this.voucherRemindBeforeExpireConfigList = JSON.parseArray(duccValue,VoucherRemindBeforeExpireConfig.class);
    }

    /**
     * 计算护士出门时间冲突范围;上下浮动30分钟
     */
    @Value("${time_conflict_scope:30}")
    private Integer timeConflictScope;


    /**
     * 二级缓存全局开关
     */
    @Value("${two_level_cache_config}")
    private String twoLevelCacheConfig;



    /**
     *  根据sku判断报告分享类型
     */
    @JsonConverter
    @LafValue("serviceReportShareType")
    private Map<String,String> serviceReportShareType;

    /**
     * 根据项目判断报告分享类型
     */
    @JsonConverter
    @LafValue("itemReportShareType")
    private Map<String,String> itemReportShareType = Maps.newHashMap();

    @JsonConverter
    @LafValue("shareTypeConfig")
    private Map<String,String> shareTypeConfig = Maps.newHashMap();

    /**
     * 报告分享换成开关
     */
    @LafValue("reportShareCacheSwitch")
    private Boolean reportShareCacheSwitch = Boolean.FALSE;

    @LafValue("shipTypeToAngelDetail")
    @JsonConverter
    private Map<String,Integer> shipTypeToAngelDetail = Maps.newHashMap();

    /**
     * 闪送城市编码和二级城市编码
     */
    @LafValue("ssCityCodeMapJdCityCode")
    @JsonConverter
    private Map<String,String> ssCityCodeMapJdCityCode = Maps.newHashMap();

    /**
     * 京标直辖市一级城市编码
     */
    @JsonConverter
    @LafValue("jdFirstAreaCodeList")
    private List<String> firstAreaList = Lists.newArrayList();
    /**
     * 派发实验室时候，无需关注服务站的列表
     */
    @LafValue("dispatchStationWithNoAngelStation")
    @JsonConverter
    private Set<String> dispatchStationWithNoAngelStation = Sets.newHashSet();
    /**
     * popJumpUrl
     */
    @LafValue("popReportJumpUrl")
    private String popReportJumpUrl;


    @LafValue("popReportMockStr")
    @JsonConverter
    private Map<String,String> popReportMockStr;

    @LafValue("popReportAuthTime")
    private Integer popReportAuthTime;
    /**
     * 报告超时预警时间
     */
    @LafValue("delayTime")
    private Long delayTime;
    /**
     * 商品优惠券配置
     */
    @LafValue("product_coupon_config")
    private String productCouponConfig;

    /**
     * 商详新接口开关
     */
    @LafValue("query_product_detail_floor_new_switch")
    private Boolean queryProductDetailFloorNewSwitch;

    /**
     * 可提前几小时取消工单
     */
    @Value("${cancel_angel_work_hour:1}")
    private Integer cancelAngelWorkHour;

    /**
     * 距离上门时间
     */
    @Value("${time_to_visit_minute:-60}")
    private Integer timeToVisitMinute;

    /**
     * 接单前后时间偏移量
     */
    @Value("${work_start_time_offset_minute:60}")
    private Integer workStartTimeOffsetMinute;

    /**
     * 护士端-待接单-头部tab是否展示
     */
    @Value("${top_tab_show:0}")
    private Integer topTabShow;

    /**
     * 命中规则的商品商品跳转到具体页面的规则
     */
    @JsonConverter
    @LafValue("skuDetailRouteJumpConfig")
    private List<SkuDetailRouteJumpConfig> skuDetailRouteJumpConfig;

    /**
     * 命中规则的商品下发跳转路由地址
     */
    @JsonConverter
    @LafValue("skuDetailRouteConfig")
    private SkuDetailRouteConfig skuDetailRouteConfig;

    /**
     * 经纬度转换开关
     */
    @LafValue("gis_convert_switch")
    private Boolean gisConvertSwitch;

    /**
     * promisego开关
     */
    @LafValue("promisegoSwitch")
    private Boolean promisegoSwitch = Boolean.FALSE;

    /**
     * promisego开关
     */
    @LafValue("usePromiseGoSwitch")
    @JsonConverter
    private UsePromiseGoSwitch usePromiseGoSwitch ;
    /**
     *
     */
    @LafValue("promisegoWhitePin")
    @JsonConverter
    private Set<String> promisegoWhitePin = Sets.newHashSet();

    @LafValue("tagTip")
    @JsonConverter
    private Map<String,String> tagTip = Maps.newHashMap();

    /**
     * 互医报告解读文案配置
     */
    @JsonConverter
    @LafValue("reportNetDoctorConfig")
    private Map<String,Object> reportNetDoctorConfig = Maps.newHashMap();


    /**
     * 报告中推荐药品的配置信息
     */
    @LafValue("reportMedicineRecommend")
    private String reportMedicineRecommend;

    /**
     * 报告中推荐药品的环境配置集合
     */
    @LafValue("reportMedicineRecommendEnv")
    @JsonConverter
    private Set<String> reportMedicineRecommendEnv = Sets.newHashSet();


    /**
     * 只有单个项目是否展示总异常结论
     */
    @LafValue("singleServiceItemAbnormal")
    private Boolean singleServiceItemAbnormal =Boolean.FALSE;

    /**
     * 商祥预测eta总开关
     */
    @LafValue("productDetailEtaSwitch")
    @Deprecated
    private Boolean productDetailEtaSwitch;

    /**
     * 商祥预测eta总开关是否使用
     */
    @LafValue("useProductDetailEtaSwitch")
    private Boolean useProductDetailEtaSwitch;

    /**
     * 商祥预测eta 白名单pin
     */
    @LafValue("productDetailEtaWhitePin")
    @JsonConverter
    private List<String> productDetailEtaPin;



    /**
     * 数据库查询最大限制
     */
    @LafValue("dbQueryMaxLimit")
    private Long dbQueryMaxLimit = 1000L;

    /**
     * 商详分享领券配置
     * @return
     */
    @LafValue("product_detail_share_get_coupon_config")
    @JsonConverter
    private String productDetailShareGetCouponConfig;

    /**
     * abTest配置
     */
    @JsonConverter
    @LafValue("abTestConfig")
    private AbTestConfig abTestConfig;

    /**
     * 默认服务站服务项目ID
     */
    @LafValue("defaultStationServiceItemId")
    private Long defaultStationServiceItemId;

    /**
     * 意向护士派单服务类型配置
     */
    @LafValue("intended_nurse_dispatch_service_type_config")
    @JsonConverter
    private String intendedNurseDispatchServiceTypeConfig;

    /**
     * 意向护士选项服务类型配置
     */
    @LafValue("intended_nurse_option_service_type_config")
    @JsonConverter
    private String intendedNurseOptionServiceTypeConfig;

    /**
     * 护士默认头像配置
     */
    @LafValue("nurse_default_head_img_config")
    @JsonConverter
    private String nurseDefaultHeadImgConfig;

    /**
     * 快检业务线预警时间配置
     */
    @LafValue("homeSelfTestAlarm")
    @JsonConverter
    private JSONObject homeSelfTestAlarm;

    /**
     * 派单失败后自动兜底指定派单成功是否发送目标机器人开关
     */
    @LafValue("dispatchFailTargetRobotSwitch")
    private Boolean dispatchFailTargetRobotSwitch = Boolean.FALSE;
    /**
     * 提问楼层sku
     */
    @LafValue("showQuestionSkuSet")
    @JsonConverter
    private Set<String> showQuestionSkuSet = Sets.newHashSet();

    /**
     * 报告详情页-医生楼层配置
     */
    @LafValue("reportDoctorNewConfig")
    private String reportDoctorNewConfig;

    /**
     * 触达事件规则映射
     */
    @JsonConverter
    @LafValue("verifyReportRootConfig")
    private List<VerifyReportRootConfig> verifyReportRootConfig = Lists.newArrayList();

    /**
     * URL域名白名单
     */
    @LafValue("urlBlankSet")
    @JsonConverter
    private Set<String> urlBlankSet = Sets.newHashSet();

    @JsonConverter
    @LafValue("portBlankSet")
    private Set<Integer> portBlankSet = Sets.newHashSet();

    /**
     * 校验报告内容serviceItemId
     */
    @LafValue("checkReportDetailItemIdSet")
    @JsonConverter
    private Set<String> checkReportDetailItemIdSet = Sets.newHashSet();

    @LafValue("stationCheckBlankItem")
    @JsonConverter
    private Map<String,List<String>> stationCheckBlankItem = Maps.newHashMap();

    /**
     * 指标检查配置
     */
    @LafValue("indicatorCheckConfig")
    @JsonConverter
    private Map<String,String> indicatorCheckConfig = Maps.newHashMap();

    /**
     * 对接顺丰配置
     */
    @LafValue("shun_feng_config")
    @JsonConverter
    private ShunFengConfig shunFengConfig;

    /**
     * 顺丰回调url前缀
     */
    @Value("${shun_feng_url_index:rider_status,rider_recall,order_complete,sf_cancel,rider_exception}")
    private List<String> shunFengUrlIndex;

    /**
     * 骑手供应商允许更改的时间
     */
    @LafValue("delivery_update_time")
    private String deliveryUpdateTime;

    /**
     * 实验室报告正常范围配置
     */
    @LafValue("reportNormalRangeStationConfig")
    @JsonConverter
    private Map<String, ReportNormalRangeStationConfig> reportNormalRangeStationConfig;

    /**
     * 合并条码校验开关
     */
    @JsonConverter
    @LafValue("mergeMedicalPromiseCheckConfig")
    private MergeMedicalPromiseCheckConfig mergeMedicalPromiseCheckConfig;

    @LafValue("dispatch_multi_rounds_config")
    @JsonConverter
    private DispatchMultiRoundsConfig dispatchMultiRoundsConfig;

    /**
     * 服务者活动和职业匹配关系（angel）
     */
    @LafValue("angel_activity_profession")
    @JsonConverter
    private Map<String, List<String>> angelActivityProfession;

    /**
     * 服务升级配置
     */
    @LafValue("service_upgrade_config")
    private String serviceUpgradeConfig;

    /**
     * 报告pdf配置
     */
    @LafValue("report_pdf_config")
    private String reportPdfConfig;

    @LafValue("pathogensTip")
    private String pathogensTip;

    @LafValue("ctTipConfig")
    private String ctTipConfig;

    /**
     * 报告新模版查询总开关
     */
    @LafValue("reportNewTemplateSwitch")
    private Boolean reportNewTemplateSwitch = Boolean.FALSE;

    /**
     * 实验室报告白名单
     */
    @LafValue("reportNewTemplateStation")
    @JsonConverter
    private Set<String> reportNewTemplateStation = Sets.newHashSet();

    /**
     * 虚拟号配置
     */
    @LafValue("securityNumberConfig")
    private String securityNumberConfig;

    @LafValue("indicatorNameToValueMap")
    @JsonConverter
    private Map<String,String> indicatorNameToValueMap = Maps.newHashMap();

    /**
     * 同步手机号开关
     */
    @Value("${syncAngelPhoneSwitch:true}")
    private Boolean syncAngelPhoneSwitch;

    /**
     * 快检一键购药处方单记录
     */
    @LafValue("rxSceneCodeSet")
    @JsonConverter
    private Set<String> rxSceneCodeSet = Sets.newHashSet();

    @LafValue("rxShowExpression")
    private String rxShowExpression;

    /**
     * 快检一键购药开关
     */
    @LafValue("reportShowRecommendSwitch")
    private Boolean reportShowRecommendSwitch = Boolean.FALSE;
    /**
     * 处方楼层开关
     */
    @LafValue("rxShowSwitch")
    private Boolean rxShowSwitch = Boolean.FALSE;

    @LafValue("pdfToJpgSwitch")
    private Boolean pdfToJpgSwitch = Boolean.FALSE;

    /**
     * 导出检测单限制数量
     */
    @LafValue("exportMedicalPromiseInfoLimit")
    private Long exportMedicalPromiseInfoLimit;

    /**
     * 测试单配置
     */
    @Value("${test_specimen_code_config:{\"stationIds\":[\"S128387374\"],\"specimenCodes\":[\"JD2398474\"]}}")
    private String testSpecimenCodeConfig;

    /**
     * 多区域站长兜底配置
     */
    @LafValue("multi_region_master_config")
    @JsonConverter
    private Map<String, Object> multiRegionMasterConfig;

    /**
     * 多区域站长兜底配置Map
     */
    private Map<String, List<Long>> multiRegionMasterMap = Maps.newHashMap();

    /**
     * 排除记录方法类,在logging下面
     */
    @LafValue("excludeRecordClassMethod")
    @JsonConverter
    private List<String> excludeRecordClassMethod;

    @LafValue("pdfToJpgMqSwitch")
    private Boolean pdfToJpgMqSwitch = Boolean.FALSE;


    /**
     * 排除记录方法类,在logging下面
     */
    @LafValue("eventConsumerFailBackConfig")
    @JsonConverter
    private EventConsumerFailBackConfig eventConsumerFailBackConfig;

    @LafValue("contentLengthMin")
    private Integer contentLengthMin = 1000;


    @LafValue("quickDrugConfig")
    @JsonConverter
    private Map<String,String> quickDrugConfig;

    /**
     * 迁移相关配置
     */
    @Value("${excutor_sql:{\"消费医疗\":[{\"tableName\":\"jdh_event_record\",\"queryAppendWhereSql\":\"id <3\",\"delField\":\"id\",\"realDel\":false,\"reserveDay\":120}]}}")
    private String excutorSql;

    /**
     * 迁移元数据配置
     */
    @Value("${big_table_config:{\"open\":true}}")
    private String bigTableConfig;

    /**
     * 虚拟号绑定权限越权校验开关
     */
    @LafValue("bindAxbCallAuthSwitch")
    private Boolean bindAxbCallAuthSwitch = Boolean.TRUE;

    /**
     *
     */
    @LafValue("gray_env_switch")
    private Boolean grayEnvSwitch = Boolean.TRUE;

    public Map<String, List<ExecutorSqlBo>> getExcutorSql() {
        return JSON.parseObject(excutorSql, new TypeReference<Map<String, List<ExecutorSqlBo>>>() {});
    }

    public BigTableConfig getBigTableConfig() {
        return JSON.parseObject(bigTableConfig,BigTableConfig.class);
    }


    /**
     * 夜间服务时间段
     */
    @LafValue("night_service_time")
    @JsonConverter
    private Map<String, JdhFeeTimeConfig> nightServiceTimeMap = Maps.newHashMap();
    /**
     * 高峰服务时间段
     */
    @LafValue("peak_service_time")
    @JsonConverter
    private Map<String,JdhFeeTimeConfig> peakServiceTimeMap = Maps.newHashMap();

    /**
     * 常规派单轮时间可用性配置
     */
    @LafValue("dispatch_time_buffer_duration")
    @JsonConverter
    private Map<String,Integer> dispatchTimeBufferDuration = Maps.newHashMap();

    /**
     * 计算距离时长本地方法配置
     */
    @LafValue("local_calculate_distance_duration_config")
    @JsonConverter
    private JSONObject localCalculateDistanceDurationConfig;

    /**
     * 自动化测试订单白名单
     */
    @LafValue("automatedTestOrderWhitelist")
    @JsonConverter
    private List<Long> automatedTestOrderWhitelist;
    /**
     * 派单是否校验实验室营业时间
     */
    @LafValue("checkStationHours")
    private Boolean checkStationHours = Boolean.FALSE;

    /**
     * 售卖渠道对应的实验室站
     */
    @LafValue("saleChannelLaboratoryStationMap")
    @JsonConverter
    private Map<String, String> saleChannelLaboratoryStationMap;

    /**
     * 京东物流用户编号
     */
    @Value("${jd_logistics_config:{\"customerCode\":\"010K96932\",\"h5Url\":\"https://kd.jd.com/express/orderDetail?source=jiankangdaojia&type=1&deliveryId=%s\"}}")
    private String jdLogisticsConfig;


    /**
     *
     * @return
     */
    public JdLogisticsConfig getJdLogisticsConfig() {
        return JSON.parseObject(jdLogisticsConfig,JdLogisticsConfig.class);
    }


    /**
     * 三方运力供应商配置
     */
    @LafValue("thirdShipSupplier")
    @JsonConverter
    private String thirdShipSupplier;


    @LafValue("canSelectShipTypeConfig")
    private String canSelectShipTypeConfig;


    /**
     * 到家可用时间窗口
     */
    @LafValue("o2o_available_time_window")
    private Integer o2oAvailableTimeWindow;

    /**
     * 实验室阳性率看板开关
     */
    @LafValue("reportPosRateSwitch")
    private Boolean reportPosRateSwitch = Boolean.FALSE;

    /**
     * 快检出报告模版
     */
    @LafValue("checkReportSmsContent")
    @JsonConverter
    private Map<String,String> checkReportSmsContent = Maps.newHashMap();
    @LafValue("preSampleFlagSkuList")
    @JsonConverter
    private List<Long> preSampleFlagSkuList = new ArrayList<>();
    /**
     * 地区费项配置描述
     */
    @LafValue("region_fee_dest_prefix_desc")
    @JsonConverter
    private Map<String, String> regionFeeDestPrefixDesc = new HashMap<>();

    /**
     * 服务质控
     */
    @LafValue("serviceQualityControlRules")
    private String serviceQualityControlRules;

    /**
     * 实验结算黑名单
     */
    @LafValue("tianSuanSettlementStationBlack")
    @JsonConverter
    private List<String> tianSuanSettlementStationBlack = Lists.newArrayList();

    /**
     * 收样指引配置
     */
    @JsonConverter
    @LafValue("acceptSampleGuideConfig")
    private Map<String,String> acceptSampleGuideConfig = Maps.newHashMap();

    /**
     * 快检推送信息配置
     */
    @LafValue("quickCheckStatusMappingConfig")
    private String quickCheckStatusMappingConfig;

    /**
     * 快检推送实验室白名单
     */
    @LafValue("quickCheckStationIdWhiteList")
    @JsonConverter
    private List<String> quickCheckStationIdWhiteList = new ArrayList<>();


    /**
     * 数据变更事件配置
     */
    @LafValue("dbFieldChangeEventConfig")
    private String dbFieldChangeEventConfig;

    /**
     * 服务者接单是否重派实验室配置
     */
    @LafValue("dispatchReceiveReStationDis")
    private String dispatchReceiveReStationDis;

    /**
     * 可开具病假单有效天数
     */
    @Value("${sick_cert_expired_day_num:2}")
    private Integer sickCertExpiredDayNum;

    @Value("${sick_config:}")
    private String sickConfig;


    @LafValue("stationTestCheckUserConfig")
    @JsonConverter
    private Map<String,Map<String,String>> stationTestCheckUserConfig = Maps.newHashMap();


    /**
     * 护士负反馈评价枚举列表
     */
    @LafValue("angel_negative_reason_enum")
    @JsonConverter(isSupportGeneric=true)
    private List<JdhAngelNegativeReasonConfig> angelNegativeReasonList = new ArrayList<>();

    /**
     * 护士负反馈原因枚举与业务模式关联关系
     */
    @LafValue("angel_negative_reason_business_relation")
    @JsonConverter
    private Map<String, JSONObject> angelNegativeReasonBusinessRelationMap = new HashMap<>();

    /**
     * 护士评价页链接配置
     */
    @LafValue("angel_evaluation_page_config")
    @JsonConverter
    private Map<String, JSONObject> angelEvaluationPageConfigMap = new HashMap<>();

    /**
     * 快检实验室推送防重-开关
     */
    @Value("${quickCheckPushRepeatSwitch: true}")
    private Boolean quickCheckPushRepeatSwitch;

    /**
     * 售后消息开关
     */
    @Value("${afsRulesSwitch: false}")
    private Boolean afsRulesSwitch;

    /**
     * 订列缓存开关开关
     */
    @Value("${jdAppOrderListFloorCacheSwitch: false}")
    private Boolean jdAppOrderListFloorCacheSwitch;

    /**
     * 订详缓存开关开关
     */
    @Value("${jdAppOrderDetailFloorCacheSwitch: false}")
    private Boolean jdAppOrderDetailFloorCacheSwitch;

    /**
     * 订详缓存开关开关
     */
    @LafValue("jdAppOrderFloorCacheRule")
    private String jdAppOrderFloorCacheRule;

    /**
     * 数据变动MQ发送开关开关
     */
    @Value("${dbFieldChangeMqPubSwitch: false}")
    private Boolean dbFieldChangeMqPubSwitch;

    /**
     * 商详价格‘起’后加叹号：点击后出现说明弹框
     */
    @Value("${productDetailPriceDescTip:最终支付金额会因选择的时间、地点不同有所差异}")
    private String productDetailPriceDescTip;

    /**
     * 档案PDF组件页面前缀
     */
    @LafValue("centerUrlPre")
    private String centerUrlPre ;

    /**
     * 快检实验室推送配置
     */
    @LafValue("quickCheckPushConfig")
    private String quickCheckPushConfig;

    /**
     * 服务者位置地址编码配置
     */
    @LafValue("angelLocationAreaConfig")
    private String angelLocationAreaConfig;

    @LafValue("codeToOrderDetailUrl")
    private String codeToOrderDetailUrl;

    /**
     * 报告已出发送短信VerticalCode白名单
     */
    @LafValue("smsWhiteVerticalCode")
    @JsonConverter
    private List<String> smsWhiteVerticalCode = Lists.newArrayList("xfylHomeSelfTest1Phase","xfylVtpHomeTest","xfylHomeTest");

    /**
     * POP体检交接job全局开关
     */
    @Value("${popForwardJobGlobalSwitch: false}")
    private Boolean popForwardJobGlobalSwitch;

    /**
     * POP体检交接job全局开关
     */
    @Value("${popForwardMqGlobalSwitch: false}")
    private Boolean popForwardMqGlobalSwitch;

    /**
     * sku中serviceType替换文本配置
     */
    @LafValue("serviceTypeReplaceWordsConfig")
    private String serviceTypeReplaceWordsConfig;


    @LafValue("workQueryValidStatusList")
    @JsonConverter
    private List<Integer> workQueryValidStatusList = Lists.newArrayList(1,2,3,4,5,6);


    @Value("${ai_context:}")
    private String aiContext;//ai解读配置

    /**
     * 京购小程序支付url
     */
    @LafValue("jingGouMiniProgramPayUrl")
    private String jingGouMiniProgramPayUrl;

    /**
     * 定详路由url
     */
    @LafValue("jmiOrderDetailUrl")
    private String jmiOrderDetailUrl;

    /**
     * 提交预约后派发护士新逻辑开关
     */
    @LafValue("submitPromiseDispatchSwitch")
    private Boolean submitPromiseDispatchSwitch = Boolean.FALSE;
    /**
     * 提交预约后派发护士新逻辑白名单，如果在submitPromiseDispatchSwitch为true情况下，白名单不为空，则按白名单来，否则则开放全量
     */
    @LafValue("submitPromiseDispatchWhitePin")
    @JsonConverter
    private List<String> submitPromiseDispatchWhitePin = Lists.newArrayList();

    /**
     * 用户操作频率限制开关
     */
    @LafValue("userOperationLimitSwitch")
    private Boolean userOperationLimitSwitch;

    @LafValue("stationIndicatorNameToId")
    @JsonConverter
    private Map<String,String> stationIndicatorNameToId = Maps.newHashMap();

    /**
     * 无人机配置
     */
    @Value("${uavConfig:}")
    private String uavConfig;//

    @LafValue("uavStoreDispatchLogic")
    private Boolean uavStoreDispatchLogic = Boolean.FALSE;

    /**
     * nps服务问卷配置
     */
    @Value("${serviceSurveyConfig:}")
    private String serviceSurveyConfig;


    /**
     * 平台服务费支出开关
     */
    @LafValue("platformExpendSwitch")
    private Boolean platformExpendSwitch;

    /**
     * 护士修改预约时间规则
     */
    @LafValue("angelModifyDateRule")
    private String angelModifyDateRule;

    /**
     * 护士到达校验与用户地址距离
     */
    @LafValue("angelActionCheckRule")
    private String angelActionCheckRule;

    /**
     * 题配置
     */
    @Value("${question_group_config:}")
    private String questionGroupConfig;

    /**
     * 护理单配置
     */
    @LafValue("angelServiceRecordConfig")
    private String angelServiceRecordConfig;

    /**
     * 服务记录图片上传配置
     */
    @LafValue("serviceRecordImageUploadConfig")
    private String serviceRecordImageUploadConfig;

    /**
     *  指定通知用户配置
     */
    @LafValue("targetNotifyUserConfig")
    @JsonConverter
    private Map<String, JSONObject> targetNotifyUserConfigMap;

    /**
     * 不可约周期
     */
    @LafValue("unableAppointmentPeriod")
    private String unableAppointmentPeriod;

    /**
     * 节假日不可约
     */
    @LafValue("unableAppointmentHoliday")
    private String unableAppointmentHoliday;

    /**
     * 节假日配置
     */
    @LafValue("festivalHolidayConfig")
    private String festivalHolidayConfig;

    /**
     * 实验室最大标签个数
     */
    @LafValue("store.label.max.num")
    private Integer storeLabelMaxNum;


    /**
     * 服务者主页(护士主页+骑手主页)
     */
    @Value("${riderHomeUrl:}")
    private String riderHomeUrl;

    /**
     * 心智订单详情之战配置
     */
    @Value("${heartSmartOrderDetailConfig:}")
    private String heartSmartOrderDetailConfig;


    /**
     * 护士上门护理订列
     */
    @LafValue("homeCareOrderList")
    private String homeCareOrderList;

    /**
     * 护士上门检测定列
     */
    @LafValue("homeTestOrderList")
    private String homeTestOrderList;

    /**
     * 骑手上门检测定列
     */
    @LafValue("selfTestOrderList")
    private String selfTestOrderList;

    /**
     * vtp护士上门检测订列
     */
    @LafValue("vtpHomeTestOrderList")
    private String vtpHomeTestOrderList;

    /**
     * vtp护士上门护理订列
     */
    @LafValue("vtpHomeCareOrderList")
    private String vtpHomeCareOrderList;

    /**
     * 体检订列
     */
    @LafValue("physicalOrderList")
    private String physicalOrderList;

    /**
     * loc订列
     */
    @LafValue("locOrderList")
    private String locOrderList;

    /**
     * 评估师评估页面链接
     */
    @LafValue("evaluation_url")
    private String evaluationUrl;

    /**
     * ai报告解读配置
     */
    @LafValue("aiReportExplainConfig")
    @JsonConverter
    private JSONObject aiReportExplainConfig;

    /**
     * 医嘱楼层描述
     */
    @LafValue("doctor_advice_pic")
    private String doctorAdvicePic;

    /**
     * 医嘱问题code
     */
    @LafValue("quesCodeMedicalCertificate")
    @JsonConverter
    private Set<Long> quesCodeMedicalCertificate;

    /**
     * 就医证明配置
     */
    @LafValue("medicalCertificateConfig")
    private String medicalCertificateConfig;


    @LafValue("excludeQUestionCode")
    @JsonConverter
    private Set<String> excludeQuestionCode = Sets.newHashSet();

    /**
     * 线程超时配置
     */
    @LafValue("completableFutureTimeOutConfig")
    private String completableFutureTimeOutConfig;

    /**
     * 京东外卖配置
     */
    @LafValue("jdWmConfig")
    private String jdWmConfig;

    @LafValue("fileMinByteLength")
    private Integer fileMinByteLength;


    @LafValue("subStatusAlarmConfig")
    @JsonConverter
    private String subStatusAlarmConfig;


    @LafValue("abnormalTemplateConfig")
    private String abnormalTemplateConfig;

    /**
     * 服务者资质信息主页链接
     */
    @LafValue("angel_authentication_url")
    private String angelAuthenticationUrl;

    /**
     * 护士认证黑名单
     */
    @LafValue("angel_main_page_black")
    @JsonConverter
    private Set<Long> angelMainPageBlack;

    @LafValue("rider_head_img")
    private String RiderHeadImg;

    /**
     * 商详服务区域地图配置
     */
    @LafValue("productServiceAreaMapConfig")
    private String productServiceAreaMapConfig;

    @LafValue("heartSmartPinBlank")
    @JsonConverter
    private Set<String> heartSmartPinBlank = org.assertj.core.util.Sets.newHashSet();


    @LafValue("heartSmartStationBlank")
    @JsonConverter
    private Set<String> heartSmartStationBlank = org.assertj.core.util.Sets.newHashSet();

    /**
     * 商品服务身份和系统mode映射关系
     */
    @LafValue("sku_service_to_mode")
    private String skuServiceToMode;
    private Map<String, List<Integer>> skuServiceToModeMap = new HashMap<>();


    @LafValue("angel_certificates_h5_url")
    private String angelCertificatesH5Url;

    @LafValue("newSmsSwitch")
    private Boolean newSmsSwitch = Boolean.FALSE;

    @LafValue("viaWeChatCardConfig")
    private String viaWeChatCardConfig;


    @Value("${callTransferException: false}")
    private Boolean callTransferException;
    /**
     * 白名单内容类型集合，用于过滤不需要处理的内容类型。
     */
    @LafValue("contentTypeWhiteSet")
    @JsonConverter
    private Set<String> contentTypeWhiteSet = Sets.newHashSet();


    @Value("${pinToErpWhiteList:xxx}")
    private List<String> pinToErpWhiteList;

    /**
     * 运单取消报警配置
     */
    @LafValue("ship_opt_robot_config")
    @JsonConverter
    private List<String> ShipOptRobotRuleConfig;
    /**
     * 需要护士自配的项目样本类型配置
     */
    @LafValue("angel_delivery_sample_type")
    @JsonConverter
    private List<Integer> angelDeliverySampleConfig;

    /**
     * 检测单回调状态机
     */
    @LafValue("medicalPromiseCallbackStatusStage")
    private String medicalPromiseCallbackStatusStage;

    /**
     * 设置上门费配置
     *
     * @param homeVisitFeeConfig 上门费配置K
     */
    public void setHomeVisitFeeConfig(String homeVisitFeeConfig) {
        this.homeVisitFeeConfig = homeVisitFeeConfig;
        this.homeVisitFeeConfigMap=new HashMap<>();
        try {
            this.homeVisitFeeConfigMap = JSON.parseObject(this.homeVisitFeeConfig, new TypeReference<Map<String,Map<String,HomeVisitFeeConfig>>>() {});
            if (Objects.isNull(homeVisitFeeConfigMap.get("xfylHomeCare"))){
                this.homeVisitFeeConfigMap.put("common",JSON.parseObject(this.homeVisitFeeConfig, new TypeReference<Map<String, HomeVisitFeeConfig>>() {}));
            }
        }catch (Exception e){
            this.homeVisitFeeConfigMap.put("common",JSON.parseObject(this.homeVisitFeeConfig, new TypeReference<Map<String, HomeVisitFeeConfig>>() {}));
        }
    }

    /**
     * 设置时段费配置
     *
     * @param timePeriodFeeConfig 上门费配置
     */
    public void setTimePeriodFeeConfig(String timePeriodFeeConfig) {
        this.timePeriodFeeConfig = timePeriodFeeConfig;
        this.timePeriodFeeConfigMap=new HashMap<>();
        try {
            this.timePeriodFeeConfigMap = JSON.parseObject(this.timePeriodFeeConfig, new TypeReference<Map<String, TimePeriodFeeConfig>>() {});
            if (Objects.isNull(timePeriodFeeConfigMap.get("xfylHomeCare"))){
                this.timePeriodFeeConfigMap=new HashMap<>();//初始化 排除干扰
                this.timePeriodFeeConfigMap.put("common",JSON.parseObject(this.timePeriodFeeConfig, TimePeriodFeeConfig.class));
            }
        }catch (Exception e){
            this.timePeriodFeeConfigMap.put("common",JSON.parseObject(this.timePeriodFeeConfig, TimePeriodFeeConfig.class));
        }
    }

    /**
     * 设置动态费用配置
     *
     * @param dynamicFeeConfigStr 动态费用配置
     */
    public void setDynamicFeeConfigStr(String dynamicFeeConfigStr) {
        this.dynamicFeeConfigStr = dynamicFeeConfigStr;
        this.dynamicFeeConfig = JSON.parseArray(this.dynamicFeeConfigStr, DynamicFee.class);
    }

    /**
     * 设置假期配置
     *
     * @param holidayConfig 假期配置
     */
    public void setHolidayConfig(String holidayConfig) {
        this.holidayConfig = holidayConfig;
        this.holidayConfigMap = JSON.parseObject(this.holidayConfig,new TypeReference<Map<String,HolidayConfig>>(){});
    }

    public Boolean checkBlankPin(String userPin) {
        if(StringUtils.isBlank(blankPin)){
            return Boolean.FALSE;
        }
        List<String> blankPinList = Splitter.on(",").splitToList(blankPin);
        return blankPinList.contains(userPin);
    }


    public Boolean checkBlankAccountId(String accountId) {
        if(StringUtils.isBlank(blankAccountId)){
            return Boolean.FALSE;
        }
        List<String> blankAccountIdList = Splitter.on(",").splitToList(blankAccountId);
        return blankAccountIdList.contains(accountId);
    }

    /**
     * 需要进行blank逻辑
     *
     * @return {@link Boolean}
     */
    public Boolean getBlankStatusSwitch() {
        if (StringUtils.isBlank(this.getBlankStatus())) {
            return Boolean.FALSE;
        }
        if (NumConstant.NUM_0.toString().equals(this.getBlankStatus())) {
            return Boolean.FALSE;
        }
        if (NumConstant.NUM_1.toString().equals(this.getBlankStatus())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    public boolean checkIsPushOrderShop(String shopId) {
        try {
            if (StringUtil.isBlank(shopId) || StringUtil.isBlank(this.pushOrderShopId)) {
                return false;
            }
            return this.pushOrderShopId.contains(shopId);
        } catch (Exception e) {
            log.error("校验是否为订单推送店铺", e);
            return false;
        }
    }

    /**
     * 获取该业务身份下的用户行为
     *
     * @param identity 业务身份
     * @return List
     */
    public List<String> getIdentityUserActionList(String identity) {
        Map<String, List<String>> identityUserActionMap = this.identityUserActionMap;
        if (MapUtils.isEmpty(identityUserActionMap)) {
            return null;
        }
        // 获取业务身份
        return identityUserActionMap.get(identity);
    }

    /**
     * 获取对应业务身份的sendPay
     *
     * @param identity 业务身份
     * @return Map
     */
    public Map<String, String> getIdentitySendPayMap(String identity) {
        Map<String, Map<String, String>> identitySendPayMap = this.identitySendPayMap;
        if (MapUtils.isEmpty(identitySendPayMap)) {
            return null;
        }
        return identitySendPayMap.get(identity);
    }

    public List<Integer> getAngelVirtualStatusByWorkType(Integer workType){
        if(MapUtils.isEmpty(angelVirtualStatusNavigationMap)){
            return null;
        }
        return angelVirtualStatusNavigationMap.get(workType);
    }

    public String getJumpUrlByType(String jumpType){
        if(MapUtils.isEmpty(angelPromiseJumpUrlMap) || StringUtils.isBlank(angelPromiseJumpUrlMap.get(jumpType))){
            return null;
        }
        return angelPromiseJumpUrlMap.get(jumpType);
    }

    public String getWorkTypeDescByType(Integer workType){
        if(MapUtils.isEmpty(workTypeDescMap)){
            return null;
        }

        return workTypeDescMap.get(workType);
    }

    public List<AngelWorkEnumConfig> getAngelWorkEnumConfigs(String enumKey){
        if(MapUtils.isEmpty(workEnumMap)){
            return null;
        }
        return workEnumMap.get(enumKey);

    }

    public String getAngelMineDefaultMsgMap(String k){
        if(MapUtils.isEmpty(angelMineDefaultMsgMap)){
            return null;
        }
        return angelMineDefaultMsgMap.get(k);
    }

    public Map<String, AngelDispatchPriceConfig> getAngelDispatchPriceConfigMap() {
        return angelDispatchPriceConfigMap;
    }

    public void setAngelDispatchPriceConfigMap(Map<String, AngelDispatchPriceConfig> angelDispatchPriceConfigMap) {
        this.angelDispatchPriceConfigMap = angelDispatchPriceConfigMap;
    }

    public Long getNewNethpProductId() {
        return newNethpProductId;
    }

    public void setNewNethpProductId(Long newNethpProductId) {
        this.newNethpProductId = newNethpProductId;
    }

    public List<Long> getDispatchIdWhitelist() {
        return dispatchIdWhitelist;
    }

    public void setDispatchIdWhitelist(List<Long> dispatchIdWhitelist) {
        this.dispatchIdWhitelist = dispatchIdWhitelist;
    }

    public String getDispatchDetailUrl() {
        return dispatchDetailUrl;
    }

    public void setDispatchDetailUrl(String dispatchDetailUrl) {
        this.dispatchDetailUrl = dispatchDetailUrl;
    }
    public static Boolean determineJsonTypeIsMap(String jsonString) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode jsonNode = mapper.readTree(jsonString);
            return jsonNode.isObject();
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {
        Map<String,Map<String,HomeVisitFeeConfig>> homeVisitFeeConfigMap =new HashMap<>();
        String homeVisitFeeConfig="{\"1\":{\"desc\":\"北京市\",\"fee\":0.01},\"5\":{\"desc\":\"河北\",\"fee\":5},\"2812\":{\"desc\":\"顺义区\",\"fee\":0.01},\"51131\":{\"desc\":\"后沙峪地区\",\"fee\":20},\"7\":{\"desc\":\"河南省\",\"fee\":20},\"412\":{\"desc\":\"郑州市\",\"fee\":20},\"3545\":{\"desc\":\"金水区\",\"fee\":20},\"53540\":{\"desc\":\"人民路街道\",\"fee\":20},\"55542\":{\"desc\":\"亦庄地区\",\"fee\":2}}";
        try {
           homeVisitFeeConfigMap = JSON.parseObject(homeVisitFeeConfig, new TypeReference<Map<String,Map<String,HomeVisitFeeConfig>>>() {});
            if (Objects.isNull(homeVisitFeeConfigMap.get("xfylHomeCare"))){
                homeVisitFeeConfigMap.put("common",JSON.parseObject(homeVisitFeeConfig, new TypeReference<Map<String, HomeVisitFeeConfig>>() {}));
            }
        }catch (Exception e){
            homeVisitFeeConfigMap.put("common",JSON.parseObject(homeVisitFeeConfig, new TypeReference<Map<String, HomeVisitFeeConfig>>() {}));
        }
        System.out.println(JSON.toJSONString(homeVisitFeeConfigMap));
    }

    /**
     * 获取http请求配置
     */
    public AoiMapTimeoutConfig getRequestConfig() {
        if(StringUtils.isBlank(httpTimeoutConfig)){
            return new AoiMapTimeoutConfig();
        }
        return JSON.parseObject(httpTimeoutConfig, AoiMapTimeoutConfig.class);
    }

    /**
     * 获取服务站库存配置
     * @return
     */
    public AngelStationInventoryConfig getAngelStationInventoryConfig(){
        return JSON.parseObject(this.angelStationInventoryConfig, AngelStationInventoryConfig.class);
    }

    /**
     * 二级缓存配置
     * @return
     */
    public TwoLevelCacheConfig getTwoLevelCacheConfig() {
        return JSON.parseObject(this.twoLevelCacheConfig, TwoLevelCacheConfig.class);
    }

    public DispatchMultiRoundsConfig getDispatchMultiRoundsConfig() {
        return dispatchMultiRoundsConfig;
    }

    public void setDispatchMultiRoundsConfig(DispatchMultiRoundsConfig dispatchMultiRoundsConfig) {
        this.dispatchMultiRoundsConfig = dispatchMultiRoundsConfig;
    }

    public AbTestConfig getAbTestConfig() {
        return abTestConfig;
    }

    /**
     * 测试单配置
     * @return
     */
    public TestSpecimenCodeConfigConfig getTestSpecimenCodeConfig() {
        return JSON.parseObject(this.testSpecimenCodeConfig, TestSpecimenCodeConfigConfig.class);
    }

    /**
     *
     * @param multiRegionMasterConfig
     */
    public void setMultiRegionMasterConfig(Map<String, Object> multiRegionMasterConfig) {
        this.multiRegionMasterConfig = multiRegionMasterConfig;

        if (Objects.isNull(multiRegionMasterConfig)) {
            return;
        }
        Map<String, String> safetyNetStrategy = JSON.parseObject(JSON.toJSONString(multiRegionMasterConfig.get("safetyNetStrategy")), new TypeReference<Map<String, String>>() {});
        Map<String, List<Long>> stationMasterGroups = JSON.parseObject(JSON.toJSONString(multiRegionMasterConfig.get("stationMasterGroups")), new TypeReference<Map<String, List<Long>>>() {});
        if (MapUtils.isEmpty(safetyNetStrategy)) {
            return;
        }
        multiRegionMasterMap = Maps.newHashMap();
        for (Map.Entry<String, String> entry : safetyNetStrategy.entrySet()) {
            if (!stationMasterGroups.containsKey(entry.getValue())) {
                continue;
            }
            multiRegionMasterMap.put(entry.getKey(), stationMasterGroups.get(entry.getValue()));
        }
        log.info("DuccConfig -> setMultiRegionMasterConfig end, multiRegionMasterMap:{}", JSON.toJSONString(multiRegionMasterMap));
    }


    public List<QuickCheckStatusMappingConfig> getQuickCheckStatusMappingConfig() {
        return JSON.parseArray(this.quickCheckStatusMappingConfig, QuickCheckStatusMappingConfig.class);
    }

    /**
     * 获取调度接收重发站点配置。
     * @return 调度接收重发站点配置对象，若配置为空则返回 null。
     */
    public DispatchReceiveReStationDisConfig getDispatchReceiveReStationDisConfig() {
        if (StringUtils.isNotBlank(this.dispatchReceiveReStationDis)) {
            return JSON.parseObject(this.dispatchReceiveReStationDis, DispatchReceiveReStationDisConfig.class);
        }
        return null;
    }

    public List<CodeToOrderDetailUrlConfig> getCodeToOrderDetailUrl() {
        return JSON.parseArray(this.codeToOrderDetailUrl, CodeToOrderDetailUrlConfig.class);
    }

    public AiContextConfig getAiContext() {
        return JSON.parseObject(this.aiContext,AiContextConfig.class);
    }

    public UavConfig getUavConfig() {
        return JSON.parseObject(this.uavConfig,UavConfig.class);
    }

    public List<QuestionGroupConfig> getQuestionGroupConfig(){
        return JSON.parseArray(this.questionGroupConfig, QuestionGroupConfig.class);
    }


    /**
     * 用户nps服务问卷
     * @return
     */
    public ServiceSurveyConfig getServiceSurveyConfig() {
        return JSON.parseObject(this.serviceSurveyConfig, ServiceSurveyConfig.class);
    }

    /**
     * 心智之战订单详情配置
     * @return
     */
    public HeartSmartOrderDetailConfig getHeartSmartOrderDetailConfig() {
        return JSON.parseObject(this.heartSmartOrderDetailConfig,HeartSmartOrderDetailConfig.class);
    }

    /**
     * 获取检测单子状态报警配置。
     * @return SubStatusAlarmConfig 对象，表示子状态报警配置。
     */
    public List<SubStatusAlarmConfig> getSubStatusAlarmConfig() {
        return JSON.parseArray(this.subStatusAlarmConfig,SubStatusAlarmConfig.class);

    }

    public List<AbnormalTemplateConfig> getAbnormalTemplateConfig(){
        return JSON.parseArray(this.abnormalTemplateConfig,AbnormalTemplateConfig.class);
    }

    public void setSkuServiceToMode(String skuServiceToMode) {
        this.skuServiceToMode = skuServiceToMode;

        if(StringUtils.isBlank(skuServiceToMode)) {
            log.info("DuccConfig -> setSkuServiceToMode, skuServiceToModeMap={}", JSON.toJSONString(skuServiceToModeMap));
            skuServiceToModeMap.clear();
            return;
        }
        JSONObject jsonObject = JSON.parseObject(skuServiceToMode);
        if(Objects.nonNull(jsonObject) && jsonObject.size() > 0) {
            jsonObject.forEach((key, value) -> {
                skuServiceToModeMap.put(key, JSON.parseArray(JSON.toJSONString(value), Integer.class));
            });
        }
        log.info("DuccConfig -> setSkuServiceToMode, skuServiceToModeMap={}", JSON.toJSONString(skuServiceToModeMap));
    }
}
