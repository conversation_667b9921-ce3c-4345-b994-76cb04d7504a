package com.jdh.o2oservice.base.constatnt;

/**
 * 网关常量
 *
 * <AUTHOR>
 * @date 2023/12/05
 */
public class GwConstants {

    /**
     * 用户pin
     */
    public static final String PIN = "pin";

    /**
     * 请求业务数据,json字符串
     */
    public static final String BODY = "body";

    /**
     * 用户请求ip
     */
    public static final String IP = "ip";

    /**
     * 用户请求浏览器 User-Agent
     */
    public static final String USER_AGENT = "User-Agent";

    /**
     * 转发请求唯一标误
     */
    public static final String REQUEST_ID = "X-API-Request-Id";

    /**
     * 用户请求cookie
     */
    public static final String COOKIE = "Cookie";
    /**
     * 设备号（注意：同一设备，设备号是固定不变的，不可以传动态变化的值，否则防刷会不准确）
     */
    public static final String Uuid = "uuid";

    /**
     * 客户端版本
     */
    public static final String CLIENT_VERSION = "clientVersion";

    /**
     * appKey
     */
    public static final String X_APP_KEY = "x_app_key";

    /**
     * agent
     */
    public static final String AGENT = "agent";

    /**
     * eid
     */
    public static final String EID = "eid";

    /**
     * location
     */
    public static final String LOCATION = "location";

}