package com.jdh.o2oservice.base.util;

import com.jdh.o2oservice.base.enums.SkuBusinessProcessTypeEnum;
import com.jdh.o2oservice.base.model.BusinessModeParam;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeNewEnum;

import java.util.Objects;

/**
 * @ClassName BusinessModeUtil
 * @Description
 * <AUTHOR>
 * @Date 2025/7/7 20:54
 **/
public class BusinessModeUtil {

    /**
     * 根据商品服务类型获取业务模式
     * @param param
     * @return
     */
    public static BusinessModeEnum getBusinessModeEnum(BusinessModeParam param) {
        if (Objects.isNull(param)) {
            return null;
        }
        return getBusinessModeEnum(param.getSkuServiceType(), param.getBusinessProcessType());
    }

    /**
     * 根据商品服务类型获取业务模式
     * @param skuServiceType
     * @return
     */
    public static BusinessModeEnum getBusinessModeEnum(Integer skuServiceType) {
        if (Objects.isNull(skuServiceType)) {
            return null;
        }
        switch (skuServiceType) {
            case 1:
                return BusinessModeEnum.SELF_TEST;
            case 2:
                return BusinessModeEnum.ANGEL_TEST;
            case 3:
                return BusinessModeEnum.ANGEL_CARE;
            case 4:
                return BusinessModeEnum.SELF_TEST_TRANSPORT;
            case 5:
                return BusinessModeEnum.ANGEL_CARE;
            default:
                return null;
        }
    }

    public static Integer getSkuServiceType(BusinessModeEnum businessModeEnum) {
        if (Objects.isNull(businessModeEnum)) {
            return null;
        }
        switch (businessModeEnum) {
            case SELF_TEST:
                return 1;
            case ANGEL_TEST:
                return 2;
            case ANGEL_CARE:
                return 3;
            case SELF_TEST_TRANSPORT:
                return 4;
            case ANGEL_TEST_NO_LABORATORY:
                return 2;
            default:
                return null;
        }
    }

    /**
     * 根据商品服务类型、业务流程类型获取业务模式
     * @param skuServiceType
     * @param businessProcessType
     * @return
     */
    public static BusinessModeEnum getBusinessModeEnum(Integer skuServiceType, Integer businessProcessType) {
        if (Objects.isNull(businessProcessType)) {
            return getBusinessModeEnum(skuServiceType);
        }
        //如果服务类型为护士上门检测，并且业务流程类型为仅采样+送样，则返回护士上门检测（无实验室履约）业务模式
        if (Objects.equals(skuServiceType, ServiceTypeNewEnum.ANGEL_TEST.getType()) &&
                Objects.equals(businessProcessType, SkuBusinessProcessTypeEnum.ONLY_SAMPLING.getType())) {
            return BusinessModeEnum.ANGEL_TEST_NO_LABORATORY;
        }
        return getBusinessModeEnum(skuServiceType);
    }
}