package com.jdh.o2oservice.base.ducc.model.fee;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
public class HomeAndTimeFeeConfig implements Serializable {
    /**
     * 描述
     */
    private String desc;
    /**
     * 上门费
     */
    private BigDecimal onSiteFee;
    /**
     * 立即
     */
    private BigDecimal immediately;
    /**
     * 节假日
     */
    private BigDecimal holiday;
    /**
     * 夜间上门费
     */
    private BigDecimal nightDoorFee;
    /**
     * 高峰时段服务费
     */
    private BigDecimal peakServiceFee;
    /**
     * '地区费项配置id'
     */
    private Long areaFeeConfigId;
}
