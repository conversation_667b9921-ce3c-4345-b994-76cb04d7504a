package com.jdh.o2oservice.base.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @ClassName:GeoDistanceUtil
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/20 11:08
 * @Vserion: 1.0
 **/
public class GeoDistanceUtil {
    // 地球半径（单位：米）
    private static final double EARTH_RADIUS = 6371000;

    /**
     * 计算两点之间的距离
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 两点之间的距离（单位：米）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        double latDistance = toRadians(lat2 - lat1);
        double lonDistance = toRadians(lon2 - lon1);
        double a = sin(latDistance / 2) * sin(latDistance / 2) +
                cos(toRadians(lat1)) * cos(toRadians(lat2)) *
                        sin(lonDistance / 2) * sin(lonDistance / 2);
        double c = 2 * atan2(sqrt(a), sqrt(1 - a));
        return EARTH_RADIUS * c;
    }

    /**
     * 计算两点之间的距离
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 两点之间的距离（单位：米）
     */
    public static String calculateDistanceDynamicUnit(double lat1, double lon1, double lat2, double lon2) {
        double latDistance = toRadians(lat2 - lat1);
        double lonDistance = toRadians(lon2 - lon1);
        double a = sin(latDistance / 2) * sin(latDistance / 2) +
                cos(toRadians(lat1)) * cos(toRadians(lat2)) *
                        sin(lonDistance / 2) * sin(lonDistance / 2);
        double c = 2 * atan2(sqrt(a), sqrt(1 - a));
        int distance = (int)(EARTH_RADIUS * c);
        if (distance <= 1000){
            return distance + "米";
        }else {
            double size = (double)distance/1000d;
            double finalResult = Math.round(size * 10.0) / 10.0;
            return finalResult + "千米";
        }
    }

    /**
     * 根据距离,自动拼接单位
     * @param distance
     * @return
     */
    public static String distanceDynamicUnit(Integer distance){
        if (distance <= 1000){
            return distance + "米";
        }else {
            double size = (double)distance/1000d;
            double finalResult = Math.round(size * 10.0) / 10.0;
            return finalResult + "千米";
        }
    }

    /**
     * 计算两点之间距离
     *
     * @return km(四舍五入)
     */
    private static double getDistance(double inputLat1, double inputLon1, double inputLat2, double inputLon2) {
        //经度
        double lat1 = (Math.PI / 180) * inputLat1;
        //传入经度
        double lat2 = (Math.PI / 180) * inputLat2;
        //纬度
        double lon1 = (Math.PI / 180) * inputLon1;
        //传入纬度
        double lon2 = (Math.PI / 180) * inputLon2;
        //地球半径
        double r = 6378.1;
        double d = Math.acos(Math.sin(lat1) * Math.sin(lat2) + Math.cos(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1)) * r;
        try {
            return new BigDecimal(d).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 两个坐标之间的直线上N个等距点的坐标
     * @param lat1
     * @param lon1
     * @param lat2
     * @param lon2
     * @param N
     * @return
     */
    public static List<double[]> getGeoEquidistantPoints(
            double lat1, double lon1, double lat2, double lon2, int N) {
        List<double[]> points = new ArrayList<>();
        double radLat1 = Math.toRadians(lat1), radLon1 = Math.toRadians(lon1);
        double radLat2 = Math.toRadians(lat2), radLon2 = Math.toRadians(lon2);

        // 转为三维坐标
        double[] A = {
                Math.cos(radLat1) * Math.cos(radLon1),
                Math.cos(radLat1) * Math.sin(radLon1),
                Math.sin(radLat1)
        };
        double[] B = {
                Math.cos(radLat2) * Math.cos(radLon2),
                Math.cos(radLat2) * Math.sin(radLon2),
                Math.sin(radLat2)
        };

        double dot = A[0]*B[0] + A[1]*B[1] + A[2]*B[2];
        double omega = Math.acos(dot);

        for (int i = 1; i <= N; i++) {
            double t = i * 1.0 / (N + 1);
            double sinOmega = Math.sin(omega);
            double kA = Math.sin((1 - t) * omega) / sinOmega;
            double kB = Math.sin(t * omega) / sinOmega;
            double x = kA * A[0] + kB * B[0];
            double y = kA * A[1] + kB * B[1];
            double z = kA * A[2] + kB * B[2];
            double lat = Math.atan2(z, Math.sqrt(x * x + y * y));
            double lon = Math.atan2(y, x);
            points.add(new double[]{Math.toDegrees(lat), Math.toDegrees(lon)});
        }
        return points;
    }

    /**
     * 获取切割点的第N个
     * @param lat1
     * @param lon1
     * @param lat2
     * @param lon2
     * @param N
     * @param getN
     * @return
     */
    public static double[] getGeoEquidistantPoints(
            double lat1, double lon1, double lat2, double lon2, int N, int getN) {
        List<double[]> lt = getGeoEquidistantPoints(lat1, lon1, lat2, lon2, N);
        if (CollUtil.isEmpty(lt)) {
            return null;
        }
        if (getN < 1) {
            getN = 1;
        }
        if (lt.size() < getN) {
            getN = lt.size();
        }
        return lt.get(getN - 1);
    }

    // 将角度转换为弧度
    private static double toRadians(double degree) {
        return degree * Math.PI / 180;
    }

    // 导入数学函数
    private static double sin(double radians) {
        return Math.sin(radians);
    }

    private static double cos(double radians) {
        return Math.cos(radians);
    }

    private static double atan2(double y, double x) {
        return Math.atan2(y, x);
    }

    private static double sqrt(double a) {
        return Math.sqrt(a);
    }

    // 测试方法
    public static void main(String[] args) {
//        double lat1 = 39.786617954	;
//        double lon1 = 116.562913321;
//        double lat2 = 39.785059000;
//        double lon2 = 116.561359000;
//
//        double distance = calculateDistance(lat1, lon1, lat2, lon2);
//        BigDecimal bd = new BigDecimal(distance);
//        double roundedDistance = bd.setScale(2, RoundingMode.HALF_UP).doubleValue();
//        System.out.println("The distance between the two points is: " + roundedDistance + " meters.");


        double lat1 = 39.786617954	;
        double lon1 = 116.562913321;
        double lat2 = 39.785059000;
        double lon2 = 119.561359000;

        double distance = getDistance(lat1, lon1, lat2, lon2);
        BigDecimal bd = new BigDecimal(distance);
        double roundedDistance = bd.setScale(2, RoundingMode.HALF_UP).doubleValue();
        System.out.println("The distance between the two points is: " + roundedDistance + " meters.");

        List<double[]> lit = getGeoEquidistantPoints(lat1, lon1, lat2, lon2, 1000);
        System.out.println(JSON.toJSONString(lit));

        Date shipStartTime = DateUtil.parseDateTime("2025-06-10 19:00:00");
        Date shipEndTime = DateUtil.parseDateTime("2025-06-10 20:40:00");
        Long curr = new Date().getTime() - shipStartTime.getTime();
        Long all = shipEndTime.getTime() - shipStartTime.getTime();
        System.out.println(curr);
        System.out.println(all);
        BigDecimal timeRate = BigDecimal.valueOf(curr).divide(BigDecimal.valueOf(all), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        System.out.println(timeRate.toString());
    }
}
