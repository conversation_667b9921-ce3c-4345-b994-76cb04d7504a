package com.jdh.o2oservice.vertical.ext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.medicine.base.common.util.http.client.SimpleHttpClient;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.core.domain.support.ship.dto.CancelShipResponse;
import com.jdh.o2oservice.core.domain.support.ship.dto.CreateShipResponse;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.bo.CancelDadaShipBo;
import com.jdh.o2oservice.ext.ship.bo.CreateDadaShipBo;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.*;
import com.jdh.o2oservice.vertical.DdApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:DadaCreateShipExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/27 02:22
 * @Vserion: 1.0
 **/
@Extension(code = DdApp.CODE)
@Slf4j
public class DdCreateShipExtImpl implements CreateShipExt {

    /**
     * 基础地址
     */
    @Value("${dada.base.url}")
    private String baseUrl;

    /**
     * 商户id
     */
    @Value("${dada.sourceId}")
    private String sourceId;

    /**
     * app_key
     */
    @Value("${dada.appKey}")
    private String dadaAppKey;

    /**
     * app_secret
     */
    @Value("${dada.appSecret}")
    private String dadaAppSecret;

    /**
     *
     * @param createDadaShip
     * @return
     */
    @Override
    public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShip, Date planOutTime, String providerShopNo) {
        log.info("[DdCreateShipExtImpl -> callTransfer],createDadaShip={}", JSON.toJSONString(createDadaShip));

        CreateDadaShipBo createDadaShipBo = new CreateDadaShipBo();
        BeanUtils.copyProperties(createDadaShip, createDadaShipBo);
        createDadaShipBo.setOriginMark(CommonConstant.BUSINESS_CODE_ABBR);
        createDadaShipBo.setOriginMarkNo(createDadaShip.getBusinessOrderNo());

        //检查骑手的时间,判断是预约单还是即时单
        LocalDateTime delayPublishTime = LocalDateTime.ofInstant(planOutTime.toInstant(), ZoneId.systemDefault()).withSecond(0);
        log.info("[DadaCreateShipExtImpl -> callTransfer], 预计发单时间!delayPublishTime={}", JSON.toJSONString(delayPublishTime));

        if(Objects.nonNull(planOutTime) && delayPublishTime.isAfter(LocalDateTime.now()) && StringUtils.isNotBlank(providerShopNo)){
            try{
                Long epochSecond = delayPublishTime.atZone(ZoneId.systemDefault()).toEpochSecond();
                createDadaShipBo.setDelayPublishTime(epochSecond.intValue());
            }catch (Exception ex){
                log.error("[DadaCreateShipExtImpl -> callTransfer], 检查预计发单时间异常!");
            }
        }

        //创建达达运单
        String methodUrl = "/api/order/addOrder";
        CreateShipResponse response = processInvoke(methodUrl, createDadaShipBo, CreateShipResponse.class);
        if (Objects.isNull(response)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }

        CreateShipExtResponse createShipExtResponse = new CreateShipExtResponse();
        createShipExtResponse.setTotalDistance(response.getDistance());
        createShipExtResponse.setEstimateCallTime(
                Objects.isNull(createDadaShipBo.getDelayPublishTime()) ?
                        TimeUtils.localDateTimeToDate(LocalDateTime.now().withSecond(0)) :
                        TimeUtils.localDateTimeToDate(ZonedDateTime.ofInstant(
                                Instant.ofEpochSecond(createDadaShipBo.getDelayPublishTime()),
                                ZoneId.systemDefault()
                        ).toLocalDateTime()));

        CreateShipExtFeeResponse extFeeResponse = new CreateShipExtFeeResponse();
        extFeeResponse.setAmount(response.getFee());
        extFeeResponse.setTotalAmount(response.getDeliverFee());
        extFeeResponse.setDiscountAmount(response.getCouponFee());
        createShipExtResponse.setExtFeeResponse(extFeeResponse);
        return ExtResponse.buildSuccess(createShipExtResponse);
    }

    /**
     * 重新呼叫运力
     * @param createDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
        String methodUrl = "/api/order/reAddOrder";
        CreateShipResponse response = processInvoke(methodUrl, createDadaShipParam, CreateShipResponse.class);
        if (Objects.isNull(response)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 取消运力
     *
     * @param cancelDadaShipParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
        String methodUrl = "/api/order/formalCancel";
        CancelDadaShipBo cancelDadaShipBo = new CancelDadaShipBo();
        BeanUtils.copyProperties(cancelDadaShipParam, cancelDadaShipBo);
        CancelShipResponse deductFee = processInvoke(methodUrl, cancelDadaShipBo, CancelShipResponse.class);
        if (Objects.isNull(deductFee)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return ExtResponse.buildSuccess(true);
    }

    /**
     * 运力供应商状态回传转换
     *
     * @param callbackParamMap
     * @return
     */
    @Override
    public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
        return ExtResponse.buildSuccess(null);
    }

    /**
     * 查询起手轨迹
     *
     * @param deliveryTrackParam 查询起手轨迹参数
     * @return
     */
    @Override
    public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
        String methodUrl = "/api/order/transporter/track";
        DeliveryTrackResponse deliveryTrackResponse = processInvoke(methodUrl, deliveryTrackParam, DeliveryTrackResponse.class);
        if (Objects.isNull(deliveryTrackResponse)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return deliveryTrackResponse;
    }

    /**
     * 查询运单详情
     *
     * @param deliveryOrderDetailRequest
     * @return
     */
    @Override
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        String methodUrl = "/api/order/status/query";
        DeliveryOrderDetailResponse deliveryOrderDetailResponse = processInvoke(methodUrl, deliveryOrderDetailRequest, DeliveryOrderDetailResponse.class);
        if (Objects.isNull(deliveryOrderDetailResponse)) {
            throw new SystemException(SystemErrorCode.JSF_INVOKE_ERROR);
        }
        return ExtResponse.buildSuccess(deliveryOrderDetailResponse);
    }

    /**
     * 调用商家信息
     */
    private <T> T processInvoke(String methodUrl, Object requestObj, Class<T> cls) {
        String requestUrl = baseUrl.concat(methodUrl);
        log.info("[DadaCreateShipExtImpl.processInvoke],开始执行请求, url: {}, 参数: {}", requestUrl, JSON.toJSONString(requestObj));

        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");

        Map<String, Object> paramMap = generateParams(requestObj);
        log.info("[DadaCreateShipExtImpl.processInvoke],paramMap={}", JSON.toJSONString(paramMap));

        String httpResponse = SimpleHttpClient.simplePost(requestUrl, headMap, JSON.toJSONString(paramMap));
        log.info("[DadaCreateShipExtImpl.processInvoke],httpResponse={}", httpResponse);

        if (StringUtils.isBlank(httpResponse)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        // 根据status判断是否执行成功
        DaDaResponse response = JSON.parseObject(httpResponse, new TypeReference<DaDaResponse>() {
        });
        if (Objects.nonNull(response) && Objects.equals(response.getStatus(), "success")) {
            Object result = response.getResult();
            log.info("[DadaCreateShipExtImpl.processInvoke],result={}", JSON.toJSONString(result));
            return JSON.parseObject(JSON.toJSONString(result), cls);
        } else {
            log.error("[DadaCreateShipExtImpl.processInvoke],达达接口访问返回异常!");
            throw new BusinessException(new DynamicErrorCode("-1",JSON.toJSONString(response)));
        }
    }


    /**
     * 组装请求参数
     *
     * @param body
     * @return
     */
    private Map<String, Object> generateParams(Object body) {
        Map<String, Object> data = Maps.newHashMap();
        data.put("source_id", sourceId);
        data.put("app_key", dadaAppKey);
        data.put("timestamp", LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
        data.put("format", "json");
        data.put("v", "1.0");
        data.put("body", JSON.toJSONString(body));
        data.put("signature", signature(data, dadaAppSecret));
        return data;
    }

    /**
     * 生成签名(具体参考文档: http://newopen.imdada.cn/#/quickStart/develop/safety?_k=kklqac)
     */
    private String signature(Map<String, Object> data, String appSecret) {
        // 请求参数按照【属性名】字典升序排序后，按照属性名+属性值拼接
        String signStr = data.keySet().stream()
                .sorted()
                .map(it -> String.format("%s%s", it, data.get(it)))
                .collect(Collectors.joining(""));

        // 拼接后的结果首尾加上appSecret
        String finalSignStr = appSecret + signStr + appSecret;

        // MD5加密并转为大写
        return DigestUtils.md5Hex(finalSignStr).toUpperCase();
    }
}
